# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# database 
dev.db
*.db-journal

# testing
coverage

# next.js
.next/
out/
build
!**/public/**/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
!packages/database/.env

# turbo
.turbo

# build output
dist/

# temp file
.temp.json
