import { createFlag, RouteFlagNotFoundException } from './flags';

describe('createFlag', () => {
  it('should create a flag object with the given flag, name, and children', () => {
    const flag = createFlag('systems', '系统管理', {
      users: createFlag('systems:users', '用户管理'),
    });

    expect(flag.flag).toBe('systems');
    expect(flag.name).toBe('系统管理');
    expect(flag.children).toEqual({
      users: {
        flag: 'systems:users',
        name: '用户管理',
        children: undefined,
        find: expect.any(Function),
      },
    });
  });
});

describe('findFlag', () => {
  it('should find the flag object with the given flag', () => {
    const flag = createFlag('systems', '系统管理', {
      users: createFlag('systems:users', '用户管理'),
    });

    const foundFlag = flag.find('systems:users');
    expect(foundFlag).toEqual({
      flag: 'systems:users',
      name: '用户管理',
      children: undefined,
      find: expect.any(Function),
    });
  });

  it('should throw an error if the flag object is not found', () => {
    const flag = createFlag('systems', '系统管理', {
      users: createFlag('systems:users', '用户管理'),
    });

    expect(() => {
      flag.find('invalidFlag');
    }).toThrow(RouteFlagNotFoundException);
  });

  it('should find the flag object with the given flag of child', () => {
    const flag = createFlag('systems', '系统管理', {
      users: createFlag('systems:users', '用户管理', {
        edit: createFlag('systems:users:edit', '用户编辑'),
        update: createFlag('systems:users:update', '用户编辑'),
      }),
    });

    const foundFlag = flag.find('systems:users:update');

    expect(foundFlag).toEqual({
      flag: 'systems:users:update',
      name: '用户编辑',
      children: undefined,
      find: expect.any(Function),
    });
  });
});
