{"name": "@pkg/commons", "version": "0.0.1", "description": "", "main": "./dist/**", "module": "./dist/**", "types": "./dist/**", "exports": {"./storage": {"import": "./dist/storage.mjs", "require": "./dist/storage.js", "types": "./dist/storage.d.ts"}, "./flags": {"import": "./dist/flags.mjs", "require": "./dist/flags.js", "types": "./dist/flags.d.ts"}}, "files": ["dist/**"], "scripts": {"build-pkg": "tsup", "dev-pkg": "tsup --watch", "test": "jest"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"config": "*", "eslint": "^8.12.0", "rimraf": "^3.0.2", "tsconfig": "*", "tsup": "^5.11.13", "typescript": "^5.0.2"}, "dependencies": {"@pkg/utils": "*"}}