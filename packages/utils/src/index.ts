/**
 * 把JSON字符串反序列化为JSON对象
 * @param str JSON字符串
 * @returns 反序列化后的JSON对象，如果解析失败则返回undefined
 */
export const parserJSON = <T = any>(str: any): T | undefined => {
  try {
    return JSON.parse(str);
  } catch (e) {
    return undefined;
  }
};

/**
 * 把JSON对象序列化为JSON字符串
 * @param obj  JSON对象
 * @returns JSON字符串，如果序列化失败则返回undefined
 */
export const stringifyJSON = (obj: any): string | undefined => {
  try {
    return JSON.stringify(obj);
  } catch (e) {
    return undefined;
  }
};

export type TreeNode<T extends { [key: string]: any }> = T & { children?: TreeNode<T>[] };

/**
 * 将列表转换为树结构
 * @param {string} idKeyName 从列表元素中获取id的键名
 * @param {string} parentIdKeyName 从列表元素中获取pid的键名
 * @param {Array} list 原始数据列表
 * @returns {Array} 树结构列表
 */
export function convertListToTree<T extends { [key: string]: any }>(
  idKeyName: keyof T,
  parentIdKeyName: keyof T,
  list: T[],
): TreeNode<T>[] {
  // 创建一个空的树结构列表
  const treeList: T[] = [];

  // 创建一个映射表，用于快速查找元素
  const map: { [key: string]: TreeNode<T> } = {};

  // 遍历原始数据列表，将每个元素添加到映射表中
  list.forEach((item) => {
    // 通过结构赋值clone一份数据
    map[item[idKeyName]] = { ...item };
  });

  // 遍历原始数据列表，将每个元素添加到树结构列表中
  list.forEach((item) => {
    // 获取当前元素的父节点ID
    const parentId = item[parentIdKeyName];

    // 如果当前元素没有父节点，则将其作为根节点添加到树结构列表中
    if (!parentId) {
      treeList.push(map[item[idKeyName]]);

      return;
    }

    // 如果当前元素有父节点，则将其添加到父节点的children属性中
    const parentItem = map[parentId];

    if (parentItem) {
      if (!parentItem.children) {
        parentItem.children = [];
      }

      parentItem.children.push(map[item[idKeyName]]);
    }
  });

  return treeList;
}
