import { convertListToTree, parserJSO<PERSON>, stringifyJ<PERSON><PERSON> } from './index';

describe('parserJSO<PERSON>', () => {
  it('should parse JSON string to object', () => {
    const str = '{"id":1,"name":"<PERSON>"}';
    const expected = { id: 1, name: '<PERSON>' };

    expect(parserJSON(str)).toEqual(expected);
  });

  it('should return undefined if parsing fails', () => {
    const str = '{id:1,name:"<PERSON>"}';

    expect(parserJSON(str)).toBeUndefined();
  });
});

describe('stringifyJSON', () => {
  it('should convert object to JSON string', () => {
    const obj = { id: 1, name: '<PERSON>' };
    const expected = '{"id":1,"name":"<PERSON>"}';

    expect(stringifyJSON(obj)).toEqual(expected);
  });

  it('should return undefined if conversion fails', () => {
    const obj = { id: 1, name: '<PERSON>', address: {} };

    // Circular reference will cause stringify to fail
    obj.address = obj;
    expect(stringifyJSON(obj)).toBeUndefined();
  });
});

describe('convertListToTree', () => {
  it('should return empty array when list is empty', () => {
    const result = convertListToTree('id', 'parentId', []);

    expect(result).toEqual([]);
  });

  it('should return tree structure when list is valid', () => {
    const list = [
      { id: 1, parentId: null, name: 'Parent 1' },
      { id: 2, parentId: 1, name: 'Child 1-1' },
      { id: 3, parentId: 1, name: 'Child 1-2' },
      { id: 4, parentId: null, name: 'Parent 2' },
      { id: 5, parentId: 4, name: 'Child 2-1' },
    ];

    const result = convertListToTree('id', 'parentId', list);

    expect(result).toEqual([
      {
        id: 1,
        parentId: null,
        name: 'Parent 1',
        children: [
          { id: 2, parentId: 1, name: 'Child 1-1' },
          { id: 3, parentId: 1, name: 'Child 1-2' },
        ],
      },
      {
        id: 4,
        parentId: null,
        name: 'Parent 2',
        children: [{ id: 5, parentId: 4, name: 'Child 2-1' }],
      },
    ]);
  });
});
