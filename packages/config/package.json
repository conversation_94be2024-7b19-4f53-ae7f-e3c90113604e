{"name": "config", "version": "1.0.0", "main": "index.js", "license": "MIT", "files": ["eslint-preset.js", "prettier-preset.js"], "dependencies": {"@tastien/eslint-config-tastien": "^0.0.10", "@typescript-eslint/eslint-plugin": "^6.2.1 ", "@typescript-eslint/parser": "^6.2.1 ", "eslint-config-next": "^13.4.1", "eslint-config-prettier": "^8.3.0", "eslint-config-turbo": "^1.9.3", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "7.28.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3"}, "devDependencies": {"@types/prettier": "^2.7.3", "typescript": "^4.5.3"}}