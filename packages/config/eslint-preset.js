module.exports = {
  root: true,
  parser: '@typescript-eslint/parser',
  extends: ['@tastien/eslint-config-tastien/next'],
  rules: {
    '@next/next/no-img-element': 'off', // 禁止使用 <img> 元素
    'max-lines': 'off', // 文件最大行数限制
    'max-lines-per-function': 'off', // 函数最大行限制
    'no-inline-comments': 'off', // 禁止内联注释
    'max-classes-per-file': 'off', // 每个文件中的类数限制
    'jsx-a11y/iframe-has-title': 'off',
    'no-template-curly-in-string': 'off',
    'prefer-destructuring': 'off',
    'import-order': 'off',
  },
};
