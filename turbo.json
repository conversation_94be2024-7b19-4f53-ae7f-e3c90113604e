{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "globalEnv": ["NODE_ENV"], "pipeline": {"build-biz": {"dependsOn": ["^build-pkg"]}, "dev-biz": {}, "build-utils": {}, "build-pkg": {"dependsOn": ["^build-utils"], "cache": false, "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "build-admin": {"dependsOn": ["^build-pkg", "^build-biz"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"], "env": ["MODE"]}, "build-h5": {"dependsOn": ["^build-pkg", "^build-biz"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "dev-pkg": {}, "dev-admin": {"dependsOn": ["dev-pkg", "dev-biz"], "cache": false, "persistent": true}, "dev-h5": {"dependsOn": ["dev-pkg", "dev-biz"], "cache": false, "persistent": true}, "tsc": {}, "lint": {}, "test": {"cache": false, "dependsOn": ["^build-pkg"]}}}