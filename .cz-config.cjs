module.exports = {
  skipQuestions: ['scope', 'body', 'breaking', 'footer'],
  types: [
    { value: 'feat', name: '新功能' },
    { value: 'fix', name: '修补bug' },
    { value: 'upd', name: '功能更新（不是重构，只是功能上的修改）' },
    { value: 'style', name: '样式更新，（不影响代码运行的变动）' },
    { value: 'revert', name: '恢复' },
    { value: 'test', name: '添加测试' },
    { value: 'perf', name: '性能优化' },
    {
      value: 'refactor',
      name: '重构（即不是新增功能，也不是修改 bug 的代码变动）',
    },
    { value: 'ci', name: '自动化流程相关' },
    { value: 'build', name: '打包相关' },
    { value: 'docs', name: '文档' },
    { value: 'chore', name: '杂项' },
  ],
};
