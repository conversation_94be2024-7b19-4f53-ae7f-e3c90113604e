{"private": true, "workspaces": ["apps/*", "packages/*"], "prisma": {"schema": "packages/database/prisma/schema.prisma", "seed": "tsx packages/database/src/seed.ts"}, "scripts": {"build-pkg": "turbo run build-pkg", "build-biz": "turbo run build-biz", "build-admin": "turbo run build-biz build-admin", "dev-admin": "turbo run dev-admin", "dev-h5": "turbo run dev-h5", "build-h5": "turbo run build-biz build-h5", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "lint": "turbo run lint", "test": "turbo run test", "prepare": "husky install", "tsc": "turbo run tsc"}, "dependencies": {"@testing-library/jest-dom": "^6.1.2"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@sentry/vite-plugin": "^2.6.2", "@testing-library/react": "14.0.0", "@types/jest": "^29.5.3", "@types/node": "^20.5.1", "@types/qs": "^6.9.8", "cross-env": "^7.0.3", "dotenv": "^16.3.1", "husky": "^8.0.3", "jest": "^29.6.3", "jest-environment-jsdom": "^29.6.3", "prettier": "^2.5.1", "sass": "^1.67.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsx": "^3.7.1", "turbo": "1.10.15"}, "packageManager": "yarn@1.22.19", "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.json": ["prettier --write"], "*.{scss,less,html}": ["prettier --write"], "*.md": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}