import { getOssFileUrl, getOssToken } from '@src/api';
import { OssTokenProps } from '@src/api/type/index.type';
import OSS from 'ali-oss';
import dayjs from 'dayjs';
// eslint-disable-next-line import/no-extraneous-dependencies
import { nanoid } from 'nanoid';

const useUploadFile = () => {
  const getToken = async () => {
    let token: OssTokenProps;

    const tokenStr = localStorage.getItem('ALI_OSS_TOKEN');
    const tokenObj = tokenStr ? JSON.parse(tokenStr || '') : null;

    if (tokenObj && tokenObj.expiration >= dayjs().unix()) {
      token = tokenObj;
    } else {
      token = (await getOssToken()) || ({} as OssTokenProps);
      localStorage.setItem('ALI_OSS_TOKEN', JSON.stringify(token));
    }

    return token;
  };

  const uploadFile = async (file: File): Promise<{ key: string; url: string }> => {
    const token = await getToken();
    const client = token
      ? new OSS({
          // yourRegion填写Bucket所在地域。以华东1（杭州）为例，yourRegion填写为oss-cn-hangzhou。
          region: 'oss-cn-shanghai',
          accessKeyId: token.accessKeyId,
          accessKeySecret: token.accessKeySecret,
          stsToken: token.securityToken,
          // 填写Bucket名称。
          bucket: token.bucket,
          // 刷新临时访问凭证。
          secure: true,
          refreshSTSToken: async () => {
            const newToken = await getOssToken();

            return {
              accessKeyId: newToken?.accessKeyId,
              accessKeySecret: newToken?.accessKeySecret,
              stsToken: newToken?.securityToken,
            };
          },
        })
      : null;

    const uid = nanoid();
    const name = `${token?.prefix}${uid}`;
    const fileName = encodeURIComponent(file.name || '');

    return new Promise(async (resolve, reject) => {
      try {
        const response = await client?.put(name, file, {
          headers: {
            'Content-Disposition': `attachment;filename=${fileName}`,
          },
        });

        console.log(response, 'oss put then');

        if (response) {
          const res = await getOssFileUrl(response.name);
          const params = {
            key: response.name,
            url: res.url,
          };

          resolve(params);
        }
      } catch (e: any) {
        console.log(e, e?.code);
        reject(e);
      }
    });
  };

  return uploadFile;
};

export default useUploadFile;
