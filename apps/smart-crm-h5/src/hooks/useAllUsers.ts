import { getAllUsers } from '@src/api';
import { useRequest } from 'ahooks';

const useAllUsers = () => {
  const { data, loading } = useRequest(
    async () => {
      const res = await getAllUsers();
      const list = res?.map((v) => ({
        ...v,
        label: v.name,
        value: String(v.id),
      }));
      return list;
    },
    {
      cacheKey: 'all-users',
    },
  );
  return { data, loading };
};

export default useAllUsers;
