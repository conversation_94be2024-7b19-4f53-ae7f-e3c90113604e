import { getBusinessFieldList } from '@src/api';
import { FieldItemType, FieldTypeEnum, ValueType } from '@src/api/type/index.type';
import { useRequest } from 'ahooks';
import useAllUsers from './useAllUsers';

const useFieldColumns = () => {
  const { data: fields = [], loading } = useRequest(() => getBusinessFieldList());
  const { data: userData } = useAllUsers();

  const getFieldProps = (item: FieldItemType) => {
    if (item.valueType === ValueType.REGION) {
      return {
        regionLevel: item.regionLevel,
        regionMode: item.regionMode,
      };
    }

    if (item.valueType === ValueType.DATE_TIME) {
      return {
        precision: 'second',
      };
    }

    if (item.valueType === ValueType.NUMBER) {
      return {
        fixed: item.precisions,
        min: -999999999,
        max: 999999999,
      };
    }

    if (item.valueType === ValueType.TEXT) {
      return {
        maxLength: 500,
      };
    }

    if (item.valueType === ValueType.MULTIPLE) {
      return {
        options: item.options || [],
      };
    }

    if (item.valueType === ValueType.PERSON) {
      return {
        options: userData,
      };
    }
  };

  const baseFields = fields.filter((v) => v.fieldType === FieldTypeEnum.BASE);
  const customFields = fields.filter((v) => v.fieldType === FieldTypeEnum.CUSTOM);

  return {
    fields,
    baseFields,
    customFields,
    loading,
    getFieldProps,
  };
};

export default useFieldColumns;
