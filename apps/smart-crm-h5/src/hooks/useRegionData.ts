import { convertListToTree } from '@pkg/utils';
import { getRegionData } from '@src/api';
import { useRequest } from 'ahooks';

// let cache: any;

const useRegionData = () => {
  const { data, loading } = useRequest(
    async () => {
      // if (!cache) {
      const res = await getRegionData();
      const list = (res || []).map((v: { name: string; code: string }) => ({
        ...v,
        label: v.name,
        value: v.code,
      }));
      const treeData = convertListToTree<OptionItem & { id: number; parentId: number }>(
        'id',
        'parentId',
        list,
      );

      // cache = treeData;
      // }
      return treeData;
    },
    {
      cacheKey: 'region-data',
    },
  );

  return { loading, data };
};

export default useRegionData;
