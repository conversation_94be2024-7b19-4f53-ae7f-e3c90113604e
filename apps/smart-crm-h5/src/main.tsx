import ReactDOM from 'react-dom/client';
import App from './App.tsx';
import './index.css';
// eslint-disable-next-line import/order
import { BrowserTracing, Replay, init as sentryInit } from '@sentry/react';

const appRootElement = document.getElementById('root');

if (!appRootElement) {
  throw new Error('应用根元素不存在');
}

if (!import.meta.env.DEV) {
  sentryInit({
    dsn: 'https://<EMAIL>/30',
    // 用户操作重放的采样率
    replaysSessionSampleRate: 0.1,

    // 当报错时，重放的采样率
    replaysOnErrorSampleRate: 1.0,
    integrations: [
      new BrowserTracing(),
      new Replay({
        // Additional SDK configuration goes in here, for example:
        maskAllText: true,
        blockAllMedia: true,
      }),
    ],
    // 性能跟踪的采样率
    tracesSampleRate: 0.1,
    environment: import.meta.env.MODE,
  });
}

ReactDOM.createRoot(appRootElement).render(<App />);
