import { Permission } from '@src/api/type';
import { getUserAuth, getUserInfo } from '@src/api/user';
import { makeObservable, observable } from 'mobx';

class UserStore {
  permissions: Permission[] = [];
  userInfo: { phone?: string; userId?: string | number } = {};
  // 标志完成用户权限的获取
  authLoading: boolean = true;

  constructor() {
    makeObservable(this, {
      permissions: observable,
      userInfo: observable,
      authLoading: observable,
    });
  }

  getUserAuth = async () => {
    const permissions = await getUserAuth();
    this.permissions = permissions;
    this.authLoading = false;
  };

  getUserInfo = async (info?: any) => {
    const userInfo = info ? info : await getUserInfo();
    this.userInfo = { ...userInfo };
  };
}

const userStore = new UserStore();
export default userStore;
