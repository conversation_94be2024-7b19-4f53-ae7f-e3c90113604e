import { AxiosResponse, AxiosRequestConfig } from 'axios';
import { NetWorkErrorException, UnauthorizedException, http } from './axios';
import { HttpResult } from './result';
import { NavigateFunction, Location } from 'react-router-dom';
import React, { useEffect } from 'react';
import { notification } from 'antd';
import { removeToken } from '@src/utils/tokenUtils';

const ref = React.createRef<{
  navigate: NavigateFunction | undefined;
  loc: Location | undefined;
}>();

// 当错误为未授权异常时返回到首页
const unauthorizedErrorHandle = (error: any) => {
  if (error instanceof UnauthorizedException) {
    removeToken();
    const loc = ref.current?.loc;
    const redirect = `${loc?.pathname || '/'}${loc?.search || ''}`;
    location.replace(`${location.origin}/h5/#/user/login?redirect=${encodeURIComponent(redirect)}`);
  }
  return Promise.reject(error);
};

const errorHandler = (alertErr: boolean) => (error: any) => {
  if (error) {
    if (error instanceof UnauthorizedException) {
      removeToken();
      const loc = ref.current?.loc;
      const redirect = `${loc?.pathname || '/'}${loc?.search || ''}`;
      location.replace(
        `${location.origin}/h5/#/user/login?redirect=${encodeURIComponent(redirect)}`,
      );
      // ref.current?.('/user/login', { replace: true });
    } else if (error instanceof NetWorkErrorException) {
      const { message } = error;
      notification.error({
        message: '网络异常',
        // message: `请求错误 ${status}: ${url}`,
        description: `${message}`,
      });
    } else if (alertErr) {
      const { response, message } = error;
      const msg = response?.data?.message || message || '';
      const code = response?.data?.code || response?.statusCode || '';
      const title = code ? `错误提示` : `未知错误`;
      notification.error({
        message: title,
        // message: `请求错误 ${status}: ${url}`,
        description: `${msg}`,
      });
      return Promise.reject(error);
    }
    return Promise.reject(error);
  }
  return Promise.reject(error);
};

type HttpGet<T, D> = typeof http.get<HttpResult<T>, AxiosResponse<HttpResult<T>, D>, D>;
type HttpPost<T, D> = typeof http.post<HttpResult<T>, AxiosResponse<HttpResult<T>, D>, D>;
type HttpPut<T, D> = typeof http.put<HttpResult<T>, AxiosResponse<HttpResult<T>, D>, D>;
type HttpDelete<T, D> = typeof http.delete<HttpResult<T>, AxiosResponse<HttpResult<T>, D>, D>;

// 这边通过将http的方法重新封装一遍，是为了于axios 库解耦，方便后续替换
const get = <T = any, D = any>(...arg: Parameters<HttpGet<T, D>>): ReturnType<HttpGet<T, D>> =>
  http.get(...arg).catch(unauthorizedErrorHandle);
const post = <T = any, D = any>(...arg: Parameters<HttpPost<T, D>>): ReturnType<HttpPost<T, D>> =>
  http.post(...arg).catch(unauthorizedErrorHandle);
const put = <T = any, D = any>(...arg: Parameters<HttpPut<T, D>>): ReturnType<HttpPut<T, D>> =>
  http.put(...arg).catch(unauthorizedErrorHandle);
const del = <T = any, D = any>(
  ...arg: Parameters<HttpDelete<T, D>>
): ReturnType<HttpDelete<T, D>> => http.delete(...arg).catch(unauthorizedErrorHandle);

type Options = AxiosRequestConfig & {
  noAlert?: boolean;
  useOriginResponse?: boolean;
};

const customRequest =
  (method: 'POST' | 'GET' | 'DELETE') =>
  <T = any>(url: string, { useOriginResponse, ...options }: Options) => {
    return http
      .request<HttpResult<T>>({
        url,
        ...options,
        method,
      })
      .then(async (rs) => {
        if (useOriginResponse) return rs as unknown as T;
        return rs.data.result || rs.data.data;
      })
      .catch(errorHandler(!options.noAlert));
  };

const request = {
  get: customRequest('GET'),
  post: customRequest('POST'),
  del: customRequest('DELETE'),
};

/**
 * 设置navigate到ref中，方便请求处理未授权异常
 * @param navigate
 */
const useOnNavigateRef = (navigate: NavigateFunction, loc: Location) => {
  useEffect(() => {
    (ref.current as any) = { navigate, loc };
    return () => {
      (ref.current as any) = null;
    };
  }, [navigate, loc]);
};

const updateCurLocation = (loc: Location) => {
  loc && ((ref.current as any) = { loc });
};

export { del, get, post, put, useOnNavigateRef, updateCurLocation, request };
