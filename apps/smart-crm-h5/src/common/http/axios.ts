import axios from 'axios';
import { NetWorkErrorException, ServerErrorException, UnauthorizedException } from './errors';
export * from './errors';
import qs from 'qs';
import { getToken } from '@src/utils/tokenUtils';

const http = axios.create({
  paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' }),
});

http.interceptors.request.use((config) => {
  const token = getToken();
  if (token) {
    config.headers.Authorization = `${token}`;
    config.headers['user-token'] = `${token}`;
    config.headers.source = 'H5';
  }
  return config;
});

http.interceptors.response.use(
  (response) => {
    const { data } = response;
    // 根据后端请求结构，如果code不为200，则认为是异常
    if (data.code !== 200) {
      if (data.code === 401) {
        return Promise.reject(new UnauthorizedException());
      }
      // 抛出服务器异常，并且携带response
      return Promise.reject(new ServerErrorException(data.message, response));
    }
    return response;
  },
  (error) => {
    const errorCode = [
      error.response?.status,
      error.response?.data?.code,
      error.response?.data?.errCode,
    ];
    if (errorCode.includes(401)) {
      return Promise.reject(new UnauthorizedException());
    }

    if (errorCode.includes(500)) {
      return Promise.reject(new ServerErrorException(error.response.data.message, error.response));
    }

    if (error.response?.data && error.response?.data?.message) {
      return Promise.reject(new ServerErrorException(error.response.data.message, error.response));
    }

    if (error.code === 'ERR_NETWORK') {
      return Promise.reject(new NetWorkErrorException());
    }
    return Promise.reject(error);
  },
);

export { http };
