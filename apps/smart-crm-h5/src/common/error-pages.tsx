/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-24
 * @LastEditors: chenweibin <EMAIL>
 * @LastEditTime: 2023-11-01
 */
import { ErrorBlock } from 'antd-mobile';
import { useRouteError } from 'react-router-dom';

export default function ErrorPage() {
  const error = useRouteError() as any;

  return (
    <div id="error-page">
      {/* <h1>Oops!</h1>
      <p>抱歉，有一个未处理的错误！</p>
      <p>
        <i>{error.statusText || error.message}</i>
      </p> */}
      <ErrorBlock
        status="default"
        style={{
          '--image-height': '200px',
        }}
        description={<i>{error?.statusText || error?.message}</i>}
      ></ErrorBlock>
    </div>
  );
}
