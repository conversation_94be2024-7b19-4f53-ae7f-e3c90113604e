import { AuditStatusEnum, OperationTypeEnum } from '@src/pages/customer/enum';
import { ValueType } from './index.type';

export type Customer = {
  id: number;
  name: string;
  gender: 'MALE' | 'FEMALE';
  phone: string;
  phoneSensitive: string;
  birthday: string;
  budget: string;
  progressId: number;
  progressName: string;
  director: number;
  directorName: string;
  finalDirectorName: string;
  finalDirector: number;
  tagId: { id: number; name: string }[];
  province: string;
  intentionCity: string;
  source: string;
  auditStatus: AuditStatusEnum;
  belongCluePoolType: string;
  age: string;
  extraCity: string;
  operationMode: string;
  remark: string;
  industry: string;
  recognitionPoint: string;
  advantage: string;
  newIntentionCity: string;
};

export type CustomerBase = Pick<
  Customer,
  'id' | 'name' | 'phone' | 'directorName' | 'director' | 'belongCluePoolType'
> & {
  progressId: number;
  progressName: string;
  callFlag: boolean;
};

export type TagItem = {
  id: number;
  name: string;
  tags?: TagItem[];
  type: string;
  status: string;
};

export type ActivityItem = {
  /** 操作类型 */
  operationType: OperationTypeEnum;
  /** 时间 */
  activityTime: string;
  /** 操作人 */
  userName: string;
  /** 线索名称 */
  customerName: string;
  /** 转让或分配员工姓名 */
  transferUserName: string;
  /** 转移线索名称 */
  transferCluePoolName: string;
  /** 更新字段名称  */
  updateFieldName: string;
  /** 更新前值 */
  updateBefore: string;
  /** 更新后值 */
  updateAfter: string;
  /** 线索手机号 */
  customerPhone: string;
  /** 通话时长 */
  callDuration: number;
  /** 信息 */
  info: string;
  /** 附件 */
  attachments?: {
    fileUrl: string;
    fileName: string;
    fileType: string;
  }[];
  valueType: ValueType;
};

export enum GenderEnum {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  UNKNOWN = 'UNKNOWN',
}

export type CreateCustomerReq = {
  fieldValues: { field: string; value: any }[];
  [key: string]: any;
};

export type PublicSeaItem = {
  id: number;
  name: string;
};
