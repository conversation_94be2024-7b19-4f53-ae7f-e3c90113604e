export type OssTokenProps = {
  accessKeyId: string;
  accessKeySecret: string;
  bucket: string; // 桶名
  expiration: string; // 过期
  prefix: string; // 上传文件路径
  securityToken: string;
};

export enum FieldTypeEnum {
  BASE = 'BASE',
  CUSTOM = 'CUSTOM',
  SYSTEM = 'SYSTEM',
}

export enum ValueType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  PHONE = 'PHONE',
  IDENTITY_CARD = 'IDENTITY_CARD',
  DATE = 'DATE',
  DATE_TIME = 'DATE_TIME',
  PERSON = 'PERSON',
  SINGLE = 'SINGLE',
  MULTIPLE = 'MULTIPLE',
  TAG = 'TAG',
  REGION = 'REGION',
  TEXT_ARRAY = 'TEXT_ARRAY',
}

export enum RegionModeEnum {
  SINGLE = 'SINGLE',
  MULTIPLE = 'MULTIPLE',
}

export type FieldItemType = {
  id: number;
  field: string;
  name: string;
  fieldType: FieldTypeEnum;
  searchFlag: boolean;
  editFlag: boolean;
  showFlag: boolean;
  valueType: ValueType;
  precisions?: number;
  options?: { label: string; value: string }[];
  regionLevel?: 1 | 2 | 3;
  regionMode?: RegionModeEnum;
  notNull: boolean;
  showRules?: { field: string; values: string[] }[];
};

export enum SensitiveTypeEnum {
  /** 手机号 */
  PHONE = 'PHONE',
  /** 身份证号 */
  ID_CARD = 'ID_CARD',
}
