import { request } from '@src/common/http';

export const getCode = (data: { redirectUri: string; scope: string; state: string }) =>
  request.post('/gaia-api/wx/cp/buildAuthorizationUrl', { data });

export const wxLogin = (code: string) =>
  request.post('/gaia-api/userlogin/wxCpLogin', { data: { code } });

export const getUserInfo = () => Promise.resolve({ userId: '', phone: '' });

export const getUserAuth = () => Promise.resolve([]);
