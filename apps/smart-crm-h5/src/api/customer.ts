import { request } from '@src/common/http';
import { DynamicEnum } from '@src/pages/customer/enum';
import {
  ActivityItem,
  CreateCustomerReq,
  Customer,
  CustomerBase,
  PublicSeaItem,
  TagItem,
} from './type/customer.type';

// 企微外部联系人id转换
export const convertId = (id: string) =>
  request.post('/api/corp-wx/clue/convert-to-clueId', { data: { externalUserid: id } });

// 查询线索详情
export const getCustomerDetail = (id: number) =>
  request.get<Customer>(`/api/corp-wx/clue/queryClueDetail/${id}`, {});

// 线索进展
export const getCustomerProgress = () =>
  request.post<{ name: string; id: number }[]>('/api/customerProgress/list', {});

export const getTagList = () =>
  request.get<{ result: TagItem[] }>('/api/tag/group/list', {
    params: { status: 'AVAILABLE' },
  });

// 线索打标
export const updateUserTag = (data: {
  addTagIds: number[];
  removeTagIds: number[];
  clueId: number;
}) => request.post('/api/corp-wx/clue/mark-tag', { data });

// 线索企业好友信息（员工）
export const getEmployee = (customerId: number) =>
  request.get<{ avatar: string; name: string; followTime: string }[]>(
    `/api/corp-wx/clue/${customerId}/corp-wx/follow-user`,
    {},
  );

// 线索动态
export const getCustomerDynamic = (
  params: {
    pageNum: number;
    pageSize: number;
    clueId: number;
    businessId?: number;
    businessType?: string;
    callPhone?: string;
  },
  active: DynamicEnum,
) => {
  const urlMap = {
    [DynamicEnum.线索动态]: '/api/corp-wx/clue/activity/page',
    [DynamicEnum.信息变更]: '/api/corp-wx/operation/log/page',
    [DynamicEnum.跟进记录]: '/api/corp-wx/clue/activity/follow/page',
    [DynamicEnum.通话记录]: '/api/corp-wx/call/log/page',
  };

  return request.get<{ result: ActivityItem[]; total: number }>(
    urlMap[active] || '/api/corp-wx/clue/activity/page',
    {
      params,
    },
  );
};
export const getQwCustomerInfo = (externalUserid: string) =>
  request.get(`/api/corp-wx/external-user/${externalUserid}`, {});
// 获取线索简单信息列表
export const getCustomerList = (params: { name?: string; phone?: string }) =>
  request.get<Customer[]>('/api/corp-wx/clue/simple/list', { params });
// 绑定线索
export const bindCustomer = (data: { corpWxExternalUserid: string; clueId: number }) =>
  request.post('/api/corp-wx/clue/bound/corp-wx-external-user', { data });

// 更新线索字段值
export const updateCustomer = ({ id, ...data }: { id: number } & CreateCustomerReq) =>
  request.post(`/api/corp-wx/clue/update/clue/${id}`, { data });

// 拥有的线索权限
export const getCustomerPermission = (customerId: number) =>
  request.get<{ editable: boolean; operable: boolean }>(
    `/api/corp-wx/clue/user/operation/auth/${customerId}`,
    {},
  );

// h5创建线索
export const createCustomer = (data: CreateCustomerReq) =>
  request.post('/api/corp-wx/clue/save/clue', { data });

// 线索列表分页
export const getCustomerPage = (data: {
  pageNum: number;
  pageSize: number;
  search?: string;
  orderField: string;
  orderType: string;
}) =>
  request.post<{ result: CustomerBase[]; total: number }>('/api/corp-wx/clue/simple/page', {
    data,
  });
export const getCustomerPageByPublic = (data: {
  pageNum: number;
  pageSize: number;
  search?: string;
  orderField: string;
  orderType: string;
  cluePoolId?: number;
}) =>
  request.post<{ result: CustomerBase[]; total: number }>('/api/corp-wx/clue/pool/simple/page', {
    data,
  });

// 线索池
export const getPublicSeaData = () =>
  request.get<PublicSeaItem[]>('/api/corp-wx/cluePool/common/permissionList', {});

export const checkMobileExist = (phone: string) =>
  request.get(`/api/corp-wx/clue/check-phone`, { noAlert: true, params: { phone } });

export const getClueRelCustomer = (clueId: number) =>
  request.get<{ id: number; name: string }>(`/api/corp-wx/clue/rel/customer/${clueId}`, {});
