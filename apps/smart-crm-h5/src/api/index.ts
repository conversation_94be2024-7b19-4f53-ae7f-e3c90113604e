import { request } from '@src/common/http';
import { FieldItemType, OssTokenProps, SensitiveTypeEnum } from './type/index.type';

export const getWxConfig = (url: string) =>
  request.post('/api/corp-wx/js-sdk/signature/config', { data: { url } });
export const getAgentConfig = (url: string) =>
  request.post('/api/corp-wx/js-sdk/signature/agent-config', { data: { url } });

// 中台地区接口
// export const getRegionData = () => request.post('/gaia-api/getProvinceCityArea', {});
export const getRegionData = () => request.get('/api/admin/district/list', {});

// 获取线索字段
export const getBusinessFieldList = () =>
  request.get<FieldItemType[]>('/api/corp-wx/field/business/list?businessType=MY_CLUE', {});

export const getOssToken = () => request.get<OssTokenProps>('/api/corp-wx/common/sts', {});

export const getOssFileUrl = (key: string) =>
  request.post('/api/corp-wx/common/oss/getFileUrl', { data: { key } });

//
export const getAllUsers = (params?: { numberLimit?: 'DEFAULT_RULE' }) =>
  request.get<{ id: number; name: string }[]>('/api/corp-wx/user/simple/list', { params });

// 解密脱敏
export const decryptSensitive = (params: { encrypted: string; sensitiveType: SensitiveTypeEnum }) =>
  request.get<string>('/api/corp-wx/common/sensitive/decrypt', {
    params,
  });
