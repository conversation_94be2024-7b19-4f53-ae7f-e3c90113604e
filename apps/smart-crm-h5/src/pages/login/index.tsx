import { getCode } from '@src/api/user';
import { Button, ErrorBlock } from 'antd-mobile';
import { useSearchParams } from 'react-router-dom';

const Login = () => {
  const [search] = useSearchParams();
  const redirect = search.get('redirect') || 'state';

  const userLogin = async () => {
    const test_login_url = 'https%3A%2F%2Fcrm-test.tastientech.com%2Fh5%2F%23%2Fuser%2FwxLogin';
    const stage_login_url = 'https%3A%2F%2Fcrm-stage.tastientech.com%2Fh5%2F%23%2Fuser%2FwxLogin';
    const pro_login_url = 'https%3A%2F%2Fcrm.tastientech.com%2Fh5%2F%23%2Fuser%2FwxLogin';
    const redirectUri = location.host.includes('stage')
      ? stage_login_url
      : location.host.includes('test')
      ? test_login_url
      : pro_login_url;
    const res = await getCode({
      redirectUri: redirectUri,
      scope: 'snsapi_privateinfo',
      state: encodeURIComponent(redirect),
    });
    location.replace(res);
  };
  return (
    <ErrorBlock
      style={{
        '--image-height': '200px',
      }}
      status="busy"
      title="企业微信授权"
      description={<span className="text-sm">请先进行企业微信授权登录！</span>}
    >
      <Button
        color="primary"
        size="middle"
        className="w-40 mt-4"
        shape="rounded"
        onClick={userLogin}
      >
        授权登录
      </Button>
    </ErrorBlock>
  );
};

export default Login;
