import { wxLogin } from '@src/api/user';
import { useLoaderFeishuLoginCode } from '@src/router/loader';
import { userStore } from '@src/store';
import { setToken } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { ErrorBlock } from 'antd-mobile';
import { useNavigate } from 'react-router-dom';

export default function FsLogin() {
  const { code, state } = useLoaderFeishuLoginCode();
  const url = state === 'state' ? '/customer' : decodeURIComponent(state);
  console.log(url);

  const navigate = useNavigate();

  const { data } = useRequest(async () => {
    const res = await wxLogin(code);
    setToken(res?.token);
    //预防
    setTimeout(() => {
      userStore.getUserAuth();
      userStore.getUserInfo(res);
      navigate(url, { replace: true });
    }, 500);
    return res;
  });

  if (!data?.token) {
    return (
      <ErrorBlock
        style={{
          '--image-height': '200px',
        }}
        status="busy"
        title="正在使用企业微信登录"
        description={<span>请稍候...</span>}
      />
    );
  }

  return (
    <ErrorBlock
      style={{
        '--image-height': '200px',
      }}
      status="busy"
      title="已登录正在跳转首页"
      description={<span>请稍候...</span>}
    />
  );
}
