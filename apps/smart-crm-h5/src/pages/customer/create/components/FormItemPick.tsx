import { useEffect, useState } from 'react';
import { CheckList, Form, FormItemProps, Popup } from 'antd-mobile';

interface SelectPopupProps {
  visible?: boolean;
  multiple?: boolean;
  value?: (string | number)[] | string | number;
  options?: { label: string; value: string | number }[];
  onChange?: (value?: (string | number)[] | string | number) => void;
  onVisibleChange?: (visible: boolean) => void;
}

const SelectPopup: React.FC<SelectPopupProps> = ({
  value,
  visible,
  multiple,
  onChange,
  options,
  onVisibleChange,
}) => {
  const [internalValue, setInternalValue] = useState<(string | number)[]>([]);

  useEffect(() => {
    if (visible) {
      if (multiple) {
        setInternalValue(Array.isArray(value) ? value : []);
      } else {
        setInternalValue(value ? [value as string | number] : []);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  const getLabel = () => {
    const placeholder = <span style={{ color: 'var(--adm-color-light)' }}>请选择</span>;

    if (multiple) {
      if (Array.isArray(value) && value.length > 0) {
        return value.map((i) => (
          <div key={i}>{options?.find((option) => option.value === i)?.label}</div>
        ));
      }

      return placeholder;
    } else {
      if (value) {
        return options?.find((i) => i.value === value)?.label;
      }

      return placeholder;
    }
  };

  return (
    <>
      {getLabel()}
      <Popup
        visible={visible}
        onMaskClick={() => {
          onVisibleChange?.(false);
        }}
        destroyOnClose
      >
        <div className="flex justify-between p-1 text-[15px]">
          <div className="p-2" onClick={() => onVisibleChange?.(false)}>
            取消
          </div>
          <div
            className="p-2"
            onClick={() => {
              onChange?.(multiple ? internalValue : internalValue[0]);
              onVisibleChange?.(false);
            }}
          >
            确定
          </div>
        </div>
        <div className="max-h-[300px] overflow-y-auto">
          <CheckList multiple={multiple} value={internalValue} onChange={setInternalValue}>
            {options?.map((item) => (
              <CheckList.Item key={item.value} value={item.value}>
                {item.label}
              </CheckList.Item>
            ))}
          </CheckList>
        </div>
      </Popup>
    </>
  );
};

const FormItemPick: React.FC<
  FormItemProps & {
    multiple?: boolean;
    options?: { label: string; value: string | number }[];
  }
> = ({ multiple, options, ...formItemProps }) => {
  const [visible, setVisible] = useState<boolean>(false);

  return (
    <Form.Item
      {...formItemProps}
      {...(multiple
        ? {
            getValueProps: (val?: string | (string | number)[]) => ({
              // 如果外面传进来的是数组，直接用，否则逗号切开成数组
              value: Array.isArray(val) ? val : val ? val.split(',') : undefined,
            }),
            normalize: (val) => val.join(','),
          }
        : {})}
      onClick={() => {
        setVisible(true);
      }}
    >
      <SelectPopup
        visible={visible}
        multiple={multiple}
        options={options}
        onVisibleChange={setVisible}
      />
    </Form.Item>
  );
};

export default FormItemPick;
