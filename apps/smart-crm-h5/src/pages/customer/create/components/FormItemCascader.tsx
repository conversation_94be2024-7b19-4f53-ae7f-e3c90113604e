import RegionPick from '@src/components/RegionPick';
import useRegionData from '@src/hooks/useRegionData';
import { Form } from 'antd-mobile';
import { Rule } from 'antd-mobile/es/components/form';

const FormItemCascader: React.FC<{
  name: string;
  label: string;
  rules?: Rule[];
}> = ({ name, label, rules }) => {
  const { data = [], loading } = useRegionData();

  return (
    <Form.Item name={name} label={label} rules={rules}>
      <RegionPick type="city" cityData={loading ? [] : data} />
    </Form.Item>
  );
};

export default FormItemCascader;
