import React from 'react';
import { Form, FormItemProps, Input } from 'antd-mobile';
import { AddSquareOutline, CloseOutline } from 'antd-mobile-icons';

interface TextArrayProps {
  value?: string[];
  onChange?: (value: string[]) => void;
}

const TextArray: React.FC<TextArrayProps> = ({ value, onChange }) => {
  const internalValue = Array.isArray(value) && value.length > 0 ? value : [''];

  return (
    <div className="flex flex-col items-start gap-4">
      {internalValue.map((val, index) => (
        <div key={index} className="flex w-full items-center">
          <Input
            type="text"
            placeholder="请输入"
            value={val}
            onChange={(text) => {
              const result = [...internalValue];

              result[index] = text;
              onChange?.(result);
            }}
          />
          {internalValue.length > 1 && (
            <CloseOutline
              className="ml-2 text-gray-300 text-2xl"
              onClick={() => onChange?.(internalValue.filter((_, idx) => idx !== index))}
            />
          )}
        </div>
      ))}
      <AddSquareOutline
        className="text-primary text-2xl"
        onClick={() => onChange?.(internalValue.concat(''))}
      />
    </div>
  );
};

interface FormItemTextArrayProps {
  formItemProps?: FormItemProps;
}

const FormItemTextArray: React.FC<FormItemTextArrayProps> = ({ formItemProps }) => {
  return (
    <Form.Item
      getValueProps={(val) => ({ value: val ? val.split(',') : undefined })}
      normalize={(val) => (val ? val.join(',') : undefined)}
      {...formItemProps}
      rules={[
        {
          validator: (_, val: string) => {
            if (val) {
              const hasValueArray = val.split(',').filter(Boolean);

              if ([...new Set(hasValueArray)].length !== hasValueArray.length) {
                return Promise.reject(new Error(`不能出现重复的${formItemProps?.label}`));
              }
            }

            return Promise.resolve();
          },
        },
        ...(formItemProps?.rules || []),
      ]}
    >
      <TextArray />
    </Form.Item>
  );
};

export default FormItemTextArray;
