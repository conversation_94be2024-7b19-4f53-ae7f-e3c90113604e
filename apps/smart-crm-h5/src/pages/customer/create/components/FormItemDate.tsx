import { useState } from 'react';
import { DatePicker, Form, FormItemProps } from 'antd-mobile';
import dayjs from 'dayjs';

const FormItemDate: React.FC<FormItemProps> = (props) => {
  const [visible, setVisible] = useState<boolean>(false);

  return (
    <>
      <Form.Item
        {...props}
        trigger="onConfirm"
        getValueProps={(val) => ({
          value: val ? dayjs(val).toDate() : undefined,
        })}
        normalize={(val) => (val ? dayjs(val).format('YYYY-MM-DD') : undefined)}
        onClick={() => {
          setVisible(true);
        }}
      >
        <DatePicker
          max={new Date()}
          min={new Date('1900-01-01')}
          visible={visible}
          onClose={() => {
            setVisible(false);
          }}
        >
          {(v) => {
            const val = v ? dayjs(v).format('YYYY-MM-DD') : '';

            return v ? val : <span style={{ color: 'var(--adm-color-light)' }}>请选择</span>;
          }}
        </DatePicker>
      </Form.Item>
    </>
  );
};

export default FormItemDate;
