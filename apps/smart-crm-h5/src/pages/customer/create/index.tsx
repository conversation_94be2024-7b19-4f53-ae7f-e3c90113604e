import { useState } from 'react';
import { createCustomer } from '@src/api/customer';
import { CreateCustomerReq } from '@src/api/type/customer.type';
import useFieldColumns from '@src/hooks/useFieldColumns';
import { useRequest } from 'ahooks';
import { Button, Form, SafeArea, Space, Tag, Toast } from 'antd-mobile';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { CustomerTag } from '../components';
import CustomerFormItems from '../components/CustomerFormItems';

const CustomerCreate = () => {
  const [search] = useSearchParams();
  const bindId = search.get('bindId');
  const [form] = Form.useForm();
  const [tags, setTags] = useState<{ label: string; value: number }[]>([]);
  const navigate = useNavigate();

  const { customFields, baseFields, getFieldProps } = useFieldColumns();
  const { run: createByManual, loading: createLoading } = useRequest(
    async (values) => {
      if (!bindId) {
        Toast.show('缺少企业微信外部联系人id');

        throw new Error('缺少企业微信外部联系人id');
      }

      const res = await createCustomer({ ...values, corpWxExternalUserid: bindId });

      return res;
    },
    {
      manual: true,
      onSuccess: () => {
        Toast.show({
          content: '录入成功',
        });
        navigate(-1);
      },
    },
  );

  const TagsItem = () => {
    return tags && tags.length ? (
      <Space wrap>
        {tags.map(({ label, value }) => (
          <Tag className={`px-2 py-1 text-inherit`} key={value} color="#eee">
            {label}
          </Tag>
        ))}
      </Space>
    ) : (
      <span style={{ color: 'var(--adm-color-light)' }}>请选择</span>
    );
  };

  return (
    <div className="h-full overflow-y-auto">
      <Form
        form={form}
        preserve={false}
        layout="horizontal"
        mode="default"
        onFinish={(values) => {
          // 所有自定义字段 field
          const allCustomFields = customFields.map((i) => i.field);
          const params: CreateCustomerReq = { fieldValues: [] };

          Object.keys(values).forEach((field) => {
            const value = values[field];

            if (allCustomFields.includes(field)) {
              params.fieldValues.push({ field, value });
            } else {
              params[field] = value;
            }
          });
          createByManual(params);
        }}
        onFinishFailed={() => {
          Toast.show({
            content: '请确保信息填写正确！',
          });
        }}
        footer={
          <>
            <div className="h-8" />
            <div className="fixed bottom-0 left-0 right-0 px-4 py-3 bg-[#f9f9f9]">
              <Button
                block
                type="submit"
                color="primary"
                loading={createLoading}
                disabled={createLoading}
                size="large"
              >
                保存
              </Button>
              <SafeArea position="bottom" />
            </div>
          </>
        }
      >
        <CustomerFormItems
          baseFields={baseFields}
          customFields={customFields}
          getFieldProps={getFieldProps}
        />
        <Form.Item name="tagIds" label="标签" arrow>
          <CustomerTag onShowTags={(v) => setTags(v)} defaultValue={tags.map((v) => v.value)}>
            <TagsItem />
          </CustomerTag>
        </Form.Item>
      </Form>
    </div>
  );
};

export default CustomerCreate;
