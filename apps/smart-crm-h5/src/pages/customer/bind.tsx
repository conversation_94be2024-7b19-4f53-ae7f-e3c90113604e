import { useState } from 'react';
import { bindCustomer, getCustomerList } from '@src/api/customer';
import { PageList } from '@src/components';
import { useRequest } from 'ahooks';
import { Button, Empty, SearchBar } from 'antd-mobile';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { CustomerCard } from './components';

const CustomerSearch = () => {
  const [search] = useSearchParams();
  const bindId = search.get('bindId');
  const [keyWords, setKeyWords] = useState<string>('');
  const navigate = useNavigate();

  const { data } = useRequest(
    async () => {
      if (!keyWords) throw new Error('keyWords is null');

      const res = await getCustomerList({ phone: keyWords });

      return res;
    },
    {
      refreshDeps: [keyWords],
    },
  );

  const { run: bindRun } = useRequest(
    async (id) => {
      if (!bindId || !id || !keyWords) throw new Error('bindId or id or keyWords is null');

      await bindCustomer({ corpWxExternalUserid: bindId, clueId: id });

      return id;
    },
    {
      manual: true,
      onSuccess: (customerId) => {
        navigate(`/customer?id=${customerId}`, { replace: true });
      },
    },
  );

  // const showData = useMemo(() => {
  //   let list = data || [];
  //   if (keyWords) {
  //     list = list.filter((v: Customer) => v.phone.includes(keyWords));
  //   }
  //   return list;
  // }, [data, keyWords]);

  return (
    <div className="h-full flex flex-col">
      <div className="bg-white px-3 py-2">
        <SearchBar placeholder="请输入线索手机号" onSearch={setKeyWords} clearable />
      </div>
      {data && data.length ? (
        <>
          <div className="flex-1 overflow-y-auto pb-3">
            <PageList
              dataSource={data || []}
              itemRender={(v) => (
                <div key={v.id} className="px-3 pt-3">
                  <CustomerCard
                    data={v}
                    // bind={() => {
                    //   bindRun(v.id);
                    // }}
                  />
                </div>
              )}
            />
          </div>
          <div className="px-4 py-3">
            <Button
              block
              color="primary"
              onClick={() => {
                bindRun(data[0].id);
              }}
            >
              关联该线索信息
            </Button>
          </div>
        </>
      ) : (
        <>
          <Empty description="暂无数据" className="mt-10" />
          {keyWords && (
            <div className="text-center">
              <Button
                color="primary"
                size="middle"
                className="w-40 mt-4"
                shape="rounded"
                onClick={() => navigate(`/customer/create?bindId=${bindId}`, { replace: true })}
              >
                新建线索
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default CustomerSearch;
