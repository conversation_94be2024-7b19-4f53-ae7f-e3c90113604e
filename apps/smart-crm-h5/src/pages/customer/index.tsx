import { useEffect, useRef, useState } from 'react';
import { getAgentConfig, getWxConfig } from '@src/api';
import {
  convertId,
  getClueRelCustomer,
  getCustomerDetail,
  getCustomerPermission,
  getQwCustomerInfo,
} from '@src/api/customer';
import useAllUsers from '@src/hooks/useAllUsers';
import PageContainer from '@src/layout/PageContainer';
import { useRequest } from 'ahooks';
import { Avatar, Button, Card, ErrorBlock, Skeleton, Space, Tag, Toast } from 'antd-mobile';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { CustomerBaseInfo, CustomerTag, Dynamic, Employee } from './components';
import EditCustomerInfoPopup from './components/EditCustomerInfoPopup';

// 线索详情
const CustomerInfo: React.FC<{ customerId: number }> = ({ customerId }) => {
  const [editInfoPopupVisible, setEditInfoPopupVisible] = useState(false);

  const { data: permission } = useRequest(
    async () => {
      if (!customerId) throw new Error('customerId is required');

      const res = await getCustomerPermission(customerId);

      return {
        allow: res?.editable,
      };
    },
    { refreshDeps: [customerId] },
  );
  const { data: clueRelCustomer } = useRequest(() => getClueRelCustomer(customerId), {
    refreshDeps: [customerId],
  });

  const { data, run, refresh } = useRequest(
    async () => {
      if (!customerId) throw new Error('customerId is required');

      const res = await getCustomerDetail(customerId);

      return res;
    },
    { refreshDeps: [customerId] },
  );

  const { data: userData } = useAllUsers();

  const getPersonName = (id?: number) => {
    const user = userData?.find((item) => item.value === String(id));

    if (user) {
      return user.label;
    }

    return '-';
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="mt-2 px-2">
        <Card>
          <div className="flex">
            <Avatar src="" />
            <div className="ml-2 flex-1">
              <div>{data?.name}</div>
              <div className="text-xs">{data?.phoneSensitive}</div>
            </div>
          </div>
          <div className="mt-2">
            <span className="text-gray-500">关联客户：</span>
            {clueRelCustomer?.name || <span className="text-gray-500">暂无</span>}
          </div>
          <div className="flex items-center mt-2">
            <div className="text-gray-500">
              {data?.belongCluePoolType === 'PRIVATE_CLUE_POOL' ? '负责人' : '最后负责人'}:
            </div>
            <div className="flex items-center ml-3">
              {/* <Avatar src="" style={{ '--size': '20px', '--border-radius': '10px' }} /> */}
              <div className="ml-1">
                {getPersonName(
                  data?.belongCluePoolType === 'PRIVATE_CLUE_POOL'
                    ? data?.director
                    : data?.finalDirector,
                )}
              </div>
            </div>
          </div>
        </Card>
      </div>
      {/* <div className="mt-2 px-2">
        <Card title="线索进展">
          <CustomerStep active={data?.progressId} />
        </Card>
      </div> */}
      <div className="mt-2 px-2">
        <Card title="Ta添加的">
          <Employee customerId={customerId} />
          <div className="flex items-center justify-between mt-2">
            <div>客户标签</div>
            {!!permission?.allow && (
              <CustomerTag
                defaultValue={data?.tagId?.map((v) => v.id)}
                customerId={customerId}
                refresh={run}
              >
                <Button color="primary" fill="none" size="mini">
                  编辑
                </Button>
              </CustomerTag>
            )}
          </div>
          <div className="mt-2">
            <Space wrap>
              {data?.tagId ? (
                data.tagId.map((v) => (
                  <Tag key={v.id} className="text-inherit" color="#EEE">
                    {v.name}
                  </Tag>
                ))
              ) : (
                <span className="text-gray-500 text-xs">暂无标签</span>
              )}
            </Space>
          </div>
        </Card>
      </div>
      <div className="mt-2 px-2">
        <Card
          title="线索信息"
          extra={
            !!permission?.allow && (
              <Button
                color="primary"
                fill="none"
                size="mini"
                onClick={() => setEditInfoPopupVisible(true)}
              >
                编辑
              </Button>
            )
          }
        >
          <CustomerBaseInfo data={data} />
        </Card>
        <EditCustomerInfoPopup
          visible={editInfoPopupVisible}
          initialValues={data}
          onClose={() => setEditInfoPopupVisible(false)}
          onSuccess={() => {
            setEditInfoPopupVisible(false);
            refresh();
          }}
        />
      </div>
      <div className="mt-2 px-2">
        <Dynamic customerId={customerId} phone={data?.phone || ''} />
      </div>
    </div>
  );
};

const CustomerBind: React.FC<{ needBind: string }> = ({ needBind }) => {
  const navigate = useNavigate();
  const { data } = useRequest(() => getQwCustomerInfo(needBind));

  return (
    <>
      <div className="flex p-3 bg-white">
        <Avatar src={data?.avatar} />
        <div className="flex-1 pl-3 text-[15px]">
          <div>{data?.name}</div>
          {/* <div className="text-xs">{data?.phone}</div> */}
        </div>
      </div>
      <ErrorBlock
        style={{
          '--image-height': '200px',
        }}
        status="empty"
        title="线索未识别"
        description={<span className="text-sm">原因：无线索手机号，需进行线索关联</span>}
      >
        <Space>
          <Button
            color="primary"
            size="middle"
            className="w-[140px] mt-4"
            shape="rounded"
            onClick={() => navigate(`/customer/bind?bindId=${needBind}`)}
          >
            关联已有线索
          </Button>
          <Button
            color="primary"
            size="middle"
            className="w-[140px] mt-4"
            shape="rounded"
            onClick={() => navigate(`/customer/create?bindId=${needBind}`)}
          >
            新建线索
          </Button>
        </Space>
      </ErrorBlock>
    </>
  );
};

// 前置处理
const CustomerDetail = () => {
  const [search] = useSearchParams();
  const paramId = search.get('id');
  const appIdRef = useRef();
  const [customerId, setCustomerId] = useState<number>(() => (paramId ? +paramId : 0));
  const [needBind, setNeedBind] = useState<string>('');

  // 转化为系统的线索id
  const formatUserId = async (id: string) => {
    const res = await convertId(id);

    console.log(res);

    if (res.convertSuccess) {
      setCustomerId(res.clueId);
    } else {
      Toast.show('未匹配到线索');
      setNeedBind(id);
    }
  };

  // formatUserId('wm4hFLEQAAbjNNcYe8i2fYP5ONWpEXSA');

  // 获取外部线索userId
  const getUserId = () => {
    wx.invoke('getCurExternalContact', {}, (res: any) => {
      console.log('getCurExternalContact11111', res);

      if (res.err_msg === 'getCurExternalContact:ok') {
        const { userId } = res; // 返回当前外部联系人userId

        formatUserId(userId);
        console.log(userId);
      } else {
        Toast.show({ content: '无权限，请联系管理员' });
        // 错误处理
        // uni.showModal({
        //   title: '无权限!',
        //   content: '请联系管理员!',
        //   confirmColor: '#fff',
        //   showCancel: false,
        //   success() {},
        // });
      }
    });
  };

  // 判断页面入口
  const getContext = () => {
    wx.invoke('getContext', {}, (res: any) => {
      console.log('getContext', res);

      if (res.err_msg === 'getContext:ok') {
        // 正常处理业务逻辑
        // 获取当前对话框userid
        if (res.entry === 'normal') {
          // 侧边栏进入
          // uni.showModal({
          //   title: '请在下列场景打开应用!',
          //   content: '外部联系人聊天窗口或详情',
          //   confirmColor: '#fff',
          //   showCancel: false,
          //   success() {},
          // });
          Toast.show('请在外部联系人详情打开');
        } else {
          getUserId();
        }

        // entry  = res.entry ;
        // shareTicket = res.shareTicket; //可用于调用getShareInfo接口
      } else {
        // 错误处理
      }
    });
  };

  // wx.agentConfig
  const agentInit = async () => {
    // eslint-disable-next-line no-restricted-globals
    const res = await getAgentConfig(location.href.split('#')[0]);

    wx.agentConfig({
      corpid: appIdRef.current, // 必填，企业微信的corpid，必须与当前登录的企业一致
      agentid: res.agentid, // 必填，企业微信的应用id （e.g. 1000247）
      timestamp: res.timestamp, // 必填，生成签名的时间戳
      nonceStr: res.nonceStr, // 必填，生成签名的随机串
      signature: res.signature, // 必填，签名，见附录-JS-SDK使用权限签名算法
      jsApiList: ['getContext', 'getCurExternalContact', 'hideMenuItems'], // 必填，传入需要使用的接口名称
      success: (response: any) => {
        // 回调
        console.log('应用身份', response);
        getContext();
      },
      fail(r: any) {
        console.log(r, 'agent error');

        if (r.errMsg.indexOf('function not exist') > -1) {
          alert('版本过低请升级');
        }
      },
    });
  };

  // wx.config
  const wxInit = async () => {
    // eslint-disable-next-line no-restricted-globals
    const res = await getWxConfig(location.href.split('#')[0]);

    appIdRef.current = res.appId;
    wx.config({
      debug: false,
      beta: true,
      appId: res.appId, // 必填，企业微信的corpid，必须与当前登录的企业一致
      timestamp: res.timestamp, // 必填，生成签名的时间戳
      nonceStr: res.nonceStr, // 必填，生成签名的随机串
      signature: res.signature, // 必填，签名，见附录-JS-SDK使用权限签名算法
      jsApiList: ['agentConfig', 'getCurExternalContact'], // 必填，传入需要使用的接口名称
      success(response: any) {
        // 回调
        console.log('config', response);
      },
      fail(response: any) {
        if (response.errMsg.indexOf('function not exist') > -1) {
          alert('版本过低请升级');
        }
      },
    });
    wx.ready(() => {
      agentInit();
    });
  };

  useEffect(() => {
    !customerId && wxInit();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <PageContainer>
      {customerId ? (
        <CustomerInfo customerId={customerId} />
      ) : needBind ? (
        <CustomerBind needBind={needBind} />
      ) : (
        <div>
          <Skeleton.Title animated />
          <Skeleton.Paragraph lineCount={5} animated />
          <Skeleton.Title animated />
          <Skeleton.Paragraph lineCount={5} animated />
          <Skeleton.Title animated />
          <Skeleton.Paragraph lineCount={5} animated />
        </div>
      )}
    </PageContainer>
  );
};

export default CustomerDetail;
