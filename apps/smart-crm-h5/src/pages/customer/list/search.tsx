import { useEffect, useState } from 'react';
import { getCustomerPage, getCustomerPageByPublic } from '@src/api/customer';
import { CustomerBase } from '@src/api/type/customer.type';
import useScrollFetch from '@src/hooks/useScrollFetch';
import PageContainer from '@src/layout/PageContainer';
import { getIsPc } from '@src/utils/utils';
import { Avatar, Dialog, Empty, InfiniteScroll, List, SearchBar, Space } from 'antd-mobile';
import { Outlet, useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import BadgeWrap from './BadgeWrap';
import { getCustomerStorage, setCustomerStorage } from './customerStorage';

const CustomerPageSearch = () => {
  const [search] = useSearchParams();
  const [keyWords, setKeyWords] = useState<string>('');
  const [type, setType] = useState<string>(search.get('type') || 'owner');
  const [storageList, setStorageList] = useState<CustomerBase[]>([]);

  const isPc = getIsPc();
  const loc = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    if (!keyWords) {
      const storageArr = getCustomerStorage();

      setStorageList(() => storageArr);
    }
  }, [keyWords]);

  const { list, over, fetchNext } = useScrollFetch(
    async ({ pageNo, pageSize }) => {
      if (!keyWords) throw new Error('keyWords is null');

      const params = {
        pageNum: pageNo,
        pageSize,
        search: keyWords,
        orderField: 'directTime',
        orderType: 'desc',
      };
      const res =
        type === 'owner'
          ? await getCustomerPage({ ...params }).catch(() => {
              return { result: [], total: 0 };
            })
          : await getCustomerPageByPublic({ ...params }).catch(() => {
              return { result: [], total: 0 };
            });

      setCustomerStorage(res?.result || []);

      return {
        total: res?.total,
        list: res?.result || [],
      };
    },
    [keyWords, type],
    {
      computeOver: (result, { pageSize }) => {
        return (result.list as unknown as any[]).length < pageSize;
      },
    },
  );

  return (
    <>
      <div className="h-full" hidden={loc.pathname !== '/customer/list/search'}>
        <PageContainer>
          <>
            <div className="px-3 py-3">
              <SearchBar
                placeholder="搜索线索姓名/手机号"
                onSearch={setKeyWords}
                onClear={() => setKeyWords('')}
                clearable
                style={{ '--background': '#ffffff' }}
              />
              <div className="text-xs py-3 flex justify-between items-center">
                <Space>
                  <div
                    className={`px-3 py-1 border rounded-full ${
                      type === 'owner'
                        ? 'bg-white text-blue-400 border-blue-400'
                        : 'bg-gray-200 text-gray-500 border-gray-200'
                    }`}
                    onClick={() => setType('owner')}
                  >
                    我的线索
                  </div>
                  <div
                    className={`px-3 py-1 border rounded-full ${
                      type !== 'owner'
                        ? 'bg-white text-blue-400 border-blue-400'
                        : 'bg-gray-200 text-gray-500 border-gray-200'
                    }`}
                    onClick={() => {
                      type === 'owner' && setType('public');
                    }}
                  >
                    线索池
                  </div>
                </Space>
              </div>
              {!keyWords && (
                <div className="flex justify-between text-gray-500 pl-1">
                  <div>最近浏览</div>
                </div>
              )}
            </div>
            {!keyWords ? (
              <div className="flex-1 overflow-y-auto">
                {storageList.length ? (
                  <List key="storage">
                    {storageList.map((item) => (
                      <List.Item key={item.id} onClick={() => setKeyWords(item.name)}>
                        {item.name}
                      </List.Item>
                    ))}
                  </List>
                ) : (
                  <Empty description="暂无数据" />
                )}
              </div>
            ) : list && list.length ? (
              <div className="flex-1 overflow-y-auto">
                <List key="result">
                  {list.map((v) =>
                    v.list.map((item) => (
                      <List.Item
                        key={item.id}
                        prefix={
                          <BadgeWrap showBadge={item.callFlag === false}>
                            <Avatar src="" />
                          </BadgeWrap>
                        }
                        onClick={() => navigate(`/customer/list/search/detail?id=${item.id}`)}
                        description={
                          <div>
                            {!isPc ? (
                              <div
                                className="text-blue-400"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  Dialog.confirm({
                                    content: '是否拨打电话？',
                                    onConfirm: () => {
                                      window.location.href = `tel:${item.phone}`;
                                    },
                                  });
                                }}
                              >
                                {item.phone}
                              </div>
                            ) : (
                              <div>{item.phone}</div>
                            )}

                            {type === 'owner' && item.directorName && (
                              <div>{item.directorName}负责</div>
                            )}
                          </div>
                        }
                        extra={
                          item.belongCluePoolType === 'PRIVATE_CLUE_POOL' ? '我的线索' : '线索池'
                        }
                      >
                        {item.name}
                      </List.Item>
                    )),
                  )}
                </List>
                <InfiniteScroll loadMore={fetchNext} hasMore={!over} />
              </div>
            ) : (
              <Empty description="暂无数据" />
            )}
          </>
        </PageContainer>
      </div>
      <Outlet />
    </>
  );
};

export default CustomerPageSearch;
