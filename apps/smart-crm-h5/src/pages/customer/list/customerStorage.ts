import { CustomerBase } from '@src/api/type/customer.type';
import { uniqBy } from 'lodash';

const LIMIT = 10;
const TOKEN_STORAGE_KEY = 'TST_CRM_H5_CUSTOMER_LIST';

const getCustomerStorage = () => {
  let res = [];
  try {
    res = JSON.parse(localStorage.getItem(TOKEN_STORAGE_KEY) || '[]');
  } catch (error) {
    res = [];
  }
  return res;
};

const setCustomerStorage = (newArr: CustomerBase[]) => {
  const hasArr = getCustomerStorage() as CustomerBase[];
  const list = [...newArr, ...hasArr];
  const res = uniqBy(list, 'id').slice(0, LIMIT);
  localStorage.setItem(TOKEN_STORAGE_KEY, JSON.stringify(res));
};

export { setCustomerStorage, getCustomerStorage };
