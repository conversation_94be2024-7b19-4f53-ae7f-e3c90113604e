import { useState } from 'react';
import { Avatar, Dialog, List, Popup, SafeArea } from 'antd-mobile';
import { CloseOutline, PhoneFill } from 'antd-mobile-icons';

type IProps = { item: { name: string; phone: string } };

const CustomerPhone: React.FC<IProps> = ({ item }) => {
  const [visible, setVisible] = useState(false);

  return (
    <>
      <PhoneFill
        className="text-xl text-primary mr-2"
        onClick={(e) => {
          e.stopPropagation();
          setVisible(true);
        }}
      />
      <Popup
        position="bottom"
        visible={visible}
        bodyClassName="pb-3 box-border rounded-t-lg"
        onMaskClick={() => {
          setVisible(false);
        }}
      >
        <div className="flex px-4 justify-between items-center" style={{ height: '3.375rem' }}>
          <div className="text-[18px] text-dark font-semibold">联系方式</div>
          <CloseOutline
            onClick={() => {
              setVisible(false);
            }}
            className="text-grey text-13"
          />
        </div>
        <div>
          <List>
            <List.Item prefix={<Avatar src="" />}>{item.name}</List.Item>
            <List.Item
              prefix={<div className="w-11 text-sm">手机:</div>}
              onClick={() => {
                Dialog.confirm({
                  content: '是否拨打电话？',
                  onConfirm: () => {
                    window.location.href = `tel:${item.phone}`;
                  },
                });
              }}
            >
              <span className="text-blue-400">{item.phone}</span>
            </List.Item>
          </List>
        </div>
        <SafeArea position="bottom" />
      </Popup>
    </>
  );
};

export default CustomerPhone;
