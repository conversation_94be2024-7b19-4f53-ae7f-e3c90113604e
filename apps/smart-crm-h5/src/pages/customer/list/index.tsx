import { useState } from 'react';
import { getCustomerPage, getCustomerPageByPublic, getPublicSeaData } from '@src/api/customer';
import { Sorter } from '@src/components';
import useScrollFetch from '@src/hooks/useScrollFetch';
import PageContainer from '@src/layout/PageContainer';
import { getIsPc } from '@src/utils/utils';
import { useRequest } from 'ahooks';
import {
  Avatar,
  Empty,
  InfiniteScroll,
  List,
  Popup,
  SafeArea,
  SearchBar,
  Selector,
  Space,
} from 'antd-mobile';
import { CloseOutline } from 'antd-mobile-icons';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import BadgeWrap from './BadgeWrap';
import CustomerPhone from './components/CustomerPhone';

const CustomerPage = () => {
  const [keyWords, setKeyWords] = useState<string>('');
  const [type, setType] = useState<string>('owner');
  const [visible, setVisible] = useState(false);
  const [sorter, setSorter] = useState<string[]>(['directTime-desc']);

  const loc = useLocation();
  const navigate = useNavigate();
  const isPc = getIsPc();

  const { list, over, fetchNext } = useScrollFetch(
    async ({ pageNo, pageSize }) => {
      const [key, order] = sorter[0].split('-');
      const params = {
        pageNum: pageNo,
        pageSize,
        search: keyWords,
        orderField: key === 'directTime' ? (type === 'owner' ? key : 'abandonTime') : key,
        orderType: order,
      };
      const res =
        type === 'owner'
          ? await getCustomerPage({ ...params }).catch(() => {
              return { result: [], total: 0 };
            })
          : await getCustomerPageByPublic({ ...params, cluePoolId: +type }).catch(() => {
              return { result: [], total: 0 };
            });

      return {
        total: res?.total,
        list: res?.result || [],
      };
    },
    [keyWords, type, sorter],
    {
      computeOver: (result, { pageSize }) => {
        return (result.list as unknown as any[]).length < pageSize;
      },
    },
  );

  const { data: publicSeaData } = useRequest(async () => {
    const res = await getPublicSeaData();

    return (res || []).map((v) => ({ label: v.name, value: String(v.id) }));
  });

  return (
    <>
      <div className="h-full" hidden={loc.pathname !== '/customer/list'}>
        <PageContainer>
          <>
            <div className="px-3 py-3">
              <div
                onClick={() =>
                  navigate(`/customer/list/search?type=${type === 'owner' ? 'owner' : 'public'}`)
                }
              >
                <SearchBar
                  placeholder="搜索线索姓名/手机号"
                  onSearch={setKeyWords}
                  clearable
                  style={{ '--background': '#ffffff' }}
                />
              </div>
              <div className="text-xs py-3 flex justify-between items-center">
                <Space>
                  <div
                    className={`px-3 py-1 border rounded-full ${
                      type === 'owner'
                        ? 'bg-white text-blue-400 border-blue-400'
                        : 'bg-gray-200 text-gray-500 border-gray-200'
                    }`}
                    onClick={() => setType('owner')}
                  >
                    我的线索
                  </div>
                  <div
                    className={`px-3 py-1 border rounded-full ${
                      type !== 'owner'
                        ? 'bg-white text-blue-400 border-blue-400'
                        : 'bg-gray-200 text-gray-500 border-gray-200'
                    }`}
                    onClick={() => {
                      type === 'owner' &&
                        setType(publicSeaData?.length ? publicSeaData[0].value : '');
                      setVisible(true);
                    }}
                  >
                    线索池
                  </div>
                </Space>
              </div>
              <div className="flex justify-between text-gray-500 pl-1">
                <div>共{list && list.length ? list[0].total : '-'}人</div>
                <Sorter
                  value={sorter}
                  config={[
                    { label: '创建时间升序', value: 'createTime-asc' },
                    { label: '创建时间降序', value: 'createTime-desc' },
                    { label: '联系时间升序', value: 'contactTime-asc' },
                    { label: '联系时间降序', value: 'contactTime-desc' },
                    { label: '入库时间升序', value: 'directTime-asc' },
                    { label: '入库时间降序', value: 'directTime-desc' },
                  ]}
                  onChange={(v) => setSorter(v)}
                />
              </div>
            </div>
            {list && list.length ? (
              <div className="flex-1 overflow-y-auto">
                <List>
                  {list.map((v) =>
                    v.list.map((item) => (
                      <List.Item
                        key={item.id}
                        prefix={
                          <BadgeWrap showBadge={item.callFlag === false}>
                            <Avatar src="" />
                          </BadgeWrap>
                        }
                        description={item?.progressName || ''}
                        extra={!isPc ? <CustomerPhone item={item} /> : <></>}
                        onClick={() => navigate(`/customer/list/detail?id=${item.id}`)}
                      >
                        {item.name}
                      </List.Item>
                    )),
                  )}
                </List>
                <InfiniteScroll loadMore={fetchNext} hasMore={!over} />
              </div>
            ) : (
              <Empty description="暂无数据" />
            )}

            <Popup
              position="bottom"
              visible={visible}
              bodyClassName="pb-10 box-border rounded-t-lg"
              onMaskClick={() => {
                setVisible(false);
              }}
            >
              <div
                className="flex px-4 justify-between items-center border-b border-solid border-line"
                style={{ height: '3.375rem' }}
              >
                <div className="text-base font-semibold">选择公海</div>
                <CloseOutline
                  onClick={() => {
                    setVisible(false);
                  }}
                  className="text-grey text-13"
                />
              </div>
              <div className="pt-5 px-4">
                <Selector
                  style={{
                    '--padding': '0.5rem 0.5rem',
                    fontSize: '0.8125rem',
                  }}
                  columns={3}
                  options={publicSeaData || []}
                  value={[type]}
                  onChange={(v) => {
                    if (v.length) {
                      setType(v[0]);
                      setVisible(false);
                    }
                  }}
                />
              </div>
              <SafeArea position="bottom" />
            </Popup>
          </>
        </PageContainer>
      </div>
      <Outlet />
    </>
  );
};

export default CustomerPage;
