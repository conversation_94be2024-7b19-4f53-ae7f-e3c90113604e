import { useMemo, useState } from 'react';
import { Customer } from '@src/api/type/customer.type';
import { ValueType } from '@src/api/type/index.type';
import useFieldColumns from '@src/hooks/useFieldColumns';
import { DownOutline, UpOutline } from 'antd-mobile-icons';
import EditDate from './EditDate';
import EditInput from './EditInput';
import EditInputNumber from './EditInputNumber';
import EditPick from './EditPick';
import EditPickMultiple from './EditPickMultiple';
import EditRegion from './EditRegion';
import ViewPhone from './ViewPhone';

const CommonNode: React.FC<{ label: string; value: string }> = ({ label, value }) => {
  return (
    <div className="flex items-center mt-2">
      <div className="w-24 text-gray-500">{label}</div>
      <div className="flex-1">{value || '-'}</div>
    </div>
  );
};

const CustomerBaseInfo: React.FC<{
  data?: Customer;
}> = ({ data }) => {
  const { loading, baseFields = [], customFields = [], getFieldProps } = useFieldColumns();

  const [more, setMore] = useState<boolean>(false);

  const componentsMap: Record<ValueType, React.FC<any>> = {
    [ValueType.TEXT]: EditInput,
    [ValueType.DATE]: EditDate,
    [ValueType.DATE_TIME]: EditDate,
    [ValueType.SINGLE]: EditPick,
    [ValueType.MULTIPLE]: EditPickMultiple,
    [ValueType.NUMBER]: EditInputNumber,
    [ValueType.PHONE]: ViewPhone,
    [ValueType.IDENTITY_CARD]: EditInput,
    [ValueType.REGION]: EditRegion,
    [ValueType.PERSON]: EditPickMultiple,
    [ValueType.TAG]: CommonNode,
    [ValueType.TEXT_ARRAY]: CommonNode,
  };

  const showFields = useMemo(() => {
    if (loading) {
      return [];
    } else if (more) {
      return [...baseFields, ...customFields];
    } else {
      return [...baseFields, ...customFields].filter((_v, index) => index <= 5);
    }
  }, [loading, more, baseFields, customFields]);

  return (
    <div>
      {showFields.map((item) => {
        const { name, field, options, valueType = ValueType.TEXT } = item || {};
        const Component = componentsMap[valueType] || EditInput;

        return (
          <Component
            key={item.field}
            label={name}
            value={data?.[field as keyof Customer] || ''}
            options={options}
            fieldProps={
              item.valueType === ValueType.PHONE
                ? {
                    sensitiveValue: data?.phoneSensitive,
                  }
                : getFieldProps(item)
            }
          />
        );
      })}
      <div
        className="flex justify-center items-center mt-3 text-gray-500"
        onClick={() => setMore((p) => !p)}
      >
        <div className="mr-1">{more ? '收起' : '展开更多'}</div>
        {more ? <UpOutline /> : <DownOutline />}
      </div>
    </div>
  );
};

export default CustomerBaseInfo;
