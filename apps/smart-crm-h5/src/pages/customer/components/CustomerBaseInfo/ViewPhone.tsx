import { useState } from 'react';
import { decryptSensitive } from '@src/api';
import { SensitiveTypeEnum } from '@src/api/type/index.type';
import { getIsPc } from '@src/utils/utils';
import { useRequest } from 'ahooks';
import { Button, Dialog } from 'antd-mobile';
import { EyeInvisibleOutline, EyeOutline } from 'antd-mobile-icons';

interface ViewPhoneProps {
  value?: string;
  fieldProps?: {
    sensitiveValue?: string;
  };
}

const ViewPhone: React.FC<ViewPhoneProps> = ({ value, fieldProps }) => {
  const [showReal, setShowReal] = useState(false);

  const isPc = getIsPc();

  const { sensitiveValue } = fieldProps || {};

  const {
    data: realValue,
    runAsync: getReal,
    loading,
  } = useRequest(decryptSensitive, { manual: true });

  return (
    <div className="flex items-center mt-2">
      <div className="w-24 text-gray-500">手机号</div>
      <div className="flex flex-1 items-center">
        <a
          {...(isPc
            ? {}
            : {
                className: 'text-blue-400',
                onClick: () => {
                  Dialog.confirm({
                    content: '是否拨打电话？',
                    onConfirm: async () => {
                      if (realValue) {
                        window.location.href = `tel:${realValue}`;
                      } else {
                        const realVal = await getReal({
                          encrypted: value!,
                          sensitiveType: SensitiveTypeEnum.PHONE,
                        });

                        window.location.href = `tel:${realVal}`;
                      }
                    },
                  });
                },
              })}
        >
          {showReal ? realValue : sensitiveValue}
        </a>
        <Button
          fill="none"
          size="small"
          loading={loading}
          className={`px-1 ml-1 ${loading ? '!py-0' : ''}`}
          onClick={async () => {
            if (!showReal) {
              if (realValue) {
                setShowReal(!showReal);
              } else {
                await getReal({ encrypted: value!, sensitiveType: SensitiveTypeEnum.PHONE });
              }
            }

            setShowReal(!showReal);
          }}
        >
          {showReal ? <EyeInvisibleOutline /> : <EyeOutline />}
        </Button>
      </div>
    </div>
  );
};

export default ViewPhone;
