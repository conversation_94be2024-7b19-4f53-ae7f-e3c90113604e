import { Popup, <PERSON><PERSON><PERSON>, Selector } from 'antd-mobile';
import { useEffect, useState } from 'react';
import { RightOutline } from 'antd-mobile-icons';

type IProps = {
  label: string;
  value?: string;
  editable: boolean;
  fieldProps: { options: OptionItem[] };
  onSave: (value: string) => void;
};
const EditPickMultiple: React.FC<IProps> = ({ label, value, editable, fieldProps, onSave }) => {
  const [visible, setVisible] = useState<boolean>(false);
  const [inputVal, setInputVal] = useState<OptionItem[]>([]);
  const [val, setVal] = useState<string[]>([]);
  const { options } = fieldProps || {};

  const formatValue = (values: string[]) => {
    const list = options.filter((v) => values.includes(v.value));
    setInputVal(list);
    setVal(values);
  };

  useEffect(() => {
    if (value) {
      formatValue(value.split(','));
    } else {
      setInputVal([]);
      setVal([]);
    }
  }, [value, visible]);

  return (
    <>
      <div
        className="flex items-center mt-2"
        onClick={() => {
          editable && setVisible(true);
        }}
      >
        <div className="w-24 text-gray-500">{label}</div>
        <div className="flex-1">
          {inputVal && inputVal.length ? inputVal.map((v) => v.label).join(',') : '-'}
        </div>
        {editable && <RightOutline />}
      </div>
      <Popup
        position="bottom"
        visible={visible}
        bodyClassName="pb-10 box-border rounded-t-lg"
        onMaskClick={() => {
          setVisible(false);
        }}
      >
        <div className="text-[15px] flex px-4 justify-between items-center border-b border-solid border-line py-3">
          <div
            onClick={() => {
              setVisible(false);
            }}
          >
            取消
          </div>
          <div className="">{label}</div>
          <div
            onClick={() => {
              formatValue(val);
              onSave && onSave(val.join(','));
              setVisible(false);
            }}
          >
            确定
          </div>
        </div>
        <div className="pt-4 px-4">
          <div className="max-h-96 overflow-y-auto">
            <Selector
              // showCheckMark={false}
              style={{
                // '--border-radius': '4px',
                '--color': 'rgba(249,250,251)',
                '--text-color': '#58595B',
                // '--checked-color': '#e1faf5',
                '--padding': '0.5rem 0.5rem',
                fontSize: '0.8125rem',
              }}
              columns={3}
              options={options}
              value={val}
              multiple={true}
              onChange={(v) => {
                if (v.length) {
                  setVal(v);
                } else {
                  setVal([]);
                }
              }}
            />
          </div>
        </div>
        <SafeArea position="bottom" />
      </Popup>
    </>
  );
};

export default EditPickMultiple;
