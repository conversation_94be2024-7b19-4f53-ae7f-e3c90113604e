import { RegionModeEnum } from '@src/api/type/index.type';
import useRegionData from '@src/hooks/useRegionData';
import EditRegionMultiple from './EditRegionMultiple';
import EditRegionSingle from './EditRegionSingle';

const EditRegion: React.FC<{
  label: string;
  value?: string;
  editable: boolean;
  fieldProps?: { regionLevel: number; regionMode: string };
  onSave: (value: string) => void;
}> = ({ fieldProps, ...rest }) => {
  const { data = [], loading } = useRegionData();
  const { regionMode = RegionModeEnum.MULTIPLE } = fieldProps || {};

  return !loading ? (
    regionMode === RegionModeEnum.MULTIPLE ? (
      <EditRegionMultiple cityData={data} fieldProps={fieldProps} {...rest} />
    ) : (
      <EditRegionSingle cityData={data} fieldProps={fieldProps} {...rest} />
    )
  ) : (
    <span>-</span>
  );
};

export default EditRegion;
