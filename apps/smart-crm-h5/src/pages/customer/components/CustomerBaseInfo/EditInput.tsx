import { Dialog, Input } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import { useEffect, useState } from 'react';

const EditInput: React.FC<{
  label: string;
  value?: string;
  editable: boolean;
  max?: number;
  type?: string;
  fieldProps: { maxLength: number };
  onSave: (value: string) => void;
}> = ({ label, value, editable, max, type = 'text', fieldProps, onSave }) => {
  const [visible, setVisible] = useState<boolean>(false);
  const [inputVal, setInputVal] = useState<string>(value || '');

  const { maxLength } = fieldProps || {};

  useEffect(() => {
    value && setInputVal(value);
  }, [value, visible]);

  return (
    <>
      <div
        className="flex items-center mt-2"
        onClick={() => {
          editable && setVisible(true);
        }}
      >
        <div className="w-24 text-gray-500">{label}</div>
        <div className="flex-1">{value || '-'}</div>
        {editable && <RightOutline />}
      </div>

      <Dialog
        visible={visible}
        title={`编辑${label}`}
        content={
          <Input
            placeholder="请输入内容"
            value={inputVal}
            type={type}
            maxLength={maxLength}
            max={max ? max : undefined}
            className="border border-gray-200 border-solid py-1 pl-1"
            style={{ '--text-align': 'left' }}
            onChange={(val) => {
              setInputVal(val);
            }}
          />
        }
        actions={[
          [
            { key: 'cancel', text: '取消', onClick: () => setVisible(false) },
            {
              key: 'confirm',
              text: '确定',
              bold: true,
              onClick: () => {
                onSave && onSave(inputVal);
                setVisible(false);
              },
            },
          ],
        ]}
      />
    </>
  );
};

export default EditInput;
