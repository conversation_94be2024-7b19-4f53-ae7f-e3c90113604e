import { useEffect, useState } from 'react';
import RegionPick from '@src/components/RegionPick';
import { RightOutline } from 'antd-mobile-icons';

const EditRegionMultiple: React.FC<{
  label: string;
  value?: string;
  editable: boolean;
  cityData: TreeWith<OptionItem>[];
  fieldProps?: { regionLevel: number };
  onSave: (value: string) => void;
}> = ({ label, value, editable, fieldProps, cityData, onSave }) => {
  const [visible, setVisible] = useState<boolean>(false);
  const [inputVal, setInputVal] = useState<string>(value || '');
  const { regionLevel = 2 } = fieldProps || {};

  const RegionTypeMap: Record<string, 'province' | 'city' | 'county'> = {
    1: 'province',
    2: 'city',
    3: 'county',
  };

  useEffect(() => {
    setInputVal(value || '');
  }, [value, visible]);

  return (
    <>
      <div
        className="flex items-center mt-2"
        onClick={() => {
          editable && setVisible(true);
        }}
      >
        <div className="w-24 text-gray-500">{label}</div>
        <div className="flex-1">
          <RegionPick
            type={RegionTypeMap[regionLevel] || 'city'}
            cityData={cityData}
            value={inputVal}
            disabled={!editable}
            onChange={(val) => onSave && onSave(val)}
          />
        </div>
        {editable && <RightOutline />}
      </div>
    </>
  );
};

export default EditRegionMultiple;
