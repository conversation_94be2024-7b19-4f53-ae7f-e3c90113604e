import { Picker } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import { PickerColumnItem } from 'antd-mobile/es/components/picker';
import { useEffect, useState } from 'react';

const EditPick: React.FC<{
  label: string;
  value?: string;
  editable: boolean;
  options: PickerColumnItem[];
  onSave: (value: string) => void;
}> = ({ label, value, editable, options, onSave }) => {
  const [visible, setVisible] = useState<boolean>(false);
  const [inputVal, setInputVal] = useState<string>(value || '');

  useEffect(() => {
    value && setInputVal(value);
  }, [value, visible]);

  return (
    <>
      <div
        className="flex items-center mt-2"
        onClick={() => {
          editable && setVisible(true);
        }}
      >
        <div className="w-24 text-gray-500">{label}</div>
        <div className="flex-1">{options.find((v) => v.value === value)?.label || '-'}</div>
        {editable && <RightOutline />}
      </div>

      <Picker
        title={`选择${label}`}
        columns={[options]}
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        value={[inputVal]}
        onConfirm={(v) => {
          v.length && setInputVal(v[0] as string);
          onSave && onSave(v[0] as string);
          setVisible(false);
        }}
      />
    </>
  );
};

export default EditPick;
