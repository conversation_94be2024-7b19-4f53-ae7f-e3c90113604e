import { Dialog, Input } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import { useEffect, useState } from 'react';

const EditInputNumber: React.FC<{
  label: string;
  value?: string;
  editable: boolean;
  max?: number;
  type?: string;
  fieldProps: { fixed: number; min: number; max: number };
  onSave: (value: number) => void;
}> = ({ label, value, editable, type = 'text', fieldProps, onSave }) => {
  const [visible, setVisible] = useState<boolean>(false);
  const [inputVal, setInputVal] = useState<string>(value || '');

  const { fixed = 0, min, max } = fieldProps || {};

  useEffect(() => {
    value && setInputVal(value);
  }, [value, visible]);

  return (
    <>
      <div
        className="flex items-center mt-2"
        onClick={() => {
          editable && setVisible(true);
        }}
      >
        <div className="w-24 text-gray-500">{label}</div>
        <div className="flex-1">{value || '-'}</div>
        {editable && <RightOutline />}
      </div>

      <Dialog
        visible={visible}
        title={`编辑${label}`}
        content={
          <Input
            placeholder="请输入内容"
            value={inputVal}
            type={type}
            maxLength={50}
            min={min}
            max={max}
            className="border border-gray-200 border-solid py-1 pl-1"
            style={{ '--text-align': 'left' }}
            onChange={(v) => {
              let value: number | string = v !== '' ? v : '';

              // if (value !== '' && Number.isNaN(+value)) {
              //   value = Number.isNaN(+inputVal) ? '' : inputVal;
              //   setInputVal(String(value));
              //   return;
              // }

              if (Number(value) < min) {
                value = min;
              }
              if (Number(value) > max) {
                value = max;
              }
              setInputVal(String(value));
            }}
            onBlur={(e) => {
              const { value: v } = e.target;
              let value: number | string = v !== '' ? v : '';
              if (value !== '' && Number.isNaN(+value)) {
                value = Number.isNaN(+inputVal) ? '' : inputVal;
                setInputVal(String(value));
                return;
              }
              setInputVal(parseFloat(String(value)).toFixed(fixed));
            }}
          />
        }
        actions={[
          [
            { key: 'cancel', text: '取消', onClick: () => setVisible(false) },
            {
              key: 'confirm',
              text: '确定',
              bold: true,
              onClick: () => {
                onSave && onSave(+inputVal);
                setVisible(false);
              },
            },
          ],
        ]}
      />
    </>
  );
};

export default EditInputNumber;
