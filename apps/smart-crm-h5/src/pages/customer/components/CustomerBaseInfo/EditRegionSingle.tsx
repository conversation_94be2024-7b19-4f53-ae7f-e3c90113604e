import { CascadePicker } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import { useEffect, useState, useMemo } from 'react';

const EditRegionSingle: React.FC<{
  label: string;
  value?: string;
  editable: boolean;
  cityData: TreeWith<OptionItem>[];
  fieldProps?: { regionLevel: number };
  onSave: (v: string) => void;
}> = ({ label, value, editable, fieldProps, cityData: data, onSave }) => {
  const [visible, setVisible] = useState<boolean>(false);
  const [inputVal, setInputVal] = useState<OptionItem[]>([]);

  const { regionLevel = 2 } = fieldProps || {};

  const showData = useMemo(() => {
    const dataMap: Record<string, any> = {
      1: data.map((v) => ({ ...v, children: undefined })),
      2: data.map((v) => ({
        ...v,
        children: v.children?.map((v) => ({ ...v, children: undefined })),
      })),
      3: data,
    };
    return dataMap[regionLevel] || dataMap[2];
  }, [data, , regionLevel]);

  const formatRegion = (valuse: string[]) => {
    let dataSource = showData || [];
    const list = valuse.map((v) => {
      const {
        label = '',
        value = '',
        children = [],
      } = dataSource.find((ele: any) => ele.value === v) || {};
      dataSource = children;
      return { label, value };
    });
    setInputVal(list);
  };

  useEffect(() => {
    value ? formatRegion(value.split('/')) : setInputVal([]);
  }, [value, visible]);

  return (
    <>
      <div
        className="flex items-center mt-2"
        onClick={() => {
          editable && setVisible(true);
        }}
      >
        <div className="w-24 text-gray-500">{label}</div>
        <div className="flex-1">
          {inputVal && inputVal.length ? inputVal.map((v) => v.label).join(' ') : '-'}
        </div>
        {editable && <RightOutline />}
      </div>

      {/* <Picker
        title={`选择${label}`}
        columns={[options]}
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        value={[inputVal]}
        onConfirm={(v) => {
          v.length && setInputVal(v[0] as string);
          onSave && onSave(v[0] as string);
          setVisible(false);
        }}
      /> */}
      <CascadePicker
        options={showData}
        visible={visible}
        value={inputVal.map((v) => v.value)}
        title={label}
        onClose={() => {
          setVisible(false);
        }}
        onConfirm={(val) => {
          if (val && val.length) {
            formatRegion(val as string[]);
            onSave && onSave(val.join('/'));
          }
        }}
      />
    </>
  );
};

export default EditRegionSingle;
