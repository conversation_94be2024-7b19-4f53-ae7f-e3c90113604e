import { DatePicker } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { Precision } from 'antd-mobile/es/components/date-picker/date-picker-utils';

const EditDate: React.FC<{
  label: string;
  value?: string | number;
  editable: boolean;
  fieldProps: { precision: Precision };
  onSave: (value: number) => void;
}> = ({ label, value, editable, fieldProps, onSave }) => {
  const [visible, setVisible] = useState<boolean>(false);
  const [inputVal, setInputVal] = useState<string | number>(value || '');

  const { precision = 'day' } = fieldProps || {};

  useEffect(() => {
    value && setInputVal(value);
  }, [value, visible]);

  return (
    <>
      <div
        className="flex items-center mt-2"
        onClick={() => {
          editable && setVisible(true);
        }}
      >
        <div className="w-24 text-gray-500">{label}</div>
        <div className="flex-1">{value ? dayjs(value).format('YYYY-MM-DD') : '-'}</div>
        {editable && <RightOutline />}
      </div>

      <DatePicker
        title={`选择${label}`}
        visible={visible}
        max={new Date()}
        min={new Date('1900-01-01')}
        onClose={() => {
          setVisible(false);
        }}
        value={dayjs(inputVal).toDate()}
        precision={precision}
        onConfirm={(val) => {
          if (val) {
            const value = dayjs(val).valueOf();
            setInputVal(value);
            onSave && onSave(value);
            setVisible(false);
          }
        }}
      />
    </>
  );
};

export default EditDate;
