import { checkMobileExist } from '@src/api/customer';
import { Customer } from '@src/api/type/customer.type';
import { FieldItemType, ValueType } from '@src/api/type/index.type';
import useFieldColumns from '@src/hooks/useFieldColumns';
import { Form, Input, TextArea } from 'antd-mobile';
import FormItemCascader from '../../create/components/FormItemCascader';
import FormItemDate from '../../create/components/FormItemDate';
import FormItemPick from '../../create/components/FormItemPick';
import FormItemTextArray from '../../create/components/FormItemTextArray';

interface CustomerFormItemsProps {
  /** 是否是编辑 */
  isEdit?: boolean;
  data?: Customer;
  baseFields: FieldItemType[];
  customFields: FieldItemType[];
  getFieldProps: ReturnType<typeof useFieldColumns>['getFieldProps'];
}

const CustomerFormItems: React.FC<CustomerFormItemsProps> = ({
  isEdit,
  data,
  baseFields,
  customFields,
  getFieldProps,
}) => {
  const checkMobile = async (_: any, val: string) => {
    if (val && val.length === 11) {
      try {
        await checkMobileExist(val);

        return Promise.resolve();
      } catch (error) {
        return Promise.reject(error);
      }
    }

    return Promise.resolve();
  };

  return (
    <>
      <Form.Item name="name" label="姓名" rules={[{ required: true, message: '不能为空' }]}>
        <Input placeholder="请输入" autoComplete="off" maxLength={20} />
      </Form.Item>
      <Form.Item
        name="phone"
        label="手机"
        validateFirst
        {...(isEdit
          ? {
              getValueProps: () => ({
                value: data?.phoneSensitive,
              }),
            }
          : {})}
        rules={[
          { required: true, message: '不能为空' },
          ...(isEdit
            ? []
            : [
                { pattern: /^[1][0-9]{10}$/, message: '请输入正确格式的手机号' },
                { validator: checkMobile },
              ]),
        ]}
      >
        <Input disabled={isEdit} placeholder="请输入" type="tel" maxLength={11} />
      </Form.Item>
      <FormItemPick
        name="source"
        label="来源"
        options={baseFields.find((i) => i.field === 'source')?.options}
        rules={[{ required: true, message: '不能为空' }]}
      />
      <Form.Item
        name="budget"
        label="总预算(万)"
        rules={[{ pattern: /^[1-9][0-9]{0,4}$/, message: '请输入正整数，最大值99999' }]}
      >
        <Input placeholder="请输入" type="number" max={99999} min={1} />
      </Form.Item>
      <Form.Item
        name="singleBudget"
        label="单店预算(万)"
        rules={[{ pattern: /^[1-9][0-9]{0,4}$/, message: '请输入正整数，最大值99999' }]}
      >
        <Input placeholder="请输入" type="number" max={99999} min={1} />
      </Form.Item>
      <FormItemPick
        name="highestEducationLevel"
        label="最高学历"
        options={baseFields.find((i) => i.field === 'highestEducationLevel')?.options}
      />
      <Form.Item name="extraCity" label="考虑其他城市">
        <Input placeholder="请输入" type="text" maxLength={500} />
      </Form.Item>
      <Form.Item name="operationMode" label="合资/独资">
        <Input placeholder="请输入" type="text" maxLength={500} />
      </Form.Item>
      <Form.Item name="industry" label="从事行业">
        <Input placeholder="请输入" type="text" maxLength={500} />
      </Form.Item>
      <Form.Item name="advantage" label="客户优势">
        <Input placeholder="请输入" type="text" maxLength={500} />
      </Form.Item>
      {customFields.map((item) => {
        const getNode = () => {
          if (item.valueType === ValueType.TEXT) {
            return (
              <Form.Item
                key={item.field}
                name={item.field}
                label={item.name}
                rules={item.notNull ? [{ required: true, message: '不能为空' }] : []}
              >
                <Input placeholder="请输入" type="text" {...getFieldProps(item)} />
              </Form.Item>
            );
          }

          if (item.valueType === ValueType.NUMBER) {
            return (
              <Form.Item
                key={item.field}
                name={item.field}
                label={item.name}
                rules={item.notNull ? [{ required: true, message: '不能为空' }] : []}
              >
                <Input placeholder="请输入" type="number" {...getFieldProps(item)} />
              </Form.Item>
            );
          }

          if (item.valueType === ValueType.DATE) {
            return (
              <FormItemDate
                key={item.field}
                name={item.field}
                label={item.name}
                rules={item.notNull ? [{ required: true, message: '不能为空' }] : []}
              />
            );
          }

          if ([ValueType.SINGLE, ValueType.MULTIPLE].includes(item.valueType)) {
            return (
              <FormItemPick
                key={item.field}
                name={item.field}
                label={item.name}
                multiple={item.valueType === ValueType.MULTIPLE}
                rules={item.notNull ? [{ required: true, message: '不能为空' }] : []}
                options={item.options}
              />
            );
          }

          // 意向城市，先写死
          if (item.valueType === ValueType.REGION) {
            return (
              <FormItemCascader
                key={item.field}
                name={item.field}
                label={item.name}
                rules={item.notNull ? [{ required: true, message: '不能为空' }] : []}
              />
            );
          }

          if (item.valueType === ValueType.TEXT_ARRAY) {
            return (
              <FormItemTextArray
                key={item.field}
                formItemProps={{
                  name: item.field,
                  label: item.name,
                  rules: item.notNull ? [{ required: true, message: '不能为空' }] : [],
                }}
              />
            );
          }
        };

        const node = getNode();

        const showRules = item.showRules || [];

        if (showRules.length > 0) {
          return (
            <Form.Item
              key={item.field}
              noStyle
              shouldUpdate={(prev, next) =>
                showRules.some((rule) => prev[rule.field] !== next[rule.field])
              }
            >
              {({ getFieldValue }) =>
                showRules.some((rule) => {
                  const fieldValue = getFieldValue(rule.field);
                  const isMultiple =
                    customFields.find((field) => field.field === rule.field)?.valueType ===
                    ValueType.MULTIPLE;

                  if (isMultiple) {
                    return rule.values.some((val) => fieldValue?.includes(val));
                  }

                  return rule.values.includes(fieldValue);
                }) && node
              }
            </Form.Item>
          );
        }

        return node;
      })}
      <Form.Item name="remark" label="备注">
        <TextArea placeholder="请输入" maxLength={500} rows={4} showCount />
      </Form.Item>
    </>
  );
};

export default CustomerFormItems;
