import { useState } from 'react';
import { getEmployee } from '@src/api/customer';
import { useRequest } from 'ahooks';
import { Avatar, List, Popup, SafeArea } from 'antd-mobile';
import { CloseOutline, RightOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';

type IProps = { customerId: number };

const Employee: React.FC<IProps> = ({ customerId }) => {
  const [visible, setVisible] = useState(false);

  const { data = [] } = useRequest(
    async () => {
      if (!customerId) throw new Error('customerId is required');

      const res = await getEmployee(customerId);

      return res;
    },
    { refreshDeps: [customerId] },
  );

  return (
    <>
      <div
        onClick={(e) => {
          e.stopPropagation();
          setVisible(true);
        }}
      >
        <div className="flex items-center">
          <div className="text-gray-500">员工:</div>
          <div className="flex items-center ml-3 flex-1">
            {data && data.length ? (
              <>
                <Avatar
                  src={data[0].avatar}
                  style={{ '--size': '20px', '--border-radius': '10px' }}
                />
                <div className="ml-1">{data[0].name}</div>
              </>
            ) : (
              <div className="text-gray-500">暂无</div>
            )}
          </div>
          <RightOutline className="text-xs leading-none ml-1" />
        </div>
      </div>
      <Popup
        position="bottom"
        visible={visible}
        bodyClassName="pb-10 box-border rounded-t-lg"
        onMaskClick={() => {
          setVisible(false);
        }}
      >
        <div
          className="flex px-4 justify-between items-center border-b border-solid border-line"
          style={{ height: '3.375rem' }}
        >
          <div className="text-[18px] text-dark font-semibold">员工</div>
          <CloseOutline
            onClick={() => {
              setVisible(false);
            }}
            className="text-grey text-13"
          />
        </div>
        <div className="pt-4 px-4">
          <div className="mb-1">{`Ta添加的员工(${data.length})`}</div>
          <div className="max-h-96 overflow-y-auto">
            <List>
              {data.map((item, index) => (
                <List.Item
                  key={index}
                  prefix={<Avatar src={item.avatar} />}
                  description={`添加时间: ${
                    item?.followTime ? dayjs(item.followTime).format('YYYY-MM-DD HH:mm:ss') : ''
                  }`}
                >
                  {item.name}
                </List.Item>
              ))}
            </List>
          </div>
        </div>
        <SafeArea position="bottom" />
      </Popup>
    </>
  );
};

export default Employee;
