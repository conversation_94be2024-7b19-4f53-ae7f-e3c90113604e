import { Customer } from '@src/api/type/customer.type';
import { Card } from 'antd-mobile';
import { SexMap } from '../../enum';

const CustomerCard: React.FC<{ data: Customer; bind?: () => void }> = ({ data }) => {
  return (
    <Card
      title="线索信息"
      // extra={
      //   <Button
      //     color="primary"
      //     fill="none"
      //     size="mini"
      //     onClick={() => {
      //       bind && bind();
      //     }}
      //   >
      //     关联该线索信息
      //   </Button>
      // }
    >
      <div className="pr-1">
        <div className="flex items-center justify-between mt-2">
          <div>姓名</div>
          <div>{data?.name}</div>
        </div>
        <div className="flex items-center justify-between mt-2">
          <div>性别</div>
          <div>{data?.gender ? SexMap[data.gender] : '-'}</div>
        </div>
        <div className="flex items-center justify-between mt-2">
          <div>手机号</div>
          <div>{data?.phone ? data.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '-'}</div>
        </div>
        <div className="flex items-center justify-between mt-2">
          <div>出生日期</div>
          <div>{data?.birthday}</div>
        </div>
        <div className="flex items-center justify-between mt-2">
          <div>预计投入金额</div>
          <div>{data?.budget}</div>
        </div>
      </div>
    </Card>
  );
};

export default CustomerCard;
