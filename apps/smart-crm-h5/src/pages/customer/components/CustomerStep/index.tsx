import { useMemo, useState } from 'react';
import { getCustomerProgress } from '@src/api/customer';
import { useRequest } from 'ahooks';
import { Popup, SafeArea, Steps } from 'antd-mobile';
import { CloseOutline, RightOutline } from 'antd-mobile-icons';

const { Step } = Steps;

type IProps = { active?: number };

const CustomerStep: React.FC<IProps> = ({ active }) => {
  const [visible, setVisible] = useState(false);

  const { data } = useRequest(() => getCustomerProgress());

  const progressName = useMemo(() => {
    const obj = data?.find((item) => item.id === active);

    if (obj) {
      return obj.name;
    }

    return '-';
  }, [data, active]);

  return (
    <>
      <div
        onClick={(e) => {
          e.stopPropagation();
          setVisible(true);
        }}
      >
        <div className="flex justify-between items-center">
          <div>{progressName}</div>
          <RightOutline className="text-xs leading-none ml-1" />
        </div>
      </div>
      <Popup
        position="bottom"
        visible={visible}
        bodyClassName="pb-10 box-border rounded-t-lg"
        onMaskClick={() => {
          setVisible(false);
        }}
      >
        <div
          className="flex px-4 justify-between items-center border-b border-solid border-line"
          style={{ height: '3.375rem' }}
        >
          <div className="text-[18px] text-dark font-semibold">线索进展</div>
          <CloseOutline
            onClick={() => {
              setVisible(false);
            }}
            className="text-grey text-13"
          />
        </div>
        <div className="pt-4 px-4">
          <div className="max-h-96 overflow-y-auto">
            <Steps direction="vertical">
              {(data || []).map((item) => (
                <Step title={item.name} status={item.id === active ? 'process' : 'wait'} />
              ))}
            </Steps>
          </div>
        </div>
        <SafeArea position="bottom" />
      </Popup>
    </>
  );
};

export default CustomerStep;
