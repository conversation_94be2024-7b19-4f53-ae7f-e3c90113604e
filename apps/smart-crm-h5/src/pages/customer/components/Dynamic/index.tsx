import { useMemo, useState } from 'react';
import { getCustomerDynamic } from '@src/api/customer';
import { ActivityItem } from '@src/api/type/customer.type';
import useScrollFetch from '@src/hooks/useScrollFetch';
import { But<PERSON>, Card, InfiniteScroll, Steps } from 'antd-mobile';
import dayjs from 'dayjs';
import { padStart } from 'lodash';
import FileList from './FileList';
import DynamicFilter from './Filter';
import { DynamicEnum, dynamicOptions, OperationTypeEnum, operationTypeLabelMap } from '../../enum';

const { Step } = Steps;

const WeekMap = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];

type IProps = {
  customerId: number;
  phone: string;
};

function convertSecondsToHMS(seconds: number) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  // padStart 补齐 0:0:0 为 00:00:00 两位
  return [hours, minutes, remainingSeconds].map((i) => padStart(String(i), 2, '0')).join(':');
}

const Dynamic: React.FC<IProps> = ({ customerId, phone }) => {
  const [activityType, setActivityType] = useState<DynamicEnum>(DynamicEnum.线索动态);
  const { list, over, fetchNext } = useScrollFetch(
    async ({ pageNo, pageSize }) => {
      const res = await getCustomerDynamic(
        {
          pageNum: pageNo,
          pageSize,
          clueId: customerId,
          businessId: activityType === DynamicEnum.信息变更 ? customerId : undefined,
          businessType: activityType === DynamicEnum.信息变更 ? 'CLUE' : undefined,
          callPhone: activityType === DynamicEnum.通话记录 ? phone : undefined,
        },
        activityType,
      ).catch(() => {
        return { result: [] };
      });

      return res?.result || [];
      // return {
      //   total: res.total,
      //   list: res.result || [],
      // };
    },
    [customerId, activityType],
  );

  const getTimeLineItem = (item: ActivityItem) => {
    const mapObj = {
      [OperationTypeEnum.ALLOCATION]: (
        <p>
          {item.userName}分配了线索给{item.transferUserName}
        </p>
      ),
      [OperationTypeEnum.GET]: <p>{item.userName}领取了线索</p>,
      [OperationTypeEnum.DELETE]: <p>{item.userName}删除了线索</p>,
      [OperationTypeEnum.ADD_STAFF]: <p>线索添加了{item.userName}的企业微信</p>,
      [OperationTypeEnum.DELETE_STAFF]: <p>线索在微信上删除了{item.userName}</p>,
      [OperationTypeEnum.STAFF_DELETE_CLUE]: <p>{item.userName}在企业微信上删除了线索</p>,
      [OperationTypeEnum.TRANSFER]: (
        <p>
          {item.userName}转让了线索，后续由{item.transferUserName}负责
        </p>
      ),
      [OperationTypeEnum.ABANDON]: (
        <>
          <p>
            {item.userName}放弃了线索，线索进入{item.transferCluePoolName}
          </p>
          <p>内容：{item.info}</p>
          <FileList data={item.attachments} />
        </>
      ),
      [OperationTypeEnum.CREATE_CLUE]: (
        <>
          <p>{item.userName}创建了线索</p>
        </>
      ),
      [OperationTypeEnum.TAG_CHANGE]: (
        <>
          <p>{item.userName}更新了标签</p>
          <p>更新前：{item.updateBefore}</p>
          <p>更新后：{item.updateAfter}</p>
        </>
      ),
      [OperationTypeEnum.CLUE_INFO_CHANGE]: (
        <>
          <p>
            {item.userName}更新了{item.updateFieldName}
          </p>
          <div>更新前：{item.updateBefore}</div>
          <div>更新后：{item.updateAfter}</div>
        </>
      ),
      [OperationTypeEnum.ADD_FOLLOW]: (
        <>
          <p>{item.userName}添加了跟进记录</p>
          <p>内容：{item.info}</p>
          <FileList data={item.attachments} />
        </>
      ),
      [OperationTypeEnum.BLOCK_CALL]: (
        <p>
          {item.userName}拨打了电话 {item.customerPhone}，未接通
        </p>
      ),
      [OperationTypeEnum.CALL_RECORDING_RETRIEVED]: (
        <>
          <p>
            {item.userName}拨打了电话 {item.customerPhone}，通话时长{' '}
            {convertSecondsToHMS(item.callDuration)}
          </p>
          <FileList data={item.attachments} />
        </>
      ),
      [OperationTypeEnum.CALL_RECORDING_LOST]: (
        <>
          <p>
            {item.userName}拨打了电话 {item.customerPhone}，通话时长{' '}
            {convertSecondsToHMS(item.callDuration)}
          </p>
          <p>通话录音丢失</p>
        </>
      ),
      [OperationTypeEnum.CALL_BACK_BLOCK_CALL]: (
        <>
          <p>
            线索 {item.customerPhone} 回拨了{item.userName}电话，未接通
          </p>
          <FileList data={item.attachments} />
        </>
      ),
      [OperationTypeEnum.CALL_BACK_CALL_RECORDING_LOST]: (
        <>
          <p>
            线索 {item.customerPhone} 回拨了{item.userName}电话，通话时长{' '}
            {convertSecondsToHMS(item.callDuration)}
          </p>
          <p>通话录音丢失</p>
        </>
      ),
      [OperationTypeEnum.CALL_BACK_CALL_RECORDING_RETRIEVED]: (
        <>
          <p>
            线索 {item.customerPhone} 回拨了{item.userName}电话，通话时长{' '}
            {convertSecondsToHMS(item.callDuration)}
          </p>
          <FileList data={item.attachments} />
        </>
      ),
    };

    return mapObj[item.operationType];
  };

  // 根据日期进行分组
  const groupData = useMemo(() => {
    const result: Record<string, ActivityItem[]> = {};

    list.map((v) =>
      v.forEach((item) => {
        const date = dayjs(item.activityTime).format('YYYY-MM-DD');

        if (result[date]) {
          result[date].push(item);
        } else {
          result[date] = [item];
        }
      }),
    );

    return result;
  }, [list]);

  return (
    <div>
      <Card
        title="动态记录"
        extra={
          <DynamicFilter
            defaultValue={activityType}
            onChange={(e) => {
              // setParams((p) => ({ ...p, dynamicType: e }));
              setActivityType(() => e);
            }}
          >
            <Button color="primary" fill="outline" size="mini" shape="rounded">
              {dynamicOptions.find((v) => v.value === activityType)?.label}
            </Button>
          </DynamicFilter>
        }
      >
        {Object.keys(groupData).map((date) => (
          <div key={date} className="mb-4">
            <p className="pl-6 mb-1 sticky top-0 bg-white z-10">
              {date} | {WeekMap[dayjs(date).day()]}
            </p>
            <Steps direction="vertical" style={{ padding: 0 }}>
              {groupData[date].map((item, index) => (
                <Step
                  key={index}
                  title={item.activityTime ? dayjs(item.activityTime).format('HH:mm') : '-'}
                  status="wait"
                  style={{ padding: 0 }}
                  description={
                    <div className="px-3 py-3 rounded text-dark-1 bg-[#F5F5F5]">
                      <div className="font-semibold mb-1 text-dark">
                        {operationTypeLabelMap[item.operationType]}
                      </div>
                      {getTimeLineItem(item)}
                    </div>
                  }
                />
              ))}
            </Steps>
          </div>
        ))}
      </Card>
      <InfiniteScroll loadMore={fetchNext} hasMore={!over} />
    </div>
  );
};

export default Dynamic;
