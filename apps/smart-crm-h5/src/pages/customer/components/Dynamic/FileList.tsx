import { CloseOutlined, DownloadOutlined, EyeOutlined, FileTwoTone } from '@ant-design/icons';
import { PdfViewer } from '@src/components';
import { Button, Image, Typography } from 'antd';
import { useState } from 'react';
import { createPortal } from 'react-dom';

type DataItem = {
  fileUrl: string;
  fileType: string;
  fileName: string;
};

interface FileListProps {
  data?: DataItem[];
}

const FileList: React.FC<FileListProps> = ({ data }) => {
  const [pdfViewerUrl, setPdfViewerUrl] = useState('');
  const [videoUrl, setVideoUrl] = useState('');

  if (!data || !data.length) {
    return null;
  }

  const renderPreview = (item: DataItem) => {
    if (item.fileType.startsWith('image')) {
      return <Image src={item.fileUrl} height={50} preview={{ mask: <EyeOutlined /> }} />;
    }

    if (item.fileType.startsWith('audio')) {
      return <audio src={item.fileUrl} controls className="h-8 w-[270px]" />;
    }
    return (
      <div className="h-[50px] w-[50px] flex justify-center items-center rounded border relative">
        <FileTwoTone className="text-[30px]" />
        {(item.fileType === 'application/pdf' || item.fileType.startsWith('video')) && (
          <div
            className="absolute flex justify-center items-center w-full h-full bg-black/50 opacity-0 hover:opacity-100 cursor-pointer"
            onClick={() => {
              if (item.fileType === 'application/pdf') {
                setPdfViewerUrl(item.fileUrl);
              }
              if (item.fileType.startsWith('video')) {
                setVideoUrl(item.fileUrl);
              }
            }}
          >
            <EyeOutlined className="text-white" />
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <div className="flex gap-2 flex-wrap py-2">
        {data.map((item, index) => (
          <div key={index} className="flex p-2 bg-white rounded">
            {renderPreview(item)}
            {!item.fileType.startsWith('audio') && (
              <div className="ml-3 flex flex-col justify-between items-end">
                <Typography.Text ellipsis={{ tooltip: true }} style={{ maxWidth: 150 }}>
                  {item.fileName}
                </Typography.Text>
                <Button type="text" icon={<DownloadOutlined />} size="small" href={item.fileUrl} />
              </div>
            )}
          </div>
        ))}
      </div>

      {/* <PdfViewer open={!!pdfViewerUrl} url={pdfViewerUrl} onClose={() => setPdfViewerUrl('')} /> */}
      <PdfViewer visible={!!pdfViewerUrl} url={pdfViewerUrl} onClose={() => setPdfViewerUrl('')} />

      {videoUrl &&
        createPortal(
          <div
            className="fixed flex justify-center items-center top-0 left-0 h-full w-full bg-black/50 z-[9999]"
            onClick={() => setVideoUrl('')}
          >
            <CloseOutlined
              className="absolute top-6 right-6 rounded-full p-2 bg-gray-200 cursor-pointer hover:opacity-60"
              onClick={() => setVideoUrl('')}
            />
            <video
              src={videoUrl}
              controls
              style={{
                maxWidth: '80%',
                maxHeight: '80%',
              }}
              onClick={(e) => e.stopPropagation()}
            />
          </div>,
          document.body,
        )}
    </>
  );
};

export default FileList;
