import { useEffect, useState } from 'react';
import { Popup, SafeArea, Selector } from 'antd-mobile';
import { CloseOutline } from 'antd-mobile-icons';
import { DynamicEnum, dynamicOptions } from '../../enum';

type IProps = {
  children: React.ReactNode;
  defaultValue?: DynamicEnum;
  onChange: (e: DynamicEnum) => void;
};

const DynamicFilter: React.FC<IProps> = ({ children, defaultValue, onChange }) => {
  const [visible, setVisible] = useState(false);
  const [value, setValue] = useState<DynamicEnum>(defaultValue || DynamicEnum.线索动态);

  useEffect(() => {
    defaultValue && setValue(() => defaultValue);
  }, [defaultValue]);

  const handleConfirm = (val: DynamicEnum) => {
    onChange && onChange(val);
    setVisible(false);
  };

  return (
    <>
      <div
        onClick={(e) => {
          e.stopPropagation();
          setVisible(true);
        }}
      >
        {children}
      </div>
      <Popup
        position="bottom"
        visible={visible}
        bodyClassName="pb-10 box-border rounded-t-lg"
        onMaskClick={() => {
          setVisible(false);
        }}
      >
        <div
          className="flex px-4 justify-between items-center border-b border-solid border-line"
          style={{ height: '3.375rem' }}
        >
          <div className="text-[18px] text-dark font-semibold">线索进展</div>
          <CloseOutline
            onClick={() => {
              setVisible(false);
            }}
            className="text-grey text-13"
          />
        </div>
        <div className="pt-4 px-4">
          <div className="max-h-96 overflow-y-auto">
            <Selector
              // showCheckMark={false}
              style={{
                // '--border-radius': '4px',
                '--color': 'rgba(249,250,251)',
                '--text-color': '#58595B',
                // '--checked-color': '#e1faf5',
                '--padding': '0.5rem 0.5rem',
                fontSize: '0.8125rem',
              }}
              columns={3}
              options={dynamicOptions}
              value={[value]}
              onChange={(v) => {
                if (v.length) {
                  setValue(v[0]);
                  handleConfirm(v[0]);
                }
              }}
            />
          </div>
        </div>
        <SafeArea position="bottom" />
      </Popup>
    </>
  );
};

export default DynamicFilter;
