import { useEffect } from 'react';
import { updateCustomer } from '@src/api/customer';
import { CreateCustomerReq, Customer } from '@src/api/type/customer.type';
import useFieldColumns from '@src/hooks/useFieldColumns';
import { useRequest } from 'ahooks';
import { Button, Form, Popup, PopupProps, Toast } from 'antd-mobile';
import { CloseOutline } from 'antd-mobile-icons';
import CustomerFormItems from '../CustomerFormItems';

interface EditCustomerInfoPopupProps extends PopupProps {
  initialValues?: Customer;
  onSuccess: () => void;
}

const EditCustomerInfoPopup: React.FC<EditCustomerInfoPopupProps> = ({
  initialValues,
  visible,
  onSuccess,
  ...props
}) => {
  const [form] = Form.useForm();
  const { baseFields, customFields, getFieldProps } = useFieldColumns();

  const { runAsync: edit, loading: editLoading } = useRequest(updateCustomer, { manual: true });

  useEffect(() => {
    if (visible) {
      form.setFieldsValue(initialValues);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  return (
    <Popup visible={visible} bodyClassName="box-border rounded-t-lg" destroyOnClose {...props}>
      <div
        className="flex px-4 justify-between items-center border-b border-solid border-line"
        style={{ height: '3.375rem' }}
      >
        <div className="text-[18px] text-dark font-semibold">编辑信息</div>
        <CloseOutline onClick={props.onClose} className="text-grey text-13" />
      </div>
      <Form
        form={form}
        preserve={false}
        layout="horizontal"
        mode="default"
        onFinish={async (values) => {
          // 所有自定义字段 field
          const allCustomFields = customFields.map((i) => i.field);
          const params: CreateCustomerReq = { fieldValues: [] };

          Object.keys(values).forEach((field) => {
            const value = values[field];

            if (allCustomFields.includes(field)) {
              params.fieldValues.push({ field, value });
            } else {
              params[field] = value;
            }
          });
          await edit({ id: initialValues?.id!, ...params });
          Toast.show({ icon: 'success', content: '修改成功' });
          onSuccess();
        }}
        onFinishFailed={() => {
          Toast.show({
            icon: 'success',
            content: '请确保信息填写正确！',
          });
        }}
      >
        <div className="max-h-[70vh] overflow-y-auto">
          <CustomerFormItems
            isEdit
            data={initialValues}
            baseFields={baseFields}
            customFields={customFields}
            getFieldProps={getFieldProps}
          />
        </div>
        <Form.Item>
          <Button color="primary" block type="submit" loading={editLoading}>
            提交
          </Button>
        </Form.Item>
      </Form>
    </Popup>
  );
};

export default EditCustomerInfoPopup;
