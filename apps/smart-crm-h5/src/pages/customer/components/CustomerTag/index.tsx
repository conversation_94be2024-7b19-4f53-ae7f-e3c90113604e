import { useEffect, useMemo, useState } from 'react';
import { getTagList, updateUserTag } from '@src/api/customer';
import { useRequest } from 'ahooks';
import { Button, Popup, SafeArea, SearchBar, Space, Tag } from 'antd-mobile';
import { CloseOutline } from 'antd-mobile-icons';

type IProps = {
  children?: React.ReactNode;
  defaultValue?: number[];
  customerId?: number;
  refresh?: () => void;
  onChange?: (v: number[]) => void;
  onShowTags?: (v: { label: string; value: number }[]) => void;
};

const CustomerTag: React.FC<IProps> = ({
  children,
  defaultValue,
  customerId,
  refresh,
  onChange,
  onShowTags,
}) => {
  const [visible, setVisible] = useState(false);
  const [keyWords, setKeyWords] = useState<string>('');
  const [value, setValue] = useState<number[]>(defaultValue || []);

  useEffect(() => {
    defaultValue && defaultValue.length && setValue(defaultValue);
  }, [defaultValue]);

  const { data } = useRequest(async () => {
    const res = await getTagList();

    return res?.result || [];
  });

  const tagNameMap = useMemo(() => {
    const map = new Map<number, string>();

    data?.forEach((item) =>
      item.tags?.forEach((v) => {
        map.set(v.id, v.name);
      }),
    );

    return map;
  }, [data]);

  const showData = useMemo(() => {
    let list = data || [];

    if (keyWords) {
      list = list?.filter((v) => {
        if (v.tags && v.tags.length) {
          return (
            v.name.includes(keyWords) ||
            v.tags.filter((item) => item.name.includes(keyWords))?.length
          );
        } else {
          return v.name.includes(keyWords);
        }
      });
    }

    return list;
  }, [data, keyWords]);

  const { run } = useRequest(
    async (values) => {
      const res = await updateUserTag(values);

      return res;
    },
    {
      manual: true,
      onSuccess: () => {
        setVisible(false);
        refresh && refresh();
      },
    },
  );

  const handleChange = () => {
    onChange && onChange(value);
    onShowTags && onShowTags(value.map((id) => ({ label: tagNameMap.get(id) || '-', value: id })));
    setVisible(false);
  };

  const handleCancel = () => {
    defaultValue && setValue(() => defaultValue);
    setVisible(false);
  };

  return (
    <>
      <div
        onClick={(e) => {
          e.stopPropagation();
          setKeyWords('');
          setVisible(true);
        }}
      >
        {children}
      </div>
      <Popup
        position="bottom"
        visible={visible}
        bodyClassName="box-border rounded-t-lg"
        // onMaskClick={() => {
        //   setVisible(false);
        // }}
      >
        <div
          className="flex px-4 justify-between items-center border-b border-solid border-line"
          style={{ height: '3.375rem' }}
        >
          <div className="text-[18px] text-dark font-semibold">选择标签</div>
          <CloseOutline onClick={handleCancel} className="text-grey text-13" />
        </div>
        <div className="pt-2 px-4">
          <div className="bg-white py-2">
            <SearchBar
              placeholder="请输入内容"
              value={keyWords}
              onChange={setKeyWords}
              onSearch={setKeyWords}
              clearable
            />
          </div>
          <div className="h-[320px] overflow-y-auto">
            {showData?.map((item) => (
              <div className="mb-3" key={item.id}>
                <div>
                  {item.name} {item.status === 'INVALID' && '(已废弃)'}
                </div>
                <div className="mt-2">
                  {item?.tags && item.tags.length ? (
                    <Space wrap>
                      {(item.tags || []).map((v) => {
                        const checked = value.includes(v.id);

                        return (
                          <Tag
                            className={`px-2 py-1 ${checked ? '' : 'text-inherit'}`}
                            key={v.id}
                            color={checked ? 'primary' : '#eee'}
                            onClick={() => {
                              const check = !checked;

                              if (item.type === 'RADIO') {
                                if (check) {
                                  setValue(
                                    value
                                      .filter(
                                        (val) =>
                                          !(item.tags || [])
                                            .map((t) => t.id)
                                            .some((id) => id === val),
                                      )
                                      .concat(v.id),
                                  );
                                } else {
                                  setValue(value.filter((val) => val !== v.id));
                                }
                              } else {
                                setValue(
                                  check ? value.concat(v.id) : value.filter((val) => val !== v.id),
                                );
                              }
                            }}
                          >
                            {v.name}
                          </Tag>
                        );
                      })}
                    </Space>
                  ) : (
                    <span className="text-gray-500">无标签</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="flex px-4 py-3">
          <Button block color="primary" fill="outline" className="mr-3" onClick={handleCancel}>
            取消
          </Button>
          <Button
            block
            color="primary"
            onClick={() => {
              console.log(value, '=value');

              const addTagIds = value.filter((id) => !defaultValue?.includes(id));
              const removeTagIds = defaultValue?.filter((id) => !value.includes(id));

              customerId
                ? run({
                    addTagIds,
                    removeTagIds,
                    clueId: customerId,
                  })
                : handleChange();
            }}
          >
            确定
          </Button>
        </div>
        <SafeArea position="bottom" />
      </Popup>
    </>
  );
};

export default CustomerTag;
