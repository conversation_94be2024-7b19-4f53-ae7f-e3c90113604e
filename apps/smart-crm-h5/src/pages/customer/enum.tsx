export enum DynamicEnum {
  线索动态 = 'CUSTOMER_ACTIVITY',
  信息变更 = 'CUSTOMER_INFO_CHANGE',
  跟进记录 = 'FOLLOW',
  通话记录 = 'CALL_LOG',
}

export const dynamicOptions = [
  { label: '线索动态', value: DynamicEnum.线索动态 },
  { label: '信息变更', value: DynamicEnum.信息变更 },
  { label: '跟进记录', value: DynamicEnum.跟进记录 },
  { label: '通话记录', value: DynamicEnum.通话记录 },
];

export const SexMap = {
  MALE: '男',
  FEMALE: '女',
};

export enum OperationTypeEnum {
  // 线索动态
  /** 领取 */
  GET = 'GET',
  /** 分配 */
  ALLOCATION = 'ALLOCATION',
  /** 转让 */
  TRANSFER = 'TRANSFER',
  /** 放弃 */
  ABANDON = 'ABANDON',
  /** 删除线索 */
  DELETE = 'DELETE',
  /** 企业微信，线索添加员工 */
  ADD_STAFF = 'ADD_STAFF',
  /** 企业微信，线索删除员工 */
  DELETE_STAFF = 'DELETE_STAFF',
  /** 企业微信，员工删除线索 */
  STAFF_DELETE_CLUE = 'STAFF_DELETE_CLUE',

  // 线索信息变更
  /** 创建线索 */
  CREATE_CLUE = 'CREATE_CLUE',
  /** 更新线索基础信息 */
  CLUE_INFO_CHANGE = 'CLUE_INFO_CHANGE',
  /** 标签变更 */
  TAG_CHANGE = 'TAG_CHANGE',

  /** 跟进动态 */
  ADD_FOLLOW = 'ADD_FOLLOW',

  // 拨打电话
  /** 未接通 */
  BLOCK_CALL = 'BLOCK_CALL',
  /** 通话录音丢失 */
  CALL_RECORDING_LOST = 'CALL_RECORDING_LOST',
  /** 通话录音已取回 */
  CALL_RECORDING_RETRIEVED = 'CALL_RECORDING_RETRIEVED',
  /** 回拨未接通 */
  CALL_BACK_BLOCK_CALL = 'CALL_BACK_BLOCK_CALL',
  /** 回拨通话记录丢失 */
  CALL_BACK_CALL_RECORDING_LOST = 'CALL_BACK_CALL_RECORDING_LOST',
  /** 回拨通话记录已取回 */
  CALL_BACK_CALL_RECORDING_RETRIEVED = 'CALL_BACK_CALL_RECORDING_RETRIEVED',
}

export const operationTypeLabelMap = {
  [OperationTypeEnum.ALLOCATION]: '分配线索',
  [OperationTypeEnum.GET]: '领取线索',
  [OperationTypeEnum.DELETE]: '删除线索',
  [OperationTypeEnum.ADD_STAFF]: '添加员工',
  [OperationTypeEnum.DELETE_STAFF]: '删除员工',
  [OperationTypeEnum.STAFF_DELETE_CLUE]: '删除线索',
  [OperationTypeEnum.TRANSFER]: '转让线索',
  [OperationTypeEnum.ABANDON]: '放弃线索',
  [OperationTypeEnum.CREATE_CLUE]: '创建线索',
  [OperationTypeEnum.TAG_CHANGE]: '更新标签',
  [OperationTypeEnum.CLUE_INFO_CHANGE]: '变更信息',
  [OperationTypeEnum.ADD_FOLLOW]: '线索跟进',
  [OperationTypeEnum.BLOCK_CALL]: '拨打电话',
  [OperationTypeEnum.CALL_RECORDING_RETRIEVED]: '拨打电话',
  [OperationTypeEnum.CALL_RECORDING_LOST]: '拨打电话',
  [OperationTypeEnum.CALL_BACK_BLOCK_CALL]: '线索回拨电话',
  [OperationTypeEnum.CALL_BACK_CALL_RECORDING_LOST]: '线索回拨电话',
  [OperationTypeEnum.CALL_BACK_CALL_RECORDING_RETRIEVED]: '线索回拨电话',
};

// 来源 options
export const sourceOptions = [
  { label: '抖音', value: 'DY' },
  { label: '企业微信', value: 'ENTERPRISE_WX' },
  { label: '微信视频', value: 'WX_VIDEO' },
  { label: '抖音客服', value: 'DY_CUSTOMER_SERVICE' },
  { label: '400', value: 'CUSTOMER_SERVICE_HOT_LINE' },
  { label: '官网', value: 'OFFICIAL_WEBSITE' },
  { label: '思企', value: 'SQ' },
  { label: '招商小程序', value: 'ZH_APPLET' },
  { label: '公众号', value: 'OFFICIAL_ACCOUNT' },
  { label: '抖音直播', value: 'DY_LIVE' },
  { label: '抖音达人合作', value: 'DY_TALENT_COOPERATE' },
  { label: '总部员工推荐', value: 'RHQ_RECOMMEND' },
  { label: '分公司转介绍', value: 'BRANCH_OFFICE_INTRODUCE' },
  { label: '老线索转介绍', value: 'OLD_CUSTOMER_INTRODUCE' },
  { label: '微博', value: 'WB' },
  { label: '小红书', value: 'XHS' },
  { label: '美团渠道', value: 'MT_CHANNEL' },
  { label: '知乎', value: 'ZH' },
  { label: '招商会', value: 'ZSH' },
  { label: '展会招商', value: 'EXHIBITION' },
  { label: '抖音企业号', value: 'DY_ENTERPRISE_ACCOUNT' },
  { label: '大众点评', value: 'DIAN_PING' },
  { label: '美团', value: 'MT' },
  { label: 'ec在线客服', value: 'EC_CUSTOMER_SERVICE' },
  { label: 'QQ浏览器', value: 'QQ_BROWSER' },
  { label: '未知', value: 'UNKNOWN' },
];

export enum AuditStatusEnum {
  CONFORM = 'CONFORM',
  NOT_CONFORM = 'NOT_CONFORM',
}

export const auditStatusOptions = [
  {
    label: '符合',
    value: AuditStatusEnum.CONFORM,
  },
  { label: '不符合', value: AuditStatusEnum.NOT_CONFORM },
];

export const educationOptions = [
  { label: '博士研究生', value: 'DOCTORAL_DEGREE' },
  { label: '硕士研究生', value: 'MASTER_DEGREE' },
  { label: '本科', value: 'UNDERGRADUATE_DEGREE' },
  { label: '专科', value: 'JUNIOR_COLLEGE' },
  { label: '高中 (高职、高技、中专)', value: 'HIGH_SCHOOL' },
  { label: '中学', value: 'MIDDLE_SCHOOL' },
  { label: '小学', value: 'PRIMARY_SCHOOL' },
];
