import React from 'react';
import { Mask, SpinLoading } from 'antd-mobile';

interface LoadingProps {
  spinning?: boolean;
  children?: React.ReactNode;
  maskClickable?: boolean;
}

const Loading: React.FC<LoadingProps> = ({ spinning = true, maskClickable = true, children }) => {
  return (
    <>
      {children}
      {spinning && (
        <Mask
          opacity={0}
          disableBodyScroll={!maskClickable}
          className={maskClickable ? 'pointer-events-none' : 'pointer-events-auto'}
          stopPropagation={['click']}
          getContainer={() => document.body}
        >
          <div className="fixed top-0 left-0 w-full h-full text-center">
            <div className="relative top-1/2 -translate-y-1/2 px-4 py-9 bg-[rgba(0,0,0,0.7)] rounded-lg inline-block text-white min-w-[150px]">
              <SpinLoading
                className="mx-auto mb-2"
                style={{ '--size': '48px', '--color': 'white' }}
              />
              <span className="text-15">加载中…</span>
            </div>
          </div>
        </Mask>
      )}
    </>
  );
};

export default Loading;
