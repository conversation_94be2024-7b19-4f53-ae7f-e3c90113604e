import CityPick from './CityPick';
import CountyPick from './CountyPick';
import ProvincePick from './ProvincePick';

/**
 * 最下级地区多选
 * @param type province city county
 */
export type RegionProps = {
  value?: string;
  disabled?: boolean;
  cityData: TreeWith<OptionItem>[];
  onChange?: (e: string) => void;
};

const RegionPick: React.FC<{ type: 'province' | 'city' | 'county' } & RegionProps> = ({
  type,
  ...rest
}) => {
  const RegionMap: Record<string, React.FC<RegionProps>> = {
    province: ProvincePick,
    city: CityPick,
    county: CountyPick,
  };
  const Component = RegionMap[type] || CityPick;

  return <Component {...rest} />;
};

export default RegionPick;
