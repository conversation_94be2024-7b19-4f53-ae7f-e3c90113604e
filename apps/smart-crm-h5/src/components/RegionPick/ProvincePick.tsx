import { useEffect, useState } from 'react';
import { Button, Popup } from 'antd-mobile';
import { CheckOutline } from 'antd-mobile-icons';
import { RegionProps } from '.';

const ProvincePick: React.FC<RegionProps> = ({
  onChange,
  disabled,
  cityData,
  value: initValue = '',
}) => {
  const [visible, setVisible] = useState(false);
  const [value, setValue] = useState<string[]>([]);
  const [valueName, setValueName] = useState<{ label: string; value: string }[]>([]);

  // 地区value => {label, value}
  const handleFormatValue = (data: TreeWith<OptionItem>[], province: string[]) => {
    const provinceList = data.filter((v) => province.includes(v.value));

    setValueName(provinceList);
  };

  // 赋值
  useEffect(() => {
    if (initValue) {
      const arr = initValue.split(',');

      setValue(arr);

      if (cityData) {
        handleFormatValue(cityData, arr);
      }
    } else {
      setValue([]);
      setValueName([]);
    }
  }, [cityData, initValue, visible]);

  return (
    <>
      <div onClick={() => !disabled && setVisible(true)}>
        {valueName.length ? (
          valueName.map((v) => `${v.label}`).join('/')
        ) : (
          <span className="text-[#ccc]">请选择</span>
        )}
      </div>
      <Popup
        position="top"
        visible={visible}
        bodyClassName="box-border"
        onMaskClick={() => {
          setVisible(false);
        }}
      >
        <div>
          <div className="flex items-center">
            <div className="overflow-y-auto flex-1" style={{ height: '25rem' }}>
              {!!cityData.length && (
                <>
                  {cityData.map((v) => {
                    const key = `${v.value}`;
                    const checked = value.includes(key);

                    return (
                      <div
                        key={v.value}
                        className={`flex items-center py-[10px] text-13 leading-[21px] text-sm px-5 ${
                          checked ? 'text-primary' : ''
                        }`}
                        onClick={() => {
                          if (checked) {
                            setValue((p) => p.filter((ele) => ele !== key));
                          } else {
                            setValue((p) => [...p, key]);
                          }
                        }}
                      >
                        <div className="flex-1 mr-1">{v.label}</div>
                        {checked ? (
                          <div className="w-4 h-4 bg-primary rounded-full flex justify-center items-center border border-primary">
                            <CheckOutline className="text-white text-xs" />
                          </div>
                        ) : (
                          <div className="w-4 h-4 rounded-full border border-[rgba(0,0,0,0.3)]" />
                        )}
                      </div>
                    );
                  })}
                </>
              )}
            </div>
          </div>
          <div className="flex p-4 gap-3">
            <Button
              block
              className="h-[42px] text-sm"
              onClick={() => {
                setValue([]);
                setValueName([]);
                onChange && onChange('');
                setVisible(false);
              }}
              color="primary"
              fill="outline"
            >
              清空
            </Button>
            <Button
              block
              className="h-[42px] text-sm"
              color="primary"
              onClick={() => {
                onChange && onChange(value.join(','));
                setVisible(false);
              }}
            >
              确定
            </Button>
          </div>
        </div>
      </Popup>
    </>
  );
};

export default ProvincePick;
