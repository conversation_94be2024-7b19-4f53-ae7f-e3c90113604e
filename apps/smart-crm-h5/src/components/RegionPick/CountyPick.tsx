import { useEffect, useState } from 'react';
import { Button, Popup } from 'antd-mobile';
import { CheckOutline } from 'antd-mobile-icons';
import { RegionProps } from '.';

const CountyPick: React.FC<RegionProps> = ({
  onChange,
  disabled,
  cityData,
  value: initValue = '',
}) => {
  const [visible, setVisible] = useState(false);
  const [value, setValue] = useState<string[]>([]);
  const [valueName, setValueName] = useState<{
    province: { label: string; value: string };
    city: { label: string; value: string };
    county: { label: string; value: string }[];
  }>({
    province: { label: '', value: '' },
    city: { label: '', value: '' },
    county: [],
  });

  const [provinceIdx, setProvinceIdx] = useState<number>(0);
  const [cityIdx, setCityIdx] = useState<number>(0);

  // 地区value => {label, value}
  const handleFormatValue = (
    data: TreeWith<OptionItem>[],
    province: string,
    cityCode: string,
    county: string[],
  ) => {
    const {
      label: pLable,
      value: pValue,
      children: vChildren,
    } = data.find((v) => v.value === province) || {
      label: '',
      value: '',
      children: [],
    };
    const {
      label,
      value: val,
      children,
    } = vChildren?.find((v) => v.value === cityCode) || {
      label: '',
      value: '',
      children: [],
    };
    const countyNames = county.map((countyValue) => ({
      label: children?.find((v) => v.value === countyValue)?.label || '',
      value: countyValue || '',
    }));

    setValueName({
      province: { label: pLable, value: pValue },
      city: { label, value: val },
      county: countyNames,
    });

    const provinceI = data.findIndex((v) => v.value === province);
    const cityI = data[provinceI].children?.findIndex((v) => v.value === cityCode) || 0;

    setProvinceIdx(() => (provinceI !== -1 ? provinceI : 0));
    setCityIdx(() => (cityI !== -1 ? cityI : 0));
  };

  // 赋值
  useEffect(() => {
    if (initValue) {
      const arr = initValue.split(',');

      setValue(arr);

      if (cityData) {
        const [provinceCode, cityCode] = arr[0].indexOf('/') ? arr[0].split('/') : ['', '', ''];
        const countyList = arr.map((v) => (v.indexOf('/') ? v.split('/')[2] : '')).filter((v) => v);

        handleFormatValue(cityData, provinceCode, cityCode, countyList);
      }
    } else {
      setValue([]);
      setValueName({
        province: { label: '', value: '' },
        city: { label: '', value: '' },
        county: [],
      });
    }
  }, [cityData, initValue, visible]);

  return (
    <>
      <div onClick={() => !disabled && setVisible(true)}>
        {disabled
          ? valueName.province.label
          : valueName.province.label || <span className="text-[#ccc]">请选择</span>}{' '}
        {valueName.city.label || ''} {valueName.county.map((v) => `${v.label}`).join('/')}
      </div>
      <Popup
        position="top"
        visible={visible}
        bodyClassName="box-border"
        onMaskClick={() => {
          setVisible(false);
        }}
      >
        <div>
          <div className="flex items-center">
            <div
              className="overflow-y-auto bg-[#f5f5f5]"
              style={{ height: '25rem', width: '6.5rem' }}
            >
              {cityData.map((item, index) => (
                <div
                  key={item.value}
                  className={`pl-4 pr-2 text-13 leading-[21px] py-[10px] ${
                    provinceIdx === index ? 'bg-white text-primary' : ''
                  }`}
                  onClick={() => {
                    const cityElement = document.getElementById('city');
                    const listElement = document.getElementById('county');

                    setProvinceIdx(index);
                    setCityIdx(0);
                    cityElement && (cityElement.scrollTop = 0);
                    listElement && (listElement.scrollTop = 0);
                  }}
                >
                  {/* {!!value[p.key].length && <IconFont type="icon-point" />} */}
                  {item.label}
                </div>
              ))}
            </div>
            <div
              className="overflow-y-auto bg-[#f5f5f5]"
              style={{ height: '25rem', width: '6.5rem' }}
              id="city"
            >
              {cityData[provinceIdx]?.children?.map((item, index) => (
                <div
                  key={item.value}
                  className={`pl-4 pr-2 text-13 leading-[21px] py-[10px] ${
                    cityIdx === index ? 'bg-white text-primary' : ''
                  }`}
                  onClick={() => {
                    const listElement = document.getElementById('county');

                    setCityIdx(index);
                    listElement && (listElement.scrollTop = 0);
                  }}
                >
                  {item.label}
                </div>
              ))}
            </div>
            <div className="overflow-y-auto flex-1" id="county" style={{ height: '25rem' }}>
              {!!cityData.length &&
                cityData[provinceIdx].children &&
                cityData[provinceIdx].children![cityIdx] && (
                  <>
                    {cityData[provinceIdx].children![cityIdx]?.children?.map((v) => {
                      const key = `${cityData[provinceIdx].value}/${
                        cityData[provinceIdx].children![cityIdx].value
                      }/${v.value}`;
                      const checked = value.includes(key);
                      const canSelect =
                        !value.length ||
                        value.some((i) =>
                          cityData[provinceIdx]
                            .children![cityIdx].children?.map(
                              (ele) =>
                                `${cityData[provinceIdx].value}/${
                                  cityData[provinceIdx].children![cityIdx].value
                                }/${ele.value}`,
                            )
                            .includes(i),
                        );

                      return (
                        <div
                          key={v.value}
                          className={`flex items-center py-[10px] text-13 leading-[21px] text-sm px-5 ${
                            checked ? 'text-primary' : ''
                          }`}
                          onClick={() => {
                            if (canSelect) {
                              if (checked) {
                                setValue((p) => p.filter((ele) => ele !== key));
                              } else {
                                setValue((p) => [...p, key]);
                              }
                            }
                          }}
                        >
                          <div className="flex-1 mr-1">{v.label}</div>
                          {canSelect ? (
                            checked ? (
                              <div className="w-4 h-4 bg-primary rounded-full flex justify-center items-center border border-primary">
                                <CheckOutline className="text-white text-xs" />
                              </div>
                            ) : (
                              <div className="w-4 h-4 rounded-full border border-[rgba(0,0,0,0.3)]" />
                            )
                          ) : (
                            <div className="text-xs text-dark-1">不允许跨市</div>
                          )}
                        </div>
                      );
                    })}
                  </>
                )}
            </div>
          </div>
          <div className="flex p-4 gap-3">
            <Button
              block
              className="h-[42px] text-sm"
              onClick={() => {
                setValue([]);
                setValueName({
                  province: { label: '', value: '' },
                  city: { label: '', value: '' },
                  county: [],
                });
                onChange && onChange('');
                setVisible(false);
              }}
              color="primary"
              fill="outline"
            >
              清空
            </Button>
            <Button
              block
              className="h-[42px] text-sm"
              color="primary"
              onClick={() => {
                console.log(value, 'value');
                onChange && onChange(value.join(','));
                setVisible(false);
              }}
            >
              确定
            </Button>
          </div>
        </div>
      </Popup>
    </>
  );
};

export default CountyPick;
