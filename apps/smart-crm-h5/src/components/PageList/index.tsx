import useScrollFetch from '@src/hooks/useScrollFetch';
import { CssProps } from '@src/common/api.type';
import { Empty, InfiniteScroll, InfiniteScrollProps } from 'antd-mobile';
import classNames from 'classnames';
import { ReactNode } from 'react';

export type PageListProps<T> = CssProps & {
  dataSource: T[];
  itemRender(item: T, index: number): ReactNode;
  pageSize?: number;
  bottom?: InfiniteScrollProps['children'];
};

function PageList<T>({
  className,
  style,
  dataSource,
  pageSize,
  bottom,
  itemRender,
}: PageListProps<T>) {
  const { list, over, fetchNext } = useScrollFetch(
    ({ pageNo, pageSize }) =>
      Promise.resolve(dataSource.slice((pageNo - 1) * pageSize, pageNo * pageSize)),
    [dataSource],
    {
      pageSize,
    },
  );

  return (
    <div className={classNames('overflow-y-auto', className)} style={style}>
      {list.map((l) => l.map(itemRender))}
      <InfiniteScroll loadMore={fetchNext} hasMore={!over}>
        {bottom ??
          (!dataSource.length ? <Empty description="暂无数据" /> : <span>--- 没有更多了 ---</span>)}
      </InfiniteScroll>
    </div>
  );
}

PageList.defaultProps = {
  pageSize: 10,
};

export default PageList;
