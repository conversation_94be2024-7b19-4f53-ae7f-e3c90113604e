import { LoaderFunctionArgs, useLoaderData } from 'react-router-dom';

/**
 * 飞书授权登录路由loader，用于获取飞书登录code，没有code则抛出异常
 * @returns 飞书登录code
 */
export const loaderFeishuLoginCode = async ({
  request,
}: LoaderFunctionArgs): Promise<Record<string, any>> => {
  // 检查一下是否有code，如果没有code则抛出异常
  const searchParams = location.search
    ? new URL(location.href).searchParams
    : new URL(request.url).searchParams;
  const code = searchParams.get('code');
  const state = searchParams.get('state');

  if (!code) {
    throw new Error('飞书登录code不存在，请检查是否使用了飞书授权登录');
  }
  return { code, state };
};

/**
 * 飞书登录路由loader的hook函数
 * @returns 飞书登录code
 */
export const useLoaderFeishuLoginCode = () => {
  return useLoaderData() as Awaited<ReturnType<typeof loaderFeishuLoginCode>>;
};
