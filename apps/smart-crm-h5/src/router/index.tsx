import { lazy, ReactNode, Suspense } from 'react';
import { updateCurLocation } from '@src/common/http';
import Layout from '@src/layout';
import Login from '@src/pages/login';
import WxLogin from '@src/pages/login/auth';
import Tip from '@src/pages/tip';
import { getToken, setToken } from '@src/utils/tokenUtils';
import { DotLoading } from 'antd-mobile';
import { parse, stringify } from 'qs';
import {
  createHashRouter,
  LoaderFunction,
  Navigate,
  redirect,
  RouteObject,
  RouterProvider,
  useLocation,
} from 'react-router-dom';
import { loaderFeishuLoginCode } from './loader';
import ErrorPage from '../common/error-pages';

// token 鉴权
const loaderCheckAuth: LoaderFunction = ({ request }) => {
  const { search, pathname } = new URL(request.url);
  const { token, ...rest } = parse(search.slice(1));

  if (token) {
    setToken(token as string);

    return redirect(`${pathname}${Object.keys(rest).length ? `?${stringify(rest)}` : ''}`);
  } else {
    const localToken = getToken();

    return localToken
      ? null
      : redirect(`/user/login?redirect=${encodeURIComponent(`${pathname || '/'}${search || ''}`)}`);
  }
};

const DocumentTitle: React.FC<{ title: string; children: ReactNode }> = ({ title, children }) => {
  const loc = useLocation();

  updateCurLocation(loc);

  document.title = title || '招商线索画像';

  return children;
};

const dig = (list: any) => {
  return list.map(({ Component, ...v }: RouteObject & { title: string }) => {
    return {
      index: v.index,
      path: v.path,
      loader: loaderCheckAuth,
      errorElement: <ErrorPage />,
      children: v.children && v.children.length ? (dig(v.children) as RouteObject[]) : undefined,
      element: Component ? (
        <Suspense
          fallback={
            <div className="pt-4 text-center">
              <DotLoading color="primary" />
            </div>
          }
        >
          <DocumentTitle title={v.title}>
            <Component />
          </DocumentTitle>
        </Suspense>
      ) : (
        v.element
      ),
    };
  });
};

const mainRoutes: RouteObject[] = dig([
  {
    path: '/',
    Component: Layout,
    children: [
      { index: true, element: <Navigate to="/customer" replace /> },
      {
        title: '招商线索画像',
        path: '/customer',
        Component: lazy(() => import('../pages/customer')),
      },
      {
        title: '招商线索画像',
        path: '/customer/bind',
        Component: lazy(() => import('../pages/customer/bind')),
      },
      {
        title: '新建线索',
        path: '/customer/create',
        Component: lazy(() => import('../pages/customer/create')),
      },
      {
        title: '线索列表',
        path: '/customer/list',
        Component: lazy(() => import('../pages/customer/list')),
        children: [
          {
            title: '线索列表',
            path: '/customer/list/search',
            Component: lazy(() => import('../pages/customer/list/search')),
            children: [
              {
                title: '招商线索画像',
                path: '/customer/list/search/detail',
                Component: lazy(() => import('../pages/customer')),
              },
            ],
          },
          {
            title: '招商线索画像',
            path: '/customer/list/detail',
            Component: lazy(() => import('../pages/customer')),
          },
        ],
      },
    ],
  },
]);

const routes: RouteObject[] = [
  ...mainRoutes,
  {
    path: '/user/login',
    element: <Login />,
    loader: () => {
      const localToken = getToken();

      return localToken ? redirect('/customer') : null;
    },
  },
  {
    path: '/user/wxLogin',
    element: (
      <DocumentTitle title="授权登录">
        <WxLogin />
      </DocumentTitle>
    ),
    loader: loaderFeishuLoginCode,
    errorElement: <ErrorPage />,
  },
  {
    path: '/tip',
    element: <Tip />,
  },
];

const Routers = () => {
  const router = createHashRouter(routes);

  return <RouterProvider router={router} />;
};

export default Routers;
