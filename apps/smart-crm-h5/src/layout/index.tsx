import { userStore } from '@src/store';
import { getToken } from '@src/utils/tokenUtils';
import { SafeArea } from 'antd-mobile';
import { useLayoutEffect } from 'react';
import { Outlet } from 'react-router-dom';

const Layout = () => {
  const init = () => {
    //获取用户信息，用户权限
    console.log('app init');
    if (getToken()) {
      console.log('get user auth');
      userStore.getUserAuth();
      // userStore.getUserInfo();
    }
  };

  useLayoutEffect(() => {
    init();
  }, []);

  return (
    <div className="flex flex-col h-screen overflow-hidden">
      <div className="flex-1 overflow-y-hidden">
        <Outlet />
      </div>
      <SafeArea position="bottom" />
    </div>
  );
};

export default Layout;
