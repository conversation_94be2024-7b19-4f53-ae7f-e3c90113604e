:root {
  --adm-color-primary: #00bbb4;
  --adm-color-text: #141414;
  --adm-font-size-main: 14px;
  // 主题浅色
  --color-primary-1: #e1faf5;
  --adm-color-border: rgba(0, 0, 0, 0.03);
  --z-index: 1002;
  --adm-dialog-z-index: 1002;
  --adm-popup-z-index: 1002;
  --adm-mask-z-index: 1002;
}
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2; // 控制多行的行数
  -webkit-box-orient: vertical;
}

.ellipsis-1 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1; // 控制多行的行数
  -webkit-box-orient: vertical;
}

.adm-error-block .adm-error-block-image svg {
  display: inline;
}

.adm-toast-icon svg {
  display: inline;
}
