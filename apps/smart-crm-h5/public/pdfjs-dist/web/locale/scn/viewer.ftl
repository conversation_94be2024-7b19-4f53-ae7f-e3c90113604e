# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.


## Main toolbar buttons (tooltips and alt text for images)

pdfjs-zoom-out-button =
    .title = Cchiù nicu
pdfjs-zoom-out-button-label = Cchiù nicu
pdfjs-zoom-in-button =
    .title = Cchiù granni
pdfjs-zoom-in-button-label = Cchiù granni

##  Secondary toolbar and context menu


## Document properties dialog


## Variables:
##   $width (Number) - the width of the (current) page
##   $height (Number) - the height of the (current) page
##   $unit (String) - the unit of measurement of the (current) page
##   $name (String) - the name of the (current) page
##   $orientation (String) - the orientation of the (current) page


##

# The linearization status of the document; usually called "Fast Web View" in
# English locales of Adobe software.
pdfjs-document-properties-linearized = Vista web lesta:
pdfjs-document-properties-linearized-yes = Se

## Print

pdfjs-print-progress-close-button = Sfai

## Tooltips and alt text for side panel toolbar buttons


## Thumbnails panel item (tooltip and alt text for images)


## Find panel button title and messages


## Predefined zoom values

pdfjs-page-scale-width = Larghizza dâ pàggina

## PDF page


## Loading indicator messages


## Annotations


## Password

pdfjs-password-cancel-button = Sfai

## Editing


## Alt-text dialog


## Editor resizers
## This is used in an aria label to help to understand the role of the resizer.

