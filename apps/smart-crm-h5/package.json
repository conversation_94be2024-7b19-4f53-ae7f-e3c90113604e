{"name": "smart-crm-h5", "private": true, "version": "0.0.1", "scripts": {"dev": "vite", "build": "tsc && vite build", "dev-h5": "vite", "build-h5": "tsc && vite build", "lint": "eslint --fix . --ext ts,tsx", "tsc": "tsc --noEmit", "preview": "vite preview"}, "dependencies": {"@pkg/commons": "*", "@pkg/utils": "*", "@sentry/react": "^7.64.0", "@sentry/vite-plugin": "^2.6.1", "@types/ramda": "^0.29.9", "ahooks": "^3.7.8", "ali-oss": "^6.20.0", "antd-mobile": "^5.32.4", "antd-mobile-icons": "^0.3.0", "axios": "^1.5.1", "dayjs": "^1.11.11", "lodash": "^4.17.21", "mobx": "^6.12.0", "mobx-react": "^9.1.0", "qs": "^6.12.1", "ramda": "^0.29.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "vite-plugin-webpackchunkname": "^1.0.1"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "config": "*", "dotenv": "^16.4.5", "eslint": "^8.42.0", "postcss": "^8.4.31", "postcss-pxtorem": "^6.0.0", "tailwindcss": "^3.3.5", "typescript": "^5.0.2", "vite": "^4.4.5"}}