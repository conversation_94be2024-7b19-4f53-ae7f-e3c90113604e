<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/logo.png" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover"
    />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta name="format-detection" content="telephone=yes" />
    <meta name="referrer" content="no-referrer" />
    <title>招商客户画像</title>
    <script src="//res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>
    <script src="//open.work.weixin.qq.com/wwopen/js/jwxwork-1.0.0.js"></script>
    <script type="text/javascript">
      (function (win, lib) {
        var doc = win.document;
        var docEl = doc.documentElement;
        var dpr = 1;
        var tid;
        var flexible = lib.flexible || (lib.flexible = {});

        docEl.setAttribute('data-dpr', dpr);

        function refreshRem() {
          var width = docEl.getBoundingClientRect().width;
          if (1980 > width / dpr && width / dpr > 540) {
            width = width * dpr;
          } else if (3840 > width / dpr && width / dpr > 3800) {
            width = 3840 * dpr;
          }
          var rem = width / 23.4375;
          docEl.style.fontSize = rem + 'px';
          flexible.rem = win.rem = rem;
        }

        win.addEventListener(
          'resize',
          function () {
            clearTimeout(tid);
            tid = setTimeout(refreshRem, 100);
          },
          false,
        );
        win.addEventListener(
          'pageshow',
          function (e) {
            if (e.persisted) {
              clearTimeout(tid);
              tid = setTimeout(refreshRem, 100);
            }
          },
          false,
        );

        refreshRem();
      })(window, window['lib'] || (window['lib'] = {}));
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
