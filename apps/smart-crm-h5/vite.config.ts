import { sentryVitePlugin } from '@sentry/vite-plugin';
import react from '@vitejs/plugin-react';
import dotenv from 'dotenv';
import { defineConfig } from 'vite';
import { manualChunksPlugin } from 'vite-plugin-webpackchunkname';

dotenv.config();

const plugins = [react(), manualChunksPlugin()];

if (process.env.NODE_ENV === 'production') {
  plugins.push(
    sentryVitePlugin({
      org: process.env.SENTRY_ORG,
      project: process.env.SENTRY_PROJECT,
      url: process.env.SENTRY_URL,
      authToken: process.env.SENTRY_AUTH_TOKEN,
    }),
  );
}

// https://vitejs.dev/config/
export default defineConfig({
  base: '/h5/',
  build: {
    sourcemap: true,
  },
  plugins,
  resolve: {
    alias: {
      '@src': '/src',
    },
  },
  server: {
    host: '0.0.0.0',
    // host: '***********',
    port: 5175,
    proxy: {
      '/api': {
        target: 'https://crm-test.tastientech.com',
        // target: 'http://***********:8099',
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/api/, ''),
      },
      '/gaia-api': {
        target: 'https://crm-test.tastientech.com',
        // target: 'http://***********:8099',
        changeOrigin: true,
      },
    },
  },
});
