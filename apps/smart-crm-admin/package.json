{"name": "smart-crm-admin", "private": true, "version": "0.0.1", "scripts": {"dev": "vite", "dev-admin": "vite --mode ${MODE:-test}", "build": "tsc && vite build", "build-admin": "tsc && vite build --mode ${MODE:-test}", "lint": "eslint --fix . --ext ts,tsx", "preview": "vite preview", "tsc": "tsc --noEmit", "test": "jest"}, "dependencies": {"@ant-design/icons": "^5.3.7", "@ant-design/pro-components": "^2.7.8", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@floating-ui/react": "^0.26.25", "@ice/stark-app": "^1.5.0", "@microsoft/fetch-event-source": "^2.0.1", "@pkg/biz": "*", "@pkg/commons": "*", "@pkg/utils": "*", "@sentry/react": "^8.55.0", "@uiw/react-amap-api-loader": "^7.1.0", "@uiw/react-amap-map": "^7.1.0", "@uiw/react-amap-marker": "^7.1.0", "ahooks": "^3.7.8", "ali-oss": "^6.19.0", "antd": "^5.26.7", "axios": "1.4.0", "classnames": "^2.3.2", "dayjs": "^1.11.10", "docx-preview": "^0.3.2", "idb": "^8.0.3", "intersection-observer": "^0.12.2", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "nanoid": "^5.0.4", "qs": "^6.12.1", "quill": "^2.0.2", "rc-overflow": "^1.3.2", "rc-util": "^5.38.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-infinite-scroll-component": "^6.1.0", "react-resizable": "^3.0.5", "react-router-dom": "^6.26.2", "react-virtuoso": "^4.7.8", "zustand": "^5.0.6"}, "devDependencies": {"@sentry/vite-plugin": "^4.1.1", "@testing-library/jest-dom": "6.1.2", "@testing-library/react": "14.0.0", "@types/ali-oss": "^6.16.11", "@types/event-source-polyfill": "^1.0.5", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-resizable": "^3.0.7", "@uiw/react-amap-types": "^7.1.8", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.16", "config": "*", "dotenv": "^16.4.5", "eslint": "^8.42.0", "lint-staged": "^14.0.1", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "ts-jest-mock-import-meta": "^1.1.0", "typescript": "^5.6.3", "vite": "^5.2.6", "vite-plugin-index-html": "^2.0.2", "vite-plugin-mkcert": "^1.17.8"}}