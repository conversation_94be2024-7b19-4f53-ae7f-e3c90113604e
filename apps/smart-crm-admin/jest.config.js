/* eslint-disable @typescript-eslint/no-var-requires */
const config = require('../../jest.config');
/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
  ...config,
  setupFilesAfterEnv: ['./jest-setup.ts'],
  moduleNameMapper: {
    '^@src/(.*)$': '<rootDir>/src/$1',
  },

  transform: {
    '^.+\\.tsx?$': [
      'ts-jest',
      {
        // 下面这坨配置是为了解决ts-jest在处理import.meta时报错的问题
        // 具体查看：https://www.npmjs.com/package/ts-jest-mock-import-meta
        diagnostics: {
          ignoreCodes: [1343],
        },
        astTransformers: {
          before: [
            {
              path: 'ts-jest-mock-import-meta', // or, alternatively, 'ts-jest-mock-import-meta' directly, without node_modules.
              options: {
                metaObjectReplacement: {
                  url: 'https://www.url.com',
                  env: {
                    PROD: false,
                    DEV: false,
                  },
                },
              },
            },
          ],
        },
      },
    ],
  },
};
