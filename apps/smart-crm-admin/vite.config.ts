import { resolve } from 'path';
import { sentryVitePlugin } from '@sentry/vite-plugin';
import react from '@vitejs/plugin-react';
import dotenv from 'dotenv';
import { defineConfig, loadEnv } from 'vite';
import htmlPlugin from 'vite-plugin-index-html';
import mkcert from 'vite-plugin-mkcert';

dotenv.config();

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');

  return {
    base: env.VITE_API_URL,
    build: {
      sourcemap: true,
    },
    plugins: [
      react(),
      mkcert(),
      htmlPlugin({
        input: resolve(__dirname, '/src/main.tsx'),
        preserveEntrySignatures: 'exports-only',
      }),
      sentryVitePlugin({
        disable: env.mode !== 'prod',
        org: 'tastien',
        project: 'scrm-pc',
        url: 'https://sentry2.tastientech.com/',
        authToken: '***********************************************************************',
        sourcemaps: {
          filesToDeleteAfterUpload: ['**/*.js.map'],
        },
      }),
    ],
    resolve: {
      alias: {
        '@src': '/src',
      },
    },
    server: {
      host: 'dev.tastientech.com',
      port: 5174,
      open: '/',
      proxy: {
        '/api': {
          target: env.VITE_API_URL,
          changeOrigin: true,
        },
        // 站内通知
        '/sse-event': {
          target: env.VITE_API_URL,
          changeOrigin: true,
        },
        // 中台
        '/zt-api': {
          target: env.VITE_GAIA_API_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/zt-api/, '/api'),
        },
      },
    },
  };
});
