import { useEffect } from 'react';
import { getDistrictList } from '@src/services/common';
import { DistrictDTO } from '@src/services/common/type';
import useDistrictsStore from '@src/store/useDistrictsStore';
import { useRequest } from 'ahooks';
import { openDB } from 'idb';

interface UseDistrictsProps {
  streets?: boolean;
  ready?: boolean;
}

const supportIndexedDB = 'indexedDB' in window;

let threeLevelDBOpened = false;
let fourLevelDBOpened = false;

const DB_VERSION = 1;

const getName = (streets?: boolean) => {
  const name = streets ? 'four' : 'three';

  return {
    databaseName: `${name}-level-districts-database`,
    storeName: `${name}-level-districts-store`,
  };
};

const useDistricts = (props?: UseDistrictsProps) => {
  const { streets, ready = true } = props || {};

  const {
    threeLevelDistricts,
    fourLevelDistricts,
    threeLevelLoading,
    fourLevelLoading,
    setThreeLevelLoading,
    setFourLevelLoading,
    setThreeLevelDistricts,
    setFourLevelDistricts,
  } = useDistrictsStore();

  useEffect(() => {
    (async () => {
      if (
        // 不支持
        !supportIndexedDB ||
        // 不获取
        !ready ||
        // 已经有值
        (streets ? fourLevelDistricts.length > 0 : threeLevelDistricts.length > 0) ||
        // 已经执行 openDB
        streets
          ? fourLevelDBOpened
          : threeLevelDBOpened
      ) {
        return;
      }

      if (streets) {
        fourLevelDBOpened = true;
      } else {
        threeLevelDBOpened = true;
      }

      const setLoading = streets ? setFourLevelLoading : setThreeLevelLoading;
      const setDistricts = streets ? setFourLevelDistricts : setThreeLevelDistricts;
      const { databaseName, storeName } = getName(streets);

      try {
        const db = await openDB(databaseName, DB_VERSION, {
          upgrade(_db) {
            if (!_db.objectStoreNames.contains(storeName)) {
              _db.createObjectStore(storeName, { keyPath: 'id' });
            }
          },
        });
        const data: DistrictDTO[] = await db.getAll(storeName);

        if (!data.length) {
          const res = await getDistrictList({ streets });

          setDistricts(res);

          const tx = db.transaction(storeName, 'readwrite');
          const store = tx.objectStore(storeName);

          res.forEach((item) => {
            store.put(item);
          });
        } else {
          setDistricts(data);
        }
      } catch (error) {}

      setLoading(false);
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [streets, ready]);

  const action = useRequest(() => getDistrictList({ streets }), {
    cacheKey: `districts${streets ? 'Street' : ''}`,
    cacheTime: -1,
    staleTime: -1,
    ready: !supportIndexedDB && ready,
  });

  if (supportIndexedDB) {
    return {
      loading: streets ? fourLevelLoading : threeLevelLoading,
      data: streets ? fourLevelDistricts : threeLevelDistricts,
    };
  }

  return {
    loading: action.loading,
    data: action.data,
  };
};

export default useDistricts;
