import { getBranchCompanyList } from '@src/services/common';
import { useRequest } from 'ahooks';
import { Options } from 'ahooks/lib/useRequest/src/types';

const useBranchCompanyList = (
  options?: Options<Awaited<ReturnType<typeof getBranchCompanyList>>, any>,
) =>
  useRequest(getBranchCompanyList, {
    cacheKey: 'branch-company-list',
    staleTime: -1,
    cacheTime: -1,
    ...options,
  });

export default useBranchCompanyList;
