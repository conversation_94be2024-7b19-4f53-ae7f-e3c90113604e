import { useState } from 'react';
import { parserJ<PERSON><PERSON> } from '@pkg/utils';
import { ETableViewItemType, ETableViewsType } from '@src/components/ETable';
import { createView, deleteView, getViewList, modifyView } from '@src/services/common';
import { BusinessTypeEnum } from '@src/services/common/type';
import { useRequest } from 'ahooks';
import { App } from 'antd';

const useTableViews = (businessType: BusinessTypeEnum) => {
  const { message } = App.useApp();
  const [activeKey, setActiveKey] = useState<number | string>('default');

  const {
    data: items = [],
    loading,
    mutate,
  } = useRequest(() =>
    getViewList(businessType).then(
      (res) =>
        [
          { id: 'default', name: '默认视图' },
          ...res.map((i) => ({
            id: i.id,
            name: i.viewName,
            filters: parserJSON(i.queryCondition),
            settings: parserJSON(i.viewFields),
          })),
        ] as ETableViewItemType[],
    ),
  );

  const views: ETableViewsType = {
    items,
    activeKey,
    onChange: setActiveKey,
    onCreate: async (name, filters, settings) => {
      const id = await createView({
        viewName: name,
        code: businessType,
        queryCondition: JSON.stringify(filters),
        viewFields: JSON.stringify(settings),
      });

      mutate(items.concat({ id, name, filters, settings }));
      setActiveKey(id);
      message.success('新建视图成功');
    },
    onDelete: async (id) => {
      await deleteView(id as number);
      mutate(items.filter((i) => i.id !== id));
      setActiveKey('default');
      message.success('删除视图成功');
    },
    onRename: async (id, name) => {
      await modifyView({ id: id as number, viewName: name });

      const result = [...items];
      const index = items.findIndex((i) => i.id === id);

      result[index].name = name;
      mutate(result);
      message.success('重命名视图成功');
    },
    onUpdate: async (id, filters, settings) => {
      await modifyView({
        id: id as number,
        queryCondition: JSON.stringify(filters),
        viewFields: JSON.stringify(settings),
      });

      const result = [...items];
      const index = items.findIndex((i) => i.id === id);

      result[index].filters = filters;
      result[index].settings = settings;
      mutate(result);
      message.success('更新视图成功');
    },
  };

  return {
    loading,
    views,
  };
};

export default useTableViews;
