import { parserJ<PERSON><PERSON> } from '@pkg/utils';
import { getOssFileUrl, getOssToken } from '@src/services/common';
import { OssTokenProps } from '@src/services/common/type';
import useUserStore from '@src/store/useUserStore';
import OSS from 'ali-oss';
import dayjs from 'dayjs';
import { nanoid } from 'nanoid';

const useUploadFile = () => {
  const {
    user: { shUserId },
  } = useUserStore();

  const getUploadToken = async () => {
    let token: OssTokenProps;

    const tokenStr = localStorage.getItem('ALI_OSS_TOKEN');
    const tokenObj = tokenStr ? parserJSON(tokenStr) : null;

    if (tokenObj && tokenObj.expiration?.epochSecond >= dayjs().unix()) {
      token = tokenObj;
    } else {
      token = await getOssToken(shUserId);
      localStorage.setItem('ALI_OSS_TOKEN', JSON.stringify(token));
    }

    return token;
  };

  const uploadFile = async (file: File, onProgress?: (percent: number) => void) => {
    const token = await getUploadToken();
    const client = new OSS({
      // yourRegion填写Bucket所在地域。以华东1（杭州）为例，yourRegion填写为oss-cn-hangzhou。
      region: 'oss-cn-shanghai',
      accessKeyId: token.accessKeyId,
      accessKeySecret: token.accessKeySecret,
      stsToken: token.securityToken,
      // 填写Bucket名称。
      bucket: token.bucket,
      // 刷新临时访问凭证。
      secure: true,
      // timeout: 120000,
      refreshSTSToken: async () => {
        const newToken = await getOssToken(shUserId);

        return {
          accessKeyId: newToken.accessKeyId,
          accessKeySecret: newToken.accessKeySecret,
          stsToken: newToken.securityToken,
        };
      },
    });
    const uid = nanoid();
    const name = `${token.prefix}${uid}`;
    const fileName = encodeURIComponent(file.name || '');

    const response = await client.multipartUpload(name, file, {
      progress: (percent: number) => {
        // 0 到 1 之间
        onProgress?.(percent * 100);
      },
      headers: {
        'Content-Disposition': `attachment;filename=${fileName}`,
      },
    });

    const res = await getOssFileUrl(response.name);
    const params = {
      key: response.name,
      url: res.url,
    };

    return params;
  };

  return { getUploadToken, uploadFile };
};

export default useUploadFile;
