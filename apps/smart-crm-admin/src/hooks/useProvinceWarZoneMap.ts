import { useMemo } from 'react';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { getWarZoneList } from '@src/services/common';
import { useRequest } from 'ahooks';

interface useProvinceWarZoneMapProps {
  ready?: boolean;
}

/** 省 code -》大区（映射） */
const useProvinceWarZoneMap = (props?: useProvinceWarZoneMapProps) => {
  const { data } = useRequest(getWarZoneList, {
    cacheKey: 'provinceWarZoneMapData',
    staleTime: -1,
    cacheTime: -1,
    ...props,
  });

  const provinceWarZoneMap = useMemo(
    () =>
      (data || []).reduce<Record<string, BelongWarZoneEnum>>((total, cur) => {
        total[cur.provinceCode] = cur.belongWarZone;

        return total;
      }, {}),
    [data],
  );

  return provinceWarZoneMap;
};

export default useProvinceWarZoneMap;
