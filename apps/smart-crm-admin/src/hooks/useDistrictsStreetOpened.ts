import { useCallback } from 'react';
import { DistrictDTO } from '@src/services/common/type';
import { getStreetOpenedRegionIds } from '@src/services/regional/quota';
import { useRequest } from 'ahooks';

interface UseDistrictsStreetOpenedProps {
  ready?: boolean;
}

const useDistrictsStreetOpened = (props?: UseDistrictsStreetOpenedProps) => {
  const { ready } = props || {};

  const { data: streetOpenedRegionIds, loading: getStreetOpenedRegionIdsLoading } = useRequest(
    getStreetOpenedRegionIds,
    { ready },
  );

  // 如果区县开启了乡镇显示名额，才能选到 4 级
  const transformDistricts = useCallback(
    (districts: DistrictDTO[]) =>
      districts.filter((i) => {
        if (i.deep === 3) {
          return streetOpenedRegionIds?.includes(i.parentId);
        }

        return true;
      }),
    [streetOpenedRegionIds],
  );

  return {
    getStreetOpenedRegionIdsLoading,
    transformDistricts,
  };
};

export default useDistrictsStreetOpened;
