import { useMemo } from 'react';
import { ProColumns } from '@ant-design/pro-components';
import { yesOrNoOptions } from '@src/common/constants';
import { UserSelect } from '@src/components';
import EditableAttachment from '@src/components/Editable/EditableAttachment';
import EditableIdentityCard from '@src/components/Editable/EditableIdentityCard';
import EditablePhone from '@src/components/Editable/EditablePhone';
import EditableRegion from '@src/components/Editable/EditableRegion';
import { GenderEnum } from '@src/services/clues/my/type';
import { StoreCategoryEnum } from '@src/services/contract/formal/type';
import {
  CustomerDTO,
  CustomerScoreLevelEnum,
  HighestEducationLevelEnum,
} from '@src/services/customers/type';
import { enum2ValueEnum, optionsToValueEnum } from '@src/utils';
import { Tag, Typography } from 'antd';
import useAllUsers from './useAllUsers';
import useBranchCompanyList from './useBranchCompanyList';

interface useCustomerColumnsProps {
  ready?: boolean;
  onCustomerNameClick?: (record: CustomerDTO) => void;
}

const useCustomerColumns = (props?: useCustomerColumnsProps) => {
  const { ready, onCustomerNameClick } = props || {};
  const { data: users } = useAllUsers({ ready });
  const { data: branchCompanyList } = useBranchCompanyList({ ready });

  const branchCompanyValueEnum = useMemo(
    () =>
      branchCompanyList?.reduce<Record<string, string>>((total, cur) => {
        total[cur.branchCompanyCode] = cur.branchCompanyName;

        return total;
      }, {}),
    [branchCompanyList],
  );

  const columns: ProColumns<CustomerDTO>[] = [
    {
      title: '姓名',
      dataIndex: 'name',
      fixed: 'left',
      renderText: (value, record) => (
        <div className="flex justify-between gap-2">
          <Typography.Text
            ellipsis={{ tooltip: true }}
            style={{ color: 'var(--ant-color-link)', cursor: 'pointer' }}
            onClick={() => onCustomerNameClick?.(record)}
          >
            {value}
          </Typography.Text>
          {record.realAuthFlag ? (
            <Tag color="success">已实名</Tag>
          ) : (
            <Tag color="error">未实名</Tag>
          )}
        </div>
      ),
    },
    {
      title: '客户编号',
      dataIndex: 'code',
    },
    {
      title: '手机号',
      dataIndex: 'phones',
      search: {
        transform: (phone) => ({
          phone,
        }),
      },
      render: (_, { phones, phonesSensitive }) =>
        phones?.map((phone, index) => (
          <EditablePhone
            key={index}
            value={phone}
            fieldProps={{ sensitiveValue: phonesSensitive?.[index] }}
          />
        )),
    },
    {
      title: '性别',
      dataIndex: 'gender',
      valueEnum: enum2ValueEnum(GenderEnum),
      search: false,
    },
    {
      title: '生日',
      dataIndex: 'birthday',
      search: false,
    },
    {
      title: '身份证号',
      dataIndex: 'identityCard',
      width: 200,
      render: (_, { identityCard, identityCardSensitive }) => (
        <EditableIdentityCard
          value={identityCard}
          fieldProps={{ sensitiveValue: identityCardSensitive }}
        />
      ),
    },
    {
      title: '籍贯',
      dataIndex: 'nativePlace',
      search: false,
      ellipsis: true,
      renderText: (value) => <EditableRegion value={value} />,
    },
    {
      title: '身份证住址',
      dataIndex: 'address',
      search: false,
      ellipsis: true,
    },
    {
      title: '客户评级',
      dataIndex: 'customerScoreLevel',
      valueEnum: enum2ValueEnum(CustomerScoreLevelEnum),
      fieldProps: {
        mode: 'multiple',
      },
      search: {
        transform: (customerScoreLevels) => ({
          customerScoreLevels,
        }),
      },
    },
    {
      title: '客户评分',
      dataIndex: 'customerScore',
      search: false,
    },
    {
      title: '客户性质',
      dataIndex: 'customerNature',
      search: false,
      valueEnum: enum2ValueEnum(StoreCategoryEnum),
    },
    {
      title: '所属区域',
      dataIndex: 'belongArea',
      search: false,
      valueEnum: branchCompanyValueEnum,
    },
    {
      title: '最高学历',
      dataIndex: 'highestEducationLevel',
      search: false,
      valueEnum: enum2ValueEnum(HighestEducationLevelEnum),
    },
    {
      title: '学历证明',
      dataIndex: 'educationLevelProves',
      search: false,
      onCell: () => ({
        className: '!py-0',
      }),
      renderText: (value) => <EditableAttachment value={value} />,
    },
    {
      title: '面审爽约',
      dataIndex: 'interviewNoArrive',
      search: false,
      valueEnum: optionsToValueEnum(yesOrNoOptions, true),
    },
    {
      title: '是否红线',
      dataIndex: 'redLine',
      search: false,
      valueEnum: optionsToValueEnum(yesOrNoOptions, true),
    },
    {
      title: '是否黑名单',
      dataIndex: 'blacklist',
      search: false,
      valueEnum: optionsToValueEnum(yesOrNoOptions, true),
    },
    {
      title: '是否员工',
      dataIndex: 'staff',
      valueEnum: optionsToValueEnum(yesOrNoOptions, true),
    },
    {
      title: '更新人',
      dataIndex: 'updateUserId',
      search: false,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      renderFormItem: () => <UserSelect />,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: (_, type) => (type === 'table' ? 'dateTime' : 'dateTimeRange'),
      search: {
        transform: (value) => ({
          beginCreateTime: value[0],
          endCreateTime: value[1],
        }),
      },
    },
  ];

  return columns;
};

export default useCustomerColumns;
