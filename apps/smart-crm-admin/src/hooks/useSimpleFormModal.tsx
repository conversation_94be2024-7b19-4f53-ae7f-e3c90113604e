import React, { useState } from 'react';
import { Form, FormItemProps, FormProps, Modal, ModalProps } from 'antd';

interface UseSimpleFormProps {
  formProps?: FormProps;
  formItems?: (FormItemProps | React.ReactNode)[];
  modalProps?: ModalProps;
}

const useSimpleForm = (params?: UseSimpleFormProps) => {
  const { formProps, modalProps, formItems } = params || {};

  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();

  const modalNode = (
    <Modal
      open={open}
      destroyOnHidden
      modalRender={(node) => (
        <Form form={form} clearOnDestroy {...formProps}>
          {node}
        </Form>
      )}
      onCancel={() => setOpen(false)}
      onOk={form.submit}
      {...modalProps}
    >
      {formItems?.map((item, index) => {
        if (typeof item === 'object' && item !== null && !(item as any).$$typeof) {
          const typeItem = item as FormItemProps;

          let key: number | string = index;

          if (typeItem.name) {
            key = typeItem.name.toString();
          }

          return <Form.Item {...typeItem} key={key} />;
        }

        return <React.Fragment key={index}>{item as React.ReactNode}</React.Fragment>;
      })}
    </Modal>
  );

  return {
    setOpen,
    modalNode,
  };
};

export default useSimpleForm;
