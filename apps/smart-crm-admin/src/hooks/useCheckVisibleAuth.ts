import { getUserVisibleItem } from '@src/services/users/auth';
import { VisibleRangeEnum } from '@src/services/users/auth/type';
import useUserStore from '@src/store/useUserStore';
import { useRequest } from 'ahooks';

interface useCheckVisibleAuthProps {
  ready?: boolean;
}

// 用来判断传入的用户是否在权限范围覆盖
const useCheckVisibleAuth = (props?: useCheckVisibleAuthProps) => {
  const { ready } = props || {};

  const {
    user: { shUserId },
  } = useUserStore();
  const { data } = useRequest(() => getUserVisibleItem(shUserId), { ready });

  const checkAuthRange = (userId?: number) => {
    function getVisibleAuth() {
      if (!userId) {
        return false;
      }

      switch (data?.visibleRange) {
        case VisibleRangeEnum.ONLY_ME: {
          return shUserId === userId;
        }

        case VisibleRangeEnum.ALL: {
          return true;
        }

        case VisibleRangeEnum.APPOINT: {
          return [...data.visibleUsers.map((i) => i.id), shUserId].includes(userId);
        }

        default:
          return false;
      }
    }

    // 是负责人或在可见范围
    return shUserId === userId || getVisibleAuth();
  };

  return checkAuthRange;
};

export default useCheckVisibleAuth;
