import { isInIcestark } from '@ice/stark-app';
import { init as sentryInit } from '@sentry/react';
import ReactDOM from 'react-dom/client';
import App from './App.tsx';
import './index.css';

if (import.meta.env.MODE === 'prod' && import.meta.env.PROD) {
  const ignoreError = ['ServerError', 'AxiosError', 'UnauthorizedError'];

  sentryInit({
    dsn: 'https://<EMAIL>/20',
    // 性能跟踪的采样率
    tracesSampleRate: 0.1,
    beforeSend(event, hint) {
      // return null 不上报，return event上报
      try {
        const message = JSON.stringify(hint?.originalException) || '';

        if (ignoreError.some((i) => message.includes(i))) {
          return null;
        }

        return event;
      } catch (e) {
        return event;
      }
    },
  });
}

const appRootElement = document.getElementById('root')!;

let root: ReactDOM.Root | undefined;

export function mount(props: {
  customerProps: Record<string, any>;
  container: ReactDOM.Container;
}) {
  root = ReactDOM.createRoot(props.container);
  root.render(<App />);
}

export function unmount() {
  root?.unmount();
}

if (!isInIcestark()) {
  ReactDOM.createRoot(appRootElement).render(<App />);
}
