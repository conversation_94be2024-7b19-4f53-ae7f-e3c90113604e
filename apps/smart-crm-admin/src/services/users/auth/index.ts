import { request } from '@src/common/http';
import { EditUserVisibleItemPayload, UserVisibleListItem } from './type';

export const getUserVisibleList = (params: {
  pageNum?: number;
  pageSize?: number;
  status?: 'NORMAL' | 'DISABLED';
  userName?: string;
}) =>
  request.get<{ total: number; result: UserVisibleListItem[] }>('/api/admin/user/visible/list', {
    params,
  });

export const getUserVisibleItem = (userId: number) =>
  request.get<UserVisibleListItem>(`/api/admin/user/visible/${userId}/detail`);

export const editUserVisibleItem = (data: EditUserVisibleItemPayload) =>
  request.put('/api/admin/user/visible', { data });

export const syncUsers = () => request.post('/api/admin/user/sync/principal');
