export enum VisibleRangeEnum {
  /** 仅本人 */
  ONLY_ME = 'ONLY_ME',
  /** 所有线索 */
  ALL = 'ALL',
  /** 指定成员线索 */
  APPOINT = 'APPOINT',
}

export type UserVisibleListItem = {
  userId: number;
  userName: string;
  visibleRange: VisibleRangeEnum;
  visibleUsers: { id: number; name: string }[];
};

export type EditUserVisibleItemPayload = {
  userId: number;
  visibleRange: VisibleRangeEnum;
  addAppointUserIds?: number[];
  removeAppointUserIds?: number[];
};
