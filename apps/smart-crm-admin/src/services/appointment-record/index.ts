import { request } from '@src/common/http';
import {
  AppointmentRecordDTO,
  CreateAppointmentRecordReq,
  ExportAppointmentRecordsReq,
  GetAppointmentRecordsReq,
  UpdateAppointmentRecordReq,
  VisitResultsEnum,
} from './type';
import {
  BusinessOpportunityDTO,
  BusinessOpportunityTypeEnum,
  ProgressNodeEnum,
} from '../business-opportunity/type';

export const getAppointmentRecords = (params: GetAppointmentRecordsReq) =>
  request.get<{
    total: number;
    result: AppointmentRecordDTO[];
  }>('/api/admin/appointment/record/page', {
    params,
  });

export const exportAppointmentRecords = (data: ExportAppointmentRecordsReq) =>
  request.post('/api/admin/appointment/record/export', { data });

export const confirmVisit = (data: {
  id: number;
  visitResults: VisitResultsEnum;
  infoUnanimous?: boolean;
}) => request.put('/api/admin/appointment/record/confirm', { data });

export const batchConfirmVisit = (data: {
  ids: number[];
  visitResults: VisitResultsEnum;
  infoUnanimous?: boolean;
}) => request.put('/api/admin/appointment/record/confirm/batch', { data });

export const getAppointmentRecordDetail = (id: number) =>
  request.get<AppointmentRecordDTO>(`/api/admin/appointment/record/detail/${id}`);

export const getAppointmentRecordSchedule = (params: {
  businessOpportunityType: BusinessOpportunityTypeEnum;
}) =>
  request.get<{ day: string; timeFrames: string[] }[]>('/api/admin/appointment/record/schedule', {
    params,
  });

export const createAppointmentRecord = (data: CreateAppointmentRecordReq) =>
  request.post('/api/admin/appointment/record', { data });

export const updateAppointmentRecord = (data: UpdateAppointmentRecordReq) =>
  request.put('/api/admin/appointment/record', { data });

export const getBusinessOpportunityByCustomerId = ({
  id,
  ...params
}: {
  id: number;
  node: ProgressNodeEnum;
}) =>
  request.get<BusinessOpportunityDTO[]>(`/api/admin/business/opportunity/simple/customer/${id}`, {
    params,
  });

export const getAppointmentRecordByCustomerId = (id: number) =>
  request.get<AppointmentRecordDTO[]>(`/api/admin/appointment/record/biz/${id}`);

export const importAppointmentRecordExcel = (data: { file: FormData }) =>
  request.post<{ failDownloadUrl: string; failNum: number; successNum: number }>(
    '/api/admin/appointment/record/import/excel',
    {
      data,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );

export const downloadAppointmentRecordExcelTemplate = () =>
  request.get<{ url: string }>('/api/admin/appointment/record/download/template');
