import { BelongWarZoneEnum, BusinessOpportunityTypeEnum } from '../business-opportunity/type';
import { AuditStatusEnum, FileDTO } from '../common/type';
import { EvaluationRejectReasonEnum, SuggestionEnum } from '../visit/type';

export type AppointmentRecordDTO = {
  id: number;
  code: string;
  customerName: string;
  customerId: number;
  bizOpportunityId: number;
  bizOpportunityName: string;
  bizOpportunityType: BusinessOpportunityTypeEnum;
  bizOpportunityCode: string;
  scheduleTime: string;
  visitPurpose: VisitPurposeEnum;
  visitTime: string;
  infoUnanimous: boolean;
  onlineReviewStatus: boolean;
  suggestion: SuggestionEnum;
  auditStatus: AuditStatusEnum;
  rejectReasons: EvaluationRejectReasonEnum[];
  bizDirectUserName: string;
  interviewerName: string;
  interviewer: number;
  intentionRegionCode: string;
  intentionProvinceCode: string;
  intentionPositions?: {
    latitude: number;
    longitude: number;
    name: string;
  }[];
  belongWarZone: BelongWarZoneEnum;
  visitResults: VisitResultsEnum;
  visitRecordCode: string;
  visitRecordId: number;
  description: string;
  createUserId: number;
  createTime: string;
  updateUserId: number;
  updateTime: string;
  openStoreScheduleAttachments?: FileDTO[];
  operationsCustomerId: number;
  operationsCustomerName: string;
  partnerCustomerId: number;
  partnerCustomerName: string;
  interviewNoticeCode: string;
};

export type GetAppointmentRecordsReq = {
  pageNum?: number;
  pageSize?: number;
  customerName?: string;
  bizOpportunityName?: string;
  bizDirectUserId?: number;
  beginDate?: string;
  endDate?: string;
  interviewer?: number;
  visitResults?: VisitResultsEnum;
};

export enum VisitResultsEnum {
  已到访 = 'VISITED',
  面审爽约 = 'NOT_VISIT',
  面审改期 = 'CHANGE_INTERVIEW_DATE',
}

export type ExportAppointmentRecordsReq = Omit<GetAppointmentRecordsReq, 'pageNum' | 'pageSize'> & {
  ids?: number[];
};

export enum VisitPurposeEnum {
  加盟面审 = 'JOIN_INTERVIEW',
  变更法人 = 'CHANGE_LAW_PERSON',
  其他事宜 = 'OTHER_AFFAIRS',
  变更店长 = 'CHANGE_SHOP_MANAGER',
}

export type CreateAppointmentRecordReq = {
  customerId: number;
  visitPurpose: VisitPurposeEnum;
  bizOpportunityId?: number;
  appointmentDay: string;
  appointmentTime: string;
  intentionRegionCode: string;
  intentionProvinceCode: string;
  belongWarZone: BelongWarZoneEnum;
  description?: string;
  onlineReviewStatus: boolean;
  openStoreScheduleAttachments?: FileDTO[];
  intentionPositions?: {
    latitude: number;
    longitude: number;
    name: string;
  }[];
};

export type UpdateAppointmentRecordReq = {
  id: number;
  intentionRegionCode: string;
  onlineReviewStatus: boolean;
  description?: string;
  openStoreScheduleAttachments?: FileDTO[];
  intentionPositions?: {
    latitude: number;
    longitude: number;
    name: string;
  }[];
};
