import { TemplateQuestionTypeEnum } from '@src/pages/standard-settings/template/components/QuestionConfig/type';
import { AuditStatusEnum, FieldConfigType } from '@src/services/common/type';
import { VisitPurposeEnum } from '../appointment-record/type';
import { BelongWarZoneEnum, BusinessOpportunityTypeEnum } from '../business-opportunity/type';

export type GetVisitRecordListReq = {
  pageNum?: number;
  pageSize?: number;
  code?: string;
  customerName?: string;
  businessOpportunityName?: string;
  beginVisitTime?: string;
  endVisitTime?: string;
  interviewer?: number;
  visitPurpose?: VisitPurposeEnum;
};

export type VisitRecordDTO = {
  id: number;
  code: string;
  customerName: string;
  customerId: number;
  businessOpportunityName: string;
  businessOpportunityId: number;
  businessOpportunityType: BusinessOpportunityTypeEnum;
  businessOpportunityCode: string;
  appointmentCode: string;
  appointmentId: number;
  visitTime: string;
  interviewer: number;
  visitPurpose: VisitPurposeEnum;
  infoUnanimous: boolean;
  intentionProvinceCode: string;
  intentionRegion: string;
  belongWarZone: BelongWarZoneEnum;
  interviewEvaluationRecordCode: string;
  interviewEvaluationRecordId: number;
  auditStatus: AuditStatusEnum;
  rejectReasons: string;
  updateTime: string;
  createTime: string;
  createUserId: number;
  suggestion: SuggestionEnum;
  businessOpportunityDirectUserId: number;
  applyUserIsPass: boolean;
  storeManagerIsPass: boolean;
  comments: string;
};

export enum SuggestionEnum {
  个人经营 = 'PERSONAL_BUSINESS',
  资源合作 = 'RESOURCE_COOPERATION',
  通过 = 'PASS',
  未通过 = 'NOT_PASS',
}

export enum EvaluationRejectReasonEnum {
  食安服务意识 = 'FOOD_SAFETY_SERVICE_AWARENESS',
  沟通配合程度 = 'COMMUNICATION_AND_COOPERATION_DEGREE',
  门店运营经验 = 'STORE_OPERATION_EXPERIENCE',
  风险投资意识 = 'RISK_INVESTMENT_CONSCIOUSNESS',
  面审到访爽约 = 'INTERVIEW_NO_SHOW',
  二次参加面审 = 'SECOND_INTERVIEW_ENTRANCE_EXAM',
  触碰公司红线 = 'TOUCHING_COMPANY_RED_LINES',
  自行放弃加盟 = 'JOINING_VOLUNTARILY_GIVING_UP',
}

export type ExportVisitRecordReq = Omit<GetVisitRecordListReq, 'pageSize' | 'pageNum'> & {
  ids?: number[];
};

export type CreateEvaluationAnswersReq = {
  suggestion: string;
  comments: string;
  rejectReasons: string[];
  interviewVisitId: number;
  examPaperId: number;
  applyUserIsPass?: boolean;
  storeManagerIsPass?: boolean;
  questionAnswers: { questionKey: string; answer: any }[];
};

export enum QuestionRuleEnum {
  IN = 'IN',
  NOT_NULL = 'NOT_NULL',
}

export type QuestionDTO = {
  questionId: number;
  questionKey: string;
  questionName: string;
  questionType: TemplateQuestionTypeEnum;
  notNull: boolean;
  description?: string;
  ruleInfo?: string;
} & Omit<FieldConfigType, 'options'> & {
    options?: { label: string; value: string; deleted: boolean }[];
  };

export type GetEvaluationQuestionsRes = {
  id: number;
  name: string;
  pageList: {
    pageId: number;
    pageName: string;
    questionList: QuestionDTO[];
  }[];
};
