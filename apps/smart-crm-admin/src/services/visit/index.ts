import { request } from '@src/common/http';
import { BusinessTypeEnum } from '@src/services/common/type';
import { ExamStatusEnum } from '@src/services/standard-settings/template/type';
import {
  CreateEvaluationAnswersReq,
  ExportVisitRecordReq,
  GetEvaluationQuestionsRes,
  GetVisitRecordListReq,
  VisitRecordDTO,
} from './type';

export const getVisitRecordList = (params: GetVisitRecordListReq) =>
  request.get<{ result: VisitRecordDTO[]; total: number }>(
    '/api/admin/interview/visit/record/page',
    {
      params,
    },
  );

export const exportVisitRecord = (data: ExportVisitRecordReq) =>
  request.post('/api/admin/interview/visit/record/export', { data });

export const changeVisitInterviewer = (data: { id: number; interviewer: number }) =>
  request.put('/api/admin/interview/visit/record/change/interviewer', { data });

export const createEvaluationAnswers = (data: CreateEvaluationAnswersReq) =>
  request.post('/api/admin/interview/visit/record/interview/evaluation', { data });

export const getExamAllList = (params: {
  businessType: BusinessTypeEnum.ONLINE_APPLY | BusinessTypeEnum.INTERVIEW_EVALUATION;
  status?: ExamStatusEnum;
}) => request.get<{ id: number; name: string }[]>('/api/admin/exam-paper/list', { params });

export const getEvaluationQuestions = (examPaperId: number) =>
  request.get<GetEvaluationQuestionsRes>(`/api/admin/exam-paper/${examPaperId}`);

export const noticeVisit = (data: { id: number; receiverUserId: number }) =>
  request.post('/api/admin/interview/visit/record/notice', { data });

export const changeInterviewer = (data: { id: number; interviewer: number }) =>
  request.put('/api/admin/interview/visit/record/change/interviewer', { data });

export const batchChangeInterviewer = (data: { ids: number[]; interviewer: number }) =>
  request.put('/api/admin/interview/visit/record/change/interviewer/batch', { data });

export const getVisitDetail = (id: number) =>
  request.get<VisitRecordDTO>(`/api/admin/interview/visit/record/detail/${id}`);

export const getVisitListByBusinessId = (id: number) =>
  request.get<VisitRecordDTO[]>(
    `/api/admin/interview/visit/record/listByBusinessOpportunityId/${id}`,
  );
