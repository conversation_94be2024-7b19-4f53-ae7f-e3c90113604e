import { request } from '@src/common/http';
import { AuditHistoryType } from '../common/type';

export const getInterviewVisibleList = (params: {
  pageNum?: number;
  pageSize?: number;
  name?: string;
}) =>
  request.get<{
    total: number;
    result: { name: string; baseBusinessId: number; period: string }[];
  }>('/api/admin/interview/visit/ext/page', { params });

export const importEvaluationExcel = (data: { file: FormData; examPaperId: number }) =>
  request.post('/api/admin/exam-interview-evaluation-record/import/excel', {
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

export const downloadEvaluationExcelTemplate = (params: { examPaperId: number }) =>
  request.get('/api/admin/exam-interview-evaluation-record/download/template', { params });

export const getEvaluationAuditHistory = (id: number) =>
  request.get<AuditHistoryType[]>(
    `/api/admin/exam-interview-evaluation-record/audit/history/${id}`,
  );

export const getEvaluationAuditAllHistory = (id: number) =>
  request.get<{ auditHistories: AuditHistoryType[]; name: string; processInstanceId: number }[]>(
    `/api/admin/exam-interview-evaluation-record/all/audit/history/${id}`,
  );

export const approveEvaluationTask = (data: { recordId: number; comment?: string }) =>
  request.post('/api/admin/exam-interview-evaluation-record/approve-task', { data });

export const rejectEvaluationTask = (data: { recordId: number; comment?: string }) =>
  request.post('/api/admin/exam-interview-evaluation-record/reject-task', { data });

// 重新发起审批
export const reApproval = (recordId: number) =>
  request.post('/api/admin/exam-interview-evaluation-record/re-create-process-instance', {
    data: { recordId },
  });
