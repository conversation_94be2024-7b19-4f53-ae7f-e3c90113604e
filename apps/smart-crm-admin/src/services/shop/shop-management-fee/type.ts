export type QueryShopManagementFeePageListReq = {
  pageNum?: number;
  pageSize?: number;
  shopNo?: string;
};

export enum RateTypeEnum {
  无统管 = 'TIER_1',
  '3%' = 'TIER_2',
  '3~6%' = 'TIER_3',
}

export enum ShopManagementFeeChangeTypeEnum {
  新店 = 'NEW',
  新店补充 = 'NEW_ADDITION',
  续约 = 'RENEW',
  搬迁 = 'RELOCATE',
  业主内部变更 = 'OWNER_INNER_CHANGE',
  业主买卖变更 = 'OWNER_TRADE_CHANGE',
}

export type ShopManagementFeeDTO = {
  id: number;
  shopNo: string;
  version: number;
  effectiveDate: string;
  startDate: string;
  endDate: string;
  rateType: RateTypeEnum;
};

export type CreateShopManagementFeeReq = {
  shopNo: string;
  effectiveDate?: string;
  startDate: string;
  endDate: string;
  rateType: RateTypeEnum;
  changeType: ShopManagementFeeChangeTypeEnum;
  originalShopNo?: string;
  description?: string;
};

export enum ShopManagementFeeVersionRecordStatusEnum {
  待生效 = 'PENDING',
  生效中 = 'ENABLED',
  失效 = 'DISABLED',
}

export type ShopManagementFeeVersionRecordDTO = {
  id: number;
  shopNo: string;
  code: string;
  changeType: ShopManagementFeeChangeTypeEnum;
  version: string;
  createTime: string;
  effectiveDate: string;
  invalidDate: string;
  startDate: string;
  endDate: string;
  status: ShopManagementFeeVersionRecordStatusEnum;
  rateType: RateTypeEnum;
};
