import { request } from '@src/common/http';
import {
  CreateShopManagementFeeReq,
  QueryShopManagementFeePageListReq,
  ShopManagementFeeDTO,
  ShopManagementFeeVersionRecordDTO,
} from './type';

export const queryShopManagementFeePageList = (data: QueryShopManagementFeePageListReq) =>
  request.post<{ result: ShopManagementFeeDTO[]; total: number }>(
    '/api/admin/shop/management/fee/page',
    { data },
  );

export const createShopManagementFee = (data: CreateShopManagementFeeReq) =>
  request.post('/api/admin/shop/management/fee/save', { data });

export const queryShopManagementFeeDetail = (shopNo: string) =>
  request.get<ShopManagementFeeVersionRecordDTO[]>(
    `/api/admin/shop/management/fee/${shopNo}/detail`,
  );

export const deleteShopManagementFeeVersion = (id: number) =>
  request.del(`/api/admin/shop/management/fee/${id}/delete`);

export const enableShopManagementFeeVersion = (id: number) =>
  request.post(`/api/admin/shop/management/fee/${id}/enable`);

export const disableShopManagementFeeVersion = (id: number) =>
  request.post(`/api/admin/shop/management/fee/${id}/disable`);
