import { request } from '@src/common/http';
import {
  ExportShopListReq,
  GetShopListReq,
  ShopCertDTO,
  ShopDTO,
  ShopListItemDTO,
  ShopTycInfoDTO,
  UpdateShopExtraReq,
} from './type';

export const getShopList = (data: GetShopListReq) =>
  request.post<{ result: ShopListItemDTO[]; total: number }>('/api/admin/shop/list', { data });

export const exportShopList = (data: ExportShopListReq) =>
  request.post('/api/admin/shop/export', { data });

export const getShopDetail = (shopId: string) =>
  request.post<ShopDTO>('/api/admin/shop/detail', { data: { shopId } });

export const updateShopExtra = (data: UpdateShopExtraReq) =>
  request.post('/api/admin/shop/update/shopExtra', { data });

export const getShopCertDetail = (shopId: string) =>
  request.get<ShopCertDTO | null>(`/api/admin/shop/certDetail/${shopId}`);

export const queryShopTycInfo = (shopId: string) =>
  request.get<ShopTycInfoDTO>(`/api/admin/shop/tyc-license-info/${shopId}`);
