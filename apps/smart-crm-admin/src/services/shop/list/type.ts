import { DevelopTagEnum } from '../../contract/formal/type';

export type GetShopListReq = {
  pageNum?: number;
  pageSize?: number;
  shopId?: string;
  shopName?: string;
  city?: string;
  shopStatus?: ShopStatusEnum;
  shopType?: ShopTypeEnum;
  siteLocationLabel?: DevelopTagEnum;
};

export enum ShopStatusEnum {
  /** 筹备中 */
  PREPARING = 1,
  /** 营业中 */
  OPEN,
  /** 歇业中 */
  OFFLINE,
  /** 已闭店 */
  CLOSE,
  /** 待营业 */
  OPEN_FOR_BUSINESS = 8,
}

export enum ShopTypeEnum {
  /** 加盟 T */
  REGULAR_CHAIN = 1,
  /** 加盟 M */
  FRANCHISE_CHAIN,
}

export type ShopListItemDTO = {
  shopId: string;
  shopName: string;
  province: string;
  city: string;
  district: string;
  shopStatus: ShopStatusEnum;
  shopType: ShopTypeEnum;
  address: string;
  latitude: number;
  longitude: number;
  siteLocationLabel: string;
  updateTime: string;
  updateUserName: string;
};

export type ShopExtraDTO = {
  signerName: string;
  signerPhone: string;
  signerIdCard: string;
  brandAuthBeginDate: string;
  brandAuthEndDate: string;
  signerMainType: SignerMainTypeEnum;
  signerMain?: string;
  signerUsci?: string;
  realUseArea?: number;
  propertyAddress?: string;
  customerId?: number;
  signerPhoneSensitive?: string;
  signerIdCardSensitive?: string;
  customerName?: string;
};

export enum SignerMainTypeEnum {
  个人 = 1,
  公司,
}

export type ShopDTO = ShopListItemDTO & ShopExtraDTO;

export type UpdateShopExtraReq = ShopExtraDTO & {
  shopId: string;
};

export type ExportShopListReq = {
  shopIds?: number[];
} & GetShopListReq;

export interface ShopCertDTO {
  /**
   * 门店id
   */
  shopId?: number;
  /**
   * 抖音校验状态描述
   */
  douyinCheckResult?: string;
  /**
   * 抖音校验状态
   */
  douyinCheckStatus?: number;
  /**
   * 法人信息
   */
  legalPerson?: {
    /**
     * 证件国徽面图片链接
     */
    idCardBackUrl?: string;
    /**
     * 有效期 时间格式：yyyy-MM-dd 长期传：9999-12-31
     */
    idCardExpiration?: string;
    /**
     * 证件人像面图片链接
     */
    idCardFrontUrl?: string;
    /**
     * 证件号码
     */
    idCardNo?: string;
    /**
     * 姓名
     */
    name?: string;
    /**
     * 联系电话
     */
    phone?: string;
  };
  /**
   * 营业执照信息
   */
  license?: {
    /**
     * 经营场所
     */
    address?: string;
    /**
     * 营业执照有效期,时间格式：yyyy-MM-dd 长期传：9999-12-31
     */
    licenseExpiration?: string;
    /**
     * 统一社会信用代码，长度为18位，以91/92开头
     */
    licenseId?: string;
    /**
     * 营业执照名称
     */
    licenseName?: string;
    /**
     * 营业执照经营者姓名
     */
    licensePersonName?: string;
    /**
     * 营业执照类型，1:三证合一，2:普通营业执照
     */
    licenseType?: number;
    /**
     * 营业执照照片链接
     */
    licenseUrl?: string;
    /**
     * 经营范围
     */
    salesRange?: string;
    /**
     * 发证日期
     */
    issueDate?: string;
  };
  /**
   * 许可证信息
   */
  qualification?: {
    /**
     * 法人姓名
     */
    legalPersonName?: string;
    /**
     * 经营场所
     */
    qualificationAddress?: string;
    /**
     * 主体业态
     */
    qualificationBusinessType?: string;
    /**
     * 有效期 时间格式：yyyy-MM-dd 长期传：9999-12-31
     */
    qualificationExpiration?: string;
    /**
     * 食品许可证编号
     */
    qualificationId?: string;
    /**
     * @ApiModelProperty("门店主营类目 code")
     * 食品许可证经营者姓名
     */
    qualificationPersonName?: string;
    /**
     * 经营项目
     */
    qualificationScope?: string;
    /**
     * 许可证照片链接
     */
    qualificationUrl?: string;
    /**
     * 发证日期
     */
    issueDate?: string;
  };
  /**
   * 网商信息
   */
  franchiseeInfo?: {
    merchantType?: MerchantTypeEnum;
    status?: MerchantStatusEnum;
    processStatus?: MerchantProcessStatusEnum;
    businessLicenseName?: string;
    businessLicenseNo?: string;
    personName?: string;
    idCard?: string;
    mobile?: string;
  };
}

export enum MerchantTypeEnum {
  自然人 = '01',
  个体工商户 = '02',
  企业商户 = '03',
}

export enum MerchantStatusEnum {
  待开户 = 1,
  待签约,
  生效中,
  开户失败,
}

export enum MerchantProcessStatusEnum {
  提交预申请 = 1,
  加盟商开户,
  加盟商签约,
  首次绑定门店,
}

export type ShopTycInfoDTO = {
  tycLicense?: {
    name: string;
    creditCode: string;
    companyOrgType: string;
    businessScope: string;
    regLocation: string;
    legalPersonName: string;
    regStatus: string;
    tags: string;
    updateTimes: string;
  };
  tycQualification?: {
    certNo: string;
    operator: string;
    legalPersonName: string;
    address: string;
    mainBusiness: string;
    expireDate: string;
    issueDate: string;
    businessProject: string;
  };
};
