import { RateTypeEnum, ShopManagementFeeChangeTypeEnum } from '../shop-management-fee/type';

export type QueryBrandManagementFeePageListReq = {
  pageNum?: number;
  pageSize?: number;
  shopNo?: string;
  period?: string;
  changeType?: ShopManagementFeeChangeTypeEnum;
};

export type ExportBrandManagementFeeReq = Omit<
  QueryBrandManagementFeePageListReq,
  'pageNum' | 'pageSize'
>;

export type BrandManagementFeeDTO = {
  id: number;
  shopNo: string;
  period: string;
  originalShopNo: string;
  rateType: RateTypeEnum;
  changeType: ShopManagementFeeChangeTypeEnum[];
};
