import { request } from '@src/common/http';
import {
  BrandManagementFeeDTO,
  ExportBrandManagementFeeReq,
  QueryBrandManagementFeePageListReq,
} from './type';

export const queryBrandManagementFeePageList = (data: QueryBrandManagementFeePageListReq) =>
  request.post<{ result: BrandManagementFeeDTO[]; total: number }>(
    '/api/admin/shop/management/fee/report/page',
    { data },
  );

export const exportBrandManagementFee = (data: ExportBrandManagementFeeReq) =>
  request.post('/api/admin/shop/management/fee/report/export', { data });

export const refreshBrandManagementFee = (params: { shopNo: string; period: string }) =>
  request.get(`/api/admin/shop/management/fee/report/refresh`, { params });

export const exportBrandManagementFeeDetail = (data: ExportBrandManagementFeeReq) =>
  request.post('/api/admin/shop/management/fee/report/detail/export', { data });
