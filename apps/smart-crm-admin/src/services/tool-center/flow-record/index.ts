import { request } from '@src/common/http';
import { FlowRecordDetailItemDto, FlowRecordItemDto, GetFlowRecordListReq } from './type';

export const getFlowRecordList = (params: GetFlowRecordListReq) =>
  request.get<{ total: number; result: FlowRecordItemDto[] }>('/api/admin/clue/flow/record/list', {
    params,
  });

export const getFlowRecordDetail = ({
  id,
  ...params
}: {
  id: number;
  pageNum?: number;
  pageSize?: number;
}) =>
  request.get<{
    total: number;
    result: FlowRecordDetailItemDto[];
  }>(`/api/admin/clue/flow/record/${id}/detail/list`, { params });
