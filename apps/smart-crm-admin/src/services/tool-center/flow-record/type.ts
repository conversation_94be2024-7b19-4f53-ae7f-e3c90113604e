export enum OperateTypeEnum {
  /** 转让线索 */
  TRANSFER = 'TRANSFER',
  /** 分配线索 */
  ALLOCATION = 'ALLOCATION',
  /** 领取线索 */
  GET = 'GET',
  /** 放弃线索 */
  ABANDON = 'ABANDON',
  /** 删除线索 */
  DELETE = 'DELETE',
  /** 转移线索池 */
  SHIFT = 'SHIFT',
}

export type GetFlowRecordListReq = {
  pageNum?: number;
  pageSize?: number;
  operateUserId?: number;
  operateType?: OperateTypeEnum;
};

export type FlowRecordItemDto = {
  id: number;
  operateUserId: number;
  operateUserName: string;
  operateType: OperateTypeEnum;
  createTime: string;
  flowReason: string;
  successNum: number;
  failNum: number;
};

export type FlowRecordDetailItemDto = {
  id: number;
  flowClueName: string;
  receiverName: string;
  rejectReasons: string;
};
