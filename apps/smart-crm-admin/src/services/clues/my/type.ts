import { ValueType } from '@src/components/ETable';
import { FieldConfigType, FieldTypeEnum } from '@src/services/common/type';

export enum GenderEnum {
  男 = 'MALE',
  女 = 'FEMALE',
  未知 = 'UNKNOWN',
}

export type FieldItemType = {
  id: number;
  field: string;
  name: string;
  fieldType: FieldTypeEnum;
  searchFlag: boolean;
  editFlag: boolean;
  showFlag: boolean;
  valueType: ValueType;
  notNull: boolean;
  showRules?: { field: string; values: string[] }[];
} & FieldConfigType;

export type ClueItemDTO = {
  id: number;
  name: string;
  phone: string;
  phoneSensitive: string;
  callFlag: boolean;
  [key: string]: any;
};

export enum BelongCluePoolTypeEnum {
  /** 默认线索池 */
  DEFAULT_CLUE_POOL = 'DEFAULT_CLUE_POOL',
  /** 指定线索池 */
  APPOINT_CLUE_POOL = 'APPOINT_CLUE_POOL',
  /** 成员线索 */
  PRIVATE_CLUE_POOL = 'PRIVATE_CLUE_POOL',
}

export type ClueDetail = {
  id: number;
  name: string;
  director: number;
  finalDirector: number;
  tagId?: { id: number; name: string }[];
  phone: string;
  phoneSensitive: string;
  belongCluePoolType: BelongCluePoolTypeEnum;
  cluePoolId?: number;
  [key: string]: any;
};
export type UpdateClueFieldValueReq = {
  id: number;
  fieldValues: { field: string; value: any }[];
  [key: string]: any;
};

export enum AllocationTypeEnum {
  ONESELF = 'ONESELF',
  AUTO = 'AUTO',
}

export type CreateClueReq = {
  allocationType: AllocationTypeEnum;
  fieldValues: { field: string; value: any }[];
  [key: string]: any;
};

export type ImportClueExcelReq = {
  allocationType: AllocationTypeEnum;
  file: FormData;
};

export type MarkClueTagReq = {
  clueId: number;
  addTagIds: number[];
  removeTagIds: number[];
};

export type AddFollowClueReq = {
  attachments?: { fileKey: string; fileName: string; fileType: string }[];
  clueId: number;
  followTime: string;
  info: string;
};

export type AbandonClueReq = {
  attachments?: { fileKey: string; fileName: string; fileType: string }[];
  clueId: number;
  info: string;
  transferCluePoolId: number;
};

export type BatchAbandonClueReq = {
  attachments?: { fileKey: string; fileName: string; fileType: string }[];
  clueIds: number[];
  info: string;
  transferCluePoolId: number;
};

export type DeleteClueReq = {
  clueId: number;
  fromCluePoolId?: number;
};

export type BatchDeleteClueReq = {
  clueIds: number[];
  fromCluePoolId?: number;
};
