import { request } from '@src/common/http';
import { ActivityItem, ExportETableListReq, GetETableListReq } from '@src/services/common/type';
import {
  AbandonClueReq,
  AddFollowClueReq,
  BatchAbandonClueReq,
  BatchDeleteClueReq,
  ClueDetail,
  ClueItemDTO,
  CreateClueReq,
  DeleteClueReq,
  ImportClueExcelReq,
  MarkClueTagReq,
  UpdateClueFieldValueReq,
} from './type';

export const getMyClueList = (data: GetETableListReq) =>
  request.post<{ total: number; result: ClueItemDTO[] }>('/api/admin/clue/selectByPage', {
    data,
  });

export const updateClueFieldValue = ({ id, ...data }: UpdateClueFieldValueReq) =>
  request.post(`/api/admin/clue/update/clue/${id}`, { data });

export const getClueDetail = (id: number) =>
  request.get<ClueDetail>(`/api/admin/clue/queryClueDetail/${id}`);

export const downloadClueTemplate = () =>
  request.get<{ url: string }>('/api/admin/clue/downloadImportTemplate');

export const createClue = (data: CreateClueReq) =>
  request.post('/api/admin/clue/save/clue', { data });

export const importClueExcel = (data: ImportClueExcelReq) =>
  request.post<{ failDownloadUrl: string; failNum: number; successNum: number }>(
    '/api/admin/clue/importExcel',
    {
      data,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );

export const markClueTag = (data: MarkClueTagReq) =>
  request.post('/api/admin/clue/mark-tag', { data });

export const callPhone = (phone: string) =>
  request.post('/api/admin/call/log/call/phone', { data: { phone } });

export const addFollowClue = (data: AddFollowClueReq) =>
  request.post('/api/admin/clue/addFollow', { data });

export const abandonClue = (data: AbandonClueReq) =>
  request.post('/api/admin/clue/abandon', { data });

export const batchAbandonClue = (data: BatchAbandonClueReq) =>
  request.post('/api/admin/clue/batchAbandon', { data });

export const transferClue = (data: { clueId: number; transferUserId: number }) =>
  request.post('/api/admin/clue/transfer', { data });

export const batchTransferClue = (data: { clueIds: number[]; transferUserIds: number[] }) =>
  request.post<{ successNum: number; failNum: number }>('/api/admin/clue/batchTransfer', { data });

export const deleteClue = (data: DeleteClueReq) => request.post('/api/admin/clue/delete', { data });

export const batchDeleteClue = (data: BatchDeleteClueReq) =>
  request.post('/api/admin/clue/batchDelete', { data });

export const exportClueExcel = (data: ExportETableListReq) =>
  request.post('/api/admin/clue/exportExcel', { data });

export const getWXFollowUser = (id: number) =>
  request.get<{ name: string; followTime: string }[]>(`/api/admin/clue/${id}/corp-wx/follow-user`);

export const checkCluePhone = (phone: string) =>
  request.get(`/api/admin/clue/check-phone`, { params: { phone }, noAlert: true });

export const getClueAuth = (id: number) =>
  request.get<{ operable: boolean; editable: boolean }>(`/api/admin/clue/operation/auth/${id}`);

export const getClueFollowActivity = (params: {
  pageNum: number;
  pageSize: number;
  clueId: number;
}) =>
  request.get<{ total: number; result: ActivityItem[] }>('/api/admin/clue/activity/follow/page', {
    params,
  });

export const getClueActivity = (params: { pageNum: number; pageSize: number; clueId: number }) =>
  request.get<{ total: number; result: ActivityItem[] }>('/api/admin/clue/activity/page', {
    params,
  });

export const relCustomerById = ({ clueId, customerId }: { clueId: number; customerId: number }) =>
  request.put(`/api/admin/clue/${clueId}/rel/${customerId}`);

export const getRelCustomer = (clueId: number) =>
  request.get<{ id: number; name: string }>(`/api/admin/clue/rel/customer/${clueId}`);
