import { ExportETableListReq, GetETableListReq } from '@src/services/common/type';

export type CluePoolItem = {
  id: number;
  name: string;
  clueCount: number;
  defaultFlag: boolean;
  userIds: number[];
  userNames: string[];
  updateByName: string;
  updateTime: number;
};

export type EditCluePoolReq = {
  id: number;
  name: string;
  addUserIds: number[];
  deleteUserIds: number[];
};

export type CreateCluePoolReq = {
  name: string;
  userIds: number[];
};

export type GetClueListByCluePoolReq = GetETableListReq & {
  cluePoolId: number;
};

export type ExportClueInPoolReq = ExportETableListReq & {
  cluePoolId: number;
  ids?: number[];
};
