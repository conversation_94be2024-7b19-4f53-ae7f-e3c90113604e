import { request } from '@src/common/http';
import {
  CluePoolItem,
  CreateCluePoolReq,
  EditCluePoolReq,
  ExportClueInPoolReq,
  GetClueListByCluePoolReq,
} from './type';
import { ClueItemDTO } from '../my/type';

export const getCluePoolList = (params: { pageNum?: number; pageSize?: number; name?: string }) =>
  request.get<{ result: CluePoolItem[]; total: number }>('/api/admin/clue/pool/manage/page', {
    params,
  });

export const createCluePool = (data: CreateCluePoolReq) =>
  request.post('/api/admin/clue/pool/manage/add', { data });

export const editCluePool = (data: EditCluePoolReq) =>
  request.post('/api/admin/clue/pool/manage/modify', { data });

export const getCluePoolDetail = (id: number) =>
  request.get<{ name: string; userIds: number[] }>(`/api/admin/clue/pool/manage/${id}`);

export const deleteCluePool = (id: number) =>
  request.post(`/api/admin/clue/pool/manage/delete/${id}`);

export const getClueListByCluePool = (data: GetClueListByCluePoolReq) =>
  request.post<{ total: number; result: ClueItemDTO[] }>('/api/admin/clue/pool/page', {
    data,
  });

export const getCluePoolPermissionList = () =>
  request.get<{ id: number; name: string }[]>('/api/admin/cluePool/common/permissionList');

export const receiveClue = (data: { clueId: number; fromCluePoolId: number }) =>
  request.post('/api/admin/clue/pool/receive', { data });

export const batchReceiveClue = (data: { clueIds: number[]; fromCluePoolId: number }) =>
  request.post('/api/admin/clue/pool/batchReceive', { data });

export const assignClue = (data: { clueId: number; toUserId: number; fromCluePoolId: number }) =>
  request.post('/api/admin/clue/pool/assign', { data });

export const batchAssignClue = (data: {
  clueIds: number[];
  toUserIds: number[];
  fromCluePoolId: number;
}) =>
  request.post<{ successNum: number; failNum: number }>('/api/admin/clue/pool/batchAssign', {
    data,
  });

export const transferClueToPool = (data: {
  clueId: number;
  toCluePoolId: number;
  fromCluePoolId: number;
}) => request.post('/api/admin/clue/pool/transfer', { data });

export const batchTransferClueToPool = (data: {
  clueIds: number[];
  toCluePoolId: number;
  fromCluePoolId: number;
}) => request.post('/api/admin/clue/pool/batchTransfer', { data });

export const exportClueInPool = (data: ExportClueInPoolReq) =>
  request.post('/api/admin/clue/pool/export', { data });
