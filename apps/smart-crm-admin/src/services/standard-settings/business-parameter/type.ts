export type BusinessParameterDTO = {
  id: number;
  configKey: ConfigKeyEnum;
  configName: string;
  updateTime: string;
  configValue: { userId: number } | { switch: boolean };
};

export enum ConfigKeyEnum {
  /** 加盟申请表默认责任人 */
  FRANCHISE_DEFAULT_OWNER = 'FRANCHISE_DEFAULT_OWNER',
  /** 线索平均分配开关 */
  CLUE_AVG_ALLOCATION_SWITCH = 'CLUE_AVG_ALLOCATION_SWITCH',
  /** 培训人员开关 */
  TRAINING_PERSON_SWITCH = 'TRAINING_PERSON_SWITCH',
}

export type UpdateBusinessParameterReq = {
  id: number;
  configValue: { userId: number };
};

export type FindSystemConfigReq = {
  configKey: ConfigKeyEnum;
};
