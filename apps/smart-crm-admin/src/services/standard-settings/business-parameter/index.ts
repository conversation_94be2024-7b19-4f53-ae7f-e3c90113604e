import { request } from '@src/common/http';
import { BusinessParameterDTO, FindSystemConfigReq, UpdateBusinessParameterReq } from './type';

export const getBusinessParameterList = () =>
  request.get<BusinessParameterDTO[]>('/api/admin/system/config/list', {
    params: {
      configType: 'BIZ_PARAM',
    },
  });

export const updateBusinessParameter = (data: UpdateBusinessParameterReq) =>
  request.post('/api/admin/system/config/value/modify', { data });

export const findSystemConfig = (data: FindSystemConfigReq) =>
  request.get<BusinessParameterDTO>(`/api/admin/system/config/find-system-config`, {
    params: {
      ...data,
      configType: 'BIZ_PARAM',
    },
  });
