import { request } from '@src/common/http';
import { BusinessOpportunityTypeEnum } from '@src/services/business-opportunity/type';
import { BusinessTypeEnum } from '@src/services/common/type';
import {
  ExamItemDTO,
  ExamPaperConfigDetailType,
  ExamStatusEnum,
  GetExamListReq,
  QuestionItemType,
  SaveExamPaperConfigReq,
} from './type';

// 获取试卷列表
export const getExamList = (params: GetExamListReq) =>
  request.get<{ result: ExamItemDTO[]; total: number }>('/api/admin/exam-paper', { params });

// 获取模版详情
export const getExamPaperConfigDetail = (params: {
  examPaperId: number;
  businessType: BusinessTypeEnum;
}) =>
  request.get<ExamPaperConfigDetailType>('/api/admin/exam-paper/config/detail', {
    params,
  });

export const saveExamPaperConfig = (data: SaveExamPaperConfigReq) =>
  request.post<number>('/api/admin/exam-paper/config/save', { data });

export const modifyExamPaperStatus = (data: { id: number; status: ExamStatusEnum }) =>
  request.post('/api/admin/exam-paper', { data });

export const deleteExamPaper = (data: { id: number }) =>
  request.del('/api/admin/exam-paper', { data });

export const copyExamPaper = (data: { id: number }) =>
  request.post('/api/admin/exam-paper/copy', { data });

export const getExamPaperAllQuestions = (params: { businessType: BusinessTypeEnum }) =>
  request.get<QuestionItemType[]>('/api/admin/exam-paper/get-questions', { params });

export const checkQuestionRule = (params: { examPaperId?: number; value: string }) =>
  request.get<{ id: number; name: string }[]>('/api/admin/exam-paper/check-question-rule', {
    params,
  });

export const configExamBizType = (data: {
  bizOpportunityType: BusinessOpportunityTypeEnum;
  examPaperId: number;
}) => request.post('/api/admin/exam-paper/config-biz-type', { data });
