import {
  TemplateQuestionType,
  TemplateQuestionTypeEnum,
} from '@src/pages/standard-settings/template/components/QuestionConfig/type';
import { BusinessOpportunityTypeEnum } from '@src/services/business-opportunity/type';
import { BusinessTypeEnum, FieldConfigType } from '@src/services/common/type';

export enum ExamStatusEnum {
  ENABLE = 'ENABLE',
  DISABLE = 'DISABLE',
}

export type GetExamListReq = {
  businessType: BusinessTypeEnum;
  pageNum?: number;
  pageSize?: number;
  name?: string;
  status?: ExamStatusEnum;
};

// 试卷
export type ExamItemDTO = {
  examPaperId: number;
  paperName: string;
  createName: string;
  createTime: string;
  status: ExamStatusEnum;
  businessOpportunityType: BusinessOpportunityTypeEnum;
};

export enum QuestionConditionEnum {
  /** 包含 */
  IN = 'IN',
  /** 包含 */
  LIKE = 'LIKE',
  /** 等于 */
  EQ = 'EQ',
  /** 大于 */
  GT = 'GT',
  /** 大于等于 */
  GE = 'GE',
  /** 小于 */
  LT = 'LT',
  /** 小于等于 */
  LE = 'LE',
  /** 不在区间 */
  NOT_BETWEEN = 'NOT_BETWEEN',
}

export type QuestionItemType = Omit<TemplateQuestionType, 'ruleInfo' | 'uniqId' | 'childList'> & {
  sort: number;
  ruleInfo?: string;
  childList?: QuestionItemType[];
};

export type PageListItemType = {
  pageName: string;
  pageId?: number;
  sort: number;
  questions: QuestionItemType[];
  deleted?: boolean;
};

export type ExamPaperConfigDetailType = {
  id: number;
  name: string;
  businessType: BusinessTypeEnum;
  pageList: PageListItemType[];
  questionBank: ({
    name: string;
    description?: string;
    id: number;
    questionType: TemplateQuestionTypeEnum;
    questionKey: string;
    notNull?: boolean;
    ruleInfo?: string;
    childList?: ExamPaperConfigDetailType['questionBank'];
  } & FieldConfigType)[];
};

export type SaveExamPaperConfigReq = {
  id?: number;
  name: string;
  businessType: BusinessTypeEnum;
  pageList: (PageListItemType | { pageId: number; deleted: boolean })[];
};
