import { request } from '@src/common/http';
import { EditTagGroupPayload, GetTagListPayload, TagGroupItem } from './type';

export const getTagList = (params?: GetTagListPayload) =>
  request.get<{ total: number; result: TagGroupItem[] }>('/api/tag/group/list', { params });

export const deleteTagGroup = (tagGroupId: number) =>
  request.put(`/api/tag/group/${tagGroupId}/invalid`);

export const createTagGroup = (data: { name: string }) => request.post('/api/tag/group', { data });

export const editTagGroup = (data: EditTagGroupPayload) => request.put('/api/tag/group', { data });

export const getTagGroupDetail = (groupId: number) =>
  request.get<TagGroupItem>(`/api/tag/group/${groupId}/detail`);
