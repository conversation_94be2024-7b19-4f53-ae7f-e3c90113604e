export enum TagStatusEnum {
  /** 可用 */
  AVAILABLE = 'AVAILABLE',
  /** 作废 */
  INVALID = 'INVALID',
}

export type GetTagListPayload = {
  status?: TagStatusEnum;
};

export enum TagTypeEnum {
  RADIO = 'RADIO',
  MULTI_SELECT = 'MULTI_SELECT',
}

export enum TagRemoveRuleEnum {
  REMOVE = 'REMOVE',
  NOT_REMOVE = 'NOT_REMOVE',
}

export enum TagCategoryEnum {
  常规 = 'COMMON',
  系统 = 'SYSTEM',
}

export type TagItem = { groupId: number; id: number; name: string; status: TagStatusEnum };

export type TagGroupItem = {
  id: number;
  name: string;
  removeRule: TagRemoveRuleEnum;
  type: TagTypeEnum;
  status: TagStatusEnum;
  tags: TagItem[];
  category: TagCategoryEnum;
};

export type EditTagGroupPayload = {
  id: number;
  name: string;
  type: TagTypeEnum;
  removeRule: TagRemoveRuleEnum;
  addTags: string[];
  updateTags: { id: number; name?: string; status?: TagStatusEnum }[];
};
