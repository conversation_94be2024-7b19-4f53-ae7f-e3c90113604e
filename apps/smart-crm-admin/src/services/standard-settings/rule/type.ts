export enum ClueLimitRuleTypeEnum {
  DEFAULT = 'DEFAULT',
  CUSTOMIZE = 'CUSTOMIZE',
}

export type ClueLimitRuleListItem = {
  id: number;
  name: string;
  limitNumber: number;
  ruleType: ClueLimitRuleTypeEnum;
  bindUsers: { id: number; name: string }[];
};

export type CreateClueLimitRulePayload = {
  limitNumber: number;
  name?: string;
  userIds?: number[];
};

export type EditClueLimitRulePayload = {
  id: number;
  limitNumber: number;
  name?: string;
  addUserIds?: number[];
  removeUserIds?: number[];
};
