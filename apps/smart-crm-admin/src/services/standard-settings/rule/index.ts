import { request } from '@src/common/http';
import {
  ClueLimitRuleListItem,
  CreateClueLimitRulePayload,
  EditClueLimitRulePayload,
} from './type';

export const getClueLimitRuleList = (params: { pageNum?: number; pageSize?: number }) =>
  request.get<{ total: number; result: ClueLimitRuleListItem[] }>(
    '/api/admin/user/number/limit/rule/list',
    { params },
  );

export const getClueLimitRuleStatus = () =>
  request.get<{ status: 'ON' | 'OFF' }>('/api/admin/user/number/limit/switch');

export const switchClueLimitRule = (data: { status: 'ON' | 'OFF' }) =>
  request.put('/api/admin/user/number/limit/switch', { data });

export const deleteClueLimitRule = (id: number) =>
  request.del(`/api/admin/user/number/limit/rule/${id}`);

export const getClueLimitRule = (id: number) =>
  request.get<ClueLimitRuleListItem>(`/api/admin/user/number/limit/rule/${id}/detail`);

export const createClueLimitRule = (data: CreateClueLimitRulePayload) =>
  request.post('/api/admin/user/number/limit/rule', { data });

export const editClueLimitRule = (data: EditClueLimitRulePayload) =>
  request.put('/api/admin/user/number/limit/rule', { data });
