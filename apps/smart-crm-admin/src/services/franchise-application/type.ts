import { TemplateQuestionTypeEnum } from '@src/pages/standard-settings/template/components/QuestionConfig/type';
import {
  AuditStatusEnum,
  BusinessTypeEnum,
  ExportETableListReq,
  FieldConfigType,
  FileDTO,
  GetETableListReq,
} from '@src/services/common/type';
import { BelongWarZoneEnum, BusinessOpportunityTypeEnum } from '../business-opportunity/type';
import { EvaluationRejectReasonEnum } from '../visit/type';

export type GetExamAnswerListReq = GetETableListReq & {
  businessType: BusinessTypeEnum.ONLINE_APPLY | BusinessTypeEnum.INTERVIEW_EVALUATION;
};

export enum FranchiseRejectOrReviewReasonEnum {
  股份占比过低 = 'PROPORTION_SHARES_TOO_LOW',
  资金维度 = 'UND_DIMENSION',
  工作经验维度 = 'WORK_EXPERIENCE_DIMENSION',
  年龄维度 = 'AGE_DIMENSION',
  经营理念维度 = 'BUSINESS_CONCEPT_DIMENSION',
  资料空白 = 'DATA_BLANK',
  培训维度 = 'TRAINING_DIMENSION',
  抗风险维度 = 'RISK_RESISTANCE_DIMENSION',
  面谈时注意抗风险 = 'RISK_NOTICE_IN_INTERVIEW',
  资料不清晰 = 'UNCLEAR_DATA',
  没有人参与培训 = 'NO_ONE_PARTICIPATED_IN_TRAINING',
  身份证号码 = 'ID_NUMBER',
  店员身份证 = 'STORE_CLERK_ID',
  '骨干+申请人超员' = 'KEY_STAFF_AND_APPLICANTS_OVER_LIMIT',
  骨干年龄 = 'KEY_STAFF_AGE',
  电话号码 = 'PHONE_NUMBER',
  老业主 = 'OLD_OWNER',
  门店管理者非本人 = 'STORE_MANAGER_NOT_SELF',
  区域信息 = 'REGIONAL_INFORMATION',
  请提供相关材料 = 'PROVIDE_RELEVANT_MATERIALS',
  学历维度 = 'EDUCATION_DIMENSION',
  合伙人维度 = 'PARTNER',
  门店运营总负责人维度 = 'STORE_MANAGER',
  经营沙县小吃 = 'SHA_XIAN_SNACK',
}

export enum PassTypeEnum {
  特批通过 = 'SPECIAL_APPROVAL',
  二次通过 = 'SECONDARY_APPROVAL',
  正常通过 = 'NORMAL_APPROVAL',
}

export enum FranchiseAutoAuditStatusEnum {
  不通过 = 'NO_PASS',
  通过 = 'PASS',
}

export type ExamAnswerDTO = {
  id: number;
  code: string;
  customerId: number;
  customerName: string;
  businessOpportunityId: number;
  businessOpportunityCode: string;
  businessOpportunityName: string;
  businessOpportunityDirectUserId: number;
  businessOpportunityType: BusinessOpportunityTypeEnum;
  auditStatus: AuditStatusEnum;
  reviewReasons: FranchiseRejectOrReviewReasonEnum[];
  rejectReasons: (FranchiseRejectOrReviewReasonEnum | EvaluationRejectReasonEnum)[];
  autoAuditStatus: FranchiseAutoAuditStatusEnum;
  autoAuditReason: FranchiseRejectOrReviewReasonEnum;
  directUserId: number;
  /** 面审到访ID */
  interviewVisitRecordId: number;
  /** 面审到访编号 */
  interviewVisitRecordCode: string;
  createTime: string;
  updateTime: string;
  updateUserId: number;
  createUserId: number;
  suggestion: string;
  comments: string;
  examPaperId: number;
  examPaperName: string;
  passType: PassTypeEnum;
  belongWarZone?: BelongWarZoneEnum;
  belongProvince?: string;
  [key: string]: any;
};

export type ExamAnswerQuestionDTO = {
  id: number;
  name: string;
  questionKey: string;
  notNull?: boolean;
  ruleInfo?: string;
  description?: string;
  questionType: TemplateQuestionTypeEnum;
  childList?: ExamAnswerQuestionDTO[];
} & Omit<FieldConfigType, 'options'> & {
    options?: { label: string; value: string; deleted: boolean }[];
  };

export type ExportExamAnswerRecordReq = ExportETableListReq & {
  businessType: BusinessTypeEnum.ONLINE_APPLY | BusinessTypeEnum.INTERVIEW_EVALUATION;
};

export type BusinessExamAnswerRecordDTO = {
  id: number;
  code: string;
  examPaperName: string;
  auditStatus: AuditStatusEnum;
  directUserId: number;
  createTime: string;
  auditTime: string;
  rejectReasons: FranchiseRejectOrReviewReasonEnum[] | EvaluationRejectReasonEnum[];
};

export type UpdateExamAnswerRecordReq = {
  questionAnswers: {
    questionKey: string;
    answer: any;
  }[];
  recordId: number;
  businessType: BusinessTypeEnum;
  comments?: string;
  rejectReasons?: EvaluationRejectReasonEnum;
  suggestion?: string;
  applyUserIsPass?: boolean;
  storeManagerIsPass?: boolean;
};

export type ReviewFranchiseTaskReq = {
  recordId: number;
  reviewDesc: string;
  reviewReasons: FranchiseRejectOrReviewReasonEnum[];
  reviewUserIds: number[];
};

export type ApproveFranchiseTaskReq = {
  recordId: number;
  passType?: PassTypeEnum;
  comments?: string;
  attachments?: FileDTO[];
};

export type RejectFranchiseTaskReq = {
  recordId: number;
  rejectReasons: FranchiseRejectOrReviewReasonEnum;
  comments?: string;
  attachments?: FileDTO[];
};
