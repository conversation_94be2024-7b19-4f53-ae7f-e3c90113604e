import { request } from '@src/common/http';
import { AuditHistoryType, BusinessTypeEnum } from '@src/services/common/type';
import {
  ApproveFranchiseTaskReq,
  BusinessExamAnswerRecordDTO,
  ExamAnswerDTO,
  ExamAnswerQuestionDTO,
  ExportExamAnswerRecordReq,
  GetExamAnswerListReq,
  RejectFranchiseTaskReq,
  ReviewFranchiseTaskReq,
  UpdateExamAnswerRecordReq,
} from './type';

// 获取加盟、面审评估列表
export const getExamAnswerList = (data: GetExamAnswerListReq) =>
  request.post<{ result: ExamAnswerDTO[]; total: number }>('/api/admin/exam/answer/record/page', {
    data,
  });

export const getExamAnswerQuestions = (
  businessType: BusinessTypeEnum.ONLINE_APPLY | BusinessTypeEnum.INTERVIEW_EVALUATION,
) =>
  request.get<ExamAnswerQuestionDTO[]>(
    `/api/admin/exam/answer/record/list/answer/question/${businessType}`,
  );

export const exportExamAnswerRecord = (data: ExportExamAnswerRecordReq) =>
  request.post('/api/admin/exam/answer/record/export', { data });

export const getExamAnswerRecordDetail = ({
  businessType,
  recordId,
}: {
  businessType: BusinessTypeEnum.ONLINE_APPLY | BusinessTypeEnum.INTERVIEW_EVALUATION;
  recordId: number;
}) =>
  request.get<ExamAnswerDTO>(`/api/admin/exam/answer/record/detail/${businessType}/${recordId}`);

// 根据商机 id 查询回答过的试卷列表
export const getExamAnswerRecordsByBusinessId = ({
  businessType,
  id,
}: {
  businessType: BusinessTypeEnum.ONLINE_APPLY | BusinessTypeEnum.INTERVIEW_EVALUATION;
  id: number;
}) =>
  request.get<BusinessExamAnswerRecordDTO[]>(
    `/api/admin/exam/answer/record/list/${businessType}/${id}`,
  );

export const getExamRecordItemQuestions = ({
  businessType,
  recordId,
}: {
  businessType: BusinessTypeEnum.ONLINE_APPLY | BusinessTypeEnum.INTERVIEW_EVALUATION;
  recordId: number;
}) =>
  request.get<{ pageId: number; pageName: string; questions: ExamAnswerQuestionDTO[] }[]>(
    `/api/admin/exam/answer/record/list/answer/question/page/${businessType}/${recordId}`,
  );

export const updateExamAnswerRecord = (data: UpdateExamAnswerRecordReq) =>
  request.put('/api/admin/exam/answer/record/update/answer/record', { data });

export const transferFranchise = (data: {
  id: number;
  userId: number;
  businessType: BusinessTypeEnum.ONLINE_APPLY;
}) => request.put('/api/admin/exam/answer/record/transfer', { data });

export const batchTransferFranchise = (data: {
  ids: number[];
  userId: number;
  businessType: BusinessTypeEnum.ONLINE_APPLY;
}) => request.put('/api/admin/exam/answer/record/transfer/batch', { data });

export const reviewFranchiseTask = (data: ReviewFranchiseTaskReq) =>
  request.post('/api/admin/exam-online-apply-record/review-task', { data });

export const approveFranchiseTask = (data: ApproveFranchiseTaskReq) =>
  request.post('/api/admin/exam-online-apply-record/approve-task', { data });

export const rejectFranchiseTask = (data: RejectFranchiseTaskReq) =>
  request.post('/api/admin/exam-online-apply-record/reject-task', { data });

export const getFranchiseAuditAllHistory = (recordId: number) =>
  request.get<{ auditHistories: AuditHistoryType[]; name: string; processInstanceId: number }[]>(
    `/api/admin/exam-online-apply-record/all/audit/history/${recordId}`,
  );

export const getFranchiseAuditHistory = (recordId: number) =>
  request.get<AuditHistoryType[]>(`/api/admin/exam-online-apply-record/audit/history/${recordId}`);

// 重新发起审批
export const reApproval = (recordId: number) =>
  request.post('/api/admin/exam-online-apply-record/re-create-process-instance', {
    data: { recordId },
  });
