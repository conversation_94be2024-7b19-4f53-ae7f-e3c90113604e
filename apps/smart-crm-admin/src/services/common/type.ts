import { ConditionEnum, SearchRelationEnum, ValueType } from '@src/components/ETable';
import { IdentityTypeEnum } from '../mini-program/identity/type';
import { ContractTypeEnum } from '../payment/type';

export interface CurrentUserDTO {
  nickName: string;
  userName: string;
  shUserId: number;
  brandBaseInfos: { brandId: number }[];
}

export type OssTokenProps = {
  accessKeyId: string;
  accessKeySecret: string;
  bucket: string; // 桶名
  expiration: string; // 过期
  prefix: string; // 上传文件路径
  securityToken: string;
};

export enum BusinessTypeEnum {
  /** 我的线索 */
  MY_CLUE = 'MY_CLUE',
  /** 线索池 */
  CLUE_POOL = 'CLUE_POOL',
  /** 加盟申请 */
  ONLINE_APPLY = 'ONLINE_APPLY',
  /** 面审到访 */
  INTERVIEW_VISIT = 'INTERVIEW_VISIT',
  /** 面审评估 */
  INTERVIEW_EVALUATION = 'INTERVIEW_EVALUATION',
  /** 意向合同 */
  INTENTION_CONTRACT = 'INTENTION_CONTRACT',
  /** 正式合同 */
  FORMAL_CONTRACT = 'FORMAL_CONTRACT',
  /** 区域名额 */
  FRANCHISE_AREA_QUOTA = 'FRANCHISE_AREA_QUOTA',
  /** 续约合同 */
  RENEW_CONTRACT = 'RENEW_CONTRACT',
  /** 搬迁意向合同 */
  RELOCATION_INTENTION_CONTRACT = 'RELOCATION_INTENTION_CONTRACT',
  /** 搬迁正式合同 */
  RELOCATION_FORMAL_CONTRACT = 'RELOCATION_FORMAL_CONTRACT',
  /** 门店重装合同 */
  RENOVATION_CONTRACT = 'RENOVATION_CONTRACT',
  /** 变更法人合同 */
  CHANGE_LEGAL_PERSON_CONTRACT = 'CHANGE_LEGAL_PERSON_CONTRACT',
  /** * 培训正式合同人员清单 */
  TRAINING_FORMAL_CONTRACT_PERSON_LIST = 'TRAINING_FORMAL_CONTRACT_PERSON_LIST',
  /** 培训搬迁正式人员清单 */
  TRAINING_R_FORMAL_CONTRACT_PERSON_LIST = 'TRAINING_R_FORMAL_CONTRACT_PERSON_LIST',
  /** 加盟商档案-加盟申请表 */
  FRANCHISEE_PROFILE = 'FRANCHISEE_PROFILE',
}

export type UserDTO = {
  id: number;
  name: string;
  identityType: IdentityTypeEnum;
  status: 'DELETED' | 'DISABLED' | 'NORMAL';
};

export type CreateViewReq = {
  code: BusinessTypeEnum;
  queryCondition: string;
  viewFields: string;
  viewName: string;
};

export type ViewItem = {
  id: number;
  viewName: string;
  queryCondition: string;
  viewFields: string;
};

export type ModifyViewReq = {
  id: number;
  queryCondition?: string;
  viewFields?: string;
  viewName?: string;
};

export enum FieldTypeEnum {
  BASE = 'BASE',
  CUSTOM = 'CUSTOM',
  SYSTEM = 'SYSTEM',
}

export enum RegionModeEnum {
  SINGLE = 'SINGLE',
  MULTIPLE = 'MULTIPLE',
}

export type RegionLevelType = 1 | 2 | 3;

export type FieldConfigType = {
  precisions?: number;
  options?: { label: string; value: string }[];
  regionLevel?: RegionLevelType;
  regionMode?: RegionModeEnum;
};

export enum OperationTypeEnum {
  // 线索动态
  /** 领取 */
  GET = 'GET',
  /** 分配 */
  ALLOCATION = 'ALLOCATION',
  /** 转让 */
  TRANSFER = 'TRANSFER',
  /** 放弃 */
  ABANDON = 'ABANDON',
  /** 删除线索 */
  DELETE = 'DELETE',
  /** 企业微信，线索添加员工 */
  ADD_STAFF = 'ADD_STAFF',
  /** 企业微信，线索删除员工 */
  DELETE_STAFF = 'DELETE_STAFF',
  /** 企业微信，员工删除线索 */
  STAFF_DELETE_CLUE = 'STAFF_DELETE_CLUE',

  // 线索信息变更
  /** 创建线索 */
  CREATE_CLUE = 'CREATE_CLUE',
  /** 更新线索基础信息 */
  CLUE_INFO_CHANGE = 'CLUE_INFO_CHANGE',
  /** 标签变更 */
  TAG_CHANGE = 'TAG_CHANGE',

  /** 跟进动态 */
  ADD_FOLLOW = 'ADD_FOLLOW',

  // 拨打电话
  /** 未接通 */
  BLOCK_CALL = 'BLOCK_CALL',
  /** 通话录音丢失 */
  CALL_RECORDING_LOST = 'CALL_RECORDING_LOST',
  /** 通话录音已取回 */
  CALL_RECORDING_RETRIEVED = 'CALL_RECORDING_RETRIEVED',
  /** 回拨未接通 */
  CALL_BACK_BLOCK_CALL = 'CALL_BACK_BLOCK_CALL',
  /** 回拨通话记录丢失 */
  CALL_BACK_CALL_RECORDING_LOST = 'CALL_BACK_CALL_RECORDING_LOST',
  /** 回拨通话记录已取回 */
  CALL_BACK_CALL_RECORDING_RETRIEVED = 'CALL_BACK_CALL_RECORDING_RETRIEVED',

  /** 创建客户 */
  CREATE_CUSTOMER = 'CREATE_CUSTOMER',
  /** 信息变更 */
  CUSTOMER_INFO_CHANGE = 'CUSTOMER_INFO_CHANGE',

  /** 创建商机 */
  CREATE_BUSINESS_OPPORTUNITY = 'CREATE_BUSINESS_OPPORTUNITY',
  /** 更新商机信息 */
  BUSINESS_OPPORTUNITY_INFO_CHANGE = 'BUSINESS_OPPORTUNITY_INFO_CHANGE',

  /** 创建打款 */
  CREATE_PAYMENT = 'CREATE_PAYMENT',
  /** 更新打款信息 */
  PAYMENT_INFO_CHANGE = 'PAYMENT_INFO_CHANGE',

  /** 创建裂变申请 */
  CREATE_FISSION_APPLY = 'CREATE_FISSION_APPLY',
  /** 更新裂变申请信息 */
  FISSION_APPLY_INFO_CHANGE = 'FISSION_APPLY_INFO_CHANGE',

  /** 创建区域名额 */
  CREATE_FRANCHISE_AREA_QUOTA = 'CREATE_FRANCHISE_AREA_QUOTA',
  /** 更新区域名额 */
  FRANCHISE_AREA_QUOTA_INFO_CHANGE = 'FRANCHISE_AREA_QUOTA_INFO_CHANGE',
}

// 聚拢动态的类型（对应接口并不是都包含以下字段）
export type ActivityItem = {
  /** 操作类型 */
  operationType: OperationTypeEnum;
  /** 时间 */
  activityTime: string;
  /** 操作人 */
  userName: string;
  /** 转让或分配员工姓名 */
  transferUserName: string;
  /** 转移线索池名称 */
  transferCluePoolName: string;
  /** 更新字段  */
  updateField: string;
  /** 更新字段名称  */
  updateFieldName: string;
  /** 更新前值 */
  updateBefore: string;
  /** 更新后值 */
  updateAfter: string;
  /** 手机号 */
  cluePhone: string;
  /** 通话时长 */
  callDuration: number;
  /** 信息 */
  info: string;
  /** 附件 */
  attachments?: {
    fileUrl: string;
    fileName: string;
    fileType: string;
  }[];
  valueType: ValueType;
};

export enum NoticeTypeEnum {
  /** 线索 */
  CLUE = 'CLUE',
  /** 商机 */
  BO = 'BO',
  /** 到访 */
  INTERVIEW_VISIT = 'INTERVIEW_VISIT',
  /** 加盟审批 */
  ONLINE_APPLY = 'ONLINE_APPLY',
  /** 合同 */
  CONTRACT = 'CONTRACT',
  /** 裂变申请 */
  FISSION_APPLY = 'FISSION_APPLY',
  /** 面审评估 */
  INTERVIEW_EVALUATION = 'INTERVIEW_EVALUATION',
  /** 意向区域变更 */
  CONTRACT_REGION_CHANGE = 'CONTRACT_REGION_CHANGE',
  /** 意向合同延期 */
  CONTRACT_POSTPONE = 'CONTRACT_POSTPONE',
}

export enum NoticeContentTypeEnum {
  /** 商机阶段变更 */
  BO_STAGE = 'BO_STAGE',
  /** 商机状态变更 */
  BO_STATUS = 'BO_STATUS',
  /** 审批通过 */
  APPROVAL_PASS = 'APPROVAL_PASS',
  /** 待审批 */
  APPROVAL_WAITING = 'APPROVAL_WAITING',
  /** 审批拒绝 */
  APPROVAL_REJECT = 'APPROVAL_REJECT',
  /** 驳回 */
  APPROVAL_RETURN = 'APPROVAL_RETURN',
  /** 分配 */
  ASSIGN = 'ASSIGN',
  /** 批量分配 */
  BATCH_ASSIGN = 'BATCH_ASSIGN',
  /** 线索激活 */
  ACTIVATION = 'ACTIVATION',
  /** 到访通知 */
  NOTICE = 'NOTICE',
  /** 转让 */
  TRANSFER = 'TRANSFER',
  /** 批量转让 */
  BATCH_TRANSFER = 'BATCH_TRANSFER',
  /** 推送泛微通知 */
  WEAVER_OA_PUSH = 'WEAVER_OA_PUSH',
  /** 通话回拨统计 */
  CALL_BACK_STATE = 'CALL_BACK_STATE',
  /** 部门通话回拨统计 */
  DEPT_CALL_BACK_STATE = 'DEPT_CALL_BACK_STATE',
}

export type NoticeDTO = {
  id: number;
  read: boolean;
  content: {
    type: NoticeContentTypeEnum;
    operatorName: string;
    customerName: string;
    submitUserName: string;
    interviewVisitRecordId: number;
    clueId: number;
    clueName: string;
    clueCount: number;
    examPaperName: string;
    examOnlineApplyRecordId: number;
    examInterviewEvaluationRecordId: number;
    businessOpportunityName: string;
    businessOpportunityId: number;
    before: string;
    after: string;
    progressName: string;
    contractId: number;
    contractCode: string;
    contractType: ContractTypeEnum;
    fissionApplyId: number;
    fissionApplyCode: string;
    clues?: { id: number; name: string }[];
    directClues?: {
      firstDirectorName: string;
      clueNum: number;
    }[];
    contractRegionChangeId: number;
    contractRegionChangeCode: string;
    contractPostponeId: number;
    contractPostponeCode: string;
  };
  createTime: string;
  notifyType: NoticeTypeEnum;
};

export type GetNoticesReq = {
  pageNum?: number;
  pageSize?: number;
  read?: boolean | null;
  type?: NoticeTypeEnum;
};

export enum TodoBusinessTypeEnum {
  ONLINE_APPLY = 'ONLINE_APPLY',
  INTENTION_CONTRACT = 'SCRM_INTENTION_CONTRACT',
  FORMAL_CONTRACT = 'SCRM_FORMAL_CONTRACT',
  RELOCATION_INTENTION_CONTRACT = 'SCRM_RELOCATION_INTENTION_CONTRACT',
  RELOCATION_FORMAL_CONTRACT = 'SCRM_RELOCATION_FORMAL_CONTRACT',
  RENOVATION_CONTRACT = 'SCRM_RENOVATION_CONTRACT',
  CHANGE_LEGAL_PERSON_CONTRACT = 'SCRM_CHANGE_LEGAL_PERSON_CONTRACT',
  FISSION_APPLY = 'SCRM_FISSION_APPLY',
  INTERVIEW_EVALUATION = 'SCRM_INTERVIEW_EVALUATION',
  INTENTION_CONTRACT_REGION_CHANGE = 'SCRM_INTENTION_CONTRACT_REGION_CHANGE',
  INTENTION_CONTRACT_POSTPONE = 'SCRM_INTENTION_CONTRACT_POSTPONE',
  TRAINING_PERSON_ARRANGEMENT = 'SCRM_TRAINING_PERSON_ARRANGEMENT',
  TRAINING_PERSON_CHANGE = 'SCRM_TRAINING_PERSON_CHANGE',
}

export enum TodoStatusEnum {
  审核中 = 1,
  审核通过,
  审核不通过,
  撤回,
  加签,
  驳回,
}

export enum AuditOperationEnum {
  待审核,
  提交,
  审核通过,
  审核不通过,
  撤回,
  驳回,
  加签,
}

export type TodoDTO = {
  businessId: number;
  businessName: string;
  businessType: TodoBusinessTypeEnum;
  createTime: string;
  createUserName: string;
  id: number;
  name: string;
  status: TodoStatusEnum;
  operationStatus?: AuditOperationEnum;
};

export type FileDTO = {
  fileKey: string;
  fileName: string;
  fileType: string;
  fileUrl: string;
};

export type AuditHistoryType = {
  id: number;
  auditTime: string;
  auditUserName: string;
  operationStatus: AuditOperationEnum;
  name: string;
  auditUserId: number;
  auditUserIds?: number[];
  reason?: string;
  description?: string;
  attachments?: FileDTO[];
  taskDefKey: string;
};

export type DistrictDTO = {
  id: number;
  code: string;
  name: string;
  parentId: number;
  deep: 0 | 1 | 2 | 3;
};

export type MiddlePlatformProvinceCityDTO = {
  id: number;
  province: string;
  city: string;
};

export enum AuditStatusEnum {
  /** 未提交 */
  NOT_SUBMIT = 'NOT_SUBMIT',
  /** 审核中 */
  AUDIT = 'AUDIT',
  /** 通过 */
  PASS = 'PASS',
  /** 不通过 */
  NOT_PASS = 'NOT_PASS',
  /** 不通过 */
  NO_PASS = 'NO_PASS',
  /** 撤回 */
  REVOKE = 'REVOKE',
  /** 驳回 */
  RETURN = 'RETURN',
  /** 待复核 */
  TO_BE_REVIEWED = 'TO_BE_REVIEWED',
}

export type GetETableListReq = {
  pageNum?: number;
  pageSize?: number;
  searchRelation?: SearchRelationEnum;
  fields?: { field: string; condition: ConditionEnum; value: any; valueType: ValueType }[];
};

export type ExportETableListReq = Pick<GetETableListReq, 'searchRelation' | 'fields'> & {
  ids?: number[];
  columns: {
    key: string;
    title: string;
  }[];
};

export enum SensitiveTypeEnum {
  /** 手机号 */
  PHONE = 'PHONE',
  /** 身份证号 */
  ID_CARD = 'ID_CARD',
}

export enum CommonContractNodeEnum {
  /** OA 未发起 */
  OA_NOT_CREATE = 'OA_NOT_CREATE',
  /** 合同审核 */
  CONTRACT_AUDIT = 'CONTRACT_AUDIT',
  /** 待签署 */
  TO_BE_SIGNED = 'TO_BE_SIGNED',
  /** 创建人审核 */
  CREATOR_AUDIT = 'CREATOR_AUDIT',
  /** 塔斯汀用印 */
  TASTIEN_SEAL = 'TASTIEN_SEAL',
  /** 已归档 */
  ARCHIVE = 'ARCHIVE',
}

export type SiteInfoDTO = {
  address: string;
  city: string;
  cityCode: string;
  district: string;
  districtCode: string;
  id: number;
  latitude: number;
  longitude: number;
  name: string;
  processInstanceId: string;
  province: string;
  provinceCode: string;
  shopNo: string;
  siteNo: string;
  deliveryDate: string;
  shopName: string;
  shopAddress: string;
  leaseContractAddress: string;
};

export type AreaCodeDTO = {
  cityCode: string;
  cityName: string;
  provinceCode: string;
  provinceName: string;
  regionCode: string;
  regionName: string;
  streetCode: string;
  streetName: string;
};
