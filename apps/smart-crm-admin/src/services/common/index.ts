import { getToken } from '@src/common/authorization';
import { request, UnauthorizedException } from '@src/common/http';
import {
  ActivityItem,
  AreaCodeDTO,
  BusinessTypeEnum,
  CreateViewReq,
  CurrentUserDTO,
  DistrictDTO,
  FieldTypeEnum,
  GetNoticesReq,
  MiddlePlatformProvinceCityDTO,
  ModifyViewReq,
  NoticeDTO,
  NoticeTypeEnum,
  OssTokenProps,
  SensitiveTypeEnum,
  SiteInfoDTO,
  TodoDTO,
  UserDTO,
  ViewItem,
} from './type';
import { BelongWarZoneEnum } from '../business-opportunity/type';
import { FieldItemType } from '../clues/my/type';
import { IdentityTypeEnum } from '../mini-program/identity/type';

export const getCurrentUserInfo = async (): Promise<CurrentUserDTO> => {
  const token = getToken();

  if (!token) {
    throw new UnauthorizedException('未登录，请先登录');
  }

  const res = await request.get('/zt-api/user/getUserInfo', { useOriginResponse: true });

  return res.data.result;
};

export const getPermissions = async () => {
  const res = await request.post<{ data: { result: string[] } }>('/zt-api/user/getUrlsByBizType', {
    data: { bizType: 12, clientType: 1 },
    useOriginResponse: true,
  });

  return res.data.result;
};

export const logoutCurrentAccount = () => request.post('/zt-api/userlogin/logout');

export const getUsers = (params?: {
  numberLimit?: 'DEFAULT_RULE';
  identityType?: IdentityTypeEnum;
}) =>
  request.get<UserDTO[]>('/api/admin/user/list/simple', {
    params,
  });

export const getOssToken = (userId: number) =>
  request.get<OssTokenProps>('/api/admin/common/sts', { params: { userId } });

export const getOssFileUrl = (key: string) =>
  request.post<{ url: string }>('/api/admin/common/oss/getFileUrl', { data: { key } });

export const getViewList = (code: BusinessTypeEnum) =>
  request.get<ViewItem[]>('/api/admin/viewInfo/list', { params: { code } });

export const createView = (data: CreateViewReq) =>
  request.post('/api/admin/viewInfo/add', { data });

export const deleteView = (id: number) => request.post(`/api/admin/viewInfo/delete/${id}`);

export const modifyView = (data: ModifyViewReq) =>
  request.post('/api/admin/viewInfo/modify', { data });

export const getFieldList = (params?: { fieldType?: FieldTypeEnum }) =>
  request.get<FieldItemType[]>('/api/admin/field/list', { params });

export const getBusinessFieldList = (params?: {
  fieldType?: FieldTypeEnum;
  businessType?: BusinessTypeEnum;
}) => request.get<FieldItemType[]>('/api/admin/field/business/list', { params });

export const deleteCustomField = (id: number) => request.del(`/api/admin/field/custom/field/${id}`);

export const getDistrictList = (params?: { streets?: boolean }) =>
  request.get<DistrictDTO[]>('/api/admin/district/list', { params });

export const getCallPhoneLog = (params: { pageNum: number; pageSize: number; callPhone: string }) =>
  request.get<{ total: number; result: ActivityItem[] }>('/api/admin/call/log/page', { params });

export const getOperationLog = (params: {
  pageNum: number;
  pageSize: number;
  businessId: number;
  businessType:
    | 'CLUE'
    | 'CUSTOMER'
    | 'BUSINESS_OPPORTUNITY'
    | 'PAYMENT'
    | 'FISSION_APPLY'
    | 'FRANCHISE_AREA_QUOTA';
}) =>
  request.get<{ total: number; result: ActivityItem[] }>('/api/admin/operation/log/page', {
    params,
  });

export const getNotices = (data: GetNoticesReq) =>
  request.post<{ total: number; result: NoticeDTO[] }>('/api/admin/notification/page', { data });

export const readNotice = (data: { notificationIds: number[] }) =>
  request.post('/api/admin/notification/read', { data });

export const getNoticeCount = () =>
  request.get<Record<NoticeTypeEnum, number>>('/api/admin/notification/count');

export const readAllNotice = (data: { type: NoticeTypeEnum }) =>
  request.post('/api/admin/notification/all/read', { data });

export const getWarZoneList = () =>
  request.get<{ provinceCode: string; belongWarZone: BelongWarZoneEnum }[]>(
    '/api/admin/war/zone/list',
  );

export const getTodoList = () => request.get<TodoDTO[]>('/api/admin/flow/audit/todo/list');

export const getDoneList = (params: { pageNum: number; pageSize: number }) =>
  request.get<{ total: number; result: TodoDTO[] }>('/api/admin/flow/audit/done/page', { params });

export const getTokenByFeiShuCode = (authorizeCode: string) =>
  request.post<{ token: string }>('/api/admin/fei-shu/authorize/login/getLoginToken', {
    data: {
      authorizeCode,
    },
  });

export const getBranchCompanyList = () =>
  request.get<
    {
      branchCompanyCode: string;
      branchCompanyName: string;
    }[]
  >('/api/admin/war/zone/list/branch-company');

export const getMiddlePlatformProvinceCity = () =>
  request.post<MiddlePlatformProvinceCityDTO[]>('/api/admin/shop/getProvinceCity');

// 解密脱敏
export const decryptSensitive = (params: { encrypted: string; sensitiveType: SensitiveTypeEnum }) =>
  request.get<string>('/api/admin/common/sensitive/decrypt', {
    params,
  });

// 根据铺位编号查询铺位基本信息
export const getSiteInfoBySiteNo = (siteNo: string) => {
  return request.get<SiteInfoDTO>(`/api/admin/contract/common/query-site?siteNo=${siteNo}`);
};

// 根据经纬度查询区域编码
export const getAreaCodeByLatLng = (params: { latitude: number; longitude: number }) => {
  return request.get<AreaCodeDTO>(`/api/admin/district/query-by-coordinates`, { params });
};

// 根据区域编码查询所属乡镇
export const getDistrictByAreaCode = (params: { regionCode: string }) => {
  return request.get<DistrictDTO[]>(`/api/admin/district/query-by-region-code`, { params });
};
