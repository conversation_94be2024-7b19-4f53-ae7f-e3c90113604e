import { request } from '@src/common/http';
import { ExportETableListReq, GetETableListReq } from '@src/services/common/type';
import { CreateRenewalContractReq, RenewalContractDTO, UpdateRenewalContractReq } from './type';

export const getRenewalContractList = (data: GetETableListReq) =>
  request.post<{ total: number; result: RenewalContractDTO[] }>('/api/admin/renew-contract/page', {
    data,
  });

export const getRenewalContractDetail = (id: number) =>
  request.get<RenewalContractDTO>(`/api/admin/renew-contract/detail/${id}`);

export const createRenewalContract = (data: CreateRenewalContractReq) =>
  request.post('/api/admin/renew-contract/save', { data });

export const submitRenewalContract = (data: CreateRenewalContractReq) =>
  request.post('/api/admin/renew-contract/submit', { data });

export const updateRenewalContract = (data: UpdateRenewalContractReq) =>
  request.post('/api/admin/renew-contract/update', { data });

export const exportRenewalContractList = (data: ExportETableListReq) =>
  request.post('/api/admin/renew-contract/export', { data });

export const getRenewalContractOriginalBeginDate = (shopId: string) =>
  request.get<string>(`/api/admin/renew-contract/find-original-contract-begin-date/${shopId}`);

export const deleteRenewalContract = (id: number) =>
  request.del(`/api/admin/renew-contract/delete/${id}`);

export const batchDeleteRenewalContract = (ids: number[]) =>
  request.post('/api/admin/renew-contract/delete/batch', { data: { ids } });

export const downloadRenewalContractExcelTemplate = () =>
  request.get<{ url: string }>('/api/admin/renew-contract/download/template');

export const importRenewalContractExcel = (data: { file: FormData }) =>
  request.post<{ failDownloadUrl: string; failNum: number; successNum: number }>(
    '/api/admin/renew-contract/import/excel',
    {
      data,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );

export const batchPushWeaverRenewalContract = (ids: number[]) =>
  request.post('/api/admin/renew-contract/push/weaver/batch', { data: { ids } });
