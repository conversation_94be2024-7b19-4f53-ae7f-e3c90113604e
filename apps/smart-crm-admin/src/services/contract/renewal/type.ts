import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { DevelopTagEnum } from '../formal/type';

export type RenewalContractDTO = {
  id: number;
  code: string;
  shopId: string;
  shopName: string;
  shopAddress: string;
  contractNode: ContractNodeEnum;
  directUserId: number;
  customerId: number;
  customerName: string;
  customerCode: string;
  signer: string;
  signerPhone: string;
  signerPhoneSensitive: string;
  signerIdentityCard: string;
  signerIdentityCardSensitive: string;
  region: string;
  belongWarZone: BelongWarZoneEnum;
  developTag: DevelopTagEnum;
  licenseSubjectName: string;
  originalContractBeginDate: string;
  originalContractEndDate: string;
  contractBeginDate: string;
  contractEndDate: string;
  shopRealUseArea: number;
  realEstateAddress: string;
  toPublic: boolean;
  signSubject: string;
  socialCreditCode: string;
  updateUserId: number;
  updateTime: string;
  createUserId: number;
  createTime: string;
  attachments?: {
    fileUrl: string;
    fileType: string;
    fileName: string;
  }[];
};

export type CreateRenewalContractReq = {
  shopId: string;
  shopName: string;
  shopAddress: string;
  licenseSubjectName: string;
  originalContractBeginDate: string;
  originalContractEndDate: string;
  contractBeginDate: string;
  contractEndDate: string;
  shopRealUseArea: number;
  realEstateAddress: string;
  customerId: number;
  signer: string;
  signerPhone: string;
  signerIdentityCard: string;
  toPublic: boolean;
  signSubject?: string;
  socialCreditCode?: string;
  region: string;
  belongWarZone: BelongWarZoneEnum;
  developTag: DevelopTagEnum;
};

export type UpdateRenewalContractReq = CreateRenewalContractReq & {
  id: number;
};

export enum ContractNodeEnum {
  OA未发起 = 'OA_NOT_CREATE',
  合同审核 = 'CONTRACT_AUDIT',
  待签署 = 'TO_BE_SIGNED',
  创建人审核 = 'CREATOR_AUDIT',
  塔斯汀用印 = 'TASTIEN_SEAL',
  已归档 = 'ARCHIVE',
}
