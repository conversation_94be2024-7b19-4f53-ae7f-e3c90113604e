import {
  BelongWarZoneEnum,
  BusinessOpportunityTypeEnum,
  OwnerTypeEnum,
} from '@src/services/business-opportunity/type';
import { AuditStatusEnum, FileDTO } from '@src/services/common/type';
import { ContractTypeEnum, PaymentMannerEnum } from '@src/services/payment/type';
import { ExpansionQuotaPositionDTO } from '@src/services/regional/expansion/type';
import { StoreCategoryEnum } from '../formal/type';

export enum IntentionNodeStateEnum {
  台账意向未创建 = 'INTENTION_CONTRACT_TO_BE_CREATED',
  审核中 = 'INTENTION_CONTRACT_AUDIT_APPROVAL_IN_PROGRESS',
  OA未发起 = 'INTENTION_CONTRACT_OA_NOT_INITIATED',
  待签署 = 'INTENTION_CONTRACT_SIGN_TO_BE_COMPLETED',
  已签署 = 'INTENTION_CONTRACT_SIGN_COMPLETED',
  已归档 = 'INTENTION_CONTRACT_ARCHIVED',
}

export enum IntentionContractStatusEnum {
  草稿 = 'DRAFT',
  意向中 = 'IN_INTENTION',
  已失效 = 'INVALID',
  已退款 = 'REFUNDED',
  已转正 = 'FORMALIZED',
  已作废 = 'DISABLED',
}

export type ContractPaymentDTO = {
  id: number;
  paymentAmount: number;
  paymentManner: PaymentMannerEnum;
  paymentTime?: string;
  receivingAccount: string;
  receivingNumber: string;
  openBank: string;
  accountSerialNumber?: string;
  remark?: string;
  attachments?: FileDTO[];
};

export enum ContractChannelTypeEnum {
  '特渠（高校）' = 'SCHOOL',
  '特渠（景区）' = 'SCENIC',
  '特渠（医院）' = 'HOSPITAL',
  '特渠（工业园区 ）' = 'INDUSTRIAL_PARKS',
  '特渠（交通枢纽-火车站）' = 'TRAFFIC_RAILWAY_STATION',
  '特渠（交通枢纽-机场）' = 'TRAFFIC_AIRPORT',
  常规店 = 'COMMON',
}

export type IntentionContractDTO = {
  id: number;
  code: string;
  nodeState: IntentionNodeStateEnum;
  status: IntentionContractStatusEnum;
  directUserId: number;
  consultantUserId: number;
  storeCategory: StoreCategoryEnum;
  customerName: string;
  customerId: number;
  customerCode: string;
  signer: string;
  bizOpportunityId: number;
  bizOpportunityName: string;
  bizOpportunityDirectUserId: number;
  bizOpportunityType: BusinessOpportunityTypeEnum;
  bizOpportunityCode: string;
  region: string;
  belongWarZone: BelongWarZoneEnum;
  intentionPositions?: {
    latitude: number;
    longitude: number;
    name: string;
  }[];
  intentionSignatureDate: string;
  intentionExpirationDate: string;
  effectiveDate: string;
  effectiveDays: number;
  ownerType: OwnerTypeEnum;
  vamFlag: boolean;
  vamExpirationDate: string;
  toPublic: boolean;
  signSubject?: string;
  socialCreditCode?: string;
  signerPhone: string;
  signerPhoneSensitive: string;
  signerIdentityCard: string;
  signerIdentityCardSensitive: string;
  channelType: ContractChannelTypeEnum;
  auditStatus: AuditStatusEnum;
  expansionPosition?: ExpansionQuotaPositionDTO;
  updateTime: string;
  updateUserId: number;
  createTime: string;
  createUserId: number;
  payments?: ContractPaymentDTO[];
  attachments?: {
    fileUrl: string;
    fileType: string;
    fileName: string;
  }[];
};

export type SaveIntentionContractReq = {
  code: string;
  consultantUserId: number;
  storeCategory: StoreCategoryEnum;
  customerId: number;
  signer: string;
  bizOpportunityId: number;
  signerPhone: string;
  signerIdentityCard: string;
  toPublic: boolean;
  signSubject?: string;
  socialCreditCode?: string;
  relocation: boolean;
  originalStoreCode?: string;
  region: string;
  belongWarZone: BelongWarZoneEnum;
  intentionPositions?: {
    latitude: number;
    longitude: number;
    name: string;
  }[];
  expansionPositionId?: number;
  intentionSignatureDate: string;
  effectiveDays: number;
  ownerType: OwnerTypeEnum;
  channelType?: ContractChannelTypeEnum;
  vamFlag: boolean;
  payments: Omit<ContractPaymentDTO, 'accountSerialNumber'>[];
};

export type UpdateIntentionContractReq = {
  id: number;
} & Partial<SaveIntentionContractReq>;

export type PassContractAuditReq = {
  id: number;
  contractType: ContractTypeEnum;
  description: string;
  payments: {
    id: number;
    paymentTime: string;
    accountSerialNumber: string;
    payerAccount: string;
    payerName: string;
    receiptScreenshot: FileDTO[];
  }[];
};
