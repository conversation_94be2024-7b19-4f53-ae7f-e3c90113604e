import { request } from '@src/common/http';
import { AuditHistoryType, ExportETableListReq, GetETableListReq } from '@src/services/common/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import {
  IntentionContractDTO,
  IntentionContractStatusEnum,
  PassContractAuditReq,
  SaveIntentionContractReq,
  UpdateIntentionContractReq,
} from './type';
import { StoreCategoryEnum } from '../formal/type';

export const getIntentionContractList = (data: GetETableListReq) =>
  request.post<{ total: number; result: IntentionContractDTO[] }>(
    '/api/admin/contract/intention/page',
    { data },
  );

export const saveIntentionContract = (data: SaveIntentionContractReq) =>
  request.post('/api/admin/contract/intention/draft', { data });

export const saveAndSubmitIntentionContract = (data: SaveIntentionContractReq) =>
  request.post('/api/admin/contract/intention/save', { data });

export const getIntentionContractDetail = (id: number) =>
  request.get<IntentionContractDTO>(`/api/admin/contract/intention/detail/${id}`);

export const exportIntentionContract = (data: ExportETableListReq) =>
  request.post('/api/admin/contract/intention/export', { data });

export const updateIntentionContract = (data: UpdateIntentionContractReq) =>
  request.put('/api/admin/contract/intention/update', { data });

export const batchDeleteIntentionContract = (ids: number[]) =>
  request.del('/api/admin/contract/intention/delete', { data: { ids } });

export const getContractAuditHistory = (params: { id: number; contractType: ContractTypeEnum }) =>
  request.get<AuditHistoryType[]>('/api/admin/contract/common/audit/history', {
    params,
  });

export const revokeContractAudit = (data: { id: number; contractType: ContractTypeEnum }) =>
  request.post('/api/admin/contract/common/cancel/process/instance', { data });

export const passContractAudit = (data: PassContractAuditReq) =>
  request.post('/api/admin/contract/common/approve/task', { data });

export const rejectContractAudit = (data: {
  id: number;
  contractType: ContractTypeEnum;
  description: string;
}) => request.post('/api/admin/contract/common/reject/task', { data });

export const submitIntentionAudit = (id: number) =>
  request.get(`/api/admin/contract/intention/${id}/submit`);

export const updateIntentionStatus = (data: {
  ids: number[];
  status: IntentionContractStatusEnum;
}) => request.post('/api/admin/contract/intention/update/status', { data });

export const generateIntentionCode = (params: { storeCategory: StoreCategoryEnum }) =>
  request.get<{ code: string }>('/api/admin/contract/intention/generate/code', { params });
