import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { AuditStatusEnum, CommonContractNodeEnum } from '@src/services/common/type';
import { TrainingPersonsDTO } from '@src/services/process/train-staff/type';
import { DevelopTagEnum, StoreCategoryEnum } from '../formal/type';
import { ContractPaymentDTO } from '../intention/type';

export enum RelocationFormalStatusEnum {
  未生效 = 'NOT_EFFECTIVE',
  生效中 = 'EFFECTIVE',
  已到期 = 'EXPIRED',
  已解除 = 'RELEASED',
}

export type RelocationFormalDTO = {
  id: number;
  code: string;
  node: CommonContractNodeEnum;
  status: RelocationFormalStatusEnum;
  auditStatus: AuditStatusEnum;
  originalShopId: string;
  originalShopName: string;
  brandAuthEndDate: string;
  directUserId: number;
  storeCategory: StoreCategoryEnum;
  customerId: number;
  customerName: string;
  customerCode: string;
  intentionCode: string;
  intentionId: number;
  consultantUserId: number;
  signer: string;
  signerPhone: string;
  signerPhoneSensitive: string;
  signerIdentityCard: string;
  signerIdentityCardSensitive: string;
  toPublic: boolean;
  signSubject: string;
  socialCreditCode: string;
  paymentDate: string;
  effectiveDate: string;
  expirationDate: string;
  storeDeliveryDate: string;
  storeCode: string;
  storeName: string;
  storeAddress: string;
  region: string;
  belongWarZone: BelongWarZoneEnum;
  developTag: DevelopTagEnum;
  siteFlowId: string;
  payments?: ContractPaymentDTO[];
  updateUserId: number;
  updateTime: string;
  createUserId: number;
  createTime: string;
  siteNo: string;
  storeCoordinates?: {
    longitude: string;
    latitude: string;
    name: string;
  };
  attachments?: {
    fileUrl: string;
    fileType: string;
    fileName: string;
  }[];
  trainingPersons?: TrainingPersonsDTO[];
};

export type SaveRelocationFormalReq = Pick<
  RelocationFormalDTO,
  | 'originalShopId'
  | 'originalShopName'
  | 'brandAuthEndDate'
  | 'storeCategory'
  | 'customerId'
  | 'intentionId'
  | 'consultantUserId'
  | 'signer'
  | 'signerPhone'
  | 'signerIdentityCard'
  | 'toPublic'
  | 'signSubject'
  | 'socialCreditCode'
  | 'paymentDate'
  | 'storeDeliveryDate'
  | 'storeName'
  | 'storeAddress'
  | 'region'
  | 'belongWarZone'
  | 'developTag'
  | 'siteFlowId'
> & {
  payments: Omit<ContractPaymentDTO, 'accountSerialNumber'>[];
};

export type UpdateRelocationFormalReq = SaveRelocationFormalReq & {
  id: number;
};
