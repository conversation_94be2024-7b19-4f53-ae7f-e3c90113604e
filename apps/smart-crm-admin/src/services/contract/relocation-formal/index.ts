import { request } from '@src/common/http';
import { ExportETableListReq, GetETableListReq } from '@src/services/common/type';
import { RelocationFormalDTO, SaveRelocationFormalReq, UpdateRelocationFormalReq } from './type';

export const getRelocationFormalList = (data: GetETableListReq) =>
  request.post<{ total: number; result: RelocationFormalDTO[] }>(
    '/api/admin/contract/relocation/formal/page',
    { data },
  );

export const getRelocationFormalDetail = (id: number) =>
  request.get<RelocationFormalDTO>(`/api/admin/contract/relocation/formal/${id}/detail`);

export const exportRelocationFormal = (data: ExportETableListReq) =>
  request.post('/api/admin/contract/relocation/formal/export', { data });

export const batchDeleteRelocationFormal = (ids: number[]) =>
  request.del('/api/admin/contract/relocation/formal/delete', { data: { ids } });

export const submitRelocationFormalAudit = (id: number) =>
  request.get(`/api/admin/contract/relocation/formal/${id}/submit`);

export const saveRelocationFormal = (data: SaveRelocationFormalReq) =>
  request.post('/api/admin/contract/relocation/formal/save', { data });

export const saveAndSubmitRelocationFormal = (data: SaveRelocationFormalReq) =>
  request.post('/api/admin/contract/relocation/formal/saveAndSubmit', { data });

export const updateRelocationFormal = (data: UpdateRelocationFormalReq) =>
  request.put('/api/admin/contract/relocation/formal/update', { data });
