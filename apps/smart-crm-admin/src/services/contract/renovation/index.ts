import { request } from '@src/common/http';
import { ExportETableListReq, GetETableListReq } from '@src/services/common/type';
import {
  RenovationContractDTO,
  SaveRenovationContractReq,
  UpdateRenovationContractReq,
} from './type';

export const getRenovationContractList = (data: GetETableListReq) =>
  request.post<{ total: number; result: RenovationContractDTO[] }>(
    '/api/admin/renovation-contract/page',
    { data },
  );

export const exportRenovationContract = (data: ExportETableListReq) =>
  request.post('/api/admin/renovation-contract/export', { data });

export const getRenovationContractDetail = (id: number) =>
  request.get<RenovationContractDTO>(`/api/admin/renovation-contract/${id}/detail`);

export const batchDeleteRenovationContract = (ids: number[]) =>
  request.del('/api/admin/renovation-contract/delete', { data: { ids } });

export const saveRenovationContract = (data: SaveRenovationContractReq) =>
  request.post('/api/admin/renovation-contract/save', { data });

export const saveAndSubmitRenovationContract = (data: SaveRenovationContractReq) =>
  request.post('/api/admin/renovation-contract/saveAndSubmit', { data });

export const updateRenovationContract = (data: UpdateRenovationContractReq) =>
  request.put('/api/admin/renovation-contract/update', { data });

export const submitRenovationContractAudit = (id: number) =>
  request.get(`/api/admin/renovation-contract/${id}/submit`);
