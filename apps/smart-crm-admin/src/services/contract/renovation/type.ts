import { AuditStatusEnum, CommonContractNodeEnum } from '@src/services/common/type';
import { ContractPaymentDTO } from '../intention/type';

export enum RenovationContractStatusEnum {
  未生效 = 'NOT_EFFECTIVE',
  生效中 = 'EFFECTIVE',
  已到期 = 'EXPIRED',
  已解除 = 'RELEASED',
}

export type RenovationContractDTO = {
  id: number;
  code: string;
  auditStatus: AuditStatusEnum;
  node: CommonContractNodeEnum;
  status: RenovationContractStatusEnum;
  shopId: string;
  shopName: string;
  customerId: number;
  customerName: string;
  customerCode: string;
  signer: string;
  licenseSubjectName: string;
  shopRealUseArea: number;
  realEstateAddress: string;
  phone: string;
  phoneSensitive: string;
  identityCard: string;
  identityCardSensitive: string;
  toPublic: boolean;
  signSubject: string;
  socialCreditCode: string;
  effectiveDate: string;
  expirationDate: string;
  payments?: ContractPaymentDTO[];
  updateUserId: number;
  updateTime: string;
  createUserId: number;
  createTime: string;
  attachments?: {
    fileUrl: string;
    fileType: string;
    fileName: string;
  }[];
  renovationType: RenovationTypeEnum;
  renovationScope: RenovationScopeEnum[];
};

export enum RenovationScopeEnum {
  外立面翻新 = 'ALL_EXTERNAL_WALL',
  客区全翻 = 'ALL_GUEST_AREA',
  厨房全翻 = 'ALL_KITCHEN',
  厨房局翻 = 'PART_KITCHEN',
}

export enum RenovationTypeEnum {
  整店全部翻新 = 'ALL',
  局部翻新 = 'PART',
}

export type SaveRenovationContractReq = Pick<
  RenovationContractDTO,
  | 'shopId'
  | 'shopName'
  | 'customerId'
  | 'signer'
  | 'licenseSubjectName'
  | 'shopRealUseArea'
  | 'realEstateAddress'
  | 'phone'
  | 'identityCard'
  | 'toPublic'
  | 'signSubject'
  | 'socialCreditCode'
> & {
  payments: Omit<ContractPaymentDTO, 'accountSerialNumber'>[];
};

export type UpdateRenovationContractReq = SaveRenovationContractReq & { id: number };
