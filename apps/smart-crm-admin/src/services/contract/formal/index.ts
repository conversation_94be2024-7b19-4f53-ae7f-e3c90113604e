import { request } from '@src/common/http';
import { ExportETableListReq, GetETableListReq } from '@src/services/common/type';
import {
  BindableFormalDTO,
  CheckContractStoreCategoryReq,
  CheckContractStoreCategoryRes,
  FormalContractDTO,
  SaveFormalContractReq,
  StoreCategoryEnum,
  UpdateFormalContractReq,
} from './type';

export const getFormalContractList = (data: GetETableListReq) =>
  request.post<{ total: number; result: FormalContractDTO[] }>('/api/admin/contract/formal/page', {
    data,
  });

export const exportFormalContract = (data: ExportETableListReq) =>
  request.post('/api/admin/contract/formal/export', { data });

export const saveFormalContract = (data: SaveFormalContractReq) =>
  request.post('/api/admin/contract/formal/draft', { data });

export const saveAndSubmitFormalContract = (data: SaveFormalContractReq) =>
  request.post('/api/admin/contract/formal/save', { data });

export const updateFormalContract = (data: UpdateFormalContractReq) =>
  request.put('/api/admin/contract/formal/update', { data });

export const batchDeleteFormalContract = (ids: number[]) =>
  request.del('/api/admin/contract/formal/delete', { data: { ids } });

export const getFormalContractDetail = (id: number) =>
  request.get<FormalContractDTO>(`/api/admin/contract/formal/detail/${id}`);

export const submitFormalAudit = (id: number) =>
  request.get(`/api/admin/contract/formal/${id}/submit`);

export const getBindableFormalList = (params: { customerId: number }) =>
  request.get<BindableFormalDTO[]>('/api/admin/contract/formal/list/bindable', {
    params,
  });

export const generateFormalCode = (params: {
  storeCategory: StoreCategoryEnum;
  provinceCode: string;
}) => request.get<{ code: string }>('/api/admin/contract/formal/generate/code', { params });

export const checkContractStoreCategory = (data: CheckContractStoreCategoryReq) =>
  request.post<CheckContractStoreCategoryRes>(
    '/api/admin/contract/common/check/contract-store-category',
    { data },
  );
