import { BelongWarZoneEnum, OwnerTypeEnum } from '@src/services/business-opportunity/type';
import { AuditStatusEnum } from '@src/services/common/type';
import { TrainingPersonsDTO } from '@src/services/process/train-staff/type';
import { JoinRegionEnum } from '@src/services/regional/quota/type';
import { ContractPaymentDTO } from '../intention/type';

export enum StoreCategoryEnum {
  '加盟 M' = 'FRANCHISE_M',
  '加盟 T' = 'FRANCHISE_T',
}

export enum DevelopTagEnum {
  常规店 = 'ROUTINE',
  '校园-校内' = 'SCHOOL_INTERNAL',
  '校园-校外' = 'SCHOOL_EXTERNAL',
  '校园-非高校' = 'SCHOOL_NON_HIGH_SCHOOL',
  '景区-景区内' = 'SCENIC_SPOT_INTERNAL',
  '景区-景区外' = 'SCENIC_SPOT_EXTERNAL',
  商场内 = 'MARKET_INTERNAL',
  商场外 = 'MARKET_EXTERNAL',
  火车站内 = 'TRAIN_STATION_INTERNAL',
  火车站外 = 'TRAIN_STATION_EXTERNAL',
  高铁站内 = 'HIGH_SPEED_RAIL_STATION_INTERNAL',
  高铁站外 = 'HIGH_SPEED_RAIL_STATION_EXTERNAL',
  机场内 = 'AIRPORT_INTERNAL',
  机场外 = 'AIRPORT_EXTERNAL',
  医院内 = 'HOSPITAL_INTERNAL',
  医院外 = 'HOSPITAL_EXTERNAL',
  社会店 = 'SOCIETY',
}

export enum StoreStatusEnum {
  暂停营业 = 'PAUSE_BUSINESS',
  意向区域变更 = 'CHANGE_INTENTION_REGION',
  更改冠名 = 'CHANGE_TITLE',
  更改地址 = 'CHANGE_ADDRESS',
  内部变更 = 'INTERNAL_CHANGE',
  门店迁址 = 'MOVE_OUT',
  '门店迁出【搬迁闭店】' = 'MOVE_OUT_TRANSFER',
  门店转让 = 'TRANSFER',
  闭店 = 'CLOSE',
  '门店属性变更(M转T)' = 'CHANGE_STORE_CATEGORY_M_TO_T',
  废弃编号 = 'ABANDON_CODE',
  '门店属性变更(T转M)' = 'CHANGE_STORE_CATEGORY_T_TO_M',
  三者一致 = 'THREE_CONSISTENT',
  品牌取消授权 = 'CANCEL_BRAND_AUTHORIZATION',
  号码变更 = 'CHANGE_CODE',
  编号互换 = 'CHANGE_CODE_EXCHANGE',
}

export type FormalContractDTO = {
  id: number;
  code: string;
  consultantUserId: number;
  auditStatus: AuditStatusEnum;
  nodeState: FormalNodeStateEnum;
  storeCategory: StoreCategoryEnum;
  signer: string;
  customerName: string;
  customerId: number;
  customerCode: string;
  directUserId: number;
  ownerType: OwnerTypeEnum;
  bizOpportunityName: string;
  bizOpportunityId: number;
  bizOpportunityDirectUserId: number;
  bizOpportunityDirectUserName: string;
  bizOpportunityCode: string;
  intentionContractId?: number;
  intentionContractCode?: string;
  vamFlag: boolean;
  signerPhone: string;
  signerPhoneSensitive: string;
  signerIdentityCard: string;
  signerIdentityCardSensitive: string;
  toPublic: boolean;
  signSubject: string;
  socialCreditCode: string;
  paymentDate: string;
  effectiveDate: string;
  formalExpirationDate: string;
  storeDeliveryDate: string;
  storeCode: string;
  storeName: string;
  region: string;
  belongWarZone: BelongWarZoneEnum;
  developTag: string;
  createUserId: number;
  createTime: string;
  updateUserId: number;
  updateTime: string;
  siteFlowId: string;
  payments?: ContractPaymentDTO[];
  storeStatus?: StoreStatusEnum[];
  storeAddress: string;
  siteNo: string;
  storeCoordinates?: {
    longitude: string;
    latitude: string;
    name: string;
  };
  attachments?: {
    fileUrl: string;
    fileType: string;
    fileName: string;
  }[];
  trainingPersons?: TrainingPersonsDTO[];
};

export enum FormalNodeStateEnum {
  台账正式未创建 = 'FORMAL_CONTRACT_TO_BE_CREATED',
  打款审核中 = 'FORMAL_CONTRACT_AUDIT_APPROVAL_IN_PROGRESS',
  OA未发起 = 'FORMAL_CONTRACT_OA_NOT_INITIATED',
  待签署 = 'FORMAL_CONTRACT_SIGN_TO_BE_COMPLETED',
  已签署 = 'FORMAL_CONTRACT_SIGN_COMPLETED',
  已归档 = 'FORMAL_CONTRACT_ARCHIVED',
}

export type SaveFormalContractReq = {
  code: string;
  storeCategory: StoreCategoryEnum;
  consultantUserId: number;
  customerId: number;
  signer: string;
  bizOpportunityId: number;
  intentionContractId?: number;
  vamFlag: boolean;
  ownerType: OwnerTypeEnum;
  paymentDate: string;
  storeDeliveryDate: string;
  storeName: string;
  region: string;
  belongWarZone: BelongWarZoneEnum;
  developTag: string;
  siteFlowId: string;
  storeAddress: string;
  signerPhone: string;
  signerIdentityCard: string;
  toPublic: boolean;
  signSubject: string;
  socialCreditCode: string;
  payments: Omit<ContractPaymentDTO, 'accountSerialNumber'>[];
};

export type UpdateFormalContractReq = {
  id: number;
  storeStatus?: StoreStatusEnum[];
} & Partial<SaveFormalContractReq>;

export type BindableFormalDTO = {
  id: number;
  code: string;
  bizOpportunityId?: number;
  bizOpportunityName?: string;
  relocation: boolean;
  vamFlag: boolean;
  originalStoreCode?: string;
  consultantUserId?: number;
  intentionRegion: string;
};

export type CheckContractStoreCategoryReq = {
  customerId: string;
  contractType: string;
  relevanceCode?: string;
  regionCode: string;
  storeCategory: StoreCategoryEnum;
};

export type CheckContractStoreCategoryRes = {
  success: boolean;
  contractStoreCategory: StoreCategoryEnum;
  relevanceStoreCategory: StoreCategoryEnum;
  customerNature: StoreCategoryEnum;
  joinRegion: JoinRegionEnum;
};
