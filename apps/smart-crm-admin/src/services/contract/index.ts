import { request } from '@src/common/http';

export enum ContractEnum {
  意向合同 = 'INTENTION_CONTRACT',
  正式合同 = 'FORMAL_CONTRACT',
  正式搬迁合同 = 'RELOCATION_FORMAL_CONTRACT',
  意向搬迁合同 = 'RELOCATION_INTENTION_CONTRACT',
  续约合同 = 'RENEW_CONTRACT',
  重装合同 = 'RENOVATION_CONTRACT',
  变更法人合同 = 'CHANGE_LEGAL_PERSON_CONTRACT',
}

type UpContractFileReq = {
  attachments?: { fileKey: string; fileName: string; fileType: string }[];
  contractId: number;
  type: ContractEnum;
};

export const addContractFile = (data: UpContractFileReq) =>
  request.post('/api/admin/contract/common/upload/attachment', { data });

export const delContractFile = (id: number | string) =>
  request.del(`/api/admin/contract/common/remove/attachment/${id}`);
