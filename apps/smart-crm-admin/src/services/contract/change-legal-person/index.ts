import { request } from '@src/common/http';
import { ExportETableListReq, GetETableListReq } from '@src/services/common/type';
import {
  ChangeLegalPersonContractDTO,
  GetOriginalShopInfoByShopIdRes,
  SaveChangeLegalPersonContractReq,
  UpdateChangeLegalPersonContractReq,
} from './type';

export const getChangeLegalPersonContractList = (data: GetETableListReq) =>
  request.post<{ total: number; result: ChangeLegalPersonContractDTO[] }>(
    '/api/admin/contract/change-legal-person/page',
    { data },
  );

export const batchDeleteChangeLegalPersonContract = (ids: number[]) =>
  request.del('/api/admin/contract/change-legal-person/delete', { data: { ids } });

export const exportChangeLegalPersonContract = (data: ExportETableListReq) =>
  request.post('/api/admin/contract/change-legal-person/export', { data });

export const getChangeLegalPersonContractDetail = (id: number) =>
  request.get<ChangeLegalPersonContractDTO>(`/api/admin/contract/change-legal-person/${id}/detail`);

export const saveChangeLegalPersonContract = (data: SaveChangeLegalPersonContractReq) =>
  request.post('/api/admin/contract/change-legal-person/save', { data });

export const saveAndSubmitChangeLegalPersonContract = (data: SaveChangeLegalPersonContractReq) =>
  request.post('/api/admin/contract/change-legal-person/saveAndSubmit', { data });

export const updateChangeLegalPersonContract = (data: UpdateChangeLegalPersonContractReq) =>
  request.put('/api/admin/contract/change-legal-person/update', { data });

export const submitChangeLegalPersonContractAudit = (id: number) =>
  request.get(`/api/admin/contract/change-legal-person/${id}/submit`);

export const getOriginalShopInfoByShopId = (shopId: string) =>
  request.get<GetOriginalShopInfoByShopIdRes>(
    '/api/admin/contract/common/find/original-contract/latest',
    { params: { shopId } },
  );
