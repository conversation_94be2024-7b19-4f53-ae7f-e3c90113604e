import { AuditStatusEnum, CommonContractNodeEnum } from '@src/services/common/type';
import { ContractPaymentDTO } from '../intention/type';

export enum ChangeTypeEnum {
  内部变更 = 'INTERNAL',
  门店转让 = 'TRANSFER',
}

export enum ChangeLegalPersonContractStatusEnum {
  未生效 = 'NOT_EFFECTIVE',
  生效中 = 'EFFECTIVE',
  已到期 = 'EXPIRED',
  已解除 = 'RELEASED',
}

export type ChangeLegalPersonContractDTO = {
  id: number;
  auditStatus: AuditStatusEnum;
  node: CommonContractNodeEnum;
  status: ChangeLegalPersonContractStatusEnum;
  code: string;
  shopId: string;
  shopName: string;
  licenseSubjectName: string;
  shopRealUseArea: number;
  realEstateAddress: string;
  originalEffectiveDate: string;
  originalExpirationDate: string;
  originalToPublic: boolean;
  originalSignSubject: string;
  originalSocialCreditCode: string;
  originalCustomerId: number;
  originalCustomerName: string;
  originalPhone: string;
  originalPhoneSensitive: string;
  originalIdentityCard: string;
  originalIdentityCardSensitive: string;
  toPublic: boolean;
  signSubject: string;
  socialCreditCode: string;
  customerId: number;
  customerName: string;
  phone: string;
  phoneSensitive: string;
  identityCard: string;
  identityCardSensitive: string;
  changeType: ChangeTypeEnum;
  createUserId: number;
  createTime: string;
  updateUserId: number;
  updateTime: string;
  payments?: ContractPaymentDTO[];
  attachments?: {
    fileUrl: string;
    fileType: string;
    fileName: string;
  }[];
};

export type SaveChangeLegalPersonContractReq = Pick<
  ChangeLegalPersonContractDTO,
  | 'shopId'
  | 'shopName'
  | 'licenseSubjectName'
  | 'shopRealUseArea'
  | 'realEstateAddress'
  | 'originalEffectiveDate'
  | 'originalExpirationDate'
  | 'originalToPublic'
  | 'originalSignSubject'
  | 'originalSocialCreditCode'
  | 'originalCustomerId'
  | 'originalPhone'
  | 'originalIdentityCard'
  | 'toPublic'
  | 'signSubject'
  | 'socialCreditCode'
  | 'customerId'
  | 'phone'
  | 'identityCard'
  | 'changeType'
> & {
  payments: Omit<ContractPaymentDTO, 'accountSerialNumber'>[];
};

export type UpdateChangeLegalPersonContractReq = SaveChangeLegalPersonContractReq & {
  id: number;
};

export type GetOriginalShopInfoByShopIdRes = {
  originalCustomerId: number;
  originalCustomerName: string;
  originalEffectiveDate: string;
  originalExpirationDate: string;
  originalIdentityCard: string;
  originalPhone: string;
  originalPhoneSensitive: string;
  originalIdentityCardSensitive: string;
};
