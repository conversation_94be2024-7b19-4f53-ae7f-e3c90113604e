import { request } from '@src/common/http';
import { ExportETableListReq, GetETableListReq } from '@src/services/common/type';
import {
  RelocationIntentionDTO,
  RelocationIntentionStatusEnum,
  SaveRelocationIntentionReq,
  UpdateRelocationIntentionReq,
} from './type';

export const getRelocationIntentionList = (data: GetETableListReq) =>
  request.post<{ result: RelocationIntentionDTO[]; total: number }>(
    '/api/admin/contract/relocation/intention/page',
    {
      data,
    },
  );

export const exportRelocationIntention = (data: ExportETableListReq) =>
  request.post('/api/admin/contract/relocation/intention/export', { data });

export const getRelocationIntentionDetail = (id: number) =>
  request.get<RelocationIntentionDTO>(`/api/admin/contract/relocation/intention/${id}/detail`);

export const saveRelocationIntention = (data: SaveRelocationIntentionReq) =>
  request.post('/api/admin/contract/relocation/intention/save', { data });

export const saveAndSubmitRelocationIntention = (data: SaveRelocationIntentionReq) =>
  request.post('/api/admin/contract/relocation/intention/saveAndSubmit', { data });

export const updateRelocationIntention = (data: UpdateRelocationIntentionReq) =>
  request.put('/api/admin/contract/relocation/intention/update', { data });

export const batchDeleteRelocationIntention = (ids: number[]) =>
  request.del('/api/admin/contract/relocation/intention/delete', { data: { ids } });

export const submitRelocationIntentionAudit = (id: number) =>
  request.get(`/api/admin/contract/relocation/intention/${id}/submit`);

export const updateRelocationIntentionStatus = (data: {
  ids: number[];
  status: RelocationIntentionStatusEnum;
}) => request.post('/api/admin/contract/relocation/intention/update/status', { data });

export const getRelocationIntentionByShopId = (shopId: string) =>
  request.get<RelocationIntentionDTO[]>(
    `/api/admin/contract/relocation/formal/find/intention/${shopId}`,
  );
