import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { AuditStatusEnum, CommonContractNodeEnum } from '@src/services/common/type';
import { StoreCategoryEnum } from '../formal/type';
import { ContractChannelTypeEnum, ContractPaymentDTO } from '../intention/type';

export enum RelocationIntentionStatusEnum {
  未生效 = 'NOT_EFFECTIVE',
  意向中 = 'IN_INTENTION',
  已经迁址 = 'ALREADY_MOVED',
  已到期 = 'EXPIRED',
  已作废需退款 = 'INVALID_REFUND',
  已作废不退款 = 'INVALID_NO_REFUND',
  已退款 = 'REFUNDED',
}

export type RelocationIntentionDTO = {
  id: number;
  auditStatus: AuditStatusEnum;
  node: CommonContractNodeEnum;
  status: RelocationIntentionStatusEnum;
  code: string;
  originalShopId: string;
  originalShopName: string;
  directUserId: number;
  storeCategory: StoreCategoryEnum;
  customerId: number;
  customerName: string;
  customerCode: string;
  consultantUserId: number;
  signer: string;
  signerPhone: string;
  signerPhoneSensitive: string;
  signerIdentityCard: string;
  signerIdentityCardSensitive: string;
  toPublic: boolean;
  signSubject: string;
  socialCreditCode: string;
  signatureDate: string;
  effectiveDays: number;
  effectiveDate: string;
  expirationDate: string;
  channelType: ContractChannelTypeEnum;
  region: string;
  belongWarZone: BelongWarZoneEnum;
  positions: {
    latitude: number;
    longitude: number;
    name: string;
  }[];
  payments?: ContractPaymentDTO[];
  createUserId: number;
  createTime: string;
  updateUserId: number;
  updateTime: string;
  attachments?: {
    fileUrl: string;
    fileType: string;
    fileName: string;
  }[];
};

export type SaveRelocationIntentionReq = Pick<
  RelocationIntentionDTO,
  | 'originalShopId'
  | 'originalShopName'
  | 'storeCategory'
  | 'customerId'
  | 'channelType'
  | 'consultantUserId'
  | 'signer'
  | 'signerPhone'
  | 'signerIdentityCard'
  | 'signatureDate'
  | 'toPublic'
  | 'signSubject'
  | 'socialCreditCode'
  | 'effectiveDays'
  | 'region'
  | 'belongWarZone'
  | 'positions'
> & {
  payments: Omit<ContractPaymentDTO, 'accountSerialNumber'>[];
};

export type UpdateRelocationIntentionReq = SaveRelocationIntentionReq & { id: number };
