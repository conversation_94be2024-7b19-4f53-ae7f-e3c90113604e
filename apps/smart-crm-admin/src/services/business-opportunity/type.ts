import { FormalNodeStateEnum, StoreCategoryEnum } from '../contract/formal/type';
import { IntentionNodeStateEnum } from '../contract/intention/type';

export type GetBusinessOpportunityListReq = {
  pageNum?: number;
  pageSize?: number;
  code?: string;
  name?: string;
  customerName?: string;
  directUserId?: number;
  businessOpportunityType?: BusinessOpportunityTypeEnum;
  progressStates?: ProgressStateEnum[];
};

export type ExportBusinessOpportunityReq = Omit<
  GetBusinessOpportunityListReq,
  'pageNum' | 'pageSize'
> & {
  ids?: number[];
};

export enum BusinessOpportunityTypeEnum {
  /** 社会店 */
  SOCIETY = 'SOCIETY',
  /** 渠道店 */
  CHANNEL = 'CHANNEL',
  /** 裂变社会店 */
  FISSION_SOCIETY = 'FISSION_SOCIETY',
  /** 裂变特渠店 */
  FISSION_CHANNEL = 'FISSION_CHANNEL',
}

export enum ProgressNodeEnum {
  意向收集 = 'FILL_BASIC_INFORMATION',
  加盟申请收集 = 'FRANCHISE_APPLICATION',
  加盟申请审核 = 'FRANCHISE_APPLICATION_AUDIT',
  预约面谈 = 'SCHEDULE_INTERVIEW',
  面谈审核 = 'INTERVIEW_AUDIT',
  意向合同 = 'INTENTION_CONTRACT',
  流程结束 = 'PROCESS_END',
  裂变申请 = 'FISSION_OA_AUDIT',
}

export enum ProgressStateEnum {
  // 意向收集节点的状态
  '意向收集：已填写' = 'INTENTION_COLLECTION_FILLED',

  // 加盟申请节点的状态
  '加盟申请：待填写' = 'FRANCHISE_APPLICATION_TO_BE_FILLED',
  '加盟申请：已填写' = 'FRANCHISE_APPLICATION_FILLED',

  // 加盟申请审核节点状态
  '加盟审核：审批中' = 'FRANCHISE_APPLICATION_AUDIT_APPROVAL_IN_PROGRESS',
  '加盟审核：通过' = 'FRANCHISE_APPLICATION_AUDIT_APPROVAL_PASS',
  '加盟审核：复核中' = 'FRANCHISE_APPLICATION_AUDIT_REVIEW_IN_PROGRESS',
  '加盟审核：未通过' = 'FRANCHISE_APPLICATION_AUDIT_APPROVAL_NO_PASS',

  // 面审预约节点的状态
  '预约面审：待预约' = 'SCHEDULE_INTERVIEW_TO_BE_APPOINTED',
  '预约面审：已预约' = 'SCHEDULE_INTERVIEW_APPOINTED',
  '预约面审：取消预约' = 'SCHEDULE_INTERVIEW_APPOINTMENT_CANCEL',

  // 面审审核节点的状态
  '面审审核：未创建' = 'INTERVIEW_AUDIT_NOT_CREATED',
  '面审审核：审批中' = 'INTERVIEW_AUDIT_APPROVAL_IN_PROGRESS',
  '面审审核：审批通过' = 'INTERVIEW_AUDIT_APPROVAL_PASS',
  '面审审核：审批不通过' = 'INTERVIEW_AUDIT_APPROVAL_NO_PASS',

  // 意向合同节点的状态
  '意向合同：台账意向未创建' = 'INTENTION_CONTRACT_TO_BE_CREATED',
  '意向合同：意向签约中' = 'INTENTION_CONTRACT_SIGNING',
  '意向合同：取消意向签约' = 'INTENTION_CONTRACT_CANCEL_SIGNING',
  '意向合同：已归档' = 'INTENTION_CONTRACT_ARCHIVED',

  // 流程结束的状态
  '流程结束：已签约' = 'PROCESS_END_SIGNED',
  '流程结束：已放弃' = 'PROCESS_END_ABANDONED',

  // 裂变申请节点的状态
  '裂变OA审批：未发起' = 'FISSION_OA_APPROVAL_NOT_INITIATED',
  '裂变OA审批：审批中' = 'FISSION_OA_APPROVAL_AUDITING',
  '裂变OA审批：未通过' = 'FISSION_OA_APPROVAL_NO_PASS',
  '裂变OA审批：已归档' = 'FISSION_OA_APPROVAL_ARCHIVE',
}

export type BusinessOpportunityDTO = {
  id: number;
  code: string;
  name: string;
  customerName: string;
  customerId: number;
  customerNature: StoreCategoryEnum;
  directUserId: number;
  businessOpportunityType: BusinessOpportunityTypeEnum;
  progressNode: ProgressNodeEnum;
  progressState: ProgressStateEnum;
  createTime: string;
  createUserId: number;
  updateUserId: number;
  updateTime: string;
  directTime: string;
  phone: string;
  phoneSensitive: string;
  ownerType?: OwnerTypeEnum;
  customerDesc?: string;
  intentionRegion: string;
  belongWarZone: BelongWarZoneEnum;
  abandonReason: BusinessOpportunityAbandonReasonEnum;
  abandonDesc?: string;
  channelTypes?: ChannelTypeEnum[];
  intentionPositions?: {
    latitude: number;
    longitude: number;
    name: string;
  }[];
};

export enum OwnerTypeEnum {
  新业主 = 'NEW_OWNER',
  老业主 = 'OLD_OWNER',
}

export enum ChannelTypeEnum {
  '特渠(高校)' = 'SCHOOL',
  '特渠(景区)' = 'SCENIC',
  '特渠(交通枢纽-火车站)' = 'TRAFFIC_RAILWAY_STATION',
  '特渠(交通枢纽-机场)' = 'TRAFFIC_AIRPORT',
  '特渠(医院)' = 'HOSPITAL',
  '特渠(工业园区)' = 'INDUSTRIAL_PARKS',
}

export enum BelongWarZoneEnum {
  东部大区 = 'EAST_WAR_ZONE',
  西部大区 = 'WEST_WAR_ZONE',
  总部大区 = 'HEADQUARTERS_WAR_ZONE',
  中部大区 = 'CENTRAL_SECTION_WAR_ZONE',
  南部大区 = 'SOUTH_WAR_ZONE',
  北部大区 = 'NORTH_WAR_ZONE',
  暂未进驻 = 'NOT_YET_STATIONED',
}

export type CreateBusinessOpportunityReq = {
  name: string;
  phone: string;
  directUserId: number;
  customerId: number;
  businessOpportunityType: BusinessOpportunityTypeEnum;
  intentionRegion: string;
  belongWarZone: BelongWarZoneEnum;
  intentionPositions?: {
    latitude: number;
    longitude: number;
    name: string;
  }[];
  channelTypes?: ChannelTypeEnum[];
  customerDesc?: string;
};

export type EditBusinessOpportunityReq = CreateBusinessOpportunityReq & {
  id: number;
};

export type IntentionContractInBusinessDTO = {
  id: number;
  code: string;
  nodeState: IntentionNodeStateEnum;
  createTime: string;
  relocation: boolean;
};

export type FormalContractInBusinessDTO = {
  id: number;
  code: string;
  nodeState: FormalNodeStateEnum;
  createTime: string;
  relocation: boolean;
};

export enum BusinessOpportunityAbandonReasonEnum {
  客户放弃加盟 = 'CUSTOMER_ABANDON',
  考察市场 = 'INVESTIGATE_MARKET',
  重新申请 = 'APPLY_AGAIN',
  未通过审核 = 'APPLY_NOT_PASS',
  无法联系客户 = 'CANNOT_CONTACT_CUSTOMER',
  区域无名额 = 'AREA_NO_QUOTA',
  不认可政策 = 'NOT_RECOGNIZE_POLICY',
  其他 = 'OTHER',
}

export type AbandonBusinessOpportunityReq = {
  id: number;
  abandonReason: BusinessOpportunityAbandonReasonEnum;
  abandonDesc?: string;
};
