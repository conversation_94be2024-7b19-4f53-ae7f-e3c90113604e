import { request } from '@src/common/http';
import {
  AbandonBusinessOpportunityReq,
  BusinessOpportunityDTO,
  CreateBusinessOpportunityReq,
  EditBusinessOpportunityReq,
  ExportBusinessOpportunityReq,
  FormalContractInBusinessDTO,
  GetBusinessOpportunityListReq,
  IntentionContractInBusinessDTO,
  ProgressNodeEnum,
  ProgressStateEnum,
} from './type';
import { FissionDTO } from '../fission/type';
import { ContractTypeEnum } from '../payment/type';

export const getBusinessOpportunityList = (data: GetBusinessOpportunityListReq) =>
  request.post<{ total: number; result: BusinessOpportunityDTO[] }>(
    '/api/admin/business/opportunity/page',
    { data },
  );

export const exportBusinessOpportunity = (data: ExportBusinessOpportunityReq) =>
  request.post('/api/admin/business/opportunity/export', { data });

export const deleteBusinessOpportunity = (id: number) =>
  request.del(`/api/admin/business/opportunity/delete/${id}`);

export const batchDeleteBusinessOpportunity = (data: { ids: number[] }) =>
  request.del('/api/admin/business/opportunity/delete/batch', { data });

export const transferBusinessOpportunity = (data: { id: number; transferUserId: number }) =>
  request.post('/api/admin/business/opportunity/transfer', { data });

export const batchTransferBusinessOpportunity = (data: { ids: number[]; transferUserId: number }) =>
  request.post('/api/admin/business/opportunity/transfer/batch', { data });

export const getBusinessOpportunityDetail = (id: number) =>
  request.get<BusinessOpportunityDTO>(`/api/admin/business/opportunity/detail/${id}`);

export const createBusinessOpportunity = (data: CreateBusinessOpportunityReq) =>
  request.post('/api/admin/business/opportunity/save', { data });

export const editBusinessOpportunity = ({ id, ...data }: EditBusinessOpportunityReq) =>
  request.put(`/api/admin/business/opportunity/update/${id}`, { data });

export const getBusinessOpportunityProgressNodes = (id: number) =>
  request.get<{ node: ProgressNodeEnum; state: ProgressStateEnum }[]>(
    `/api/admin/business/opportunity/progress/node/${id}`,
  );

export const startAppointmentInProgress = (id: number) =>
  request.post(`/api/admin/business/opportunity/progress/start/appointment/${id}`);

export const batchStartAppointment = (data: { ids: number[] }) =>
  request.post('/api/admin/business/opportunity/progress/start/appointment/batch', { data });

// 商机进展中查询所有可用的加盟申请表
export const getFranchiseExamsInProgress = (id: number) =>
  request.get(`/api/admin/business/opportunity/progress/find-exam/${id}`);

export const assignExamInProgress = (data: { bizOpportunityId: number; examPaperId: number }) =>
  request.post('/api/admin/business/opportunity/progress/assign/exam', { data });

export const batchAssignExam = (data: { bizOpportunityIds: number[]; examPaperId: number }) =>
  request.post('/api/admin/business/opportunity/progress/assign/exam/batch', { data });

export const getIntentionContractByBusinessId = (id: number) =>
  request.get<IntentionContractInBusinessDTO[]>(
    `/api/admin/business/opportunity/simple/contract/intention/${id}`,
  );

export const getFormalContractByBusinessId = (id: number) =>
  request.get<FormalContractInBusinessDTO[]>(
    `/api/admin/business/opportunity/simple/contract/formal/${id}`,
  );

export const getWeaverOAList = (params: { contractType: ContractTypeEnum }) =>
  request.get<{ requestName: string; workflowId: number }[]>(
    '/api/admin/contract/common/find-all-weaver-oa-request-name',
    { params },
  );

export const pushWeaverOA = (data: {
  contractType: ContractTypeEnum;
  ids: number[];
  workflowId: number;
  requestName: string;
}) => request.post('/api/admin/contract/common/push/weaver-oa', { data });

export const getFissionListByBusinessId = (id: number) =>
  request.get<FissionDTO[]>(`/api/admin/fission/apply/list/${id}`);

export const importBusinessExcel = (data: { file: FormData }) =>
  request.post<{ failDownloadUrl: string; failNum: number; successNum: number }>(
    '/api/admin/business/opportunity/import/excel',
    {
      data,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );

export const downloadBusinessExcelTemplate = () =>
  request.get<{ url: string }>('/api/admin/business/opportunity/download/template');

export const checkCustomerCreateRecently = (customerId: number) =>
  request.get<Boolean>(`/api/admin/business/opportunity/customer/create-recently/${customerId}`);

export const abandonBusinessOpportunity = (data: AbandonBusinessOpportunityReq) =>
  request.post('/api/admin/business/opportunity/abandon', { data });
