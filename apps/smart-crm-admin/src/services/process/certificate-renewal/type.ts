import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { WorkflowAuditStatusEnum } from '@src/services/workflow/type';
import { ExceptionTypeEnum } from '../abnormal-certificate/type';
import { ValidityTypeEnum } from '../store-setup-task/business-license/type';

export type QueryCertificateRenewalPageListReq = {
  pageNum?: number;
  pageSize?: number;
  shopId?: string;
  exceptionType?: ExceptionTypeEnum;
  status?: CertificateRenewalStatusEnum;
  auditStatus?: WorkflowAuditStatusEnum;
  weaverNodeStatus?: CertificateRenewalWeaverNodeStatusEnum;
  createUserId?: number;
  createBeginTime?: string;
  createEndTime?: string;
};

export type ExportCertificateRenewalReq = Omit<
  QueryCertificateRenewalPageListReq,
  'pageNum' | 'pageSize'
> & {
  ids?: number[];
};

export enum CertificateRenewalStatusEnum {
  未完成 = 'NOT_COMPLETE',
  已完成 = 'COMPLETE',
}

export enum CertificateRenewalWeaverNodeStatusEnum {
  未发起 = 'NOT_INITIATED',
  已发起 = 'INITIATED',
  已归档 = 'ARCHIVED',
}

export type CertificateRenewalDTO = {
  id: number;
  shopId: string;
  exceptionTypes: ExceptionTypeEnum[];
  description: string;
  status: CertificateRenewalStatusEnum;
  expireDate: string;
  flowCreateTime: string;
  weaverFlowCreateTime: string;
  auditStatus: WorkflowAuditStatusEnum;
  weaverNodeStatus: CertificateRenewalWeaverNodeStatusEnum;
  createUserId: number;
  createTime: string;
  updateUserId: number;
  updateTime: string;
  processInstanceId: string;
  belongWarZone: BelongWarZoneEnum;
  businessLicense?: {
    licenseCode: string;
    licenseUrls: string[];
    licenseName: string;
    licenseType: string;
    licenseRegisterDate: string;
    licenseAddress: string;
    licenseExpireDate: string;
    licenseEffectiveType: ValidityTypeEnum;
    licenseOperator: string;
    licenseBusinessScope: string;
    licenseIssueDate: string;
  };
  qualification?: {
    qualificationCode: string;
    qualificationUrls: string[];
    qualificationIssueDate: string;
    qualificationExpireDate: string;
    qualificationOperator: string;
    qualificationMainBusiness: string;
    qualificationLegerPerson: string;
    qualificationAddress: string;
    qualificationProject: string;
  };
  legalPerson?: {
    name: string;
    idCardNo: string;
    phone: string;
    idCardExpireDate: string;
    idCardFrontUrl: string;
    idCardBackUrl: string;
    holdIdCardFrontUrl: string;
    holdIdCardBackUrl: string;
  };
};

export type SubmitCertificateRenewalReviewReq = {
  file: File;
  fileInfos: Record<string, string>;
};

export type SubmitCertificateRenewalReviewRes = {
  successNum: number;
  failNum: number;
  failDownloadUrl: string;
};

export type SubmitCertificateRenewalReq = {
  shopId: string;
  exceptionTypes: ExceptionTypeEnum[];
  description: string;
} & Pick<CertificateRenewalDTO, 'businessLicense' | 'qualification' | 'legalPerson'>;
