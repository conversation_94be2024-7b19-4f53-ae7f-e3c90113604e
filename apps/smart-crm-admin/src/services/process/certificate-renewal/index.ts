import { request } from '@src/common/http';
import {
  CertificateRenewalDTO,
  ExportCertificateRenewalReq,
  QueryCertificateRenewalPageListReq,
  SubmitCertificateRenewalReq,
  SubmitCertificateRenewalReviewReq,
  SubmitCertificateRenewalReviewRes,
} from './type';

export const queryCertificateRenewalPageList = (data: QueryCertificateRenewalPageListReq) =>
  request.post<{ result: CertificateRenewalDTO[]; total: number }>(
    '/api/admin/shop-license-trace/page',
    {
      data,
    },
  );

export const queryCertificateRenewalDetail = (id: number) =>
  request.get<CertificateRenewalDTO>(`/api/admin/shop-license-trace/detail/${id}`);

export const exportCertificateRenewal = (data: ExportCertificateRenewalReq) =>
  request.post('/api/admin/shop-license-trace/export', { data });

export const deleteCertificateRenewal = (id: number) =>
  request.del(`/api/admin/shop-license-trace/delete/${id}`);

export const downloadCertificateRenewalTaskTemplate = () =>
  request.get<{ url: string }>('/api/admin/shop-license-trace/download/import-template');

export const importCertificateRenewalTask = (data: { file: File }) =>
  request.post<SubmitCertificateRenewalReviewRes>('/api/admin/shop-license-trace/import', {
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

export const downloadCertificateRenewalReviewTemplate = () =>
  request.get<{ url: string }>('/api/admin/shop-license-trace/download/import-submit-template');

export const submitCertificateRenewalReview = (data: SubmitCertificateRenewalReviewReq) =>
  request.post<SubmitCertificateRenewalReviewRes>('/api/admin/shop-license-trace/import/submit', {
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

export const submitCertificateRenewal = (data: SubmitCertificateRenewalReq) =>
  request.post('/api/admin/shop-license-trace/submit', { data });

export const pushWeaverCertificateRenewal = (id: number) =>
  request.post(`/api/admin/shop-license-trace/push-weaver/${id}`);
