import { FileDTO } from '@src/services/common/type';

export type QueryFoodLicensePageListReq = {
  pageNum?: number;
  pageSize?: number;
  shopId?: string;
  shopName?: string;
  operatorName?: string;
};

export type FoodLicenseDTO = {
  id: number;
  shopId: string;
  shopName: string;
  operatorName: string;
  businessCategory: string;
  licenseNumber: number;
  createTime: string;
  updateTime: string;
  issueDate: string;
  licenseExpiryDate: string;
  businessAddress: string;
  businessScope: string;
  attachments: {
    moduleType: FoodLicenseAttachmentModuleTypeEnum;
    attachments: FileDTO[];
  }[];
};

export enum FoodLicenseAttachmentModuleTypeEnum {
  FOOD_BUSINESS_LICENSE = 'FOOD_BUSINESS_LICENSE',
}

export type CreateFoodLicenseReq = {
  shopId: string;
  shopName: string;
  operatorName: string;
  attachments: {
    moduleType: FoodLicenseAttachmentModuleTypeEnum;
    attachments: FileDTO[];
  }[];
  businessCategory: string;
  licenseNumber: string;
  issueDate: string;
  licenseExpiryDate: string;
  businessAddress: string;
  businessScope: string;
};

export type UpdateFoodLicenseReq = CreateFoodLicenseReq & {
  id: number;
};
