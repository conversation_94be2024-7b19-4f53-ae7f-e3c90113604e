import { request } from '@src/common/http';
import {
  CreateFoodLicenseReq,
  FoodLicenseDTO,
  QueryFoodLicensePageListReq,
  UpdateFoodLicenseReq,
} from './type';

export const queryFoodLicensePageList = (data: QueryFoodLicensePageListReq) =>
  request.post<{ total: number; result: FoodLicenseDTO[] }>(
    '/api/admin/license/food/business/page',
    { data },
  );

export const createFoodLicense = (data: CreateFoodLicenseReq) =>
  request.post('/api/admin/license/food/business/save', { data });

export const updateFoodLicense = (data: UpdateFoodLicenseReq) =>
  request.post('/api/admin/license/food/business/update', { data });

export const queryFoodLicenseDetail = (id: number) =>
  request.get<FoodLicenseDTO>(`/api/admin/license/food/business/detail/${id}`);
