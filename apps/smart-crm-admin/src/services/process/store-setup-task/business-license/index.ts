import { request } from '@src/common/http';
import {
  BusinessLicenseDTO,
  CreateBusinessLicenseReq,
  QueryBusinessLicensePageListReq,
  UpdateBusinessLicenseReq,
} from './type';

export const queryBusinessLicensePageList = (data: QueryBusinessLicensePageListReq) =>
  request.post<{ total: number; result: BusinessLicenseDTO[] }>(
    '/api/admin/license/business/page',
    { data },
  );

export const queryBusinessLicenseDetail = (id: number) =>
  request.get<BusinessLicenseDTO>(`/api/admin/license/business/${id}/detail`);

export const createBusinessLicense = (data: CreateBusinessLicenseReq) =>
  request.post('/api/admin/license/business/save', { data });

export const updateBusinessLicense = (data: UpdateBusinessLicenseReq) =>
  request.post('/api/admin/license/business/update', { data });

// 查询该门店是否存在搬迁合同
export const queryRelocationStatusByShop = (shopId: string) =>
  request.get<boolean>(`/api/admin/contract/relocation/formal/find/${shopId}/check-relocation`);
