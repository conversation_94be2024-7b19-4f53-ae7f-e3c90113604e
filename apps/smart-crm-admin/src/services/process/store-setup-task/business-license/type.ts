import { FileDTO } from '@src/services/common/type';

export type QueryBusinessLicensePageListReq = {
  pageNum?: number;
  pageSize?: number;
};

export enum LicenseTypeEnum {
  企业法人营业执照 = 'COMPANY',
  个体工商户营业执照 = 'INDIVIDUAL',
}

export enum ValidityTypeEnum {
  长期 = 'LONG_TERM',
  具体期限 = 'DETERMINED_TERM',
}

export enum BusinessLicenseAttachmentModuleTypeEnum {
  /** 营业执照照片 */
  BUSINESS_LICENSE = 'BUSINESS_LICENSE',
  /** 身份证正面 */
  CERTIFICATE_PHOTO = 'CERTIFICATE_PHOTO',
  /** 身份证反面 */
  CERTIFICATE_SEAL = 'CERTIFICATE_SEAL',
  /** 身份证手持正面 */
  HOLD_CERTIFICATE_PHOTO = 'HOLD_CERTIFICATE_PHOTO',
  /** 身份证手持反面 */
  HOLD_CERTIFICATE_SEAL = 'HOLD_CERTIFICATE_SEAL',
}

export type BusinessLicenseDTO = {
  id: number;
  shopId: string;
  shopName: string;
  licenseNumber: string;
  licenseName: string;
  licenseType: LicenseTypeEnum;
  licenseExpiryDate: string;
  businessAddress: string;
  establishmentDate: string;
  validityType: ValidityTypeEnum;
  legalPerson: string;
  legalIdentityCard: string;
  legalIdentityCardSensitive: string;
  legalContact: string;
  legalContactSensitive: string;
  createTime: string;
  updateTime: string;
  attachments: {
    moduleType: BusinessLicenseAttachmentModuleTypeEnum;
    attachments: FileDTO[];
  }[];
  reuseFoodAccount?: boolean;
  retainGrouponAccount?: boolean;
};

export type CreateBusinessLicenseReq = {
  shopId: string;
  shopName: string;
  licenseName: string;
  licenseType: LicenseTypeEnum;
  licenseNumber: string;
  establishmentDate: string;
  validityType: ValidityTypeEnum;
  licenseExpiryDate?: string;
  businessAddress: string;
  legalPerson: string;
  legalIdentityCard: string;
  legalContact: string;
  attachments: {
    moduleType: BusinessLicenseAttachmentModuleTypeEnum;
    attachments: FileDTO[];
  }[];
  reuseFoodAccount?: boolean;
  retainGrouponAccount?: boolean;
};

export type UpdateBusinessLicenseReq = CreateBusinessLicenseReq & {
  id: number;
};
