import { request } from '@src/common/http';
import { OwnerUpdateDTO, OwnerUpdateReq } from './type';

/** 新增 */
export const saveOwnerUpdate = (data: OwnerUpdateDTO) => {
  return request.post('/api/admin/process/change-legal-person/save', { data });
};

/** 分页查询 */
export const getOwnerUpdatePage = (data: OwnerUpdateReq) => {
  return request.post('/api/admin/process/change-legal-person/page', { data });
};

/** 详情 */
export const getOwnerUpdateDetail = (id: number) => {
  return request.get(`/api/admin/process/change-legal-person/load/${id}`);
};

/** 导出 */
export const exportOwnerUpdate = (data: OwnerUpdateReq) => {
  return request.post('/api/admin/process/change-legal-person/export', { data });
};
