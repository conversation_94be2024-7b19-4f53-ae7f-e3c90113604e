import { OwnerTypeEnum } from '@src/services/business-opportunity/type';

export enum OwnerUpdateTypeEnum {
  加盟商内部转让 = 'INTERNAL',
  加盟商买卖转让 = 'TRANSFER',
}

export type OwnerUpdateDTO = {
  contractAttachments: [
    {
      fileKey: string;
      fileName: string;
      fileType: string;
      fileUrl: string;
    },
  ];
  licenseAttachments: [
    {
      fileKey: string;
      fileName: string;
      fileType: string;
      fileUrl: string;
    },
  ];
  customerId: number;
  customerPhone: string;
  identityCard: string;
  merchantNo: string;
  originalCustomerId: number;
  originalCustomerPhone: string;
  originalIdentityCard: string;
  ownerRelation: string;
  reason: string;
  ownerType: OwnerTypeEnum;
  transferType: OwnerUpdateTypeEnum;
  shopId: string;
  shopName: string;

  weaverDepartmentId: string;
  weaverDepartmentName: string;

  weaverWarZoneId: string;
  weaverWarZoneName: string;
  weaverZoneManagerId: string;

  weaverProvinceId: string;
  weaverProvinceName: string;
  weaverProvinceHeadId: string;
  weaverProvinceManagerId: string;
};

export type OwnerUpdateReq = {
  pageNum?: number;
  pageSize?: number;
  code?: string;
  transferType?: OwnerUpdateTypeEnum;
  ids?: number[];
  shopId?: string;
};
