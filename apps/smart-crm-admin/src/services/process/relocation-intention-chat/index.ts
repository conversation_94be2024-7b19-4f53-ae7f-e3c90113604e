import { request } from '@src/common/http';
import {
  CreateRelocationApplyReq,
  GetRelocationApplyListReq,
  RelocationApplyDTO,
  UpdateRelocationApplyReq,
} from './type';

export const getRelocationApplyList = (params: GetRelocationApplyListReq) =>
  request.get<{ total: number; result: RelocationApplyDTO[] }>(
    '/api/admin/relocation-intention-chat/page',
    { params },
  );

export const deleteRelocationApply = (id: number) =>
  request.del(`/api/admin/relocation-intention-chat/delete/${id}`);

export const createRelocationApply = (data: CreateRelocationApplyReq) =>
  request.post('/api/admin/relocation-intention-chat/save', { data });

export const editRelocationApply = (data: UpdateRelocationApplyReq) =>
  request.post('/api/admin/relocation-intention-chat/update', { data });
