import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';

export type GetRelocationApplyListReq = {
  pageNum?: number;
  pageSize?: number;
  chatCode?: string;
  chatName?: string;
  chatType?: ChatTypeEnum;
  customerPhone?: string;
};

export type RelocationApplyDTO = {
  id: number;
  chatCode: string;
  chatName: string;
  customerPhone: string;
  customerPhoneSensitive: string;
  customerId: number;
  customerName: string;
  chatType?: ChatTypeEnum;
  createTime: string;
};

export type CreateRelocationApplyReq = {
  chatName: string;
  channelType: ContractChannelTypeEnum;
  customerId: number;
  shopId?: string;
  customerPhone: string;
  chatType?: ChatTypeEnum;
};

export type UpdateRelocationApplyReq = {
  id: string;
  chatName: string;
  customerPhone: string;
  channelType?: ContractChannelTypeEnum;
};

export enum ChatTypeEnum {
  搬迁群名 = 'RELOCATION',
  特殊渠道群名 = 'SPECIAL_CHANNEL',
}
