import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';

export enum AbnormalCertificateTaskStatusEnum {
  /** 未完成 */
  NOT_COMPLETE = 'NOT_COMPLETE',
  /** 完成 */
  COMPLETE = 'COMPLETE',
}

export type GetAbnormalCertificateListReq = {
  pageNum?: number;
  pageSize?: number;
  findAll?: boolean;
  businessId?: string;
  taskStatus?: AbnormalCertificateTaskStatusEnum;
  taskType?: AbnormalCertificateTaskTypeEnum[];
  expireBeginTime?: string;
  expireEndTime?: string;
  createBeginTime?: string;
  createEndTime?: string;
};

export type ExportAbnormalCertificateReq = Omit<
  GetAbnormalCertificateListReq,
  'pageNum' | 'pageSize'
> & {
  ids?: number[];
};

export enum AbnormalCertificateTaskTypeEnum {
  门店合同即将过期 = 'SHOP_CONTRACT_EXPIRATION',
  门店营业执照即将过期 = 'SHOP_LICENSE_EXPIRATION',
  门店食品经营许可证即将过期 = 'SHOP_QUALIFICATION_EXPIRATION',
  门店三证不一致 = 'SHOP_LICENSE_INCONSISTENCY',
  三者不一致 = 'SHOP_OPERATOR_MISMATCH',
  现场巡检不一致 = 'INSPECTION_INCONSISTENCY',
  资质合规承诺函签署方与出租方不一致 = 'COMMITMENT_SIGNATORY_INCONSISTENCY',
  CRM与网商验证 = 'CRM_MERCHANT_VERIFY',
  CRM与营执验证 = 'CRM_LICENSE_VERIFY',
  CRM与中台法人验证 = 'CRM_LEGAL_PERSON_VERIFY',
  营执与网商验证 = 'LICENSE_MERCHANT_VERIFY',
  营执与食安验证 = 'LICENSE_QUALIFICATION_VERIFY',
  营执第三方未验证 = 'LICENSE_THIRD_PARTY_NOT_VERIFY',
  营执第三方验证失败 = 'LICENSE_THIRD_PARTY_VERIFY_FAIL',
  食安第三方未验证 = 'QUALIFICATION_THIRD_PARTY_NOT_VERIFY',
  食安第三方验证失败 = 'QUALIFICATION_THIRD_PARTY_VERIFY_FAIL',
}

export type AbnormalCertificateDTO = {
  id: number;
  businessId: string;
  taskType: AbnormalCertificateTaskTypeEnum;
  taskStatus: AbnormalCertificateTaskStatusEnum;
  createTime: string;
  expireTime: string;
  completeTime: string;
  shopName: string;
  province: string;
  city: string;
  district: string;
  belongWarZone: BelongWarZoneEnum;
  issued: boolean;
};

export enum ExceptionTypeEnum {
  门店营业执照 = 'SHOP_LICENSE',
  门店食品经营许可证 = 'SHOP_QUALIFICATION',
  法人信息 = 'LEGAL_PERSON_INFO',
}

export type IssueShopLicenseTraceTask = {
  shopId: string;
  exceptionTypes: ExceptionTypeEnum[];
  expireDate: string;
  description: string;
};
