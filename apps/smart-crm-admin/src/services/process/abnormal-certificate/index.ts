import { request } from '@src/common/http';
import {
  AbnormalCertificateDTO,
  ExportAbnormalCertificateReq,
  GetAbnormalCertificateListReq,
  IssueShopLicenseTraceTask,
} from './type';

export const getAbnormalCertificateList = (params: GetAbnormalCertificateListReq) =>
  request.get<{ total: number; result: AbnormalCertificateDTO[] }>('/api/admin/task/page', {
    params,
  });

export const exportAbnormalCertificate = (params: ExportAbnormalCertificateReq) =>
  request.get('/api/admin/task/export', { params });

export const processedAbnormalCertificate = (id: number) =>
  request.get(`/api/admin/task/refresh-task/${id}`);

export const getWeaverProvinceList = () =>
  request.get<{ belongProvince: string; provinceManagerId: string; provinceName: string }[]>(
    '/api/admin/weaver/component/province',
  );

export const getWeaverWarZoneList = () =>
  request.get<
    {
      belongWarZone: string;
      belongWarZoneName: string;
      provinceHeadId: string;
      zoneManagerId: string;
    }[]
  >('/api/admin/weaver/component/warZone');

export const getWeaverDepartmentList = (departmentName?: string) =>
  request.get<
    {
      id: string;
      departmentname: string;
      tlevel: string;
    }[]
  >(`/api/admin/weaver/component/department?departmentName=${departmentName || ''}`);

export const checkShopLicenseTrace = (shopId: string) =>
  request.post(`/api/admin/task/check/issued/shop-license-trace/${shopId}`);

export const issueShopLicenseTraceTask = (data: IssueShopLicenseTraceTask) =>
  request.post('/api/admin/task/issued/shop-license-trace', { data });

export const completeAbnormalCertificateTask = (taskId: number) =>
  request.post(`/api/admin/task/complete/task/${taskId}`);
