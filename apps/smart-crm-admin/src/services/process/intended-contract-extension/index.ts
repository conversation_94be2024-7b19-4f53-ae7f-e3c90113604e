import { request } from '@src/common/http';
import { AuditHistoryType } from '@src/services/common/type';
import {
  CreateIntendedContractExtensionReq,
  ExportIntendedContractExtensionReq,
  GetIntendedContractExtensionListReq,
  IntendedContractExtensionDTO,
  UpdateIntendedContractExtensionReq,
} from './type';

export const getIntendedContractExtensionList = (params: GetIntendedContractExtensionListReq) =>
  request.get<{ total: number; result: IntendedContractExtensionDTO[] }>(
    '/api/admin/contract/postpone/page',
    { params },
  );

export const exportIntendedContractExtension = (params: ExportIntendedContractExtensionReq) =>
  request.get('/api/admin/contract/postpone/export', { params });

export const createIntendedContractExtension = (data: CreateIntendedContractExtensionReq) =>
  request.post('/api/admin/contract/postpone/save', { data });

export const updateIntendedContractExtension = ({
  id,
  ...data
}: UpdateIntendedContractExtensionReq) =>
  request.put(`/api/admin/contract/postpone/update/${id}`, { data });

export const getIntendedContractExtensionDetail = (id: number) =>
  request.get<IntendedContractExtensionDTO>(`/api/admin/contract/postpone/detail/${id}`);

export const deleteIntendedContractExtension = (id: number) =>
  request.del(`/api/admin/contract/postpone/delete/${id}`);

export const batchDeleteIntendedContractExtension = (ids: number[]) =>
  request.post('/api/admin/contract/postpone/delete/batch', { data: { ids } });

export const getIntendedContractExtensionAuditHistory = (id: number) =>
  request.get<AuditHistoryType[]>(`/api/admin/contract/postpone/audit/history/${id}`);

export const revokeIntendedContractExtensionAudit = (id: number) =>
  request.post(`/api/admin/contract/postpone/cancel/process/instance/${id}`);

export const approveIntendedContractExtensionTask = (data: { id: number; description: string }) =>
  request.post('/api/admin/contract/postpone/approve/task', { data });

export const rejectIntendedContractExtensionTask = (data: { id: number; description: string }) =>
  request.post('/api/admin/contract/postpone/reject/task', { data });
