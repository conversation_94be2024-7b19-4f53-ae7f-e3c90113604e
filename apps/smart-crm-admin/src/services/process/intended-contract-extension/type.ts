import { AuditStatusEnum, FileDTO } from '@src/services/common/type';

export type GetIntendedContractExtensionListReq = {
  pageNum?: number;
  pageSize?: number;
  contractCode?: string;
  auditStatus?: AuditStatusEnum[];
  customerName?: string;
  regionCode?: string;
  createUserId?: number;
  createBeginTime?: string;
  createEndTime?: string;
};

export type ExportIntendedContractExtensionReq = GetIntendedContractExtensionListReq & {
  ids?: number[];
};

export type IntendedContractExtensionDTO = {
  id: number;
  code: string;
  contractCode: string;
  contractId: number;
  auditStatus: AuditStatusEnum;
  customerId: number;
  customerName: string;
  regionCode: string;
  effectiveDate: string;
  expirationDate: string;
  postponeDayNum: number;
  postponeExpirationDate: string;
  description: string;
  applyAttachments?: FileDTO[];
  createUserId: number;
  createTime: string;
};

export type CreateIntendedContractExtensionReq = {
  contractCode: string;
  customerId: number;
  effectiveDate: string;
  expirationDate: string;
  postponeDayNum: number;
  postponeExpirationDate: string;
  description: string;
  applyAttachments?: FileDTO[];
};

export type UpdateIntendedContractExtensionReq = CreateIntendedContractExtensionReq & {
  id: number;
};
