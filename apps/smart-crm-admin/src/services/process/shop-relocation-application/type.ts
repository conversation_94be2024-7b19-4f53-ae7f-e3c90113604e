import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';

export type GetShopRelocationListReq = {
  pageNum?: number;
  pageSize?: number;
  shopId?: string;
  regionCode?: string;
  shopCapacityStatus?: ShopCapacityStatusEnum;
  releaseAgreementSign?: boolean;
  capacityStatus?: CapacityStatusEnum;
};

export enum ShopCapacityStatusEnum {
  未释放 = 'NOT_RELEASED',
  已释放 = 'RELEASED',
}

export enum CapacityStatusEnum {
  已占用 = 'OCCUPIED',
  未占用 = 'UNOCCUPIED',
  无需占用 = 'NOT_REQUIRED',
}

export type ShopRelocationDTO = {
  id: number;
  code: string;
  requestId: string;
  shopId: string;
  releaseAgreementSign: boolean;
  relocationReason: string;
  channelType: ContractChannelTypeEnum;
  shopAddress: string;
  regionCode: string;
  shopCapacityStatus: ShopCapacityStatusEnum;
  capacityStatus: CapacityStatusEnum;
  relocationSuccess: boolean;
};

export type UpdateShopRelocationReq = {
  id: number;
  regionCode: string;
  shopCapacityStatus: ShopCapacityStatusEnum;
  capacityStatus: CapacityStatusEnum;
};
