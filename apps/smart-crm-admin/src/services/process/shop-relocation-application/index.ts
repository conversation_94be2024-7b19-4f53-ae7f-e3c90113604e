import { request } from '@src/common/http';
import { GetShopRelocationListReq, ShopRelocationDTO, UpdateShopRelocationReq } from './type';

export const getShopRelocationList = (data: GetShopRelocationListReq) =>
  request.post<{ total: number; result: ShopRelocationDTO[] }>('/api/admin/shop-reloc-apply/page', {
    data,
  });

export const getShopRelocationDetail = (id: number) =>
  request.get<ShopRelocationDTO>(`/api/admin/shop-reloc-apply/detail/${id}`);

export const updateShopRelocation = (data: UpdateShopRelocationReq) =>
  request.post('/api/admin/shop-reloc-apply/update', { data });
