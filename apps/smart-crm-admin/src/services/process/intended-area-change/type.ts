import { AuditStatusEnum, FileDTO } from '@src/services/common/type';
import { StoreCategoryEnum } from '@src/services/contract/formal/type';
import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';
import { ExpansionQuotaPositionDTO } from '@src/services/regional/expansion/type';
import { JoinRegionEnum } from '@src/services/regional/quota/type';

export enum IntendedAreaChangeReasonEnum {
  开发评估影响周边 = 'DEVELOPMENT_EVALUATION_AFFECTS_SURROUNDING',
  无符合标准门店转让 = 'NO_STANDARD_STORE_TRANSFER',
  开发评估无容量市场 = 'DEVELOPMENT_EVALUATION_NO_CAPACITY_MARKET',
  在其他区域找到门店 = 'FOUND_STORE_IN_OTHER_AREA',
  多次铺面审核未通过 = 'MULTIPLE_PREMISES_APPROVAL_FAILED',
}

export type CreateIntendedAreaChangeReq = {
  contractCode: string;
  customerId: number;
  originalRegionCode: string;
  regionCode: string;
  originalChannelType?: ContractChannelTypeEnum;
  channelType?: ContractChannelTypeEnum;
  originalPositions?: {
    name: string;
    latitude: number;
    longitude: number;
  };
  positions?: {
    name: string;
    latitude: number;
    longitude: number;
  };
  reason: IntendedAreaChangeReasonEnum;
  description: string;
  applyAttachments?: FileDTO[];
};

export type UpdateIntendedAreaChangeReq = CreateIntendedAreaChangeReq & {
  id: number;
};

export type GetIntendedAreaChangeListReq = {
  pageNum?: number;
  pageSize?: number;
  contactCode?: string;
  auditStatus?: AuditStatusEnum[];
  originalRegionCode?: string;
  customerName?: string;
  createUserId?: number;
  createBeginTime?: string;
  createEndTime?: string;
};

export type ExportIntendedAreaChangeReq = GetIntendedAreaChangeListReq & {
  ids?: number[];
};

export type IntendedAreaChangeDTO = {
  id: number;
  code: string;
  contractCode: string;
  contractId: number;
  auditStatus: AuditStatusEnum;
  customerId: number;
  customerName: string;
  effectiveDate: string;
  expirationDate: string;
  originalRegionCode: string;
  regionCode: string;
  originalPositions?: {
    name: string;
    latitude: number;
    longitude: number;
  }[];
  positions?: {
    name: string;
    latitude: number;
    longitude: number;
  }[];
  originalExpansionPosition?: ExpansionQuotaPositionDTO;
  expansionPosition?: ExpansionQuotaPositionDTO;
  originalChannelType?: ContractChannelTypeEnum;
  channelType?: ContractChannelTypeEnum;
  reason: string;
  description: string;
  applyAttachments?: FileDTO[];
  createUserId: number;
  createTime: string;
  customerNature: StoreCategoryEnum;
  originalJoinRegion: JoinRegionEnum;
  joinRegion: JoinRegionEnum;
  remainQuotaNum: number;
};

export type CheckIntendedAreaChangeInfoReq = {
  customerId: number;
  originalRegionCode: string;
  regionCode: string;
};

export type CheckIntendedAreaChangeInfoRes = {
  customerNature: StoreCategoryEnum;
  originalJoinRegion: JoinRegionEnum;
  joinRegion: JoinRegionEnum;
  success: boolean;
};
