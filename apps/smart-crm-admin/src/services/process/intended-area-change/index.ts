import { request } from '@src/common/http';
import { AuditHistoryType } from '@src/services/common/type';
import {
  CheckIntendedAreaChangeInfoReq,
  CheckIntendedAreaChangeInfoRes,
  CreateIntendedAreaChangeReq,
  ExportIntendedAreaChangeReq,
  GetIntendedAreaChangeListReq,
  IntendedAreaChangeDTO,
  UpdateIntendedAreaChangeReq,
} from './type';

export const createIntendedAreaChange = (data: CreateIntendedAreaChangeReq) =>
  request.post('/api/admin/contract/region/change/save', { data });

export const updateIntendedAreaChange = ({ id, ...data }: UpdateIntendedAreaChangeReq) =>
  request.put(`/api/admin/contract/region/change/update/${id}`, { data });

export const getIntendedAreaChangeList = (params: GetIntendedAreaChangeListReq) =>
  request.get<{ total: number; result: IntendedAreaChangeDTO[] }>(
    '/api/admin/contract/region/change/page',
    { params },
  );

export const exportIntendedAreaChange = (params: ExportIntendedAreaChangeReq) =>
  request.get('/api/admin/contract/region/change/export', { params });

export const getIntendedAreaChangeDetail = (id: number) =>
  request.get<IntendedAreaChangeDTO>(`/api/admin/contract/region/change/detail/${id}`);

export const getIntendedAreaChangeAuditHistory = (id: number) =>
  request.get<AuditHistoryType[]>(`/api/admin/contract/region/change/audit/history/${id}`);

export const revokeIntendedAreaChangeAudit = (id: number) =>
  request.post(`/api/admin/contract/region/change/cancel/process/instance/${id}`);

export const approveIntendedAreaChangeTask = (data: { id: number; description: string }) =>
  request.post('/api/admin/contract/region/change/approve/task', { data });

export const rejectIntendedAreaChangeTask = (data: { id: number; description: string }) =>
  request.post('/api/admin/contract/region/change/reject/task', { data });

export const deleteIntendedAreaChange = (id: number) =>
  request.del(`/api/admin/contract/region/change/delete/${id}`);

export const batchDeleteIntendedAreaChange = (ids: number[]) =>
  request.post('/api/admin/contract/region/change/delete/batch', { data: { ids } });

export const checkIntendedAreaChangeInfo = (data: CheckIntendedAreaChangeInfoReq) =>
  request.post<CheckIntendedAreaChangeInfoRes>('/api/admin/contract/region/change/check-info', {
    data,
  });
