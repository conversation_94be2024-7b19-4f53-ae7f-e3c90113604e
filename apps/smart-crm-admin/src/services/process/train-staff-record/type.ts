import { WorkflowAuditStatusEnum } from '@src/services/workflow/type';
import { PersonTypeEnum } from '../train-staff/type';

export enum TrainingPersonRecordChangeReasonEnum {
  业主变更 = 'OWNER_CHANGE',
  鉴定多次未通过 = 'APPRAISAL_MANY_UNQUALIFIED',
  业主发起 = 'OWNER_INITIATED',
}

export type GetTrainingPersonRecordPageReq = {
  pageNum?: number;
  pageSize?: number;
  auditStatus?: WorkflowAuditStatusEnum;
  shopId?: string;
};

export type TrainingPersonRecordItemsDTO = {
  trainingPersonRecordId: number;
  personType: PersonTypeEnum;
  originalCustomerId: number;
  originalName: string;
  originalPhone: string;
  originalPhoneSensitive: string;
  originalIdentityCard: string;
  originalIdentityCardSensitive: string;
  targetCustomerId: number;
  targetName: string;
  targetPhone: string;
  targetPhoneSensitive: string;
  targetIdentityCard: string;
  targetIdentityCardSensitive: string;
};

export type TrainingPersonRecordListDTO = {
  id: number;
  shopId: string;
  shopName: string;
  changeReason: TrainingPersonRecordChangeReasonEnum;
  processInstanceId: string;
  auditStatus: WorkflowAuditStatusEnum;
  canReSubmit: boolean;
  auditPassTime: string;
  createTime: string;
  createUserId: number;
  createUserName: string;
  trainingInfoId: number;
  trainingPersonRecordItems: TrainingPersonRecordItemsDTO[];
};

export type TrainingPersonRecordDetailDTO = {
  id: number;
  trainingInfoId: number;
  shopId: string;
  changeReason: TrainingPersonRecordChangeReasonEnum;
  trainingPersonRecordItems: TrainingPersonRecordItemsDTO[];
};
