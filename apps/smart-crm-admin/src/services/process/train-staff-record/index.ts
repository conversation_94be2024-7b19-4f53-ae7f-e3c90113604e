import { request } from '@src/common/http';
import {
  GetTrainingPersonRecordPageReq,
  TrainingPersonRecordDetailDTO,
  TrainingPersonRecordListDTO,
} from './type';

export const getTrainingPersonRecordPage = (data: GetTrainingPersonRecordPageReq) =>
  request.post<{ total: number; result: TrainingPersonRecordListDTO[] }>(
    '/api/admin/training/person/record/page',
    {
      data,
    },
  );

export const getTrainingPersonRecordDetail = (id: number) =>
  request.get<TrainingPersonRecordListDTO>(`/api/admin/training/person/record/detail/${id}`);

export const saveTrainingPersonRecord = (data: TrainingPersonRecordDetailDTO) =>
  request.post(`/api/admin/training/person/record/save`, {
    data,
  });

export const updateTrainingPersonRecord = (data: TrainingPersonRecordDetailDTO) =>
  request.post(`/api/admin/training/person/record/update`, {
    data,
  });
