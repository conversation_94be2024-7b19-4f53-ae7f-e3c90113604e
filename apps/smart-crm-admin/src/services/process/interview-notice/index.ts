import { request } from '@src/common/http';
import { InterviewNoticeDTO, InterviewNoticeReq } from './type';

/** 新增 */
export const saveInterviewNotice = (data: InterviewNoticeDTO) => {
  return request.post('/api/admin/process/interview/notice/save', { data });
};

/** 分页查询 */
export const getInterviewNoticePage = (data: InterviewNoticeReq) => {
  return request.post('/api/admin/process/interview/notice/page', { data });
};

/** 详情 */
export const getInterviewNoticeDetail = (id: string | number) => {
  return request.get(`/api/admin/process/interview/notice/${id}`);
};

/** 详情 */
export const getInterviewNoticeDetailByCode = (code: string | number) => {
  return request.get(`/api/admin/process/interview/notice/detail/${code}/byCode`);
};

/** 导出 */
export const exportInterviewNotice = (data: InterviewNoticeReq) => {
  return request.post('/api/admin/process/interview/notice/export', { data });
};

/** 获取所有门店搬迁流程节点 */
export const getInterviewNoticeNodes = () => {
  return request.get('/api/admin/process/interview/notice/nodes');
};

export const getInterviewNoticeList = (data: { customerId: string; referenceTime: string }) =>
  request.get('/api/admin/process/interview/notice/list/by-time', { params: data });
