export type InterviewNoticeDTO = {
  code: string;
  archiveTime: string;
  createTime: string;
  customerId: string;
  customerName: string;
  id: number;
  node: string;
  signSubject: string;
  signerIdentityCard: string;
  signerIdentityCardSensitive: string;
  signerPhone: string;
  signerPhoneSensitive: string;
  socialCreditCode: string;
  toPublic: number;
  updateTime: string;
  provinceCode: string;
  cityCode: string;
  regionCode: string;
};

export type InterviewNoticeReq = {
  pageNum?: number;
  pageSize?: number;
  code?: string;
  ids?: number[];
  shopId?: string;
};
