import { request } from '@src/common/http';
import {
  QueryRelocationAreaChangeListReq,
  RelocationAreaChangeDTO,
  SubmitRelocationAreaChangeReq,
  UpdateRelocationAreaChangeReq,
} from './type';

export const queryRelocationAreaChangeList = (data: QueryRelocationAreaChangeListReq) =>
  request.post<{ total: number; result: RelocationAreaChangeDTO[] }>(
    '/api/admin/contract/relocation/region/change/page',
    { data },
  );

export const queryRelocationAreaChangeDetail = (id: number) =>
  request.get<RelocationAreaChangeDTO>(`/api/admin/contract/relocation/region/change/detail/${id}`);

export const submitRelocationAreaChange = (data: SubmitRelocationAreaChangeReq) =>
  request.post('/api/admin/contract/relocation/region/change/submit', { data });

export const updateRelocationAreaChange = (data: UpdateRelocationAreaChangeReq) =>
  request.post('/api/admin/contract/relocation/region/change/update', { data });
