import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';
import { WorkflowAuditStatusEnum } from '@src/services/workflow/type';

export type QueryRelocationAreaChangeListReq = {
  pageNum?: number;
  pageSize?: number;
  shopId?: string;
  auditStatus?: WorkflowAuditStatusEnum;
};

export enum RelocationTypeEnum {
  同区 = 'SAME_DISTRICT',
  跨区 = 'CROSS_DISTRICT',
}

export type RelocationAreaChangeDTO = {
  id: number;
  code: string;
  shopId: string;
  relocationType: RelocationTypeEnum;
  relocationReason: string;
  originalChannelType: ContractChannelTypeEnum;
  originalShopAddress: string;
  originalDistrictCode: string;
  newShopAddress: string;
  newChannelType: ContractChannelTypeEnum;
  newDistrictCode: string;
  newShopCoordinates?: {
    name: string;
    longitude: number;
    latitude: number;
  };
  consultantUserId: number;
  auditStatus: WorkflowAuditStatusEnum;
  processInstanceId: string;
  shopRelocApplyCode: string;
};

export type SubmitRelocationAreaChangeReq = {
  shopRelocApplyCode: string;
  shopId: string;
  relocationType: RelocationTypeEnum;
  relocationReason: string;
  originalChannelType: ContractChannelTypeEnum;
  originalShopAddress: string;
  originalDistrictCode: string;
  newChannelType: ContractChannelTypeEnum;
  newShopCoordinates: {
    name: string;
    longitude: number;
    latitude: number;
  };
  newShopAddress: string;
  newDistrictCode: string;
  consultantUserId: number;
};

export type UpdateRelocationAreaChangeReq = SubmitRelocationAreaChangeReq & {
  id: number;
};
