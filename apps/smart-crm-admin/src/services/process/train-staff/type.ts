import { OwnerTypeEnum } from '@src/services/business-opportunity/type';
import { CommonContractNodeEnum } from '@src/services/common/type';
import { FormalNodeStateEnum, StoreCategoryEnum } from '@src/services/contract/formal/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import { WorkflowAuditStatusEnum } from '@src/services/workflow/type';

export enum PersonTypeEnum {
  /** 店长 */
  SHOP_MANAGER = 'SHOP_MANAGER',
  /** 店员 */
  EMPLOYEE = 'EMPLOYEE',
}

export enum TrainingModeEnum {
  自主培训 = 'SELF_DIRECTED_TRAINING',
  委托培训 = 'DELEGATE_TRAINING',
}

export type TrainingPersonsDTO = {
  customerId: number;
  personType: PersonTypeEnum;
  name: string;
  phone: string;
  phoneSensitive: string;
  identityCard: string;
  identityCardSensitive: string;
  crossFunctionalCertCode: string;
  completionCertCode: string;
};

export type TrainingPersonListDTO = {
  id: string;
  code: string;
  contractType: ContractTypeEnum;
  storeCode: string;
  storeName: string;
  provinceCode: string;
  provinceName: string;
  cityCode: string;
  cityName: string;
  regionCode: string;
  regionName: string;
  streetCode: string;
  streetName: string;
  storeCategory: StoreCategoryEnum;
  signer: string;
  signerPhone: string;
  trainingPersons: TrainingPersonsDTO[];
  needTraining: boolean;
  trainingMode: string;
  trainingShopId: string;
  trainingDate: string;
  expectAppraisalDate: string;
  certIsRefine: boolean;
  auditStatus: WorkflowAuditStatusEnum;
  signerPhoneSensitive: string;
  processInstanceId?: string;
  ownerType?: OwnerTypeEnum;
  trainingShopDockingUserName?: string;
  trainingUserName?: string;
  trainingManagerName?: string;
  trainingShopName?: string;
  nodeState?: FormalNodeStateEnum;
  node?: CommonContractNodeEnum;
  trainingInfoId?: number;
};

export type GetTrainingPersonDetailReq = {
  contractId: string;
  contractType: ContractTypeEnum;
};

export type UpdateTrainingPersonListReq = {
  contractId: string;
  contractType: ContractTypeEnum;
  trainingPersons: TrainingPersonsDTO[];
};

export type UpdateTrainingInfoReq = {
  id: number;
  shopId?: string;
  contractId?: string;
  contractType?: ContractTypeEnum;
  needTraining?: boolean;
  trainingMode?: TrainingModeEnum;
  trainingShopId?: string;
  trainingDate?: string;
  expectAppraisalDate?: string;
};
