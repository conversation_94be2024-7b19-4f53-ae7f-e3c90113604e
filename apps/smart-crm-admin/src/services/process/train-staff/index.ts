import { request } from '@src/common/http';
import { GetETableListReq } from '@src/services/common/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import {
  GetTrainingPersonDetailReq,
  TrainingPersonListDTO,
  UpdateTrainingInfoReq,
  UpdateTrainingPersonListReq,
} from './type';

export const getTrainingPersonList = (
  data: GetETableListReq & { contractType: ContractTypeEnum },
) =>
  request.post<{ total: number; result: TrainingPersonListDTO[] }>(
    '/api/admin/training/person/list/page',
    {
      data,
    },
  );

export const getTrainingPersonDetail = (params: GetTrainingPersonDetailReq) =>
  request.get<TrainingPersonListDTO>(
    `/api/admin/training/person/list/detail/${params.contractId}/${params.contractType}`,
  );

export const updateTrainingPersonList = (data: UpdateTrainingPersonListReq) =>
  request.post(`/api/admin/training/person/list/update`, {
    data,
  });

export const saveTrainingPersonList = (data: UpdateTrainingPersonListReq) =>
  request.post(`/api/admin/training/person/list/save`, {
    data,
  });

export const updateTrainingInfo = (data: UpdateTrainingInfoReq) =>
  request.post(`/api/admin/training/person/list/trainingInfo/update`, {
    data,
  });

export const syncTrainingPersonCertInfo = (data: {
  contractId: string;
  contractType: ContractTypeEnum;
}) =>
  request.post(
    `/api/admin/training/person/list/syncTrainingPersonCertInfo/${data?.contractId}/${data?.contractType}`,
  );

export const getTrainingInfoPassPage = (data: {
  pageNum?: number;
  pageSize?: number;
  shopId?: string;
}) =>
  request.post<{ total: number; result: TrainingPersonListDTO[] }>(
    '/api/admin/training/person/list/trainingInfo/passPage',
    {
      data,
    },
  );
