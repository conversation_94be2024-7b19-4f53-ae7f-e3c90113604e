import { request } from '@src/common/http';
import { CategoryListItemType } from './type';

export const getCategoryList = (params: { pageNum?: number; pageSize?: number; title?: string }) =>
  request.get<{ result: CategoryListItemType[]; total: number }>(
    '/api/admin/portal/category/page',
    { params },
  );

export const deleteCategory = (id: number) => request.del(`/api/admin/portal/category/${id}`);

export const saveCategory = (data: { id?: number; title: string; parentId?: number }) =>
  request.post('/api/admin/portal/category/save', { data });

export const changeCollegeStatus = (data: { id: number; collegeFlag: boolean }) =>
  request.post('/api/admin/portal/category/openOrClose', { data });

export const changeHomePosterJumpFlag = (data: { id: number; homePosterJumpFlag: boolean }) =>
  request.post('/api/admin/portal/category/openOrCloseHomePosterJump', { data });

export const getAllCategoryList = (params?: { parentId?: number }) =>
  request.get<{ id: number; title: string }[]>('/api/admin/portal/category/all', { params });

export const getAllCategoryTree = () =>
  request.get<CategoryListItemType[]>('/api/admin/portal/category/tree/all');
