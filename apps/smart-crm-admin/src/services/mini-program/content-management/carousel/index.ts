import { request } from '@src/common/http';
import { CarouselItemDetailType, CarouselItemType, SaveCarouselReq } from './type';

export const getCarouselList = (params: { pageNum?: number; pageSize?: number; title?: string }) =>
  request.get<{ result: CarouselItemType[]; total: number }>('/api/admin/portal/carousel/page', {
    params,
  });

export const getCarouselDetail = (id: number) =>
  request.get<CarouselItemDetailType>(`/api/admin/portal/carousel/${id}`);

export const deleteCarousel = (id: number) => request.del(`/api/admin/portal/carousel/${id}`);

export const saveCarousel = (data: SaveCarouselReq) =>
  request.post('/api/admin/portal/carousel/save', { data });

export const getAllNewsList = () =>
  request.get<{ id: number; title: string }[]>('/api/admin/portal/news/all');
