export type CarouselItemType = {
  id: number;
  title: string;
  imageOssUrl: string;
  createTime: string;
  updateTime: string;
  sort: number;
  readCount: number;
};

export enum ClassifierEnum {
  NEWS = 'NEWS',
  CATEGORY_LIST = 'CATEGORY_LIST',
}

export type CarouselItemDetailType = {
  title: string;
  imageOssUrl: string;
  imageOssKey: string;
  classifier: ClassifierEnum;
  classifierId: number;
  sort: number;
};

export type SaveCarouselReq = {
  id?: number;
  title: string;
  imageOssKey: string;
  classifier: ClassifierEnum;
  classifierId: number;
  sort: number;
};
