import { request } from '@src/common/http';
import { CreateArticleReq, EditArticleReq, GetArticleDetailReq } from './type';

export const getArticleList = (params: { pageNum?: number; pageSize?: number; title?: string }) =>
  request.get<{ result: any[]; total: number }>('/api/admin/article/page', { params });

export const createArticle = (data: CreateArticleReq) =>
  request.post('/api/admin/article/insert', { data });

export const editArticle = ({ id, ...data }: EditArticleReq) =>
  request.put(`/api/admin/article/update/${id}`, { data });

export const deleteArticle = (id: number) => request.del(`/api/admin/article/delete/${id}`);

export const getArticleDetail = (id: number) =>
  request.get<GetArticleDetailReq>(`/api/admin/article/${id}`);
