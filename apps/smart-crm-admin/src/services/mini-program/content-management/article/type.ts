export enum ArticleTypeEnum {
  服务协议 = 'SERVICE_AGREEMENT',
  加盟声明 = 'JOIN_STATEMENT',
  加盟条件 = 'JOIN_CONDITION',
  加盟费用 = 'JOIN_EXPENSES',
  加盟流程 = 'JOIN_PROCESS',
  加盟优势 = 'JOIN_ADVANTAGE',
  加盟问题 = 'JOIN_QUESTION',
  最新动态 = 'NEW_DYNAMIC',
  隐私声明 = 'PRIVACY_STATEMENT',
  面试须知 = 'INTERVIEW_INSTRUCTIONS',
  填表须知 = 'FILLING_INSTRUCTIONS',
  品牌大数据 = 'BRAND_BIG_DATA',
}

export type CreateArticleReq = {
  title: string;
  articleType: ArticleTypeEnum;
  imageOssKey: string;
};

export type EditArticleReq = {
  id: number;
  title: string;
  articleType: ArticleTypeEnum;
  imageOssKey: string;
};

export type GetArticleDetailReq = {
  title: string;
  imageOssKey: string;
  imageOssUrl: string;
  articleType: ArticleTypeEnum;
};
