import { request } from '@src/common/http';

export const getFeedbackList = (params: {
  pageNum?: number;
  pageSize?: number;
  name?: string;
  phone?: string;
}) =>
  request.get<{
    result: {
      id: number;
      phone: string;
      phoneSensitive: string;
      message: string;
      createTime: string;
    }[];
    total: number;
  }>('/api/admin/portal/suggestion/page', { params });

export const deleteFeedback = (id: number) => request.del(`/api/admin/portal/suggestion/${id}`);

export const exportFeedback = (data: { name?: string; phone?: string; ids?: number[] }) =>
  request.post('/api/admin/portal/suggestion/export', { data });
