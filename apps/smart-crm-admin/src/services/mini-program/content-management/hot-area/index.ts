import { request } from '@src/common/http';
import {
  CreateHotAreaReq,
  EditHotAreaReq,
  GetHotAreaListReq,
  HorAreaDetailType,
  HotAreaListItemType,
} from './type';

export const getHotAreaList = (params: GetHotAreaListReq) =>
  request.get<{ result: HotAreaListItemType[]; total: number }>(
    '/api/admin/franchise/area/advantage/page',
    {
      params,
    },
  );

export const getHorAreaDetail = (id: number) =>
  request.get<HorAreaDetailType>(`/api/admin/franchise/area/advantage/detail/${id}`);

export const createHotArea = (data: CreateHotAreaReq) =>
  request.post('/api/admin/franchise/area/advantage/insert', { data });

export const editHotArea = ({ id, ...data }: EditHotAreaReq) =>
  request.put(`/api/admin/franchise/area/advantage/update/${id}`, { data });

export const deleteHotArea = (id: number) =>
  request.del(`/api/admin/franchise/area/advantage/delete/${id}`);
