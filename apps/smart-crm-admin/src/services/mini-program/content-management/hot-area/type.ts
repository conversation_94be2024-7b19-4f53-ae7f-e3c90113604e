export type GetHotAreaListReq = {
  pageNum?: number;
  pageSize?: number;
  title?: string;
  districtId?: number;
  type?: HotAreaTypeEnum;
};
export type HotAreaListItemType = {
  id: number;
  title: string;
  districtId: number;
  type: HotAreaTypeEnum;
  imageOssKey: string;
  imageOssUrl: string;
  sort: number;
  readCount: number;
  createTime: number;
  updateTime: string;
};

export type HorAreaDetailType = HotAreaListItemType & {
  content: string;
  editorText: string;
};

export enum HotAreaTypeEnum {
  重点区域 = 'FOCUS_AREA',
  热门区域 = 'POPULAR_AREA',
}

export type CreateHotAreaReq = {
  title: string;
  districtId: number;
  type: HotAreaTypeEnum;
  imageOssKey: string;
  content: string;
  editorText: string;
};

export type EditHotAreaReq = CreateHotAreaReq & {
  id: number;
};
