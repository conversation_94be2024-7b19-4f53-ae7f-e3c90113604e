import { request } from '@src/common/http';
import { NewsDetailType, NewsListItemType, NewsStatusEnum, SaveNewsReq } from './type';

export const getNewsList = (params: {
  pageNum?: number;
  pageSize?: number;
  title?: string;
  categoryId?: number;
}) =>
  request.get<{ total: number; result: NewsListItemType[] }>('/api/admin/portal/news/page', {
    params,
  });

export const deleteNews = (id: number) => request.del(`/api/admin/portal/news/${id}`);

export const getNewsDetail = (id: number) =>
  request.get<NewsDetailType>(`/api/admin/portal/news/${id}`);

export const saveNews = (data: SaveNewsReq) =>
  request.post('/api/admin/portal/news/save', { data });

export const savePublishNews = (data: SaveNewsReq) =>
  request.post('/api/admin/portal/news/save-publish', { data });

export const updateNewsStatus = (data: { id: number; newsStatus: NewsStatusEnum }) =>
  request.put('/api/admin/portal/news/update/news-status', { data });
