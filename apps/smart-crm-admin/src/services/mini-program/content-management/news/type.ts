export type NewsListItemType = {
  id: number;
  sort: number;
  createTime: string;
  updateTime: string;
  imageOssUrl: string;
  horizontalImageOssUrl: string;
  categoryTitle: string;
  readCount: number;
  newsStatus: NewsStatusEnum;
};

export type NewsDetailType = {
  title: string;
  sort: number;
  categoryId: number;
  imageOssKey: string;
  imageOssUrl: string;
  horizontalImageOssUrl: string;
  horizontalImageOssKey: string;
  content: string;
  editorText: string;
};

export type SaveNewsReq = {
  id?: number;
  sort: number;
  newsType: NewsTypeEnum;
  categoryId: number;
  imageOssKey: string;
  horizontalImageOssKey: string;
  content: string;
  editorText?: string;
  channelsId?: string;
  videoId?: string;
};

export enum NewsTypeEnum {
  图文 = 'IMAGE_TEXT',
  图片 = 'IMAGE',
  视频号 = 'CHANNELS',
}

export enum NewsStatusEnum {
  /** 未发布 */
  TO_BE_PUBLISH = 'TO_BE_PUBLISH',
  /** 发布 */
  PUBLISH = 'PUBLISH',
}
