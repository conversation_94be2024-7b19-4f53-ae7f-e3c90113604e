export enum IdentityTypeEnum {
  招商顾问 = 'CONSULTANT',
  客户体验师 = 'CUSTOMER_EXPERIENCE_ENGINEER',
  基础成员 = 'MEMBER',
  '400客服' = 'CUSTOMER_SERVICE',
  面审老师 = 'INTERVIEW_TEACHER',
  大客户招商 = 'SPECIAL_CHANNEL_CONSULTANT',
  '训练大狮兄/大狮姐' = 'TRAINING_USER',
  训练经理 = 'TRAINING_MANAGER',
}

export type IdentityUserDTO = {
  id: number;
  name: string;
  identityType: IdentityTypeEnum;
  qrCodeOssKey?: string;
  qrCodeUrl?: string;
  proportion?: number;
  territories?: number[];
};

export type UpdateIdentifyConfigReq = {
  userId: number;
  identityType: IdentityTypeEnum;
  qrCodeOssKey: string;
  proportion?: number;
  territories?: number[];
};
