import { request } from '@src/common/http';
import { IdentityTypeEnum, IdentityUserDTO, UpdateIdentifyConfigReq } from './type';

export const getIdentityUserList = (params: {
  pageNum?: number;
  pageSize?: number;
  userId?: number;
  identityType?: IdentityTypeEnum;
}) =>
  request.get<{
    total: number;
    result: IdentityUserDTO[];
  }>('/api/admin/user/list', { params });

export const getIdentityUser = (userId: number) =>
  request.get<IdentityUserDTO>(`/api/admin/user/${userId}/identify/config`);

export const updateIdentifyConfig = (data: UpdateIdentifyConfigReq) =>
  request.post('/api/admin/user/identify/config', { data });
