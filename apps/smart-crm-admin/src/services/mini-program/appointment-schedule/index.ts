import { request } from '@src/common/http';
import {
  AppointmentScheduleDTO,
  AppointmentUserDTO,
  CreateAppointmentScheduleReq,
  CreateOrUpdateAppointmentUserReq,
  GetAppointmentScheduleReq,
  UpdateAppointmentScheduleReq,
} from './type';

export const getAppointmentUserList = (params: {
  pageNum?: number;
  pageSize?: number;
  userId?: number;
}) =>
  request.get<{ result: AppointmentUserDTO[]; total: number }>('/api/admin/appointment/user/page', {
    params,
  });

export const createOrUpdateAppointmentUser = (data: CreateOrUpdateAppointmentUserReq) =>
  request.post('/api/admin/appointment/user', { data });

export const deleteAppointmentUser = (id: number) =>
  request.del(`/api/admin/appointment/user/${id}`);

export const getAppointmentSchedule = (params: GetAppointmentScheduleReq) =>
  request.get<AppointmentScheduleDTO[]>('/api/admin/appointment/schedule', { params });

export const createAppointmentSchedule = (data: CreateAppointmentScheduleReq) =>
  request.post('/api/admin/appointment/schedule', { data });

export const updateAppointmentSchedule = (data: UpdateAppointmentScheduleReq) =>
  request.put('/api/admin/appointment/schedule', { data });
