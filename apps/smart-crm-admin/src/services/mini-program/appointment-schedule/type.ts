export type AppointmentUserDTO = {
  id: number;
  name: string;
  phone: string;
  status: AppointmentUserStatusEnum;
  responsibleType: ResponsibleTypeEnum;
};

export enum AppointmentUserStatusEnum {
  ENABLE = 'ENABLE',
  DISABLE = 'DISABLE',
}

export enum ResponsibleTypeEnum {
  SOCIETY = 'SOCIETY',
  CHANNEL = 'CHANNEL',
}

export type CreateOrUpdateAppointmentUserReq = {
  id?: number;
  userId?: number;
  responsibleType?: ResponsibleTypeEnum;
  status?: AppointmentUserStatusEnum;
};

export type GetAppointmentScheduleReq = {
  beginDate: string;
  endDate: string;
  interviewUserId: number;
};

export type AppointmentScheduleDTO = {
  day: string;
  timeFrames: {
    id: number;
    beginTime: string;
    lockCount: boolean;
  }[];
};

export type CreateAppointmentScheduleReq = {
  appointmentDayBegin: string;
  appointmentDayEnd: string;
  interviewManagerId: number;
  appointmentTimeList: string[];
};

export type UpdateAppointmentScheduleReq = {
  date: string;
  interviewManagerId: number;
  timeFrameList: { id: number; beginTime: string }[];
};
