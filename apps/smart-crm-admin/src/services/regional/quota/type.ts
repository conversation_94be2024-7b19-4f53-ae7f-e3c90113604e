import { BelongWarZoneEnum } from '../../business-opportunity/type';

export enum JoinRegionEnum {
  '邀请加盟区(T)' = 'T_JOIN',
  '公开加盟区(M)' = 'M_JOIN',
}

export enum RegionalQuotaOpenedEnum {
  对外开放 = 'OPENED',
  锁定业主 = 'LOCK_OWNER',
  暂停状态 = 'PAUSE',
  指定点位 = 'SPECIFY_POSITION',
}

export type RegionalQuotaDTO = {
  id: number;
  districtCode: number;
  belongWarZone: BelongWarZoneEnum;
  provinceCode: number;
  cityCode: number;
  regionCode: number;
  joinRegion: JoinRegionEnum;
  opened: RegionalQuotaOpenedEnum;
  availableQuotaNum: number;
  expUseNum: number;
  expNotUseNum: number;
  franchiseNote: string;
  recommendPosition: string;
  remainQuotaNum: number;
  formalSignNum: number;
  intentionSignNum: number;
  serviceGroupRemark: string;
  remark: string;
  updateTime: string;
};
