import { request } from '@src/common/http';
import { ExportETableListReq, GetETableListReq } from '@src/services/common/type';
import { RegionalQuotaDTO } from './type';

export const getRegionalQuotaList = (data: GetETableListReq) =>
  request.post<{ total: number; result: RegionalQuotaDTO[] }>(
    '/api/admin/franchise/area/quota/page',
    { data },
  );

export const getRegionalQuotaDetail = (id: number) =>
  request.get<RegionalQuotaDTO>(`/api/admin/franchise/area/quota/detail/${id}`);

export const exportRegionalQuota = (data: ExportETableListReq) =>
  request.post('/api/admin/franchise/area/quota/export', { data });

export const updateRegionalQuota = (data: RegionalQuotaDTO) =>
  request.put('/api/admin/franchise/area/quota/update', { data });

export const getStreetOpenedRegionIds = () =>
  request.get<number[]>('/api/admin/franchise/area/list/region-id/opened/street');

// 获取区域剩余名额
export const getAreaQuotaByDistrictCode = (data: { districtCode: string }) =>
  request.post<{ remainQuotaNum: number }>('/api/admin/franchise/area/quota/get-area-quota', {
    data,
  });
