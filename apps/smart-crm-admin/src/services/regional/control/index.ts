import { request } from '@src/common/http';
import { AreaListItemType, UpdateDistrictStreetOpenedReq } from './type';

export const getAreaList = (params: { districtId?: number; streetOpened?: boolean }) =>
  request.get<AreaListItemType[]>('/api/admin/franchise/area/list', {
    params,
  });

export const updateDistrictStreetOpened = ({ id, ...data }: UpdateDistrictStreetOpenedReq) =>
  request.put(`/api/admin/franchise/area/update-street-opened/${id}`, { data });
