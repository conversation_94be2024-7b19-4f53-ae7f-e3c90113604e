import { JoinRegionEnum } from '@src/services/regional/quota/type';

export type AreaListItemType = {
  id: number;
  districtId: number;
  districtName: string;
  districtCode: string;
  createTime: string;
  updateTime: string;
  streetOpened: boolean;
  parentId: number;
  deep: number;
  childrenList?: AreaListItemType[];
};

export type UpdateDistrictStreetOpenedReq = {
  id: number;
  streetOpened: boolean;
  franchiseAreaQuotas: {
    districtCode: string;
    joinRegion: JoinRegionEnum;
  }[];
};
