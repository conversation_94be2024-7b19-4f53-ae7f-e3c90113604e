export type GetExpansionQuotaListReq = {
  pageNum?: number;
  pageSize?: number;
  regionCode?: string;
  expansionPositionName?: string;
  useStatus?: ExpansionQuotaUseStatusEnum;
  intentionContractCode?: string;
  formalContractCode?: string;
};

export type ExportExpansionQuotaReq = Omit<GetExpansionQuotaListReq, 'pageNum' | 'pageSize'> & {
  ids?: number[];
};

export enum ExpansionQuotaUseStatusEnum {
  /** 未使用 */
  NOT_USE = 'NOT_USE',
  /** 使用 */
  USE = 'USE',
  /** 作废 */
  DISABLED = 'DISABLED',
  /** 转正 */
  FORMALIZED = 'FORMALIZED',
}

export type ExpansionQuotaPositionDTO = {
  id: number;
  expansionQuotaId: number;
  longitude: number;
  latitude: number;
  name: string;
};

export type ExpansionQuotaDTO = {
  id: number;
  regionCode: string;
  positions?: ExpansionQuotaPositionDTO[];
  useStatus: ExpansionQuotaUseStatusEnum;
  intentionContractId?: number;
  intentionContractCode?: string;
  formalContractId?: number;
  formalContractCode?: string;
  createUserId: number;
  createTime: string;
  updateUserId: number;
  updateTime: string;
};

export type CreateExpansionQuotaReq = {
  regionCode: string;
  positions: Pick<ExpansionQuotaPositionDTO, 'name' | 'longitude' | 'latitude'>[];
};

export type UpdateExpansionQuotaReq = {
  id: number;
  regionCode: string;
  positions: (Pick<ExpansionQuotaPositionDTO, 'name' | 'longitude' | 'latitude'> &
    Partial<Pick<ExpansionQuotaPositionDTO, 'id' | 'expansionQuotaId'>>)[];
};
