import { request } from '@src/common/http';
import {
  CreateExpansionQuotaReq,
  ExpansionQuotaDTO,
  ExpansionQuotaPositionDTO,
  ExportExpansionQuotaReq,
  GetExpansionQuotaListReq,
  UpdateExpansionQuotaReq,
} from './type';

export const getExpansionQuotaList = (params: GetExpansionQuotaListReq) =>
  request.get<{ total: number; result: ExpansionQuotaDTO[] }>('/api/admin/expansion/quota/page', {
    params,
  });

export const exportExpansionQuota = (params: ExportExpansionQuotaReq) =>
  request.get('/api/admin/expansion/quota/export', { params });

export const createExpansionQuota = (data: CreateExpansionQuotaReq) =>
  request.post('/api/admin/expansion/quota/save', { data });

export const updateExpansionQuota = ({ id, ...data }: UpdateExpansionQuotaReq) =>
  request.put(`/api/admin/expansion/quota/update/${id}`, { data });

export const getExpansionQuotaDetail = (id: number) =>
  request.get<ExpansionQuotaDTO>(`/api/admin/expansion/quota/detail/${id}`);

export const deleteExpansionQuota = (id: number) =>
  request.del(`/api/admin/expansion/quota/delete/${id}`);

export const batchDeleteExpansionQuota = (ids: number[]) =>
  request.del('/api/admin/expansion/quota/delete/batch', { data: { ids } });

/** 传入完整的区域编码或者最后一个编码都可以 */
export const getExpansionPositionsByDistrictCode = (districtCode: string) =>
  request.get<ExpansionQuotaPositionDTO[]>('/api/admin/expansion/quota/list/district-code', {
    params: { districtCode },
  });

export const importExpansionQuotaExcel = (data: { file: FormData }) =>
  request.post<{ failDownloadUrl: string; failNum: number; successNum: number }>(
    '/api/admin/expansion/quota/import/excel',
    {
      data,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );

export const downloadExpansionQuotaExcelTemplate = () =>
  request.get<{ url: string }>('/api/admin/expansion/quota/download/template');
