import { JoinRegionEnum } from '../quota/type';

export type RegionalQuotaTextDTO = {
  id: number;
  code: string;
  deep: number;
  name: string;
  franchiseAreaQuotaNums: {
    districtName: string;
    districtCode: string;
    remark: string;
    remainQuotaNum: number;
  }[];
};

export type GetRegionalQuotaTextProvinceListReq = {
  joinRegion: JoinRegionEnum;
  provinceCodes?: string[];
};

export type GetRegionalQuotaTextCityListReq = {
  joinRegion: JoinRegionEnum;
  provinceCode: string;
};

export type GetRegionalQuotaCityTextReq = {
  joinRegion: JoinRegionEnum;
  cityCode: string;
};
