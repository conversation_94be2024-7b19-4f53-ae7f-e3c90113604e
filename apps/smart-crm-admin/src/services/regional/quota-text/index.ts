import { request } from '@src/common/http';
import {
  GetRegionalQuotaCityTextReq,
  GetRegionalQuotaTextCityListReq,
  GetRegionalQuotaTextProvinceListReq,
  RegionalQuotaTextDTO,
} from './type';

export const getRegionalQuotaTextProvinceList = (data: GetRegionalQuotaTextProvinceListReq) =>
  request.post<RegionalQuotaTextDTO[]>('/api/admin/franchise/area/quota/text/province/list', {
    data,
  });

export const getRegionalQuotaTextCityList = ({
  provinceCode,
  joinRegion,
}: GetRegionalQuotaTextCityListReq) =>
  request.get<RegionalQuotaTextDTO[]>(
    `/api/admin/franchise/area/quota/text/city/text/list/${provinceCode}/${joinRegion}`,
  );

export const getRegionalQuotaCityText = ({ cityCode, joinRegion }: GetRegionalQuotaCityTextReq) =>
  request.get<RegionalQuotaTextDTO>(
    `/api/admin/franchise/area/quota/text/city/text/${cityCode}/${joinRegion}`,
  );
