import { request } from '@src/common/http';
import {
  ExportPaymentListReq,
  GetPaymentListReq,
  PaymentAccountListItemDTO,
  PaymentDTO,
} from './type';

export const getPaymentList = (params: GetPaymentListReq) =>
  request.get<{ total: number; result: PaymentDTO[] }>('/api/admin/payment/page', { params });

export const exportPaymentList = (params: ExportPaymentListReq) =>
  request.get('/api/admin/payment/export', { params });

export const getPaymentDetail = (id: number) =>
  request.get<PaymentDTO>(`/api/admin/payment/detail/${id}`);

/** 查询收款账户列表 */
export const getPaymentAccountList = () =>
  request.get<PaymentAccountListItemDTO[]>('/api/admin/payment/bank-payment-info/list');
