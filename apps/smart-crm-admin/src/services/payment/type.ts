import { AuditStatusEnum, FileDTO } from '../common/type';

export type GetPaymentListReq = {
  pageNum?: number;
  pageSize?: number;
  code?: string;
  contractCode?: string;
  createBeginTime?: string;
  createEndTime?: string;
  directUserId?: number;
  auditStatusList?: AuditStatusEnum[];
};

export type ExportPaymentListReq = Omit<GetPaymentListReq, 'pageNum' | 'pageSize'> & {
  ids?: number[];
};

export enum ContractTypeEnum {
  意向合同 = 'INTENTION',
  正式合同 = 'FORMAL',
  搬迁意向合同 = 'RELOCATION_INTENTION',
  搬迁正式合同 = 'RELOCATION_FORMAL',
  门店重装合同 = 'RENOVATION',
  变更法人合同 = 'CHANGE_LEGAL_PERSON',
}

export enum PaymentMannerEnum {
  线下汇款 = 'OFFLINE_MONEY_TRANSFERS',
  网上转账 = 'ONLINE_TRANSFERS',
}

export type PaymentDTO = {
  id: number;
  code: string;
  customerId: number;
  customerName: string;
  contractType: ContractTypeEnum;
  contracts: { id: number; code: string }[];
  paymentTime: string;
  paymentAmount: number;
  paymentManner: PaymentMannerEnum;
  receivingAccount: string;
  receivingNumber: string;
  openBank: string;
  auditStatus: AuditStatusEnum;
  remark: string;
  accountSerialNumber: string;
  // 列表不返回
  attachments: FileDTO[];
  payerName: string;
  payerAccount: string;
  // 列表不返回
  receiptScreenshotAttachment: FileDTO[];
  createUserId: number;
  createTime: string;
  updateUserId: number;
  updateTime: string;
};

export type PaymentAccountListItemDTO = {
  recipientAccountNo: string;
  recipientAccountName: string;
  bankBranchName: string;
  externalSystemId: string;
};
