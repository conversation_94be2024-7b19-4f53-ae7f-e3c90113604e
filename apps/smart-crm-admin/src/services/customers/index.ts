import { request } from '@src/common/http';
import {
  BatchUpdateCustomerScoreReq,
  CreateCustomerReq,
  CustomerDTO,
  ExportCustomerReq,
  FranchiseDTO,
  GetClueListByRelCustomerRes,
  GetCustomerListReq,
  UpdateCustomerFieldReq,
} from './type';

export const getCustomerList = (params: GetCustomerListReq) =>
  request.get<{ total: number; result: CustomerDTO[] }>('/api/admin/customer/page', { params });

export const getCustomerDetail = (id: number) =>
  request.get<CustomerDTO>(`/api/admin/customer/detail/${id}`);

export const getClueListByRelCustomer = (id: number) =>
  request.get<GetClueListByRelCustomerRes>(`/api/admin/customer/list/clue/rel/${id}`);

export const deleteCustomer = (id: number) => request.del(`/api/admin/customer/delete/${id}`);

export const batchDeleteCustomer = (data: { ids: number[] }) =>
  request.del('/api/admin/customer/delete/batch', { data });

export const exportCustomer = (params: ExportCustomerReq) =>
  request.get('/api/admin/customer/export', { params });

export const createCustomer = (data: CreateCustomerReq) =>
  request.post('/api/admin/customer/save', { data });

export const checkCustomerPhone = (data: { id?: number; phone: string }) =>
  request.post<boolean>('/api/admin/customer/valid/phone/exists', { data });

export const checkCustomerIdentityCard = (data: { id?: number; identityCard: string }) =>
  request.post<boolean>('/api/admin/customer/valid/identity-card/exists', { data });

export const updateCustomerField = ({ id, ...data }: UpdateCustomerFieldReq) =>
  request.put<CustomerDTO>(`/api/admin/customer/update/${id}`, { data });

export const downloadCustomerExcelTemplate = () =>
  request.get<{ url: string }>('/api/admin/customer/download/template');

export const importCustomerExcel = (data: { file: FormData }) =>
  request.post<{ failDownloadUrl: string; failNum: number; successNum: number }>(
    '/api/admin/customer/import/excel',
    {
      data,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );

export const batchUpdateCustomerScore = (data: BatchUpdateCustomerScoreReq) =>
  request.post('/api/admin/customer/update/batch', { data });

// 查询是否是员工
export const getIsStaffByCustomerId = (customerId: number) =>
  request.post<{ customerId: number; staff: boolean }>('/api/admin/customer/staff/queryById', {
    data: { customerId },
  });

export const getFranchiseList = (params: GetCustomerListReq) =>
  request.get<{ total: number; result: FranchiseDTO[] }>('/api/admin/franchise/profile/page', {
    params,
  });

export const getFranchiseDetail = (id: number) =>
  request.get<FranchiseDTO>(`/api/admin/franchise/profile/detail/${id}`);

export const exportFranchise = (params: ExportCustomerReq) =>
  request.get('/api/admin/franchise/profile/export', { params });
