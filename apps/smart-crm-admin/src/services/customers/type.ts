import { GenderEnum } from '../clues/my/type';
import { FileDTO } from '../common/type';
import { StoreCategoryEnum } from '../contract/formal/type';
import { ShopStatusEnum } from '../shop/list/type';

export type GetCustomerListReq = {
  pageNum?: number;
  pageSize?: number;
  name?: string;
  code?: string;
  phone?: string;
  identityCard?: string;
  createUserId?: number;
  beginCreateTime?: string;
  endCreateTime?: string;
  customerScoreLevels?: string[];
};

export type ExportCustomerReq = Omit<GetCustomerListReq, 'pageNum' | 'pageSize'> & {
  ids?: number[];
};

export enum CustomerScoreSourceTypeEnum {
  /** 来源大数据 */
  BigData = 1,
  /** 来源客户编辑 */
  Normal,
}

export type CustomerDTO = {
  id: number;
  code: string;
  name: string;
  gender: GenderEnum;
  phones?: string[];
  phonesSensitive?: string[];
  birthday: string;
  identityCard: string;
  identityCardSensitive: string;
  nativePlace: string;
  address: string;
  createUserId: number;
  updateUserId: number;
  createTime: string;
  updateTime: string;
  realAuthFlag: boolean;
  highestEducationLevel: HighestEducationLevelEnum;
  educationLevelProves: FileDTO[];
  interviewNoArrive: boolean;
  redLine: boolean;
  blacklist: boolean;
  customerScore: number;
  customerScoreLevel: CustomerScoreLevelEnum;
  customerScoreSourceType: CustomerScoreSourceTypeEnum;
  customerNature: StoreCategoryEnum;
  belongArea: string;
  staff: boolean;
  age: string;
  hasShop: boolean;
  hasClue: boolean;
};

export enum CustomerScoreLevelEnum {
  A = 'A',
  B = 'B',
  C = 'C',
  D = 'D',
}

export type FranchiseShopDTO = {
  shopId: string;
  shopStatus: ShopStatusEnum;
  shopName: string;
  shopLevel: string;
  district: string;
};

export type FranchiseDTO = CustomerDTO & {
  customerId: number;
  shops: string[];
  shopRelList: FranchiseShopDTO[];
};

export enum HighestEducationLevelEnum {
  小学 = 'PRIMARY_SCHOOL',
  初中 = 'MIDDLE_SCHOOL',
  '高中 (高职、高技、中专)' = 'HIGH_SCHOOL',
  专科 = 'JUNIOR_COLLEGE',
  本科 = 'UNDERGRADUATE_DEGREE',
  硕士研究生 = 'MASTER_DEGREE',
  博士研究生 = 'DOCTORAL_DEGREE',
}

export type CreateCustomerReq = {
  name: string;
  phones: string[];
  gender?: GenderEnum;
  birthday?: string;
  identityCard?: string;
  nativePlace?: string;
  address?: string;
  highestEducationLevel?: HighestEducationLevelEnum;
  educationLevelProves?: FileDTO[];
  customerScoreLevel?: string;
  customerScore?: number;
  customerNature?: StoreCategoryEnum;
  belongArea?: string;
};

export type UpdateCustomerFieldReq = CreateCustomerReq & {
  id: number;
};

export type BatchUpdateCustomerScoreReq = {
  customerLevel: string;
  customerScore: number;
  ids: number[];
};

export type GetClueListByRelCustomerRes = {
  clueId: number;
  clueName: string;
  cluePhone: string;
  cluePhoneSensitive: string;
  createTime: string;
}[];
