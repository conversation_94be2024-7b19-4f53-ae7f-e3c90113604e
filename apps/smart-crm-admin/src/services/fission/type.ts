import { BelongWarZoneEnum, ChannelTypeEnum } from '../business-opportunity/type';
import { AuditStatusEnum } from '../common/type';
import { StoreCategoryEnum } from '../contract/formal/type';
import { CustomerScoreLevelEnum } from '../customers/type';

export type GetFissionListReq = {
  pageNum?: number;
  pageSize?: number;
  customerName?: string;
  bizOpportunityName?: string;
  bizOpportunityDirectUserId?: number;
  fissionAttribute?: string;
  shopType?: ShopTypeEnum;
};

export type ExportFissionReq = Omit<GetFissionListReq, 'pageNum' | 'pageSize'> & {
  ids?: number[];
};

export enum ShopTypeEnum {
  社会店 = 'SOCIETY',
  渠道店 = 'CHANNEL',
}

export enum FissionAttributeEnum {
  常规裂变 = 'CONVENTIONAL_FISSION',
  加密裂变 = 'CRYPTO_FISSION',
}

export type FissionDTO = {
  id: number;
  code: string;
  customerName: string;
  customerId: number;
  customerNature: StoreCategoryEnum;
  phone: string;
  phoneSensitive: string;
  channelTypes: ChannelTypeEnum[];
  bizOpportunityName: string;
  bizOpportunityId: number;
  bizOpportunityDirectUserId: number;
  bizOpportunityCode: string;
  auditStatus: AuditStatusEnum;
  shopType: ShopTypeEnum;
  fissionAttribute: FissionAttributeEnum;
  belongWarZone: BelongWarZoneEnum;
  intentionRegion: string;
  intentionPositions?: {
    latitude: number;
    longitude: number;
    name: string;
  }[];
  planStoreCount: number;
  newStoreReserveManagers: {
    id: number;
    identityCard: string;
    identityCardSensitive: string;
    name: string;
    phones: string[];
    phonesSensitive: string[];
  }[];
  originalStoreManagerCount: number;
  originalStoreReserveManagers?: {
    id: number;
    identityCard: string;
    identityCardSensitive: string;
    name: string;
    phones: string[];
    phonesSensitive: string[];
  }[];
  relStore: string;
  customerHasScoreLevel: boolean;
  crmManagerUserId: number;
  customerScore: number;
  customerScoreLevel: CustomerScoreLevelEnum;
  createUserId: number;
  createTime: string;
  updateUserId: number;
  updateTime: string;
  remark: string;
};

export type CreateFissionReq = {
  bizOpportunityId: number;
} & Pick<
  FissionDTO,
  | 'customerId'
  | 'customerScore'
  | 'customerScoreLevel'
  | 'shopType'
  | 'channelTypes'
  | 'fissionAttribute'
  | 'phone'
  | 'intentionRegion'
  | 'belongWarZone'
  | 'intentionPositions'
  | 'planStoreCount'
  | 'newStoreReserveManagers'
  | 'originalStoreManagerCount'
  | 'originalStoreReserveManagers'
  | 'relStore'
>;

export type UpdateFissionReq = { id: number } & CreateFissionReq;
