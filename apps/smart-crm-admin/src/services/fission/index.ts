import { request } from '@src/common/http';
import {
  CreateFissionReq,
  ExportFissionReq,
  FissionDTO,
  GetFissionListReq,
  UpdateFissionReq,
} from './type';
import { AuditHistoryType } from '../common/type';
import { CustomerScoreLevelEnum } from '../customers/type';

export const getFissionList = (params: GetFissionListReq) =>
  request.get<{ total: number; result: FissionDTO[] }>('/api/admin/fission/apply/page', { params });

export const exportFission = (params: ExportFissionReq) =>
  request.get('/api/admin/fission/apply/export', { params });

export const getFissionDetail = (id: number) =>
  request.get<FissionDTO>(`/api/admin/fission/apply/detail/${id}`);

export const createFission = (data: CreateFissionReq) =>
  request.post('/api/admin/fission/apply/save', { data });

export const updateFission = ({ id, ...data }: UpdateFissionReq) =>
  request.put(`/api/admin/fission/apply/update/${id}`, { data });

export const passFissionAudit = (data: { id: number; description?: string }) =>
  request.post('/api/admin/fission/apply/approve/task', { data });

export const rejectFissionAudit = (data: { id: number; description: string }) =>
  request.post('/api/admin/fission/apply/reject/task', { data });

export const revokeFissionAudit = (id: number) =>
  request.post(`/api/admin/fission/apply/cancel/process/instance/${id}`);

export const getFissionAuditHistory = (id: number) =>
  request.get<AuditHistoryType[]>(`/api/admin/fission/apply/audit/history/${id}`);

export const submitFissionAudit = (id: number) =>
  request.put(`/api/admin/fission/apply/submit/${id}`);

export const getFissionAuditAllHistory = (id: number) =>
  request.get<{ auditHistories: AuditHistoryType[]; name: string; processInstanceId: number }[]>(
    `/api/admin/fission/apply/all/audit/history/${id}`,
  );

export const getCustomerScoreByDistrictCode = (params: { id: number; districtCode: string }) =>
  request.get<{ customerScore: string; customerScoreLevel: CustomerScoreLevelEnum }>(
    '/api/admin/customer/customer-score/optimal',
    { params },
  );
