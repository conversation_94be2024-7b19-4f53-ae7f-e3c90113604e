import { FileDTO } from '../common/type';

export enum WorkflowBusinessTypeEnum {
  /** 培训人员安排 */
  SCRM_TRAINING_PERSON_ARRANGEMENT = 'SCRM_TRAINING_PERSON_ARRANGEMENT',
  /** 培训人员变更 */
  SCRM_TRAINING_PERSON_CHANGE = 'SCRM_TRAINING_PERSON_CHANGE',
  /** 搬迁意向区域变更 */
  SCRM_RELOCATION_INTENTION_REGION_CHANGE = 'SCRM_RELOCATION_INTENTION_REGION_CHANGE',
  /** 证照更新任务 */
  SCRM_SHOP_LICENSE_TRACE_AUDIT = 'SCRM_SHOP_LICENSE_TRACE_AUDIT',
}

export enum OperationStatusEnum {
  /** 发起 */
  START = 1,
  /** 审批通过 */
  PASS = 2,
  /** 审批不通过 */
  REJECT = 3,
  /** 撤回 */
  REVOKE = 4,
}

export enum WorkflowAuditStatusEnum {
  /** 审核中 */
  AUDIT = 1,
  /** 审核通过 */
  APPROVE = 2,
  /** 审核不通过 */
  REJECT = 3,
  /** 撤回 */
  REVOKE = 4,
  /** 加签 */
  ADD_SIGN = 5,
  /** 驳回 */
  RETURN = 6,
}

/** 审核操作类型 */
export enum WorkflowAuditOperationTypeEnum {
  APPROVE = 'approve',
  REJECT = 'reject',
  REVOKE = 'revoke',
  RETURN = 'return',
}

export type WorkflowHistoryRecordDTO = {
  id: number;
  processInstanceId: string;
  name: string;
  taskId: string;
  auditUserId: number;
  auditUserName: string;
  operationStatus: OperationStatusEnum;
  reason: string;
  description: string;
  auditTime: string;
  attachments: FileDTO[];
  auditUserIds: number[];
  taskName: string;
  taskDefKey: string;
};

export type WorkflowHistoryDTO = {
  businessId: string;
  businessName: string;
  processInstanceId: string;
  records: WorkflowHistoryRecordDTO[];
};

export type WorkFlowAuditDTO = {
  businessId: number;
  businessType: WorkflowBusinessTypeEnum;
  businessName: string;
  processInstanceId?: string;
  rejectType?: string;
  addSignType?: string;
  reason?: string;
  description?: string;
  remark?: string;
  addSignAssignee?: string;
  attachments?: FileDTO[];
  extendParams?: Record<string, any>;
  userId?: number;
};
