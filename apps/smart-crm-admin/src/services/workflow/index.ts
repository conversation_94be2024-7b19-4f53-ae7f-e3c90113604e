import { request } from '@src/common/http';
import { WorkFlowAuditDTO, WorkflowBusinessTypeEnum, WorkflowHistoryDTO } from './type';
import { AuditHistoryType } from '../common/type';

/** 根据业务查询审批历史记录 */
export const getWorkflowHistory = (params: {
  businessId: string;
  businessType: WorkflowBusinessTypeEnum;
}) => {
  return request.get<WorkflowHistoryDTO[]>(
    `/api/admin/workflow/history/${params.businessId}/${params.businessType}`,
  );
};

/** 根据实例id查询审批记录 */
export const getWorkflowHistoryByInstanceId = (params: { processInstanceId: string }) => {
  return request.get<AuditHistoryType[]>(`/api/admin/workflow/history/${params.processInstanceId}`);
};

/** 审核通过 */
export const auditPass = (params: WorkFlowAuditDTO) => {
  return request.post<{ finish: boolean }>(`/api/admin/workflow/approve`, {
    data: params,
  });
};

/* 审核不通过 */
export const auditReject = (params: WorkFlowAuditDTO) => {
  return request.post<void>(`/api/admin/workflow/reject`, {
    data: params,
  });
};

/* 加签 */
export const auditAddSign = (params: WorkFlowAuditDTO) => {
  return request.post<void>(`/api/admin/workflow/addSign`, {
    data: params,
  });
};

/* 驳回 */
export const auditReturn = (params: WorkFlowAuditDTO) => {
  return request.post<void>(`/api/admin/workflow/doReturn`, {
    data: params,
  });
};

/** 取消 */
export const auditCancel = (params: WorkFlowAuditDTO) => {
  return request.post<void>(`/api/admin/workflow/cancel`, {
    data: params,
  });
};

/** 提交 */
export const submitWorkflow = (params: WorkFlowAuditDTO) => {
  return request.post<void>(`/api/admin/workflow/submit`, {
    data: params,
  });
};
