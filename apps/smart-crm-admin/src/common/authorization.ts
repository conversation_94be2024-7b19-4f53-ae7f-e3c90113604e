import Cookies from 'js-cookie';

const COOKIE_KEY = import.meta.env.VITE_GAIA_TOKEN_KEY;

const getToken = (): string | undefined => {
  return Cookies.get(COOKIE_KEY);
};

const setToken = (token: string) => {
  Cookies.set(COOKIE_KEY, token, {
    domain: '.tastientech.com',
    path: '/',
    SameSite: 'Lax',
    secure: true,
  });
};

const removeToken = () => {
  Cookies.remove(COOKIE_KEY, {
    domain: '.tastientech.com',
    path: '/',
    SameSite: 'Lax',
    secure: true,
  });
};

export { getToken, setToken, removeToken };
