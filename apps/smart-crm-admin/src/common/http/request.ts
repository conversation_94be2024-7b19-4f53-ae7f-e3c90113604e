import { navigateToGaiaLogin } from '@src/utils';
import { notification } from 'antd';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { http, UnauthorizedException } from './axios';
import { HttpResult } from './result';
import { removeToken } from '../authorization';

const errorHandler = (alertErr: boolean) => (error: any) => {
  if (error) {
    if (error instanceof UnauthorizedException) {
      notification.error({ message: '错误提示', description: error.message });
      removeToken();
      navigateToGaiaLogin();
    } else if (alertErr) {
      const { response, message } = error;
      const description = response?.data.msg || response?.data?.message || message || '';
      const code = response?.data?.code || response?.statusCode || '';
      const title = code ? `错误提示` : `未知错误`;

      notification.error({
        message: title,
        description,
      });

      return Promise.reject(error);
    }

    return Promise.reject(error);
  }

  return Promise.reject(error);
};

type HttpGet<T, D> = typeof http.get<HttpResult<T>, AxiosResponse<HttpResult<T>, D>, D>;

type HttpPost<T, D> = typeof http.post<HttpResult<T>, AxiosResponse<HttpResult<T>, D>, D>;

type HttpPut<T, D> = typeof http.put<HttpResult<T>, AxiosResponse<HttpResult<T>, D>, D>;

type HttpDelete<T, D> = typeof http.delete<HttpResult<T>, AxiosResponse<HttpResult<T>, D>, D>;

// 这边通过将http的方法重新封装一遍，是为了于axios 库解耦，方便后续替换
const get = <T = any, D = any>(...arg: Parameters<HttpGet<T, D>>): ReturnType<HttpGet<T, D>> =>
  http.get(...arg);
const post = <T = any, D = any>(...arg: Parameters<HttpPost<T, D>>): ReturnType<HttpPost<T, D>> =>
  http.post(...arg);
const put = <T = any, D = any>(...arg: Parameters<HttpPut<T, D>>): ReturnType<HttpPut<T, D>> =>
  http.put(...arg);
const del = <T = any, D = any>(
  ...arg: Parameters<HttpDelete<T, D>>
): ReturnType<HttpDelete<T, D>> => http.delete(...arg);

type Options = AxiosRequestConfig & {
  noAlert?: boolean;
  useOriginResponse?: boolean;
};

const customRequest =
  (method: 'POST' | 'GET' | 'DELETE' | 'PUT') =>
  <T = any>(url: string, options?: Options) => {
    const { useOriginResponse, ...restOptions } = options || {};

    return http
      .request<HttpResult<T>>({
        url,
        ...restOptions,
        method,
      })
      .then(async (rs) => {
        if (useOriginResponse) return rs as unknown as T;

        return rs.data.data;
      })
      .catch(errorHandler(!restOptions.noAlert));
  };

const request = {
  get: customRequest('GET'),
  post: customRequest('POST'),
  del: customRequest('DELETE'),
  put: customRequest('PUT'),
};

export { del, get, post, put, request };
