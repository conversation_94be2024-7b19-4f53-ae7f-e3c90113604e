import { AxiosResponse } from 'axios';
import { HttpResult } from './result';

/**
 * 没有登录或者登录已过期的异常
 */
export class UnauthorizedException extends Error {
  constructor(message = '未登录或登录已过期，请重新登录') {
    super(message);
    this.name = 'UnauthorizedException';
  }
}

/**
 * 没有权限的异常
 */
export class ForbiddenException extends Error {
  constructor(message = '没有权限') {
    super(message);
    this.name = 'ForbiddenException';
  }
}

/**
 * 服务器端异常
 */

export class ServerErrorException extends Error {
  /** 本次请求的axios response */
  public readonly response?: AxiosResponse<HttpResult<unknown>>;
  constructor(message = '服务器异常', response?: AxiosResponse<HttpResult<unknown>>) {
    super(message);
    this.response = response;
    this.name = 'ServerErrorException';
  }
}
