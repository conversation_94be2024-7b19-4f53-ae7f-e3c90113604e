import { isInIcestark } from '@ice/stark-app';
import axios from 'axios';
import qs from 'qs';
import { ServerErrorException, UnauthorizedException } from './errors';
import { getToken } from '../authorization';

export * from './errors';

const http = axios.create({
  paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' }),
  baseURL: isInIcestark() ? import.meta.env.VITE_API_URL : undefined,
});

http.interceptors.request.use((config) => {
  const token = getToken();

  if (token) {
    config.headers['User-Token'] = token;
    config.headers['Brand-Id'] = localStorage.getItem('brandId');
  }

  return config;
});

http.interceptors.response.use(
  (response) => {
    const { data } = response;

    // 根据后端请求结构，如果code不为200，则认为是异常
    if (data.code !== 200) {
      // 抛出服务器异常，并且携带response
      return Promise.reject(new ServerErrorException(data.message, response));
    }

    return response;
  },
  (error) => {
    const errorCode = [
      error.response?.status,
      error.response?.data?.code,
      error.response?.data?.errCode,
    ];

    if (errorCode.includes(401)) {
      return Promise.reject(new UnauthorizedException());
    }

    if (errorCode.includes(500)) {
      return Promise.reject(new ServerErrorException(error.response.data.message, error.response));
    }

    if (error.response?.data?.message) {
      return Promise.reject(new ServerErrorException(error.response.data.message, error.response));
    }

    return Promise.reject(error);
  },
);

export { http };
