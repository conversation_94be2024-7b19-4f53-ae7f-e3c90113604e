import React from 'react';
import { ValueType } from '@src/components/ETable';
import { FormItemProps } from 'antd';

export interface ModalRef {
  open: () => void;
  close: () => void;
}

export type DescriptionFieldNormalType<T = any> = {
  field: keyof T | [keyof T, ...string[]] | (string & {});
  label: React.ReactNode;
  valueType?: ValueType;
  fieldProps?: Record<string, any>;
  formItemProps?: FormItemProps;
  hidden?: boolean;
};

export type DescriptionFieldChildrenType = {
  label: React.ReactNode;
  hidden?: boolean;
  children: React.ReactNode;
};

export type DescriptionFieldType<T = any> =
  | DescriptionFieldNormalType<T>
  | DescriptionFieldChildrenType;

export type DescriptionFieldGroupType<T = any> = {
  label: string;
  children: DescriptionFieldType<T>[];
};
