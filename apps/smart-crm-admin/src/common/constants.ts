/**
 * 这个文件放有可能被多个模块共用到的常量与枚举值定义
 * 假如发现一个常量被多个模块用到，可以提升到这个位置
 */

import {
  BusinessOpportunityTypeEnum,
  ProgressNodeEnum,
  ProgressStateEnum,
} from '@src/services/business-opportunity/type';
import { AuditStatusEnum } from '@src/services/common/type';
import { WorkflowAuditStatusEnum } from '@src/services/workflow/type';
import checkMobile from 'rc-util/lib/isMobile';

// 文件上传大小统一限制，单位 M
export const FILE_UPLOAD_MAX_SIZE = 100;
export const pdfFileType = 'application/pdf';
export const docxFileType =
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
export const phonePattern = /^1[0-9]{10}$/;
export const idCardPattern =
  /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;

export const isMobile = checkMobile();

export const franchiseAuditStatusOptions = [
  { label: '审核中', value: AuditStatusEnum.AUDIT },
  { label: '待复核', value: AuditStatusEnum.TO_BE_REVIEWED },
  { label: '通过', value: AuditStatusEnum.PASS },
  { label: '未通过', value: AuditStatusEnum.NO_PASS },
];

export const evaluationAuditStatusOptions = [
  { label: '审核中', value: AuditStatusEnum.AUDIT },
  { label: '通过', value: AuditStatusEnum.PASS },
  { label: '未通过', value: AuditStatusEnum.NO_PASS },
];

export const commonBOTypeOptions = [
  { label: '社会店', value: BusinessOpportunityTypeEnum.SOCIETY },
  { label: '渠道店', value: BusinessOpportunityTypeEnum.CHANNEL },
];

export const fissionBOTypeOptions = [
  { label: '裂变社会店', value: BusinessOpportunityTypeEnum.FISSION_SOCIETY },
  { label: '裂变特渠店', value: BusinessOpportunityTypeEnum.FISSION_CHANNEL },
];

export const progressNodesAndProgressStatesArray = [
  {
    value: ProgressNodeEnum.意向收集,
    children: [ProgressStateEnum['意向收集：已填写']],
  },
  {
    value: ProgressNodeEnum.裂变申请,
    children: [
      ProgressStateEnum['裂变OA审批：未发起'],
      ProgressStateEnum['裂变OA审批：审批中'],
      ProgressStateEnum['裂变OA审批：未通过'],
    ],
  },
  {
    value: ProgressNodeEnum.加盟申请收集,
    children: [ProgressStateEnum['加盟申请：待填写'], ProgressStateEnum['加盟申请：已填写']],
  },
  {
    value: ProgressNodeEnum.加盟申请审核,
    children: [
      ProgressStateEnum['加盟审核：审批中'],
      ProgressStateEnum['加盟审核：通过'],
      ProgressStateEnum['加盟审核：复核中'],
      ProgressStateEnum['加盟审核：未通过'],
    ],
  },
  {
    value: ProgressNodeEnum.预约面谈,
    children: [ProgressStateEnum['预约面审：待预约'], ProgressStateEnum['预约面审：已预约']],
  },
  {
    value: ProgressNodeEnum.面谈审核,
    children: [
      ProgressStateEnum['面审审核：未创建'],
      ProgressStateEnum['面审审核：审批中'],
      ProgressStateEnum['面审审核：审批通过'],
      ProgressStateEnum['面审审核：审批不通过'],
    ],
  },
  {
    value: ProgressNodeEnum.意向合同,
    children: [
      ProgressStateEnum['意向合同：台账意向未创建'],
      ProgressStateEnum['意向合同：意向签约中'],
      ProgressStateEnum['意向合同：取消意向签约'],
      ProgressStateEnum['意向合同：已归档'],
    ],
  },
  {
    value: ProgressNodeEnum.流程结束,
    children: [ProgressStateEnum['流程结束：已签约'], ProgressStateEnum['流程结束：已放弃']],
  },
];

// 合同和打款的审核状态
export const contractAuditStatusOptions = [
  {
    label: '未提交',
    value: AuditStatusEnum.NOT_SUBMIT,
  },
  {
    label: '审核中',
    value: AuditStatusEnum.AUDIT,
  },
  {
    label: '驳回',
    value: AuditStatusEnum.RETURN,
  },
  {
    label: '审核通过',
    value: AuditStatusEnum.PASS,
  },
];

export const yesOrNoOptions = [
  { label: '是', value: true },
  { label: '否', value: false },
];

export const fissionAuditStatusOptions = [
  {
    label: '未提交',
    value: AuditStatusEnum.NOT_SUBMIT,
  },
  {
    label: '审核中',
    value: AuditStatusEnum.AUDIT,
  },
  {
    label: '驳回',
    value: AuditStatusEnum.RETURN,
  },
  {
    label: '审核通过',
    value: AuditStatusEnum.PASS,
  },
  {
    label: '审核不通过',
    value: AuditStatusEnum.NO_PASS,
  },
];

export const intendedAreaChangeAuditStatusOptions = [
  {
    label: '审核中',
    value: AuditStatusEnum.AUDIT,
  },
  {
    label: '通过',
    value: AuditStatusEnum.PASS,
  },
  {
    label: '不通过',
    value: AuditStatusEnum.NOT_PASS,
  },
  {
    label: '撤回',
    value: AuditStatusEnum.REVOKE,
  },
];

export const intendedContractExtensionAuditStatusOptions = [
  {
    label: '审核中',
    value: AuditStatusEnum.AUDIT,
  },
  {
    label: '通过',
    value: AuditStatusEnum.PASS,
  },
  {
    label: '不通过',
    value: AuditStatusEnum.NOT_PASS,
  },
  {
    label: '撤回',
    value: AuditStatusEnum.REVOKE,
  },
];

export const workflowAuditStatusOptions = [
  {
    label: '审核中',
    value: WorkflowAuditStatusEnum.AUDIT,
  },
  {
    label: '审核通过',
    value: WorkflowAuditStatusEnum.APPROVE,
  },
  {
    label: '审核不通过',
    value: WorkflowAuditStatusEnum.REJECT,
  },
  {
    label: '撤回',
    value: WorkflowAuditStatusEnum.REVOKE,
  },
];

export const commonProgressNodeOptions = [
  {
    label: '意向收集',
    value: ProgressNodeEnum.意向收集,
  },
  {
    label: '加盟申请收集',
    value: ProgressNodeEnum.加盟申请收集,
  },
  {
    label: '加盟申请审核',
    value: ProgressNodeEnum.加盟申请审核,
  },
  {
    label: '预约面谈',
    value: ProgressNodeEnum.预约面谈,
  },
  {
    label: '面谈审核',
    value: ProgressNodeEnum.面谈审核,
  },
  {
    label: '意向合同',
    value: ProgressNodeEnum.意向合同,
  },
  {
    label: '流程结束',
    value: ProgressNodeEnum.流程结束,
  },
];

export const fissionProgressNodeOptions = [
  {
    label: '意向收集',
    value: ProgressNodeEnum.意向收集,
  },
  {
    label: '裂变申请',
    value: ProgressNodeEnum.裂变申请,
  },
  {
    label: '意向合同',
    value: ProgressNodeEnum.意向合同,
  },
  {
    label: '流程结束',
    value: ProgressNodeEnum.流程结束,
  },
];
