import { useCallback, useState } from 'react';
import useUserStore from '@src/store/useUserStore';
import { App } from 'antd';
import { useEvent } from 'rc-util';

type ValueOf<T> = T[keyof T];

export type PermissionType = ValueOf<typeof PermissionsMap>;

export const commonCheckPermission = (permissions: string[], permission?: PermissionType) => {
  return (
    // 开发环境中台有问题，这里特殊判断
    import.meta.env.MODE === 'dev' ||
    import.meta.env.DEV ||
    !permission ||
    permissions.includes(permission)
  );
};

export const usePermission = () => {
  const { permissions } = useUserStore();

  const checkPermission = useCallback(
    (permission?: PermissionType) => commonCheckPermission(permissions, permission),
    [permissions],
  );

  return checkPermission;
};

export const Permission = ({
  children,
  value,
}: {
  children: React.ReactNode;
  value: PermissionType;
}) => {
  const checkPermission = usePermission();

  return checkPermission(value) ? children : null;
};

export const usePermissionOpen = (
  permission: PermissionType,
): [boolean, (value: boolean) => void] => {
  const [open, setInternalOpen] = useState(false);
  const checkPermission = usePermission();
  const { notification } = App.useApp();

  const setOpen = useEvent((value: boolean) => {
    if (value && !checkPermission(permission)) {
      notification.warning({
        message: '访问受限',
        description: '您没有该详情页权限，请联系管理员开通',
      });

      return;
    }

    setInternalOpen(value);
  });

  return [open, setOpen];
};

export const PermissionsMap = {
  // ----------------------------------- 成员管理 -----------------------------------

  /** 成员管理 */
  Users: 'users',
  /** 权限配置 */
  UsersAuth: 'users:auth',
  /** 编辑 */
  UsersAuthUpdate: 'users:auth:update',

  // ----------------------------------- 客户管理 -----------------------------------

  /** 线索管理 */
  Clues: 'clues',

  /** 我的线索 */
  MyClues: 'clues:my',
  /** 我的线索-录入线索 */
  MyCluesImport: 'clues:my:import',
  /** 我的线索-放弃线索 */
  MyCluesAbandon: 'clues:my:abandon',
  /** 我的线索-转让线索 */
  MyCluesTransfer: 'clues:my:transfer',
  /** 我的线索-删除线索 */
  MyCluesDelete: 'clues:my:delete',
  /** 我的线索-导出数据 */
  MyCluesExport: 'clues:my:export',
  /** 我的线索-拨打电话 */
  MyCluesCall: 'clues:my:call',
  /** 我的线索-添加跟进 */
  MyCluesFollow: 'clues:my:follow',

  /** 线索池 */
  CluePool: 'clues:pool',
  /** 线索池列表 */
  CluePoolList: 'clues:pool:list',
  /** 线索池-录入线索 */
  CluePoolListImport: 'clues:pool:list:import',
  /** 线索池-领取线索 */
  CluePoolListReceive: 'clues:pool:list:receive',
  /** 线索池-转移线索池 */
  CluePoolListTransfer: 'clues:pool:list:transfer',
  /** 线索池-删除线索 */
  CluePoolListDelete: 'clues:pool:list:delete',
  /** 线索池-导出数据 */
  CluePoolListExport: 'clues:pool:list:export',
  /** 线索池-分配线索 */
  CluePoolListAssign: 'clues:pool:list:assign',
  /** 线索池-拨打电话 */
  CluePoolListCall: 'clues:pool:list:call',
  /** 线索池管理 */
  CluePoolManager: 'clues:pool:management',
  /** 线索池管理-添加 */
  CluePoolManagerCreate: 'clues:pool:management:create',
  /** 线索池管理-编辑 */
  CluePoolManagerUpdate: 'clues:pool:management:update',
  /** 线索池管理-删除 */
  CluePoolManagerDelete: 'clues:pool:management:delete',

  // ----------------------------------- 客户管理 -----------------------------------
  Customers: 'customers',
  CustomersExport: 'customers:export',
  CustomersDelete: 'customers:delete',
  CustomerBatchUpdateScore: 'customers:batch-update-score',

  // ----------------------------------- 商机管理 -----------------------------------
  BusinessOpportunity: 'business-opportunity',
  BusinessOpportunityExport: 'business-opportunity:export',
  BusinessOpportunityDelete: 'business-opportunity:delete',

  // ----------------------------------- 预约记录 -----------------------------------
  AppointmentRecord: 'appointment-record',
  AppointmentRecordExport: 'appointment-record:export',
  AppointmentRecordBatchConfirmVisit: 'appointment-record:batch-confirm-visit',
  AppointmentRecordCancel: 'appointment-record:cancel',

  // ----------------------------------- 加盟申请表 -----------------------------------

  /** 加盟申请表 */
  FranchiseApplication: 'franchise-application',
  /** 加盟申请表-导出 */
  FranchiseApplicationExport: 'franchise-application:export',
  /** 加盟申请表-超管编辑 */
  FranchiseApplicationSuperEdit: 'franchise-application:super-edit',

  // ----------------------------------- 面审客户到访表 -----------------------------------

  /** 面审客户到访表 */
  Visit: 'visit',
  /** 面审客户到访表-导出 */
  VisitExport: 'visit:export',
  /** 面审客户到访表-转让 */
  VisitTransfer: 'visit:transfer',
  /** 面审客户到访表-新建面审评估 */
  VisitCreateEvaluation: 'visit:create-evaluation',

  // ----------------------------------- 面审评估信息表 -----------------------------------

  /** 面审评估信息表 */
  Evaluation: 'evaluation',
  /** 面审评估信息表-导出 */
  EvaluationExport: 'evaluation:export',

  // ----------------------------------- 合同管理 -----------------------------------

  Contract: 'contract',

  ContractIntention: 'contract:intention',
  ContractIntentionCreate: 'contract:intention:create',
  ContractIntentionEdit: 'contract:intention:edit',
  ContractIntentionExport: 'contract:intention:export',
  ContractIntentionIntendedAreaChange: 'contract:intention:intended-area-change',
  ContractIntentionIntendedContractExtension: 'contract:intention:intended-contract-extension',

  ContractFormal: 'contract:formal',
  ContractFormalCreate: 'contract:formal:create',
  ContractFormalSuperEdit: 'contract:formal:super-edit',
  ContractFormalExport: 'contract:formal:export',

  ContractRenewal: 'contract:renewal',
  ContractRenewalCreate: 'contract:renewal:create',
  ContractRenewalEdit: 'contract:renewal:edit',
  ContractRenewalExport: 'contract:renewal:export',
  ContractRenewalDelete: 'contract:renewal:delete',

  ContractRelocationIntention: 'contract:relocation-intention',
  ContractRelocationIntentionCreate: 'contract:relocation-intention:create',
  ContractRelocationIntentionEdit: 'contract:relocation-intention:edit',
  ContractRelocationIntentionDelete: 'contract:relocation-intention:delete',
  ContractRelocationIntentionExport: 'contract:relocation-intention:export',
  ContractRelocationIntentionSubmitAudit: 'contract:relocation-intention:submit-audit',
  ContractRelocationIntentionInvalidRefund: 'contract:relocation-intention:invalid-refund',
  ContractRelocationIntentionInvalidNoRefund: 'contract:relocation-intention:invalid-no-refund',
  ContractRelocationIntentionSubmitRefund: 'contract:relocation-intention:refund',

  ContractRelocationFormal: 'contract:relocation-formal',
  ContractRelocationFormalCreate: 'contract:relocation-formal:create',
  ContractRelocationFormalEdit: 'contract:relocation-formal:edit',
  ContractRelocationFormalDelete: 'contract:relocation-formal:delete',
  ContractRelocationFormalExport: 'contract:relocation-formal:export',
  ContractRelocationFormalSubmitAudit: 'contract:relocation-formal:submit-audit',

  ContractRenovation: 'contract:renovation',
  ContractRenovationCreate: 'contract:renovation:create',
  ContractRenovationEdit: 'contract:renovation:edit',
  ContractRenovationDelete: 'contract:renovation:delete',
  ContractRenovationExport: 'contract:renovation:export',
  ContractRenovationSubmitAudit: 'contract:renovation:submit-audit',

  ContractChangeLegalPerson: 'contract:change-legal-person',
  ContractChangeLegalPersonCreate: 'contract:change-legal-person:create',
  ContractChangeLegalPersonEdit: 'contract:change-legal-person:edit',
  ContractChangeLegalPersonDelete: 'contract:change-legal-person:delete',
  ContractChangeLegalPersonExport: 'contract:change-legal-person:export',
  ContractChangeLegalPersonSubmitAudit: 'contract:change-legal-person:submit-audit',

  // ----------------------------------- 打款管理 -----------------------------------

  Payment: 'payment',

  // ----------------------------------- 裂变申请 -----------------------------------

  Fission: 'fission',
  FissionExport: 'fission:export',

  // ----------------------------------- 区域管理 -----------------------------------

  Regional: 'regional',

  RegionalQuota: 'regional:quota',
  RegionalQuotaExport: 'regional:quota:export',

  RegionalControl: 'regional:control',

  RegionalQuotaText: 'regional:quota-text',

  RegionalExpansion: 'regional:expansion',

  // ----------------------------------- 加盟流程 -----------------------------------

  Process: 'process',

  ProcessIntendedAreaChange: 'process:intended-area-change',
  ProcessIntendedAreaChangeCreate: 'process:intended-area-change:create',
  ProcessIntendedAreaChangeEdit: 'process:intended-area-change:edit',
  ProcessIntendedAreaChangeDelete: 'process:intended-area-change:delete',
  ProcessIntendedAreaChangeExport: 'process:intended-area-change:export',

  ProcessIntendedContractExtension: 'process:intended-contract-extension',
  ProcessIntendedContractExtensionCreate: 'process:intended-contract-extension:create',
  ProcessIntendedContractExtensionEdit: 'process:intended-contract-extension:edit',
  ProcessIntendedContractExtensionDelete: 'process:intended-contract-extension:delete',
  ProcessIntendedContractExtensionExport: 'process:intended-contract-extension:export',

  ProcessRelocationApply: 'process:relocation-apply',
  ProcessRelocationApplyCreate: 'process:relocation-apply:create',
  ProcessRelocationApplyDelete: 'process:relocation-apply:delete',
  ProcessRelocationApplyEdit: 'process:relocation-apply:edit',

  ProcessAbnormalCertificate: 'process:abnormal-certificate',
  ProcessAbnormalCertificateExport: 'process:abnormal-certificate:export',
  ProcessAbnormalCertificateProcess: 'process:abnormal-certificate:process',
  ProcessAbnormalCertificateClose: 'process:abnormal-certificate:close',
  ProcessAbnormalCertificateIssueTask: 'process:abnormal-certificate:issue-task',

  ProcessCertificateRenewal: 'process:certificate-renewal',
  ProcessCertificateRenewalExport: 'process:certificate-renewal:export',
  ProcessCertificateRenewalEdit: 'process:certificate-renewal:edit',
  ProcessCertificateRenewalDelete: 'process:certificate-renewal:delete',
  ProcessCertificateRenewalPushWeaver: 'process:certificate-renewal:push-weaver',
  ProcessCertificateRenewalDetail: 'process:certificate-renewal:detail',
  ProcessCertificateRenewalImportTask: 'process:certificate-renewal:import-task',
  ProcessCertificateRenewalImportReview: 'process:certificate-renewal:import-review',

  ShopRelocationApplication: 'process:shop-relocation-application',
  ShopRelocationApplicationEdit: 'process:shop-relocation-application:edit',

  ProcessRelocationAreaChange: 'process:relocation-area-change',
  ProcessRelocationAreaChangeCreate: 'process:relocation-area-change:create',
  ProcessRelocationAreaChangeEdit: 'process:relocation-area-change:edit',
  ProcessRelocationAreaChangeDetail: 'process:relocation-area-change:detail',

  ShopOwnerUpdate: 'process:shop-owner-update',
  ShopOwnerUpdateExport: 'process:shop-owner-update:export',
  ShopOwnerUpdateCreate: 'process:shop-owner-update:create',

  InterviewNotice: 'process:interview-notice',
  InterviewNoticeExport: 'process:interview-notice:export',
  InterviewNoticeCreate: 'process:interview-notice:create',

  TrainStaff: 'process:train-staff',
  TrainStaffCreate: 'process:train-staff:create',
  TrainStaffEdit: 'process:train-staff:edit',
  TrainStaffDetail: 'process:train-staff:detail',
  TrainStaffSync: 'process:train-staff:sync',
  TrainStaffUpdate: 'process:train-staff:update',

  TrainStaffRecord: 'process:train-staff-record',
  TrainStaffRecordCreate: 'process:train-staff-record:create',
  TrainStaffRecordDetail: 'process:train-staff-record:detail',

  ProcessStoreSetupTask: 'process:store-setup-task',
  ProcessStoreSetupTaskBusinessLicense: 'process:store-setup-task:business-license',
  ProcessStoreSetupTaskBusinessLicenseCreate: 'process:store-setup-task:business-license:create',
  ProcessStoreSetupTaskBusinessLicenseEdit: 'process:store-setup-task:business-license:edit',
  ProcessStoreSetupTaskBusinessLicenseDetail: 'process:store-setup-task:business-license:detail',
  ProcessStoreSetupTaskFoodLicense: 'process:store-setup-task:food-license',
  ProcessStoreSetupTaskFoodLicenseCreate: 'process:store-setup-task:food-license:create',
  ProcessStoreSetupTaskFoodLicenseEdit: 'process:store-setup-task:food-license:edit',
  ProcessStoreSetupTaskFoodLicenseDetail: 'process:store-setup-task:food-license:detail',

  // ----------------------------------- 门店管理 -----------------------------------

  Shop: 'shop',
  ShopList: 'shop:list',
  ShopListExport: 'shop:list:export',
  ShopListEdit: 'shop:list:edit',
  ShopManagementFee: 'shop:shop-management-fee',
  ShopManagementFeeCreate: 'shop:shop-management-fee:create',
  ShopManagementFeeDetail: 'shop:shop-management-fee:detail',
  BrandManagementFee: 'shop:brand-management-fee',
  BrandManagementFeeExport: 'shop:brand-management-fee:export',
  BrandManagementFeeRefresh: 'shop:brand-management-fee:refresh',

  // ----------------------------------- 加盟商档案 -----------------------------------

  Franchise: 'franchise',
  FranchiseExport: 'franchise:export',

  // ----------------------------------- 标准设置 -----------------------------------

  /** 标准设置 */
  StandardSettings: 'standard-settings',
  /** 标签管理 */
  StandardSettingsTag: 'standard-settings:tag',
  /** 信息模版 */
  StandardSettingsTemplate: 'standard-settings:template',
  /** 信息模版-加盟申请表 */
  StandardSettingsTemplateApply: 'standard-settings:template:apply',
  /** 信息模版-加盟申请表-新建 */
  StandardSettingsTemplateApplyCreate: 'standard-settings:template:apply:create',
  /** 信息模版-加盟申请表-编辑 */
  StandardSettingsTemplateApplyEdit: 'standard-settings:template:apply:edit',
  /** 信息模版-加盟申请表-启用停用 */
  StandardSettingsTemplateApplySwitch: 'standard-settings:template:apply:switch',
  /** 信息模版-加盟申请表-删除 */
  StandardSettingsTemplateApplyDelete: 'standard-settings:template:apply:delete',
  /** 信息模版-加盟申请表-复制 */
  StandardSettingsTemplateApplyCopy: 'standard-settings:template:apply:copy',
  /** 信息模版-面谈评估表 */
  StandardSettingsTemplateEvaluation: 'standard-settings:template:evaluation',
  /** 信息模版-面谈评估表-新建 */
  StandardSettingsTemplateEvaluationCreate: 'standard-settings:template:evaluation:create',
  /** 信息模版-面谈评估表-编辑 */
  StandardSettingsTemplateEvaluationEdit: 'standard-settings:template:evaluation:edit',
  /** 信息模版-面谈评估表-启用停用 */
  StandardSettingsTemplateEvaluationSwitch: 'standard-settings:template:evaluation:switch',
  /** 信息模版-面谈评估表-删除 */
  StandardSettingsTemplateEvaluationDelete: 'standard-settings:template:evaluation:delete',
  /** 信息模版-面谈评估表-复制 */
  StandardSettingsTemplateEvaluationCopy: 'standard-settings:template:evaluation:copy',

  /** 线索规则 */
  StandardSettingsRule: 'standard-settings:rule',
  /** 线索规则-启用开关 */
  StandardSettingsRuleSwitch: 'standard-settings:rule:switch',
  /** 线索规则-添加 */
  StandardSettingsRuleCreate: 'standard-settings:rule:create',
  /** 线索规则-编辑 */
  StandardSettingsRuleUpdate: 'standard-settings:rule:update',
  /** 线索规则-删除 */
  StandardSettingsRuleDelete: 'standard-settings:rule:delete',

  /** 业务参数配置 */
  StandardSettingsBusinessParameter: 'standard-settings:business-parameter',

  // ----------------------------------- 小程序管理 -----------------------------------

  /** 小程序管理 */
  MiniProgram: 'mini-program',
  /** 预约排班 */
  MiniProgramAppointmentSchedule: 'mini-program:appointment-schedule',
  /** 身份配置 */
  MiniProgramIdentity: 'mini-program:identity',
  /** 内容配置 */
  MiniProgramContent: 'mini-program:content',
  /** 内容配置-轮播图片 */
  MiniProgramContentCarousel: 'mini-program:content:carousel',
  /** 内容配置-分类管理 */
  MiniProgramContentCategory: 'mini-program:content:category',
  /** 内容配置-资讯管理 */
  MiniProgramContentNews: 'mini-program:content:news',
  /** 内容配置-热门区域 */
  MiniProgramContentHotArea: 'mini-program:content:hot-area',
  /** 内容配置-系统文章 */
  MiniProgramContentArticle: 'mini-program:content:article',
  /** 内容配置-反馈中心 */
  MiniProgramContentFeedback: 'mini-program:content:feedback',
};
