import { ProLayoutProps } from '@ant-design/pro-components';
import { CurrentUserDTO } from '@src/services/common/type';
import { create } from 'zustand';

interface UserStoreType {
  user: CurrentUserDTO;
  permissions: string[];
  menus: ProLayoutProps['route'];
}

const useUserStore = create<UserStoreType>(() => ({
  user: {} as CurrentUserDTO,
  permissions: [],
  menus: undefined,
}));

export default useUserStore;
