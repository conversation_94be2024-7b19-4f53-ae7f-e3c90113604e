import { DistrictDTO } from '@src/services/common/type';
import { create } from 'zustand';

interface DistrictsStoreType {
  threeLevelLoading: boolean;
  fourLevelLoading: boolean;
  threeLevelDistricts: DistrictDTO[];
  fourLevelDistricts: DistrictDTO[];
  setThreeLevelLoading: (value: boolean) => void;
  setFourLevelLoading: (value: boolean) => void;
  setThreeLevelDistricts: (value: DistrictDTO[]) => void;
  setFourLevelDistricts: (value: DistrictDTO[]) => void;
}

const useDistrictsStore = create<DistrictsStoreType>((set) => ({
  threeLevelLoading: true,
  fourLevelLoading: true,
  threeLevelDistricts: [],
  fourLevelDistricts: [],
  setThreeLevelLoading: (threeLevelLoading: boolean) => set({ threeLevelLoading }),
  setFourLevelLoading: (fourLevelLoading: boolean) => set({ fourLevelLoading }),
  setThreeLevelDistricts: (threeLevelDistricts: DistrictDTO[]) => set({ threeLevelDistricts }),
  setFourLevelDistricts: (fourLevelDistricts: DistrictDTO[]) => set({ fourLevelDistricts }),
}));

export default useDistrictsStore;
