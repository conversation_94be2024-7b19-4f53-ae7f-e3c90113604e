import { IndexRouteObject, NonIndexRouteObject } from 'react-router-dom';

interface RouteMetaObject {
  name?: string;
  permission?: string;
  hiddenInMenu?: boolean;
  hideChildrenInMenu?: boolean;
  icon?: React.ReactNode;
}

export type RouteObjWithMeta = (
  | (Omit<NonIndexRouteObject, 'children' | 'handle'> & {
      children?: RouteObjWithMeta[];
    })
  | Omit<IndexRouteObject, 'handle'>
) & {
  handle?: RouteMetaObject;
};
