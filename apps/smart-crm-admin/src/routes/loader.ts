import { setUser } from '@sentry/react';
import { getToken, setToken } from '@src/common/authorization';
import { getCurrentUserInfo, getPermissions, getTokenByFeiShuCode } from '@src/services/common';
import useUserStore from '@src/store/useUserStore';
import { convertAppRouteToProLayoutMenuData, navigateToGaiaLogin } from '@src/utils';
import { LoaderFunction, redirect, replace } from 'react-router-dom';
import { mainRoutes } from './main-routes';

// 全局状态的加载函数
export const loaderGlobalState: LoaderFunction = async ({ request }) => {
  const url = new URL(request.url);
  const code = url.searchParams.get('code');

  // code 只能用一次
  if (code) {
    try {
      const res = await getTokenByFeiShuCode(code);

      url.searchParams.delete('code');
      setToken(res.token);
      sessionStorage.setItem('is-fei-shu-login', 'true');

      return replace(url.toString());
    } catch (error) {}
  }

  const token = getToken();

  if (!token) {
    navigateToGaiaLogin();
  }

  const user = await getCurrentUserInfo();

  localStorage.setItem('brandId', String(user.brandBaseInfos[0].brandId));

  setUser(user);

  const permissions = await getPermissions();

  const menus = convertAppRouteToProLayoutMenuData({ children: mainRoutes }, permissions);

  useUserStore.setState({ user, permissions, menus });

  return null;
};

/**
 * 授权路由loader，用于检查用户是否已经登录，已经登录则跳转到首页，不会再次进入登录页
 */
export const loaderAuthentication = () => {
  // 先检查一下用户登录没有，如果已经登录了则直接跳转到首页
  const token = getToken();

  // 根据token判断是否已经登录
  if (token) {
    return redirect('/');
  }

  return null;
};
