import { lazy } from 'react';
import {
  AppstoreOutlined,
  ClockCircleOutlined,
  ContactsOutlined,
  EnvironmentOutlined,
  FileDoneOutlined,
  FileSearchOutlined,
  FormOutlined,
  MoneyCollectOutlined,
  NodeIndexOutlined,
  PartitionOutlined,
  SettingOutlined,
  ShareAltOutlined,
  ShopOutlined,
  SmileOutlined,
  TeamOutlined,
  ToolOutlined,
  TransactionOutlined,
  UserOutlined,
} from '@ant-design/icons';
import WelcomePage from '@src/pages/welcome';
import { PermissionsMap } from '@src/permissions';
import { Navigate } from 'react-router-dom';
import { RouteObjWithMeta } from './type';

export // 主路由
const mainRoutes: RouteObjWithMeta[] = [
  {
    path: '/',
    element: <Navigate replace to="/welcome" />,
  },
  {
    path: '/welcome',
    Component: WelcomePage,
    handle: { name: '欢迎页', icon: <SmileOutlined /> },
  },
  {
    path: '/clues',
    handle: {
      name: '线索管理',
      icon: <NodeIndexOutlined />,
      permission: PermissionsMap.Clues,
    },
    children: [
      { index: true, element: <Navigate to="my" /> },
      {
        path: 'my',
        handle: {
          name: '我的线索',
          permission: PermissionsMap.MyClues,
        },
        Component: lazy(() => import('../pages/clues/my')),
      },
      {
        path: 'pool',
        handle: {
          name: '线索池',
          permission: PermissionsMap.CluePool,
        },
        Component: lazy(() => import('../pages/clues/pool')),
      },
    ],
  },
  {
    path: 'customers',
    handle: {
      name: '客户管理',
      icon: <TeamOutlined />,
      permission: PermissionsMap.Customers,
    },
    Component: lazy(() => import('../pages/customers')),
  },
  {
    path: 'business-opportunity',
    handle: {
      name: '商机管理',
      icon: <MoneyCollectOutlined />,
      permission: PermissionsMap.BusinessOpportunity,
    },
    Component: lazy(() => import('../pages/business-opportunity')),
  },
  {
    path: 'franchise-application',
    handle: {
      name: '加盟申请表',
      icon: <FormOutlined />,
      permission: PermissionsMap.FranchiseApplication,
    },
    Component: lazy(() => import('../pages/franchise-application')),
  },
  {
    path: 'appointment-record',
    handle: {
      name: '预约记录',
      icon: <ClockCircleOutlined />,
      permission: PermissionsMap.AppointmentRecord,
    },
    Component: lazy(() => import('../pages/appointment-record')),
  },
  {
    path: 'visit',
    handle: {
      name: '面审客户到访表',
      icon: <ContactsOutlined />,
      permission: PermissionsMap.Visit,
    },
    Component: lazy(() => import('../pages/visit')),
  },
  {
    path: 'evaluation',
    handle: {
      name: '面审客户评估表',
      icon: <FileSearchOutlined />,
      permission: PermissionsMap.Evaluation,
    },
    Component: lazy(() => import('../pages/evaluation')),
  },
  {
    path: 'contract',
    handle: {
      name: '合同管理',
      icon: <FileDoneOutlined />,
      permission: PermissionsMap.Contract,
    },
    children: [
      { index: true, element: <Navigate replace to="intention" /> },
      {
        path: 'intention',
        handle: {
          name: '意向合同',
          permission: PermissionsMap.ContractIntention,
        },
        Component: lazy(() => import('../pages/contract/intention')),
      },
      {
        path: 'formal',
        handle: {
          name: '正式合同',
          permission: PermissionsMap.ContractFormal,
        },
        Component: lazy(() => import('../pages/contract/formal')),
      },
      {
        path: 'renewal',
        handle: {
          name: '续约合同',
          permission: PermissionsMap.ContractRenewal,
        },
        Component: lazy(() => import('../pages/contract/renewal')),
      },
      {
        path: 'relocation-intention',
        handle: {
          name: '搬迁意向',
          permission: PermissionsMap.ContractRelocationIntention,
        },
        Component: lazy(() => import('../pages/contract/relocation-intention')),
      },
      {
        path: 'relocation-formal',
        handle: {
          name: '搬迁正式',
          permission: PermissionsMap.ContractRelocationFormal,
        },
        Component: lazy(() => import('../pages/contract/relocation-formal')),
      },
      {
        path: 'renovation',
        handle: {
          name: '门店重装',
          permission: PermissionsMap.ContractRenovation,
        },
        Component: lazy(() => import('../pages/contract/renovation')),
      },
      {
        path: 'change-legal-person',
        handle: {
          name: '变更法人',
          permission: PermissionsMap.ContractChangeLegalPerson,
        },
        Component: lazy(() => import('../pages/contract/change-legal-person')),
      },
    ],
  },
  {
    path: 'payment',
    handle: {
      name: '打款管理',
      icon: <TransactionOutlined />,
      permission: PermissionsMap.Payment,
    },
    Component: lazy(() => import('../pages/payment')),
  },
  {
    path: 'fission',
    handle: {
      name: '裂变申请',
      icon: <ShareAltOutlined />,
      permission: PermissionsMap.Fission,
    },
    Component: lazy(() => import('../pages/fission')),
  },
  {
    path: 'regional',
    handle: {
      name: '区域管理',
      icon: <EnvironmentOutlined />,
      permission: PermissionsMap.Regional,
    },
    children: [
      { index: true, element: <Navigate replace to="quota" /> },
      {
        path: 'quota',
        handle: {
          name: '区域名额',
          permission: PermissionsMap.RegionalQuota,
        },
        Component: lazy(() => import('../pages/regional/quota')),
      },
      {
        path: 'control',
        handle: {
          name: '区域控制',
          permission: PermissionsMap.RegionalControl,
        },
        Component: lazy(() => import('../pages/regional/control')),
      },
      {
        path: 'quota-text',
        handle: {
          name: '区域名额文本',
          permission: PermissionsMap.RegionalQuotaText,
        },
        Component: lazy(() => import('../pages/regional/quota-text')),
      },
      {
        path: 'expansion',
        handle: {
          name: '扩容清单',
          permission: PermissionsMap.RegionalExpansion,
        },
        Component: lazy(() => import('../pages/regional/expansion')),
      },
    ],
  },
  {
    path: 'process',
    handle: {
      name: '加盟管理',
      icon: <PartitionOutlined />,
      permission: PermissionsMap.Process,
    },
    children: [
      { index: true, element: <Navigate replace to="intended-area-change" /> },
      {
        path: 'intended-area-change',
        handle: {
          name: '意向区域变更',
          permission: PermissionsMap.ProcessIntendedAreaChange,
        },
        Component: lazy(() => import('../pages/process/intended-area-change')),
      },
      {
        path: 'intended-contract-extension',
        handle: {
          name: '意向合同延期',
          permission: PermissionsMap.ProcessIntendedContractExtension,
        },
        Component: lazy(() => import('../pages/process/intended-contract-extension')),
      },
      {
        path: 'relocation-apply',
        handle: {
          name: '意向群名',
          permission: PermissionsMap.ProcessRelocationApply,
        },
        Component: lazy(() => import('../pages/process/relocation-apply')),
      },
      {
        path: 'abnormal-certificate',
        handle: {
          name: '证照异常清单',
          permission: PermissionsMap.ProcessAbnormalCertificate,
        },
        Component: lazy(() => import('../pages/process/abnormal-certificate')),
      },
      {
        path: 'certificate-renewal',
        handle: {
          name: '证照更新任务',
          permission: PermissionsMap.ProcessCertificateRenewal,
        },
        Component: lazy(() => import('../pages/process/certificate-renewal')),
      },
      {
        path: 'shop-relocation-application',
        handle: {
          name: '门店搬迁申请',
          permission: PermissionsMap.ShopRelocationApplication,
        },
        Component: lazy(() => import('../pages/process/shop-relocation-application')),
      },
      {
        path: 'relocation-area-change',
        handle: {
          name: '搬迁区域变更申请',
          permission: PermissionsMap.ProcessRelocationAreaChange,
        },
        Component: lazy(() => import('../pages/process/relocation-area-change')),
      },
      {
        path: 'shop-owner-update',
        handle: {
          name: '门店业主变更申请',
          permission: PermissionsMap.ShopOwnerUpdate,
        },
        Component: lazy(() => import('../pages/process/shop-owner-update')),
      },
      {
        path: 'interview-notice',
        handle: {
          name: '面谈前告知函',
          permission: PermissionsMap.InterviewNotice,
        },
        Component: lazy(() => import('../pages/process/interview-notice')),
      },
      {
        path: 'franchise',
        handle: {
          name: '加盟商档案',
          permission: PermissionsMap.Franchise,
        },
        Component: lazy(() => import('../pages/franchise')),
      },
      {
        path: 'train-staff',
        handle: {
          name: '培训人员清单',
          permission: PermissionsMap.TrainStaff,
        },
        Component: lazy(() => import('../pages/process/train-staff')),
      },
      {
        path: 'train-staff-record',
        handle: {
          name: '培训人员更换记录',
          permission: PermissionsMap.TrainStaffRecord,
        },
        Component: lazy(() => import('../pages/process/train-staff-record')),
      },
      {
        path: 'store-setup-task',
        handle: {
          name: '建店任务',
          permission: PermissionsMap.ProcessStoreSetupTask,
        },
        children: [
          {
            path: 'business-license',
            handle: {
              name: '营业执照',
              permission: PermissionsMap.ProcessStoreSetupTaskBusinessLicense,
            },
            Component: lazy(() => import('../pages/process/store-setup-task/business-license')),
          },
          {
            path: 'food-license',
            handle: {
              name: '食品许可证',
              permission: PermissionsMap.ProcessStoreSetupTaskFoodLicense,
            },
            Component: lazy(() => import('../pages/process/store-setup-task/food-license')),
          },
        ],
      },
    ],
  },
  {
    path: 'shop',
    handle: {
      name: '门店管理',
      icon: <ShopOutlined />,
      permission: PermissionsMap.Shop,
    },
    children: [
      { index: true, element: <Navigate replace to="list" /> },
      {
        path: 'list',
        handle: {
          name: '门店列表',
          permission: PermissionsMap.ShopList,
        },
        Component: lazy(() => import('../pages/shop/list')),
      },
      {
        path: 'shop-management-fee',
        handle: {
          name: '门店管理费',
          permission: PermissionsMap.ShopManagementFee,
        },
        Component: lazy(() => import('../pages/shop/shop-management-fee')),
      },
      {
        path: 'brand-management-fee',
        handle: {
          name: '品牌管理费',
          permission: PermissionsMap.BrandManagementFee,
        },
        Component: lazy(() => import('../pages/shop/brand-management-fee')),
      },
    ],
  },
  {
    path: 'standard-settings',
    handle: {
      name: '标准设置',
      icon: <SettingOutlined />,
      permission: PermissionsMap.StandardSettings,
    },
    children: [
      { index: true, element: <Navigate replace to="tag" /> },
      {
        path: 'tag',
        handle: {
          name: '标签管理',
          permission: PermissionsMap.StandardSettingsTag,
        },
        Component: lazy(() => import('../pages/standard-settings/tag')),
      },
      {
        path: 'template',
        handle: {
          name: '信息模版',
          permission: PermissionsMap.StandardSettingsTemplate,
        },
        children: [
          {
            index: true,
            handle: {
              permission: PermissionsMap.StandardSettingsTemplate,
            },
            Component: lazy(() => import('../pages/standard-settings/template')),
          },
          {
            path: 'franchise-application',
            children: [
              {
                index: true,
                element: <Navigate to="/standard-settings/template" replace />,
              },
              {
                path: 'create',
                handle: {
                  name: '新建加盟申请表',
                },
                Component: lazy(() => import('../pages/standard-settings/template/apply-config')),
              },
              {
                path: ':id',
                handle: {
                  name: '编辑加盟申请表',
                },
                Component: lazy(() => import('../pages/standard-settings/template/apply-config')),
              },
            ],
          },
          {
            path: 'evaluation',
            children: [
              {
                index: true,
                element: <Navigate to="/standard-settings/template" replace />,
              },
              {
                path: 'create',
                handle: {
                  name: '新建面谈评估表',
                },
                Component: lazy(
                  () => import('../pages/standard-settings/template/evaluation-config'),
                ),
              },
              {
                path: ':id',
                handle: {
                  name: '编辑面谈评估表',
                },
                Component: lazy(
                  () => import('../pages/standard-settings/template/evaluation-config'),
                ),
              },
            ],
          },
        ],
      },
      {
        path: 'clue-rule',
        handle: {
          name: '线索规则',
          permission: PermissionsMap.StandardSettingsRule,
        },
        Component: lazy(() => import('../pages/standard-settings/rule')),
      },
      {
        path: 'business-parameter',
        handle: {
          name: '业务参数配置',
          permission: PermissionsMap.StandardSettingsBusinessParameter,
        },
        Component: lazy(() => import('../pages/standard-settings/business-parameter')),
      },
    ],
  },
  {
    path: 'mini-program',
    handle: {
      name: '小程序管理',
      icon: <AppstoreOutlined />,
      permission: PermissionsMap.MiniProgram,
    },
    children: [
      { index: true, element: <Navigate to="appointment-schedule" /> },
      {
        path: 'appointment-schedule',
        handle: {
          name: '预约排班',
          hideChildrenInMenu: true,
          permission: PermissionsMap.MiniProgramAppointmentSchedule,
        },
        children: [
          {
            index: true,
            Component: lazy(() => import('../pages/mini-program/appointment-schedule')),
          },
          {
            path: ':id',
            handle: {
              name: '设置排班',
            },
            Component: lazy(() => import('../pages/mini-program/appointment-schedule/detail')),
          },
        ],
      },
      {
        path: 'identity',
        handle: {
          name: '身份配置',
          permission: PermissionsMap.MiniProgramIdentity,
        },
        Component: lazy(() => import('../pages/mini-program/identity')),
      },
      {
        path: 'content-management',
        handle: {
          name: '内容配置',
          permission: PermissionsMap.MiniProgramContent,
        },
        children: [
          { index: true, element: <Navigate replace to="carousel" /> },
          {
            path: 'carousel',
            handle: {
              name: '轮播图片',
              permission: PermissionsMap.MiniProgramContentCarousel,
            },
            Component: lazy(() => import('../pages/mini-program/content-management/carousel')),
          },
          {
            path: 'category',
            handle: {
              name: '分类管理',
              permission: PermissionsMap.MiniProgramContentCategory,
            },
            Component: lazy(() => import('../pages/mini-program/content-management/category')),
          },
          {
            path: 'news',
            handle: {
              name: '资讯管理',
              permission: PermissionsMap.MiniProgramContentNews,
            },
            Component: lazy(() => import('../pages/mini-program/content-management/news')),
          },
          {
            path: 'hot-area',
            handle: {
              name: '热门区域',
              permission: PermissionsMap.MiniProgramContentHotArea,
            },
            Component: lazy(() => import('../pages/mini-program/content-management/hot-area')),
          },
          {
            path: 'article',
            handle: {
              name: '系统文章',
              permission: PermissionsMap.MiniProgramContentArticle,
            },
            Component: lazy(() => import('../pages/mini-program/content-management/article')),
          },
          {
            path: 'feedback',
            handle: {
              name: '反馈中心',
              permission: PermissionsMap.MiniProgramContentFeedback,
            },
            Component: lazy(() => import('../pages/mini-program/content-management/feedback')),
          },
        ],
      },
    ],
  },
  {
    path: 'users',
    handle: {
      name: '成员管理',
      icon: <UserOutlined />,
      permission: PermissionsMap.Users,
    },
    children: [
      { index: true, element: <Navigate to="auth" /> },
      {
        path: 'auth',
        handle: {
          name: '权限配置',
          permission: PermissionsMap.UsersAuth,
        },
        Component: lazy(() => import('../pages/users/auth')),
      },
    ],
  },
  {
    path: 'tool-center',
    handle: {
      name: '工具中心',
      icon: <ToolOutlined />,
    },
    children: [
      { index: true, element: <Navigate to="export" /> },
      {
        path: 'export',
        handle: { name: '导出中心' },
        Component: lazy(() => import('../pages/tool-center/export')),
      },
      {
        path: 'flow-record',
        handle: { name: '流转记录' },
        Component: lazy(() => import('../pages/tool-center/flow-record')),
      },
    ],
  },
  {
    path: '*',
    Component: lazy(() => import('@src/pages/404')),
  },
];
