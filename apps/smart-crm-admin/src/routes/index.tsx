import { ErrorBoundary } from '@src/components';
import { GlobalLayout } from '@src/layout';
import { loaderGlobalState } from './loader';
import { mainRoutes } from './main-routes';
import { RouteObjWithMeta } from './type';

export const routes: RouteObjWithMeta[] = [
  {
    errorElement: <ErrorBoundary />,
    Component: GlobalLayout,
    loader: loaderGlobalState,
    shouldRevalidate: () => false,
    children: mainRoutes,
  },
];
