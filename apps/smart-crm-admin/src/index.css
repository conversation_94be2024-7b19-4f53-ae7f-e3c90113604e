@tailwind base;
@tailwind components;
@tailwind utilities;

html, body, #root, .ant-app {
  height: 100%
}

a {
  color: var(--ant-color-link);
}


.ant-pro-layout .ant-pro-sider-actions-list-item {
  padding: 0
}

/* 解决页面初次抖动 */
.ant-pro-layout .ant-pro-layout-content {
  padding: 0;
}

.layout-isInIcestark.ant-pro-layout .ant-pro-sider.ant-pro-sider-fixed {
  top: 40px;
  height: calc(100% - 40px);
}
 
/* Cascader disabledCheckbox 为 true 时隐藏 Checkbox */
.cascader-popup-hidden-disabled-checkbox .ant-cascader-checkbox-disabled{
  display: none;
}
