import React from 'react';
import { AuditHistoryType } from '@src/services/common/type';
import { useRequest } from 'ahooks';
import { Empty, Skeleton, Timeline } from 'antd';
import { renderAuditHistoryItem } from './utils';

interface AuditFlowRecordProps {
  request: () => Promise<
    {
      auditHistories: AuditHistoryType[];
      name: string;
      processInstanceId: number;
    }[]
  >;
  renderItem?: (
    item: AuditHistoryType,
    defaultRenderItem: (item: AuditHistoryType) => React.ReactNode,
  ) => React.ReactNode;
}

const AuditFlowRecord: React.FC<AuditFlowRecordProps> = ({ request, renderItem }) => {
  const { data, loading } = useRequest(request);

  return loading ? (
    <Skeleton />
  ) : !data?.length ? (
    <Empty />
  ) : (
    <>
      {data?.map((history) => (
        <div key={history.processInstanceId}>
          <p className="text-base font-bold mb-5">{history.name}</p>
          <Timeline
            items={history.auditHistories.map((item) => ({
              children: renderItem
                ? renderItem(item, renderAuditHistoryItem)
                : renderAuditHistoryItem(item),
            }))}
          />
        </div>
      ))}
    </>
  );
};

export default AuditFlowRecord;
