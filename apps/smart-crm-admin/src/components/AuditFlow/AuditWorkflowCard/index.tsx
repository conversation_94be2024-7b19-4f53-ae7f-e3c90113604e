import React, { useLayoutEffect, useState } from 'react';
import { AuditHistoryType } from '@src/services/common/type';
import { getWorkflowHistoryByInstanceId } from '@src/services/workflow';
import {
  WorkflowAuditOperationTypeEnum,
  WorkflowAuditStatusEnum,
  WorkflowBusinessTypeEnum,
} from '@src/services/workflow/type';
import useUserStore from '@src/store/useUserStore';
import { useRequest } from 'ahooks';
import { FormProps } from 'antd';
import { last } from 'lodash-es';
import AuditModal from './AuditModal';
import AuditFlowOperateCard from '../AuditFlowOperateCard';

interface AuditWorkflowCardProps {
  loading?: boolean;
  auditStatus: WorkflowAuditStatusEnum | undefined;
  auditButtons?: Partial<Record<WorkflowAuditOperationTypeEnum, boolean>>;
  processInstanceId: string | undefined;
  businessId: number | undefined;
  businessType: WorkflowBusinessTypeEnum;
  businessName: string | undefined;
  children?: (
    originalNode: React.ReactNode,
    type?: WorkflowAuditOperationTypeEnum,
    currentAuditHistory?: AuditHistoryType,
  ) => React.ReactNode;
  formProps?: FormProps;
  onBeforeSubmit?: (
    values: any,
    type: WorkflowAuditOperationTypeEnum,
    currentAuditHistory?: AuditHistoryType,
  ) => Promise<void> | void;
  onAfterSubmit?: (
    type: WorkflowAuditOperationTypeEnum,
    values?: { finish: boolean } | void,
  ) => Promise<void> | void;
  onSuccess: () => void;
}

interface AuditWorkflowCardRef {
  refresh: () => void;
}

const AuditWorkflowCard = React.forwardRef<AuditWorkflowCardRef, AuditWorkflowCardProps>(
  (
    {
      processInstanceId,
      loading,
      auditStatus,
      auditButtons,
      businessId,
      businessName,
      businessType,
      children,
      formProps,
      onBeforeSubmit,
      onAfterSubmit,
      onSuccess,
    },
    ref,
  ) => {
    const {
      user: { shUserId },
    } = useUserStore();
    const [type, setType] = useState<WorkflowAuditOperationTypeEnum>();
    const [auditModalOpen, setAuditModalOpen] = useState(false);
    const [getAuditHistoryLoading, setGetAuditHistoryLoading] = useState(true);

    const ready = !!processInstanceId;

    const {
      data: auditHistory = [],
      refresh,
      mutate,
    } = useRequest(
      () =>
        getWorkflowHistoryByInstanceId({
          processInstanceId: processInstanceId!,
        }),
      {
        ready,
        refreshDeps: [processInstanceId],
        onBefore: () => mutate(undefined),
        onFinally: () => setGetAuditHistoryLoading(false),
      },
    );

    // 不使用 useRequest 的 loading，因为会闪一下（processInstanceId 和 loading 不同步的问题）
    useLayoutEffect(() => {
      mutate(undefined);
      setGetAuditHistoryLoading(ready);
    }, [mutate, ready]);

    React.useImperativeHandle(ref, () => ({
      refresh,
    }));

    const currentAuditHistory = last(auditHistory);
    // 是否是审批人
    const isAuditUser = !!currentAuditHistory?.auditUserIds?.includes(shUserId);

    const isAuditStatus = auditStatus === WorkflowAuditStatusEnum.AUDIT;

    // 是否是提交人
    const isSubmitter = shUserId === auditHistory[0]?.auditUserId;

    return (
      <>
        <AuditFlowOperateCard
          loading={loading || getAuditHistoryLoading}
          data={auditHistory || []}
          operateButtons={[
            {
              show:
                isAuditUser &&
                isAuditStatus &&
                auditButtons?.[WorkflowAuditOperationTypeEnum.APPROVE] !== false,
              children: '通过',
              onClick: () => {
                setType(WorkflowAuditOperationTypeEnum.APPROVE);
                setAuditModalOpen(true);
              },
            },
            {
              show:
                isAuditUser &&
                isAuditStatus &&
                auditButtons?.[WorkflowAuditOperationTypeEnum.REJECT] !== false,
              children: '不通过',
              danger: true,
              onClick: () => {
                setType(WorkflowAuditOperationTypeEnum.REJECT);
                setAuditModalOpen(true);
              },
            },
            {
              show:
                isAuditUser &&
                isAuditStatus &&
                auditButtons?.[WorkflowAuditOperationTypeEnum.RETURN] !== false,
              children: '驳回',
              danger: true,
              onClick: () => {
                setType(WorkflowAuditOperationTypeEnum.RETURN);
                setAuditModalOpen(true);
              },
            },
            {
              show:
                isSubmitter &&
                isAuditStatus &&
                auditButtons?.[WorkflowAuditOperationTypeEnum.REVOKE] !== false,
              children: '撤回',
              className: 'ml-auto',
              onClick: () => {
                setType(WorkflowAuditOperationTypeEnum.REVOKE);
                setAuditModalOpen(true);
              },
            },
          ]}
        />
        <AuditModal
          open={auditModalOpen}
          type={type}
          processInstanceId={processInstanceId}
          businessId={businessId}
          businessName={businessName}
          businessType={businessType}
          formProps={formProps}
          onCancel={() => setAuditModalOpen(false)}
          onBeforeSubmit={
            onBeforeSubmit
              ? (values, _type) => onBeforeSubmit(values, _type, currentAuditHistory)
              : undefined
          }
          onAfterSubmit={onAfterSubmit}
          onSuccess={() => {
            setAuditModalOpen(false);
            refresh();
            onSuccess();
          }}
          renderFormItems={
            children
              ? (originalNode) => children(originalNode, type, currentAuditHistory)
              : undefined
          }
        />
      </>
    );
  },
);

export default AuditWorkflowCard;
