import React, { useState } from 'react';
import { auditCancel, auditPass, auditReject, auditReturn } from '@src/services/workflow';
import {
  WorkflowAuditOperationTypeEnum,
  WorkflowBusinessTypeEnum,
} from '@src/services/workflow/type';
import { App, Form, FormProps, Input, Modal, ModalProps } from 'antd';

interface AuditModalProps extends ModalProps {
  type?: WorkflowAuditOperationTypeEnum;
  processInstanceId: string | undefined;
  businessId: number | undefined;
  businessType: WorkflowBusinessTypeEnum;
  businessName: string | undefined;
  formProps?: FormProps;
  renderFormItems?: (originalNode: React.ReactNode) => React.ReactNode;
  onBeforeSubmit?: (values: any, type: WorkflowAuditOperationTypeEnum) => Promise<void> | void;
  onAfterSubmit?: (
    type: WorkflowAuditOperationTypeEnum,
    values?: { finish: boolean } | void,
  ) => Promise<void> | void;
  onSuccess: () => void;
}

const AuditModal: React.FC<AuditModalProps> = ({
  type,
  processInstanceId,
  businessId,
  businessName,
  businessType,
  formProps,
  renderFormItems,
  onBeforeSubmit,
  onAfterSubmit,
  onSuccess,
  ...props
}) => {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const [confirmLoading, setConfirmLoading] = useState(false);

  const titleMap = {
    [WorkflowAuditOperationTypeEnum.APPROVE]: '审批通过',
    [WorkflowAuditOperationTypeEnum.REVOKE]: '审批撤回',
    [WorkflowAuditOperationTypeEnum.REJECT]: '审批不通过',
    [WorkflowAuditOperationTypeEnum.RETURN]: '审批驳回',
  };

  const handleSave = async (values: any) => {
    setConfirmLoading(true);

    const request = {
      [WorkflowAuditOperationTypeEnum.APPROVE]: auditPass,
      [WorkflowAuditOperationTypeEnum.REVOKE]: auditCancel,
      [WorkflowAuditOperationTypeEnum.REJECT]: auditReject,
      [WorkflowAuditOperationTypeEnum.RETURN]: auditReturn,
    }[type!];

    try {
      if (onBeforeSubmit) {
        await onBeforeSubmit(values, type!);
      }

      const res = await request({
        processInstanceId,
        businessId,
        businessType,
        businessName,
        ...values,
      });

      message.success('操作成功');

      // 这个可能会失败，失败后也应该执行 onSuccess 来更新审批状态
      if (onAfterSubmit) {
        try {
          await onAfterSubmit(type!, res);
        } catch (error) {}
      }

      onSuccess();
    } catch (error) {}

    setConfirmLoading(false);
  };

  const originalNode = (
    <Form.Item label="说明" name="description" rules={[{ required: true, message: '请输入说明' }]}>
      <Input.TextArea placeholder="说明" maxLength={255} />
    </Form.Item>
  );

  return (
    <Modal
      title={type && titleMap[type]}
      destroyOnHidden
      confirmLoading={confirmLoading}
      onOk={form.submit}
      {...props}
    >
      <Form form={form} clearOnDestroy onFinish={handleSave} {...formProps}>
        {renderFormItems ? renderFormItems(originalNode) : originalNode}
      </Form>
    </Modal>
  );
};

export default AuditModal;
