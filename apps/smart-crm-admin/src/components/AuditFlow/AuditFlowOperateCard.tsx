import React from 'react';
import { isMobile } from '@src/common/constants';
import { AuditHistoryType } from '@src/services/common/type';
import { Button, ButtonProps, Card, Empty, Timeline } from 'antd';
import classNames from 'classnames';
import { renderAuditHistoryItem } from './utils';

interface AuditFlowOperateCardProps {
  data: AuditHistoryType[];
  loading?: boolean;
  renderItem?: (
    item: AuditHistoryType,
    defaultRenderItem: (item: AuditHistoryType) => React.ReactNode,
  ) => React.ReactNode;
  operateButtons?: (Omit<ButtonProps, 'hidden'> & { show: boolean })[];
}

const AuditFlowOperateCard: React.FC<AuditFlowOperateCardProps> = ({
  loading,
  data,
  operateButtons,
  renderItem,
}) => {
  const buttonsNode = (operateButtons || []).reduce<React.ReactNode[]>(
    (total, { show, ...buttonProps }, index) =>
      show ? total.concat(<Button key={index} block={isMobile} {...buttonProps} />) : total,
    [],
  );

  return (
    <Card
      title="审批流信息"
      loading={loading}
      // 移动端操作按钮固定在底部，这里补上其高度
      style={isMobile && buttonsNode.length > 0 ? { marginBottom: 57 } : {}}
    >
      {data.length > 0 ? (
        <>
          <Timeline
            items={data.map((item) => ({
              children: (
                <div className="flex flex-col gap-1 px-4 py-2 rounded bg-[#fafafa]">
                  {renderItem
                    ? renderItem(item, renderAuditHistoryItem)
                    : renderAuditHistoryItem(item)}
                </div>
              ),
              // 最后一个去除 paddingBottom
              className:
                (!buttonsNode.length || isMobile) && item.id === data[data.length - 1].id
                  ? '!pb-0'
                  : '',
            }))}
          />
          {buttonsNode.length > 0 && (
            <div
              className={classNames('flex gap-2', {
                'fixed left-0 bottom-0 w-full border-t bg-white p-3 z-10': isMobile,
              })}
            >
              {buttonsNode}
            </div>
          )}
        </>
      ) : (
        <Empty />
      )}
    </Card>
  );
};

export default AuditFlowOperateCard;
