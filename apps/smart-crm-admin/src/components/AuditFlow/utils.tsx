import EditableAttachment from '@src/components/Editable/EditableAttachment';
import { AuditHistoryType, AuditOperationEnum } from '@src/services/common/type';

export const renderAuditHistoryItem = (item: AuditHistoryType) => {
  if (item.operationStatus === AuditOperationEnum.提交) {
    return (
      <>
        <div className="flex justify-between">
          <span>{item.auditTime}</span>
          <span className="font-bold">提交申请</span>
        </div>
        <p>{item.auditUserName}提交了审批申请</p>
      </>
    );
  }

  if (item.operationStatus === AuditOperationEnum.加签) {
    return (
      <>
        <div className="flex justify-between">
          <span>{item.auditTime}</span>
          <span className="font-bold">邀请复核</span>
        </div>
        <p>{item.auditUserName}提交了复核</p>
        <p>复核原因：{item.reason}</p>
        <p>复核说明：{item.description}</p>
      </>
    );
  }

  if (item.operationStatus === AuditOperationEnum.待审核) {
    return (
      <div className="flex justify-between gap-2">
        <p className="flex-1">审核人：{item.auditUserName}</p>
        <span className="font-bold">审核中</span>
      </div>
    );
  }

  if (item.operationStatus === AuditOperationEnum.审核通过) {
    return (
      <>
        <div className="flex justify-between">
          <span>{item.auditTime}</span>
          <span className="font-bold">通过</span>
        </div>
        <p>{item.auditUserName}审批通过</p>
        {item.reason && <p>说明：{item.reason}</p>}
        {item.description && <p>说明：{item.description}</p>}
        {item.attachments && <EditableAttachment value={item.attachments} />}
      </>
    );
  }

  if (item.operationStatus === AuditOperationEnum.审核不通过) {
    return (
      <>
        <div className="flex justify-between">
          <span>{item.auditTime}</span>
          <span className="font-bold text-red-500">不通过</span>
        </div>
        <p>{item.auditUserName}审批不通过</p>
        {item.reason && <p>原因：{item.reason}</p>}
        {item.description && <p>说明：{item.description}</p>}
        {item.attachments && <EditableAttachment value={item.attachments} />}
      </>
    );
  }

  if (item.operationStatus === AuditOperationEnum.驳回) {
    return (
      <>
        <div className="flex justify-between">
          <span>{item.auditTime}</span>
          <span className="font-bold text-red-500">驳回</span>
        </div>
        <p>{item.auditUserName}驳回了审批</p>
        {item.reason && <p>原因：{item.reason}</p>}
        {item.description && <p>说明：{item.description}</p>}
      </>
    );
  }

  if (item.operationStatus === AuditOperationEnum.撤回) {
    return (
      <>
        <div className="flex justify-between">
          <span>{item.auditTime}</span>
          <span className="font-bold">撤回</span>
        </div>
        <p>{item.auditUserName}撤回了审批</p>
        {item.reason && <p>原因：{item.reason}</p>}
        {item.description && <p>说明：{item.description}</p>}
      </>
    );
  }
};
