import React, { useEffect, useRef, useState } from 'react';
import rocketImg from '@src/assets/rocket.svg';
import { Button, Result } from 'antd';

interface CheckVersionProps {
  forceRefresh?: boolean;
}

const isDev = import.meta.env.DEV;

const CheckVersion: React.FC<CheckVersionProps> = ({ forceRefresh = true }) => {
  const timer = useRef<ReturnType<typeof setInterval>>();
  const [hasNewVersion, setHasNewVersion] = useState(false);
  const currentTag = useRef<string | null>(null);
  const currentLastModified = useRef<string | null>(null);

  // 获取当前 etag 和 last-modified
  const getCurrentInfo = async () => {
    try {
      const res = await fetch(import.meta.env.VITE_API_URL, {
        method: 'HEAD',
        cache: 'no-cache',
      });

      return {
        etag: res.headers.get('etag'),
        lastModified: res.headers.get('last-modified'),
      };
    } catch (error) {}
  };

  const run = async () => {
    const latestInfo = await getCurrentInfo();

    if (
      latestInfo &&
      (latestInfo.etag !== currentTag.current ||
        latestInfo.lastModified !== currentLastModified.current)
    ) {
      clearInterval(timer.current);
      setHasNewVersion(true);

      if (forceRefresh) {
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }
    }
  };

  // 轮询检测
  const pollingCheck = (immediately?: boolean) => {
    if (immediately) {
      run();
    }

    timer.current = setInterval(() => {
      run();
    }, 30000);
  };

  useEffect(() => {
    if (isDev) {
      return;
    }

    const onVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        clearInterval(timer.current);
      } else {
        pollingCheck(true);
      }
    };

    getCurrentInfo().then((info) => {
      if (info) {
        currentTag.current = info.etag;
        currentLastModified.current = info.lastModified;
      }

      pollingCheck();
      document.addEventListener('visibilitychange', onVisibilityChange);
    });

    return () => {
      clearInterval(timer.current);
      document.removeEventListener('visibilitychange', onVisibilityChange);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!hasNewVersion) {
    return null;
  }

  return forceRefresh ? (
    <div className="fixed top-0 left-0 h-full w-full z-[99999] flex justify-center items-center bg-[rgba(0,0,0,.5)]">
      <Result
        title={<span className="text-xl text-white">正在刷新页面升级系统...</span>}
        icon={
          <div className="flex justify-center">
            <img src={rocketImg} alt="" style={{ height: '50vh' }} />
          </div>
        }
      />
    </div>
  ) : (
    <div className="fixed bottom-5 right-5 z-[9999] bg-white w-[250px] p-3 rounded-md shadow-lg">
      <p>检测到有新版本，继续在当前版本操作可能会发生错误，是否刷新页面以应用更新？</p>
      <div className="flex gap-2 mt-2 justify-end">
        <Button
          size="small"
          onClick={() => {
            setHasNewVersion(false);
            clearInterval(timer.current);
          }}
        >
          忽略
        </Button>
        <Button
          size="small"
          type="primary"
          onClick={() => {
            setHasNewVersion(false);
            window.location.reload();
          }}
        >
          刷新
        </Button>
      </div>
    </div>
  );
};

export default CheckVersion;
