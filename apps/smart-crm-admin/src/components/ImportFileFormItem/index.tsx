import React, { useState } from 'react';
import { InboxOutlined } from '@ant-design/icons';
import { downloadUrl } from '@src/utils';
import { App, Button, Form, Upload } from 'antd';

interface ImportFileFormItemProps {
  downloadTemplate: () => Promise<{ url: string }>;
}

const ImportFileFormItem: React.FC<ImportFileFormItemProps> & {
  usePrompt: typeof usePrompt;
} = ({ downloadTemplate }) => {
  const [loading, setLoading] = useState(false);

  return (
    <Form.Item
      label={
        <div className="flex items-baseline">
          <span>仅支持 xlsx 格式，模版中表头不可更改</span>
          <Button
            type="link"
            loading={loading}
            onClick={async () => {
              setLoading(true);

              try {
                const res = await downloadTemplate();

                if (res) {
                  downloadUrl(res.url);
                }
              } catch (error) {}

              setLoading(false);
            }}
          >
            下载模版
          </Button>
        </div>
      }
      labelCol={{ span: 24 }}
      className="mb-0"
      name="file"
      rules={[{ required: true, message: '请上传附件' }]}
      getValueProps={(value) => ({ fileList: value ? [value] : undefined })}
      getValueFromEvent={(e) => (e.fileList.length ? e.file : undefined)}
    >
      <Upload.Dragger maxCount={1} accept=".xlsx,.csv" beforeUpload={() => false}>
        <div className="flex flex-col justify-center items-center h-[150px]">
          <InboxOutlined className="text-primary mb-2 text-[50px]" />
          将文件拖动到这里，或点击上传
        </div>
      </Upload.Dragger>
    </Form.Item>
  );
};

const usePrompt = () => {
  const { modal } = App.useApp();

  const showImportPrompt = (
    res: {
      successNum: number;
      failNum: number;
      failDownloadUrl: string;
    },
    onSuccess: () => void,
  ) => {
    modal.info({
      title: '导入信息',
      content: (
        <span>
          导入成功 {res.successNum} 条，导入失败 {res.failNum} 条。
          {res.failDownloadUrl && (
            <Button type="link" danger href={res.failDownloadUrl}>
              下载失败文件
            </Button>
          )}
        </span>
      ),
    });

    if (res.successNum > 0) {
      onSuccess();
    }
  };

  return { showImportPrompt };
};

ImportFileFormItem.usePrompt = usePrompt;

export default ImportFileFormItem;
