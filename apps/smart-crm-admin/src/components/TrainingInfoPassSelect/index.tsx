import { useEffect, useMemo, useRef, useState } from 'react';
import { getTrainingInfoPassPage } from '@src/services/process/train-staff';
import { TrainingPersonListDTO } from '@src/services/process/train-staff/type';
import { useRequest } from 'ahooks';
import { App, Button, Select, SelectProps, Spin, Tooltip } from 'antd';
import { debounce } from 'lodash-es';

interface TrainingInfoPassSelectProps extends SelectProps {
  showRefresh?: boolean;
  onRefresh?: () => Promise<void>;
}

const TrainingInfoPassSelect: React.FC<TrainingInfoPassSelectProps> = ({
  showRefresh,
  onRefresh,
  ...props
}) => {
  const { message } = App.useApp();
  const pageNum = useRef(1);
  const searchValue = useRef('');
  const [trainingInfoPassOptions, setTrainingInfoPassOptions] = useState<TrainingPersonListDTO[]>(
    [],
  );
  const [refreshLoading, setRefreshLoading] = useState(false);

  const {
    data,
    runAsync: getTrainingInfoPassOptions,
    loading,
    cancel,
  } = useRequest(getTrainingInfoPassPage, { manual: true });

  useEffect(() => {
    getTrainingInfoPassOptions({ pageNum: pageNum.current, pageSize: 20 }).then((res) => {
      setTrainingInfoPassOptions(res.result);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onSearch = useMemo(() => {
    const fetchOptions = (shopId: string) => {
      cancel();
      setTrainingInfoPassOptions([]);
      searchValue.current = shopId;
      pageNum.current = 1;
      getTrainingInfoPassOptions({ pageNum: pageNum.current, pageSize: 20, shopId }).then((res) => {
        setTrainingInfoPassOptions(res.result);
      });
    };

    return debounce(fetchOptions, 1000);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="flex items-center gap-2">
      <Select
        className="flex-1"
        placeholder="输入门店编号进行搜索"
        filterOption={false}
        showSearch
        fieldNames={{ label: 'shopId', value: 'shopId' }}
        options={trainingInfoPassOptions}
        notFoundContent={
          loading ? (
            <div className="flex justify-center items-center h-10">
              <Spin />
            </div>
          ) : undefined
        }
        onSearch={onSearch}
        onPopupScroll={({ currentTarget }) => {
          if (
            currentTarget.scrollHeight - currentTarget.scrollTop - currentTarget.offsetHeight <
              10 &&
            !loading &&
            data?.total &&
            trainingInfoPassOptions.length < data.total
          ) {
            pageNum.current += 1;
            getTrainingInfoPassOptions({
              pageNum: pageNum.current,
              pageSize: 20,
              shopId: searchValue.current,
            }).then((res) => {
              setTrainingInfoPassOptions((prev) => prev.concat(res.result));
            });
          }
        }}
        {...props}
      />
      {showRefresh && (
        <Tooltip title="重新获取门店最新数据">
          <Button
            className="p-0"
            type="link"
            disabled={refreshLoading}
            onClick={async () => {
              setRefreshLoading(true);

              try {
                await onRefresh?.();
                message.success('更新成功');
              } catch (error) {}

              setRefreshLoading(false);
            }}
          >
            刷新
          </Button>
        </Tooltip>
      )}
    </div>
  );
};

export default TrainingInfoPassSelect;
