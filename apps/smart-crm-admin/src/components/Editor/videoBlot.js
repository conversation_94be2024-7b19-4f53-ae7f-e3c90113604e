import Quill from 'quill';

const BlockEmbed = Quill.import('blots/block/embed');

class VideoBlot extends BlockEmbed {
  static blotName = 'customVideo';
  static tagName = 'video';

  static create(url) {
    const node = super.create();

    node.setAttribute('src', url);
    node.setAttribute('controls', true);
    node.setAttribute('width', '100%');

    return node;
  }

  static formats(node) {
    const format = {};

    if (node.hasAttribute('height')) {
      format.height = node.getAttribute('height');
    }

    if (node.hasAttribute('width')) {
      format.width = node.getAttribute('width');
    }

    return format;
  }

  static value(node) {
    return node.getAttribute('src');
  }

  format(name, value) {
    if (name === 'height' || name === 'width') {
      if (value) {
        this.domNode.setAttribute(name, value);
      } else {
        this.domNode.removeAttribute(name, value);
      }
    } else {
      super.format(name, value);
    }
  }
}

Quill.register(VideoBlot);
