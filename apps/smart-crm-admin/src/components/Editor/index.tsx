import React, { useEffect, useRef } from 'react';
import { parserJSON } from '@pkg/utils';
import useUploadFile from '@src/hooks/useUploadFile';
import { Upload, UploadProps } from 'antd';
import Quill from 'quill';
import 'quill/dist/quill.snow.css';
import './index.css';
import './videoBlot';
import { useEvent } from 'rc-util';

export interface EditorRef {
  getHTML: () => string;
  setContents: (html: string) => void;
}

interface EditorProps {
  onChange?: (value: string) => void;
}

const Editor = React.forwardRef<EditorRef, EditorProps>(({ onChange }, ref) => {
  const { uploadFile } = useUploadFile();

  const containerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<Quill | null>(null);
  const imageButtonRef = useRef<HTMLDivElement>(null);
  const videoButtonRef = useRef<HTMLDivElement>(null);

  const handleChange = useEvent((val: string) => {
    onChange?.(val);
  });

  useEffect(() => {
    const container = containerRef.current!;
    const editorContainer = container.appendChild(container.ownerDocument.createElement('div'));
    const quill = new Quill(editorContainer, {
      theme: 'snow',
      placeholder: '请输入内容',
      modules: {
        toolbar: {
          container: [
            ['bold', 'italic', 'underline', 'strike'],
            ['blockquote', 'code-block'],
            [{ list: 'ordered' }, { list: 'bullet' }],
            [{ indent: '-1' }, { indent: '+1' }],
            [{ size: ['small', false, 'large', 'huge'] }],
            [{ header: [1, 2, 3, 4, 5, 6, false] }],
            [{ color: [] }, { background: [] }],
            [{ font: [] }],
            [{ align: [] }],
            ['link', 'image', 'video'],
          ],
          handlers: {
            image: () => {
              imageButtonRef.current?.click();
            },
            video: () => {
              videoButtonRef.current?.click();
            },
          },
        },
      },
    });

    quill.on('text-change', () => {
      handleChange(JSON.stringify(quill.getContents().ops));
    });

    quill.root.addEventListener(
      'paste',
      async (e) => {
        const file = e.clipboardData?.files?.[0];

        if (file?.type.startsWith('image/')) {
          e.preventDefault();

          const res = await uploadFile(file as File);

          const range = quill.getSelection(true);

          editorRef.current?.insertEmbed(range.index, 'image', res?.url, Quill.sources.USER);
        }
      },
      true,
    );

    editorRef.current = quill;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  React.useImperativeHandle(ref, () => ({
    getHTML: () => editorRef.current?.getSemanticHTML() || '',
    setContents: (html) => {
      editorRef.current?.setContents(parserJSON(html) || []);
    },
  }));

  const getUploadProps = (type: 'image' | 'video'): UploadProps => ({
    className: 'hidden',
    accept: `${type}/*`,
    customRequest: async ({ file, onSuccess, onError }) => {
      try {
        const res = await uploadFile(file as File);
        const range = editorRef.current?.getSelection(true);

        if (range) {
          if (type === 'image') {
            editorRef.current?.insertEmbed(range.index, type, res?.url, Quill.sources.USER);
          } else {
            editorRef.current?.insertText(range.index, '\n', Quill.sources.USER);
            editorRef.current?.insertEmbed(
              range.index + 1,
              'customVideo',
              res?.url,
              Quill.sources.USER,
            );
            editorRef.current?.setSelection(range.index + 2, Quill.sources.SILENT);
          }
        }

        onSuccess?.(res);
      } catch (error) {
        onError?.(error as Error);
      }
    },
  });

  return (
    <>
      <div ref={containerRef} />
      <Upload {...getUploadProps('image')}>
        <div ref={imageButtonRef} />
      </Upload>
      <Upload {...getUploadProps('video')}>
        <div ref={videoButtonRef} />
      </Upload>
    </>
  );
});

export default Editor;
