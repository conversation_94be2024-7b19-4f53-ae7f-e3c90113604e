import { useState } from 'react';
import {
  EyeOutlined,
  FilePdfTwoTone,
  FileTwoTone,
  LoadingOutlined,
  PictureTwoTone,
  PlusOutlined,
  VideoCameraTwoTone,
} from '@ant-design/icons';
import { docxFileType, FILE_UPLOAD_MAX_SIZE, pdfFileType } from '@src/common/constants';
import useUploadFile from '@src/hooks/useUploadFile';
import { downloadUrl, isImageUrl, previewPdf } from '@src/utils';
import { App, Form, Image, Popover, PopoverProps, Upload, UploadFile, UploadProps } from 'antd';
import classNames from 'classnames';
import Overflow, { OverflowProps } from 'rc-overflow';
import DocxViewer from '../DocxViewer';
import VideoViewer from '../VideoViewer';

const UPLOAD_ICON_PLACEHOLDER = 'UPLOAD_ICON_PLACEHOLDER';

const placeholderBase64 =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3PTWBSGcbGzM6GCKqlIBRV0dHRJFarQ0eUT8LH4BnRU0NHR0UEFVdIlFRV7TzRksomPY8uykTk/zewQfKw/9znv4yvJynLv4uLiV2dBoDiBf4qP3/ARuCRABEFAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghggQAQZQKAnYEaQBAQaASKIAQJEkAEEegJmBElAoBEgghgg0Aj8i0JO4OzsrPv69Wv+hi2qPHr0qNvf39+iI97soRIh4f3z58/u7du3SXX7Xt7Z2enevHmzfQe+oSN2apSAPj09TSrb+XKI/f379+08+A0cNRE2ANkupk+ACNPvkSPcAAEibACyXUyfABGm3yNHuAECRNgAZLuYPgEirKlHu7u7XdyytGwHAd8jjNyng4OD7vnz51dbPT8/7z58+NB9+/bt6jU/TI+AGWHEnrx48eJ/EsSmHzx40L18+fLyzxF3ZVMjEyDCiEDjMYZZS5wiPXnyZFbJaxMhQIQRGzHvWR7XCyOCXsOmiDAi1HmPMMQjDpbpEiDCiL358eNHurW/5SnWdIBbXiDCiA38/Pnzrce2YyZ4//59F3ePLNMl4PbpiL2J0L979+7yDtHDhw8vtzzvdGnEXdvUigSIsCLAWavHp/+qM0BcXMd/q25n1vF57TYBp0a3mUzilePj4+7k5KSLb6gt6ydAhPUzXnoPR0dHl79WGTNCfBnn1uvSCJdegQhLI1vvCk+fPu2ePXt2tZOYEV6/fn31dz+shwAR1sP1cqvLntbEN9MxA9xcYjsxS1jWR4AIa2Ibzx0tc44fYX/16lV6NDFLXH+YL32jwiACRBiEbf5KcXoTIsQSpzXx4N28Ja4BQoK7rgXiydbHjx/P25TaQAJEGAguWy0+2Q8PD6/Ki4R8EVl+bzBOnZY95fq9rj9zAkTI2SxdidBHqG9+skdw43borCXO/ZcJdraPWdv22uIEiLA4q7nvvCug8WTqzQveOH26fodo7g6uFe/a17W3+nFBAkRYENRdb1vkkz1CH9cPsVy/jrhr27PqMYvENYNlHAIesRiBYwRy0V+8iXP8+/fvX11Mr7L7ECueb/r48eMqm7FuI2BGWDEG8cm+7G3NEOfmdcTQw4h9/55lhm7DekRYKQPZF2ArbXTAyu4kDYB2YxUzwg0gi/41ztHnfQG26HbGel/crVrm7tNY+/1btkOEAZ2M05r4FB7r9GbAIdxaZYrHdOsgJ/wCEQY0J74TmOKnbxxT9n3FgGGWWsVdowHtjt9Nnvf7yQM2aZU/TIAIAxrw6dOnAWtZZcoEnBpNuTuObWMEiLAx1HY0ZQJEmHJ3HNvGCBBhY6jtaMoEiJB0Z29vL6ls58vxPcO8/zfrdo5qvKO+d3Fx8Wu8zf1dW4p/cPzLly/dtv9Ts/EbcvGAHhHyfBIhZ6NSiIBTo0LNNtScABFyNiqFCBChULMNNSdAhJyNSiECRCjUbEPNCRAhZ6NSiAARCjXbUHMCRMjZqBQiQIRCzTbUnAARcjYqhQgQoVCzDTUnQIScjUohAkQo1GxDzQkQIWejUogAEQo121BzAkTI2agUIkCEQs021JwAEXI2KoUIEKFQsw01J0CEnI1KIQJEKNRsQ80JECFno1KIABEKNdtQcwJEyNmoFCJAhELNNtScABFyNiqFCBChULMNNSdAhJyNSiECRCjUbEPNCRAhZ6NSiAARCjXbUHMCRMjZqBQiQIRCzTbUnAARcjYqhQgQoVCzDTUnQIScjUohAkQo1GxDzQkQIWejUogAEQo121BzAkTI2agUIkCEQs021JwAEXI2KoUIEKFQsw01J0CEnI1KIQJEKNRsQ80JECFno1KIABEKNdtQcwJEyNmoFCJAhELNNtScABFyNiqFCBChULMNNSdAhJyNSiECRCjUbEPNCRAhZ6NSiAARCjXbUHMCRMjZqBQiQIRCzTbUnAARcjYqhQgQoVCzDTUnQIScjUohAkQo1GxDzQkQIWejUogAEQo121BzAkTI2agUIkCEQs021JwAEXI2KoUIEKFQsw01J0CEnI1KIQJEKNRsQ80JECFno1KIABEKNdtQcwJEyNmoFCJAhELNNtScABFyNiqFCBChULMNNSdAhJyNSiEC/wGgKKC4YMA4TAAAAABJRU5ErkJggg==';

interface PopoverUploadProps extends Omit<UploadProps, 'fileList'> {
  showUploadIcon?: boolean;
  editable?: boolean;
  popoverProps: PopoverProps;
  fileList?: string[] | UploadProps['fileList'];
  overflowProps?: OverflowProps<typeof UPLOAD_ICON_PLACEHOLDER | UploadFile>;
}

const iconRender = (file: UploadFile) => {
  if (file.status === 'uploading') {
    return '文件上传中';
  }

  if (file.type?.startsWith('image')) {
    return <PictureTwoTone />;
  }

  if (file.type === 'application/pdf') {
    return <FilePdfTwoTone />;
  }

  if (file.type?.startsWith('video')) {
    return <VideoCameraTwoTone />;
  }

  return <FileTwoTone />;
};

const DEFAULT_MAX_COUNT = 10;

const PopoverUpload: React.FC<PopoverUploadProps> = ({
  popoverProps,
  showUploadIcon,
  editable,
  fileList: propsFileList,
  overflowProps,
  ...restProps
}) => {
  const form = Form.useFormInstance();
  const { uploadFile } = useUploadFile();
  const { message } = App.useApp();
  const [previewImgSrc, setPreviewImgSrc] = useState('');
  const [previewImgVisible, setPreviewImgVisible] = useState(false);
  const [videoUrl, setVideoUrl] = useState('');
  const [docxUrl, setDocxUrl] = useState('');

  const maxCount = restProps.maxCount || DEFAULT_MAX_COUNT;

  const fileList = propsFileList?.map((file, index) => {
    if (typeof file === 'string') {
      return {
        uid: `${index}`,
        name: file,
        // 如果是 string，都认为是图片
        type: 'image/png',
        url: file,
        status: 'done',
      } as UploadFile;
    }

    return file;
  });

  return (
    <Popover
      destroyOnHidden
      trigger={['click']}
      classNames={{
        body: 'max-h-[456px]',
      }}
      {...popoverProps}
      onOpenChange={(op) => {
        if (op) {
          popoverProps.onOpenChange?.(op);
        } else {
          // 关闭图片预览时，不触发 popover 关闭
          if (!previewImgVisible) {
            popoverProps.onOpenChange?.(op);
          }
        }
      }}
      content={
        // 解决 React.createPortal 冒泡问题
        <div onClick={(e) => e.stopPropagation()}>
          <Upload
            multiple
            disabled={!editable}
            iconRender={iconRender}
            fileList={fileList}
            isImageUrl={isImageUrl}
            listType="picture-card"
            showUploadList={{
              // 只有这些类型支持预览
              previewIcon: (file) =>
                file.type?.startsWith('image') ||
                file.type?.startsWith('video') ||
                [pdfFileType, docxFileType].includes(file.type!) ? (
                  <EyeOutlined />
                ) : null,
              showDownloadIcon: true,
            }}
            beforeUpload={(file, beforeFiles) => {
              if ((fileList || []).length + beforeFiles.length > maxCount) {
                message.error(`不能超过 ${maxCount} 个文件`);

                return Upload.LIST_IGNORE;
              }

              if (file.size / 1024 / 1024 > FILE_UPLOAD_MAX_SIZE) {
                message.error(`文件大小不能大于 ${FILE_UPLOAD_MAX_SIZE}M`);

                return Upload.LIST_IGNORE;
              }

              return true;
            }}
            customRequest={async ({ file, onSuccess, onError, onProgress }) => {
              try {
                const res = await uploadFile(file as File, (percent) => onProgress?.({ percent }));

                onSuccess?.(res);
              } catch (error) {
                message.error('上传失败，请重新上传');
                onError?.(error as Error);
              }

              form.submit();
            }}
            onDownload={(file) => downloadUrl(file.url)}
            onRemove={(file) => {
              restProps.onChange?.({
                file,
                fileList: (fileList || []).filter((i) => i.uid !== file.uid),
              });

              form.submit();
            }}
            onPreview={(file) => {
              if (file.url) {
                if (file.type?.startsWith('image')) {
                  setPreviewImgVisible(true);
                  setPreviewImgSrc(file.url);
                } else if (file.type?.startsWith('video')) {
                  setVideoUrl(file.url);
                } else if (file.type === pdfFileType) {
                  previewPdf(file.url);
                } else if (file.type === docxFileType) {
                  setDocxUrl(file.url);
                }
              }
            }}
            {...restProps}
          >
            {editable && (fileList || []).length < maxCount && (
              <PlusOutlined className="text-2xl" />
            )}
          </Upload>
        </div>
      }
    >
      <Overflow
        maxCount="responsive"
        data={[UPLOAD_ICON_PLACEHOLDER, ...(fileList || [])]}
        renderRest={(restItems) => (
          <span className="text-gray-500 cursor-pointer ml-1">+{restItems.length}</span>
        )}
        renderItem={(file) => {
          if (file === UPLOAD_ICON_PLACEHOLDER) {
            if (showUploadIcon) {
              return (
                <div className="h-6 w-6 flex justify-center items-center border border-gray-200 border-dashed mr-1 cursor-pointer rounded bg-gray-100 hover:border-primary">
                  <PlusOutlined />
                </div>
              );
            }

            return null;
          } else if (typeof file === 'object') {
            const isLast =
              fileList?.findIndex((i) => i.uid === file.uid) === (fileList || []).length - 1;
            const mrClassName = isLast ? '' : 'mr-1';

            let showComponent;

            if (file.status === 'uploading') {
              showComponent = (
                <div
                  className={`flex justify-center items-center border rounded w-6 h-6 border-gray-200 ${mrClassName}`}
                >
                  <LoadingOutlined />
                </div>
              );
            } else if (file.type?.startsWith('image')) {
              showComponent = (
                <img
                  alt=""
                  src={file.url}
                  // 加载中的占位符
                  style={{ backgroundImage: `url(${placeholderBase64})` }}
                  className={`h-6 w-6 bg-contain rounded cursor-pointer object-contain ${mrClassName}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    setPreviewImgSrc(file.url || '');
                    setPreviewImgVisible(true);
                  }}
                />
              );
            } else {
              showComponent = (
                <div
                  className={`flex justify-center items-center border rounded w-6 h-6 border-gray-200 cursor-pointer ${mrClassName}`}
                  onClick={(e) => {
                    e.stopPropagation();

                    if (file.url) {
                      if (file.type === pdfFileType) {
                        previewPdf(file.url);
                      } else if (file.type === docxFileType) {
                        setDocxUrl(file.url);
                      } else if (file.type?.startsWith('video')) {
                        setVideoUrl(file.url);
                      } else {
                        message.error('该文件类型暂不支持预览');
                      }
                    }
                  }}
                >
                  {iconRender(file)}
                </div>
              );
            }

            return showComponent;
          }
        }}
        {...overflowProps}
        className={classNames('flex flex-nowrap h-full items-center', overflowProps?.className)}
      />

      {/* 解决 React.createPortal 冒泡问题 */}
      <div onClick={(e) => e.stopPropagation()}>
        <Image
          wrapperStyle={{ display: 'none' }}
          src={previewImgSrc}
          preview={{
            destroyOnHidden: true,
            visible: previewImgVisible,
            onVisibleChange: setPreviewImgVisible,
          }}
        />

        <VideoViewer open={!!videoUrl} url={videoUrl} onClose={() => setVideoUrl('')} />

        <DocxViewer open={!!docxUrl} url={docxUrl} onClose={() => setDocxUrl('')} />
      </div>
    </Popover>
  );
};

export default PopoverUpload;
