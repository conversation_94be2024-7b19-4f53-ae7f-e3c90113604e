import { Form, InputNumber, InputNumberProps } from 'antd';
import { EditableFieldProps } from './interface';

interface EditableNumberProps extends EditableFieldProps {
  fieldProps?: InputNumberProps;
  value?: number;
}

const EditableNumber: React.FC<EditableNumberProps> = ({
  value,
  editable,
  fieldProps,
  formItemProps,
}) => {
  if (editable) {
    return (
      <Form.Item {...formItemProps}>
        <InputNumber style={{ width: '100%' }} placeholder="请输入" {...fieldProps} />
      </Form.Item>
    );
  }

  return value;
};

export default EditableNumber;
