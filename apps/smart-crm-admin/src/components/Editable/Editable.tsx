import EditableAttachment from './EditableAttachment';
import EditableDate from './EditableDate';
import EditableDateRange from './EditableDateRange';
import EditableDateTime from './EditableDateTime';
import EditableIdentityCard from './EditableIdentityCard';
import EditableInput from './EditableInput';
import EditableMultiple from './EditableMultiple';
import EditableNumber from './EditableNumber';
import EditablePerson from './EditablePerson';
import EditablePhone from './EditablePhone';
import EditablePosition from './EditablePosition';
import EditableRegion from './EditableRegion';
import EditableSingle from './EditableSingle';
import EditableTextArray from './EditableTextArray';
import { EditableProps } from './interface';
import { ValueType } from '../ETable';

const componentsMap: Partial<Record<ValueType, React.FunctionComponent<any>>> = {
  [ValueType.TEXT]: EditableInput,
  [ValueType.DATE]: EditableDate,
  [ValueType.DATE_TIME]: EditableDateTime,
  [ValueType.SINGLE]: EditableSingle,
  [ValueType.MULTIPLE]: EditableMultiple,
  [ValueType.NUMBER]: EditableNumber,
  [ValueType.PHONE]: EditablePhone,
  [ValueType.IDENTITY_CARD]: EditableIdentityCard,
  [ValueType.ATTACHMENT]: EditableAttachment,
  [ValueType.REGION]: EditableRegion,
  [ValueType.PERSON]: EditablePerson,
  [ValueType.TEXT_ARRAY]: EditableTextArray,
  [ValueType.DATE_RANGE]: EditableDateRange,
  [ValueType.POSITION]: EditablePosition,
};

const Editable: React.FC<EditableProps> = ({
  initialValue,
  editable,
  valueType = ValueType.TEXT,
  fieldProps,
  formItemProps,
}) => {
  const Component = componentsMap[valueType] || EditableInput;

  return (
    <Component
      value={initialValue}
      editable={editable}
      fieldProps={fieldProps}
      formItemProps={formItemProps}
    />
  );
};

export default Editable;
