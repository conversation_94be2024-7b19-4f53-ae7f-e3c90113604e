import { Form, Input } from 'antd';
import { TextAreaProps } from 'antd/lib/input';
import { EditableFieldProps } from './interface';

interface EditableInputProps extends EditableFieldProps {
  fieldProps?: TextAreaProps;
  value?: string;
}

const EditableInput: React.FC<EditableInputProps> = ({
  value,
  editable,
  fieldProps,
  formItemProps,
}) => {
  if (editable) {
    return (
      <Form.Item {...formItemProps}>
        <Input.TextArea
          autoSize
          placeholder={`请输入${
            typeof formItemProps?.label === 'string' ? formItemProps.label : ''
          }`}
          {...fieldProps}
        />
      </Form.Item>
    );
  }

  return value;
};

export default EditableInput;
