import { parserJ<PERSON><PERSON> } from '@pkg/utils';
import { Form } from 'antd';
import { EditableFieldProps } from './interface';
import ChooseMapPoints from '../ChooseMapPoints';

interface EditablePositionProps extends EditableFieldProps {
  value?: string;
}

type ValueType = {
  name: string;
  longitude: number;
  latitude: number;
};

const EditablePosition: React.FC<EditablePositionProps> = ({ value, editable, formItemProps }) => {
  if (editable) {
    return (
      <Form.Item
        getValueProps={(val) => {
          let result;

          if (val) {
            const obj = parserJSON(val);

            if (obj) {
              result = [obj];
            }
          }

          return {
            value: result,
          };
        }}
        normalize={(val) =>
          Array.isArray(val) && val.length > 0 ? JSON.stringify(val[0]) : undefined
        }
        {...formItemProps}
      >
        <ChooseMapPoints multiple={false} />
      </Form.Item>
    );
  }

  if (value) {
    const obj: ValueType | undefined = parserJSON(value);

    if (obj) {
      return `${obj.name}（经度：${obj.longitude}；纬度：${obj.latitude}）`;
    }
  }
};

export default EditablePosition;
