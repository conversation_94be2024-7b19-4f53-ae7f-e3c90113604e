import { useState } from 'react';
import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons';
import { idCardPattern } from '@src/common/constants';
import { decryptSensitive } from '@src/services/common';
import { SensitiveTypeEnum } from '@src/services/common/type';
import { useRequest } from 'ahooks';
import { Button, Form, Input, InputProps } from 'antd';
import { EditableFieldProps } from './interface';
import Encrypt from '../Encrypt';

interface EditableIdentityCardProps extends EditableFieldProps {
  fieldProps?: InputProps & {
    // 默认为 true
    sensitive?: boolean;
    sensitiveValue?: string;
    encryptSensitiveMap?: Record<string, string | undefined>;
  };
  value?: string;
}

const EditableIdentityCard: React.FC<EditableIdentityCardProps> = ({
  value,
  editable,
  fieldProps,
  formItemProps,
}) => {
  const [showReal, setShowReal] = useState(false);

  const {
    data: realValue,
    runAsync: getRealValue,
    loading,
  } = useRequest(decryptSensitive, { manual: true });

  const {
    sensitive = true,
    sensitiveValue,
    encryptSensitiveMap,
    ...restFieldProps
  } = fieldProps || {};

  if (editable) {
    if (sensitive) {
      return (
        <Encrypt.IdCardFormItem
          formItemProps={formItemProps}
          fieldProps={{
            sensitiveValue,
            encryptSensitiveMap,
            ...(restFieldProps as any),
          }}
        />
      );
    }

    return (
      <Form.Item
        validateFirst
        {...formItemProps}
        rules={[
          {
            pattern: idCardPattern,
            message: '请输入正确的身份证号码',
          },
          ...(formItemProps?.rules || []),
        ]}
      >
        <Input
          placeholder={`请输入${
            typeof formItemProps?.label === 'string' ? formItemProps.label : ''
          }`}
          {...restFieldProps}
        />
      </Form.Item>
    );
  }

  if (sensitiveValue && sensitive) {
    return (
      <div>
        {showReal ? realValue : sensitiveValue}
        <Button
          type="text"
          size="small"
          loading={loading}
          className="ml-1"
          icon={showReal ? <EyeInvisibleOutlined /> : <EyeOutlined />}
          onClick={async () => {
            if (!showReal && !realValue) {
              await getRealValue({
                encrypted: value || '',
                sensitiveType: SensitiveTypeEnum.ID_CARD,
              });
              setShowReal(!showReal);
            } else {
              setShowReal(!showReal);
            }
          }}
        />
      </div>
    );
  }

  return value;
};

export default EditableIdentityCard;
