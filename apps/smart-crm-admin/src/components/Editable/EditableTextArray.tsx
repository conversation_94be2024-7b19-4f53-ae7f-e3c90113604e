import React from 'react';
import { CloseCircleFilled, PlusOutlined } from '@ant-design/icons';
import { Button, Form, Input } from 'antd';
import { EditableFieldProps } from './interface';

interface TextArrayProps {
  value?: string[];
  onChange?: (value: string[]) => void;
}

const TextArray: React.FC<TextArrayProps> = ({ value, onChange }) => {
  const internalValue = Array.isArray(value) && value.length > 0 ? value : [''];

  return (
    <div className="flex flex-col items-start gap-2">
      {internalValue.map((val, index) => (
        <div key={index} className="flex w-full">
          <Input
            placeholder="请输入"
            value={val}
            onChange={(e) => {
              const result = [...internalValue];

              result[index] = e.target.value;
              onChange?.(result);
            }}
          />
          {internalValue.length > 1 && (
            <Button
              icon={<CloseCircleFilled className="text-gray-400" />}
              type="text"
              className="ml-2"
              onClick={() => onChange?.(internalValue.filter((_, idx) => idx !== index))}
            />
          )}
        </div>
      ))}
      <Button
        type="text"
        className="!text-primary"
        icon={<PlusOutlined />}
        onClick={() => onChange?.(internalValue.concat(''))}
      >
        添加
      </Button>
    </div>
  );
};

interface EditableTextArrayProps extends EditableFieldProps {
  value?: string;
}

const EditableTextArray: React.FC<EditableTextArrayProps> = ({
  value,
  editable,
  formItemProps,
}) => {
  if (editable) {
    return (
      <Form.Item
        getValueProps={(val) => ({ value: val ? val.split(',') : undefined })}
        normalize={(val) => (val ? val.join(',') : undefined)}
        {...formItemProps}
        rules={[
          {
            validator: (_, val: string) => {
              if (val) {
                const hasValueArray = val.split(',').filter(Boolean);

                if ([...new Set(hasValueArray)].length !== hasValueArray.length) {
                  const label = typeof formItemProps?.label === 'string' ? formItemProps.label : '';

                  return Promise.reject(
                    new Error(label ? `不能出现重复的${formItemProps?.label}` : '不能出现重复'),
                  );
                }
              }

              return Promise.resolve();
            },
          },
          ...(formItemProps?.rules || []),
        ]}
      >
        <TextArray />
      </Form.Item>
    );
  }

  const array = Array.isArray(value) ? value : value?.split(',');

  return array?.join('、');
};

export default EditableTextArray;
