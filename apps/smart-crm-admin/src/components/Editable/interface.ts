import { FormItemProps } from 'antd';
import { ValueType } from '../ETable';

export interface EditableProps {
  /** 默认值 */
  initialValue?: any;
  /** 是否可编辑 */
  editable?: boolean;
  /** 类型 */
  valueType?: ValueType;
  /** formItem 属性 */
  formItemProps?: FormItemProps;
  /** 控件属性 */
  fieldProps?: Record<string, any>;
}

export interface EditableFieldProps {
  value?: any;
  fieldProps?: Record<string, any>;
  formItemProps?: FormItemProps;
  editable?: boolean;
}
