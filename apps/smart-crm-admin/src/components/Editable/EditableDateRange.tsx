import React from 'react';
import { DatePicker, Form } from 'antd';
import { RangePickerProps } from 'antd/lib/date-picker';
import dayjs, { Dayjs } from 'dayjs';
import { EditableFieldProps } from './interface';

interface EditableDateRangeProps extends EditableFieldProps {
  fieldProps?: RangePickerProps;
  value?: string;
}

const EditableDateRange: React.FC<EditableDateRangeProps> = ({
  value,
  editable,
  fieldProps,
  formItemProps,
}) => {
  if (editable) {
    return (
      <Form.Item
        getValueProps={(val) => ({
          value: val ? val.split(',').map((i: string) => dayjs(i)) : undefined,
        })}
        normalize={(val: [Dayjs, Dayjs] | null) =>
          val ? val.map((i) => i.format('YYYY-MM-DD')).join(',') : undefined
        }
        {...formItemProps}
      >
        <DatePicker.RangePicker {...fieldProps} />
      </Form.Item>
    );
  }

  return value ? value.split(',').join(' - ') : value;
};

export default EditableDateRange;
