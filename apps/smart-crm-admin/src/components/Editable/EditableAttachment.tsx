import { useState } from 'react';
import { parserJSON } from '@pkg/utils';
import { FileDTO } from '@src/services/common/type';
import { UploadProps } from 'antd';
import { UploadFile } from 'antd/lib';
import classNames from 'classnames';
import { OverflowProps } from 'rc-overflow';
import { EditableFieldProps } from './interface';
import FileUpload from '../FileUpload';
import { InternalUploadProps } from '../FileUpload/InternalUpload';
import PopoverUpload from '../PopoverUpload';

interface EditableAttachmentProps extends EditableFieldProps {
  fieldProps?: InternalUploadProps & {
    overflowProps?: OverflowProps<any>;
  };
  value?: string | FileDTO[] | string[];
}

const EditableAttachment: React.FC<EditableAttachmentProps> = ({
  value,
  editable,
  fieldProps,
  formItemProps,
}) => {
  const [popoverOpen, setPopoverOpen] = useState(false);

  if (editable) {
    return (
      <FileUpload formItemProps={formItemProps} uploadProps={{ format: 'json', ...fieldProps }} />
    );
  }

  const getFileList = (): string[] | UploadFile[] | undefined => {
    if (fieldProps?.format === 'url') {
      return value ? [value as string] : undefined;
    }

    return ((Array.isArray(value) ? value : parserJSON(value)) || []).map(
      (file: FileDTO | string) =>
        typeof file === 'string'
          ? file
          : ({
              uid: file.fileKey,
              url: file.fileUrl,
              name: file.fileName,
              type: file.fileType,
              status: 'done',
              response: {
                key: file.fileKey,
              },
            } as UploadFile<any>),
    );
  };

  const fileList = getFileList();

  if (!fileList?.length) {
    return null;
  }

  return (
    <div
      className={classNames('p-1 rounded-md hover:bg-gray-100 cursor-pointer', {
        'bg-gray-100': popoverOpen,
      })}
      style={{ width: '100%' }}
      onClick={() => {
        setPopoverOpen(!popoverOpen);
      }}
    >
      <PopoverUpload
        fileList={fileList}
        popoverProps={{
          open: popoverOpen,
          onOpenChange: (op) => {
            if (!op) {
              setPopoverOpen(op);
            }
          },
        }}
        {...(fieldProps as UploadProps)}
      />
    </div>
  );
};

export default EditableAttachment;
