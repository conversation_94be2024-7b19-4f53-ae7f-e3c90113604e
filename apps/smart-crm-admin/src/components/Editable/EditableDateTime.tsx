import { DatePicker, DatePickerProps, Form } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { EditableFieldProps } from './interface';

interface EditableDateTimeProps extends EditableFieldProps {
  fieldProps?: DatePickerProps;
  value?: string | number | Dayjs;
}

const EditableDateTime: React.FC<EditableDateTimeProps> = ({
  value,
  editable,
  fieldProps,
  formItemProps,
}) => {
  if (editable) {
    return (
      <Form.Item
        getValueProps={(val) => ({ value: val ? dayjs(val) : undefined })}
        normalize={(val: dayjs.Dayjs) => (val ? val.format('YYYY-MM-DD HH:mm:ss') : undefined)}
        {...formItemProps}
      >
        <DatePicker showTime {...fieldProps} />
      </Form.Item>
    );
  }

  return value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : value;
};

export default EditableDateTime;
