import { Form, Popover, SelectProps, Tag } from 'antd';
import Overflow from 'rc-overflow';
import { EditableFieldProps } from './interface';
import { PopoverValidatorSelect } from '../PopoverValidator';

export interface EditableMultipleProps extends EditableFieldProps {
  fieldProps?: SelectProps;
  value?: string | (string | number)[];
}

const EditableMultiple: React.FC<EditableMultipleProps> = ({
  value: propsValue,
  editable,
  fieldProps,
  formItemProps,
}) => {
  const fieldNames = fieldProps?.fieldNames;
  const fieldNameLabel = fieldNames?.label || 'label';

  if (editable) {
    return (
      <Form.Item
        getValueProps={(val?: string | (string | number)[]) => ({
          // 如果外面传进来的是数组，直接用，否则逗号切开成数组
          value: Array.isArray(val) ? val : val ? val.split(',') : undefined,
        })}
        normalize={(val) => val.join(',')}
        {...formItemProps}
      >
        <PopoverValidatorSelect
          allowClear
          popover={false}
          mode="multiple"
          maxTagCount="responsive"
          optionFilterProp={fieldNameLabel}
          placeholder={`请选择${
            typeof formItemProps?.label === 'string' ? formItemProps.label : ''
          }`}
          {...fieldProps}
          style={{ width: '100%', ...fieldProps?.style }}
        />
      </Form.Item>
    );
  }

  const value = Array.isArray(propsValue) ? propsValue : propsValue ? propsValue.split(',') : [];

  const fieldNameValue = fieldNames?.value || 'value';
  const getLabel = (val: any) => {
    return fieldProps?.options?.find((option) => option[fieldNameValue] === val)?.[fieldNameLabel];
  };

  return (
    <Overflow
      className="flex flex-nowrap w-full"
      data={value}
      renderItem={(item) => {
        const isLast = value.findIndex((i: any) => i === item) === value.length - 1;

        return <Tag className={isLast ? 'mr-0' : ''}>{getLabel(item)}</Tag>;
      }}
      maxCount="responsive"
      renderRest={(items) => (
        <Popover
          destroyOnHidden
          classNames={{
            body: '!p-0',
          }}
          content={
            <div
              className="flex gap-2 flex-wrap max-w-[240px] p-3"
              // 阻止 createPortal 点击事件冒泡，否则点击后导致触发编辑模式
              onClick={(e) => e.stopPropagation()}
            >
              {items.map((item: any) => (
                <Tag key={item} className="mr-0">
                  {getLabel(item)}
                </Tag>
              ))}
            </div>
          }
        >
          <span className="cursor-pointer text-gray-500">+{items.length}</span>
        </Popover>
      )}
    />
  );
};

export default EditableMultiple;
