import useDistricts from '@src/hooks/useDistricts';
import { Form, Popover, Tag } from 'antd';
import Overflow, { OverflowProps } from 'rc-overflow';
import { EditableFieldProps } from './interface';
import { getRegionMultipleLabelArr, getRegionSingleLabel } from '../ETable/utils';
import RegionCascader, { RegionCascaderProps } from '../RegionCascader';

interface EditableRegionProps extends EditableFieldProps {
  fieldProps?: RegionCascaderProps & { overflowProps?: OverflowProps<any> };
  value?: string;
}

const EditableRegion: React.FC<EditableRegionProps> = ({
  value,
  editable,
  fieldProps,
  formItemProps,
}) => {
  const streets = fieldProps?.regionLevel === 4;
  const { data } = useDistricts({ streets });

  const isMultiple = fieldProps?.multiple;

  if (editable) {
    return (
      <Form.Item
        getValueProps={(val: string) => {
          let result;

          if (val) {
            if (isMultiple) {
              result = val.split(',').map((i) => i.split('/'));
            } else {
              result = val.split('/');
            }
          }

          return {
            value: result,
          };
        }}
        normalize={(val) => {
          if (val) {
            if (isMultiple) {
              return val.map((item: number[]) => item.join('/')).join(',');
            }

            return val.join('/');
          }

          return val;
        }}
        {...formItemProps}
      >
        <RegionCascader placeholder="请选择" {...fieldProps} />
      </Form.Item>
    );
  }

  if (value) {
    if (isMultiple) {
      const labelArray = getRegionMultipleLabelArr(value, data);

      return (
        <Overflow
          data={labelArray}
          className="flex flex-nowrap w-full"
          renderItem={(item) => {
            const isLast = labelArray.findIndex((i) => i === item) === labelArray.length - 1;

            return <Tag className={isLast ? 'mr-0' : ''}>{item}</Tag>;
          }}
          maxCount="responsive"
          renderRest={(items) => (
            <Popover
              destroyOnHidden
              classNames={{
                body: 'max-h-[400px] overflow-auto',
              }}
              content={
                <div className="flex gap-2 flex-wrap max-w-[240px]">
                  {items.map((item) => (
                    <Tag key={item} className="mr-0">
                      {item}
                    </Tag>
                  ))}
                </div>
              }
            >
              <span className="cursor-pointer text-gray-500">+{items.length}</span>
            </Popover>
          )}
          {...fieldProps.overflowProps}
        />
      );
    } else {
      return getRegionSingleLabel(value, data);
    }
  }
};

export default EditableRegion;
