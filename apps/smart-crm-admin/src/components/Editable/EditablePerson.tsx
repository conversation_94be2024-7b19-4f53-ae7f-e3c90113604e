import useAllUsers from '@src/hooks/useAllUsers';
import { SelectProps } from 'antd';
import EditableMultiple, { EditableMultipleProps } from './EditableMultiple';
import EditableSingle from './EditableSingle';

const fieldNames = { label: 'name', value: 'id' };

const EditablePerson: React.FC<EditableMultipleProps> = ({ value, ...props }) => {
  const { data, loading } = useAllUsers();

  const arrayValue = Array.isArray(value)
    ? value
    : typeof value === 'number'
    ? [value]
    : value?.split(',').map(Number);

  if (!props.editable && Array.isArray(arrayValue) && arrayValue.length === 1) {
    return (
      <EditableSingle
        {...props}
        value={arrayValue[0]}
        fieldProps={
          {
            loading,
            fieldNames,
            options: data,
            ...props.fieldProps,
          } as SelectProps
        }
      />
    );
  }

  return (
    <EditableMultiple
      {...props}
      value={value}
      formItemProps={{
        getValueProps: (val?: string) => ({
          value: Array.isArray(val) ? val : val ? val.split(',').map(Number) : undefined,
        }),
        ...props.formItemProps,
      }}
      fieldProps={
        {
          loading,
          showSearch: true,
          fieldNames,
          optionFilterProp: 'name',
          options: data,
          ...props.fieldProps,
        } as SelectProps
      }
    />
  );
};

export default EditablePerson;
