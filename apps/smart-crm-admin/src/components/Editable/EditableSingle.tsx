import { Form, Select, SelectProps } from 'antd';
import { EditableFieldProps } from './interface';

interface EditableSingleProps extends EditableFieldProps {
  fieldProps?: SelectProps;
  value?: any;
}

const EditableSingle: React.FC<EditableSingleProps> = ({
  value,
  editable,
  fieldProps,
  formItemProps,
}) => {
  const fieldNames = fieldProps?.fieldNames;
  const fieldNameLabel = fieldNames?.label || 'label';

  if (editable) {
    return (
      <Form.Item {...formItemProps}>
        <Select
          showSearch
          allowClear
          optionFilterProp={fieldNameLabel}
          placeholder={`请选择${
            typeof formItemProps?.label === 'string' ? formItemProps.label : ''
          }`}
          {...fieldProps}
          style={{ width: '100%', ...fieldProps?.style }}
        />
      </Form.Item>
    );
  }

  const fieldNameValue = fieldNames?.value || 'value';

  return fieldProps?.options?.find((i) => i[fieldNameValue] === value)?.[fieldNameLabel];
};

export default EditableSingle;
