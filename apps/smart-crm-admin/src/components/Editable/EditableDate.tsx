import React from 'react';
import { DatePicker, DatePickerProps, Form } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { EditableFieldProps } from './interface';

interface EditableDateProps extends EditableFieldProps {
  fieldProps?: DatePickerProps;
  value?: string | number | Dayjs;
}

const EditableDate: React.FC<EditableDateProps> = ({
  value,
  editable,
  fieldProps,
  formItemProps,
}) => {
  if (editable) {
    return (
      <Form.Item
        getValueProps={(val) => ({ value: val ? dayjs(val) : undefined })}
        normalize={(val: dayjs.Dayjs) => (val ? val.format('YYYY-MM-DD') : undefined)}
        {...formItemProps}
      >
        <DatePicker {...fieldProps} />
      </Form.Item>
    );
  }

  return value ? dayjs(value).format('YYYY-MM-DD') : value;
};

export default EditableDate;
