import React, { useEffect, useMemo, useRef, useState } from 'react';
import { CloseCircleFilled, EnvironmentOutlined } from '@ant-design/icons';
import { APILoader } from '@uiw/react-amap-api-loader';
import { Map as ReactMap, useMapContext } from '@uiw/react-amap-map';
import { Marker } from '@uiw/react-amap-marker';
import { AutoComplete, Button, Empty, Modal, ModalProps } from 'antd';
import { debounce } from 'lodash-es';

interface SearchInputProps {
  onSelect: (info: {
    formattedAddress: string;
    location: { lng: number; lat: number };
    adcode: string;
  }) => void;
}

const SearchInput: React.FC<SearchInputProps> = ({ onSelect }) => {
  const { map } = useMapContext();
  const [options, setOptions] = useState([]);
  const locationMapRef = useRef<Map<string, any>>(new Map());

  const handleSearch = useMemo(() => {
    const searchFn = (address: string) => {
      setOptions([]);
      window.AMap.plugin(['AMap.Geocoder'], function () {
        const geocoder = new window.AMap.Geocoder({});

        geocoder.getLocation?.(address, function (status: string, result: any) {
          if (status === 'complete' && result.info === 'OK' && result.geocodes.length) {
            setOptions(
              result.geocodes.map((i: any) => {
                locationMapRef.current.set(i.formattedAddress, i);

                return {
                  label: i.formattedAddress,
                  value: i.formattedAddress,
                };
              }),
            );
          } else {
            setOptions([]);
          }
        });
      });
    };

    return debounce(searchFn, 1000);
  }, []);

  return (
    <AutoComplete
      className="absolute top-2 left-2 w-[90%]"
      options={options}
      placeholder="请输入具体地址进行搜索"
      notFoundContent={<div className="text-center">搜索无结果</div>}
      onSearch={handleSearch}
      onSelect={(value) => {
        const info = locationMapRef.current.get(value);

        map?.setCenter(info.location);
        onSelect(info);
      }}
    />
  );
};

type ValueType = {
  latitude: number;
  longitude: number;
  name: string;
};

interface ChooseMapPointsProps extends ModalProps {
  multiple?: boolean;
  placeholder?: string;
  disabled?: boolean;
  value?: ValueType[];
  beforeSelect?: (
    info: {
      longitude: number;
      latitude: number;
      adcode: string;
      towncode?: string;
    },
    beforeValues: ValueType[],
  ) => boolean | Promise<boolean>;
  onChange?: (value: ValueType[]) => void;
}

const ChooseMapPoints: React.FC<ChooseMapPointsProps> = ({
  multiple = true,
  placeholder = '未选择点位',
  disabled,
  value,
  beforeSelect,
  onChange,
}) => {
  const [open, setOpen] = useState(false);
  const [internalValue, setInternalValue] = useState<ValueType[]>([]);

  useEffect(() => {
    window._AMapSecurityConfig = {
      serviceHost: `${import.meta.env.VITE_API_URL}/_AMapService`,
    };
  }, []);

  return (
    <>
      {value?.map((item, index) => (
        <div key={index} className="flex mt-2 items-center">
          <span className="flex-1">
            {multiple && `${index + 1}.`} {item.name}（经度：{item.longitude}；纬度：
            {item.latitude}）
          </span>
          {!disabled && (
            <Button
              icon={<CloseCircleFilled className="text-gray-400" />}
              type="text"
              className="ml-2"
              onClick={() => onChange?.(value?.filter((_, idx) => idx !== index))}
            />
          )}
        </div>
      ))}
      {!disabled && (
        <Button
          type="text"
          icon={<EnvironmentOutlined />}
          className="!text-primary"
          onClick={() => {
            setOpen(true);
            setInternalValue(value || []);
          }}
        >
          去选择
        </Button>
      )}
      {disabled && !value?.length && <span className="text-[#bfbfbf]">{placeholder}</span>}
      <Modal
        open={open}
        title="选择点位"
        width={1000}
        destroyOnHidden
        onCancel={() => setOpen(false)}
        onOk={() => {
          setOpen(false);
          onChange?.(internalValue);
        }}
      >
        <div className="flex flex-col sm:flex-row gap-5 sm:h-[50vh]">
          <div className="relative sm:flex-1 h-[50vh]">
            <APILoader version="2.0" akey="173b941f0f0ff5b2f5084d56dbcd210f">
              <ReactMap
                // @ts-ignore
                onClick={(e: any) => {
                  window.AMap.plugin(['AMap.Geocoder'], () => {
                    const instance = new window.AMap.Geocoder({});

                    instance.getAddress(e.lnglat, async (status: string, result: any) => {
                      if (status === 'complete' && result.info === 'OK') {
                        const { lng, lat } = e.lnglat;

                        if (
                          (await beforeSelect?.(
                            {
                              longitude: lng,
                              latitude: lat,
                              adcode: result.regeocode.addressComponent.adcode,
                              towncode: result.regeocode.addressComponent.towncode,
                            },
                            internalValue,
                          )) === false
                        ) {
                          return;
                        }

                        setInternalValue((prev) => {
                          const newItem = {
                            latitude: lat,
                            longitude: lng,
                            name: result.regeocode.formattedAddress,
                          };

                          return multiple ? prev.concat(newItem) : [newItem];
                        });
                      }
                    });
                  });
                }}
              >
                <SearchInput
                  onSelect={async (info) => {
                    if (
                      (await beforeSelect?.(
                        {
                          longitude: info.location.lng,
                          latitude: info.location.lat,
                          adcode: info.adcode,
                        },
                        internalValue,
                      )) === false
                    ) {
                      return;
                    }

                    setInternalValue((prev) => {
                      const newItem = {
                        latitude: info.location.lat,
                        longitude: info.location.lng,
                        name: info.formattedAddress,
                      };

                      return multiple ? prev.concat(newItem) : [newItem];
                    });
                  }}
                />
                {internalValue?.map((marker, index) => (
                  <Marker key={index} position={[marker.longitude, marker.latitude]} />
                ))}
              </ReactMap>
            </APILoader>
          </div>
          <div className="flex flex-col gap-2 overflow-auto sm:w-[300px]">
            {!internalValue.length ? (
              <div className="flex justify-center items-center h-full">
                <Empty description="暂未选择点位" />
              </div>
            ) : (
              internalValue.map((item, index) => (
                <div key={index} className="flex items-center bg-gray-50 rounded-lg py-2 px-3">
                  <span className="flex-1">
                    {multiple && `${index + 1}.`} {item.name}（经度：{item.longitude}；纬度：
                    {item.latitude}）
                  </span>
                  <Button
                    icon={<CloseCircleFilled className="text-gray-400" />}
                    type="text"
                    className="ml-2"
                    onClick={() =>
                      setInternalValue((prev) => prev.filter((_, idx) => idx !== index))
                    }
                  />
                </div>
              ))
            )}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ChooseMapPoints;
