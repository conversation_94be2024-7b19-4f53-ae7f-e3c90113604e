import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { Form, FormItemProps, Modal, ModalProps } from 'antd';
import UserSelect, { UserSelectProps } from '../UserSelect';

interface UserSelectModalProps extends ModalProps {
  userSelectProps?: UserSelectProps;
  formItemProps?: FormItemProps;
  onSave: (values: any) => Promise<void>;
}

const UserSelectModal = React.forwardRef<ModalRef, UserSelectModalProps>(
  ({ userSelectProps, formItemProps, onSave, ...props }, ref) => {
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    const [confirmLoading, setConfirmLoading] = useState(false);

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    return (
      <Modal
        open={open}
        confirmLoading={confirmLoading}
        onCancel={() => setOpen(false)}
        onOk={form.submit}
        {...props}
      >
        <Form
          form={form}
          clearOnDestroy
          onFinish={async (values) => {
            setConfirmLoading(true);

            try {
              await onSave(values);
              setOpen(false);
            } catch (error) {}

            setConfirmLoading(false);
          }}
        >
          <Form.Item
            label="人员"
            rules={[{ required: true, message: '请选择人员' }]}
            {...formItemProps}
          >
            <UserSelect {...userSelectProps} />
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default UserSelectModal;
