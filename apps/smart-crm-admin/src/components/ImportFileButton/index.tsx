import { useState } from 'react';
import { Button, Form, Modal } from 'antd';
import ImportFileFormItem from '../ImportFileFormItem';

interface ImportFileButtonProps {
  children?: React.ReactNode;
  downloadTemplate: () => Promise<{ url: string }>;
  request: (params: {
    file: File;
  }) => Promise<{ successNum: number; failNum: number; failDownloadUrl: string }>;
  onSuccess: () => void;
}

const ImportFileButton: React.FC<ImportFileButtonProps> = ({
  children = '导入',
  downloadTemplate,
  request,
  onSuccess,
}) => {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);

  const { showImportPrompt } = ImportFileFormItem.usePrompt();

  const handleSave = async (values: any) => {
    try {
      setConfirmLoading(true);

      const res = await request(values);

      showImportPrompt(res, onSuccess);

      setOpen(false);
    } catch (error) {}

    setConfirmLoading(false);
  };

  return (
    <>
      <Button type="text" onClick={() => setOpen(true)}>
        {children}
      </Button>
      <Modal
        title={children}
        destroyOnHidden
        open={open}
        confirmLoading={confirmLoading}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
      >
        <Form form={form} clearOnDestroy onFinish={handleSave}>
          <ImportFileFormItem downloadTemplate={downloadTemplate} />
        </Form>
      </Modal>
    </>
  );
};

export default ImportFileButton;
