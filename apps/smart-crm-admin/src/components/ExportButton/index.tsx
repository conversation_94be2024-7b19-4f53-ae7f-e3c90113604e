import React, { useState } from 'react';
import { Button, message, Modal, ModalProps } from 'antd';
import { Link } from 'react-router-dom';

interface ExportButtonProps extends ModalProps {
  total?: number;
  request: () => Promise<void>;
}

const ExportButton: React.FC<ExportButtonProps> = ({
  total,
  request,
  children = '导出',
  ...props
}) => {
  // message 里使用 Link 标签需要这种方式，否则会崩溃
  const [messageApi, contextHolder] = message.useMessage();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleExport = async () => {
    try {
      setLoading(true);
      await request();
      messageApi.success(
        <div>
          请到导出中心进行下载 <Link to="/tool-center/export">前往</Link>
        </div>,
      );
      setOpen(false);
    } catch (error) {}

    setLoading(false);
  };

  return (
    <>
      <Button type="text" onClick={() => setOpen(true)}>
        {children}
      </Button>
      {contextHolder}
      <Modal
        title={children}
        destroyOnHidden
        okButtonProps={{ disabled: !total, loading }}
        onOk={handleExport}
        onCancel={() => setOpen(false)}
        {...props}
        open={open}
      >
        将导出 {total || 0} 条信息
      </Modal>
    </>
  );
};

export default ExportButton;
