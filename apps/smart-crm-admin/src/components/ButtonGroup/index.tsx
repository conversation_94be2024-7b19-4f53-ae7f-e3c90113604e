import React from 'react';
import { EllipsisOutlined } from '@ant-design/icons';
import { isMobile } from '@src/common/constants';
import { Button, Dropdown } from 'antd';

interface ButtonGroupProps {
  items?: {
    label: React.ReactNode | ((isMobile: boolean) => React.ReactNode);
    danger?: boolean;
    loading?: boolean;
    disabled?: boolean;
    show?: boolean;
    onClick?: () => void;
  }[];
}

const ButtonGroup: React.FC<ButtonGroupProps> = ({ items: propsItems }) => {
  const items = propsItems?.reduce<typeof propsItems>(
    (total, { show, ...item }) => (show === undefined || show ? total.concat(item) : total),
    [],
  );

  if (!items?.length) {
    return null;
  }

  if (isMobile) {
    return (
      <Dropdown
        menu={{
          items: items.map((i, index) => ({
            label: typeof i.label === 'function' ? i.label(isMobile) : i.label,
            key: index,
            danger: i.danger,
            disabled: i.disabled || !!i.loading,
            onClick: i.onClick,
          })),
        }}
      >
        <Button icon={<EllipsisOutlined />} />
      </Dropdown>
    );
  }

  return (
    <div className="flex gap-2">
      {items.map((i, index) =>
        typeof i.label === 'function' ? (
          <React.Fragment key={index}>{i.label(isMobile)}</React.Fragment>
        ) : (
          <Button
            key={index}
            danger={i.danger}
            disabled={i.disabled}
            loading={i.loading}
            onClick={i.onClick}
          >
            {i.label}
          </Button>
        ),
      )}
    </div>
  );
};

export default ButtonGroup;
