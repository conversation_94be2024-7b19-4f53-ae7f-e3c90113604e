import { idCardPattern } from '@src/common/constants';
import { SensitiveTypeEnum } from '@src/services/common/type';
import { Form } from 'antd';
import EncryptInput, { EncryptFormItemProps } from './EncryptInput';

const EncryptIdCardFormItem: React.FC<EncryptFormItemProps> = ({ formItemProps, fieldProps }) => {
  return (
    <Form.Item
      validateFirst
      validateTrigger={['onChange', 'onBlur']}
      {...formItemProps}
      rules={[
        {
          validateTrigger: 'onBlur',
          validator: (_, value) => {
            if (
              !value ||
              (typeof value === 'string' &&
                // 18 是平常的长度，44 是加密后的长度
                ((value.length === 18 && idCardPattern.test(value)) || value.length === 44))
            ) {
              return Promise.resolve();
            }

            return Promise.reject(new Error('请输入正确的身份证号码'));
          },
        },
        ...(formItemProps?.rules || []),
      ]}
    >
      <EncryptInput
        maxLength={18}
        sensitiveType={SensitiveTypeEnum.ID_CARD}
        placeholder={`请输入${typeof formItemProps?.label === 'string' ? formItemProps.label : ''}`}
        {...fieldProps}
      />
    </Form.Item>
  );
};

export default EncryptIdCardFormItem;
