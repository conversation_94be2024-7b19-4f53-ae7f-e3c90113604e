import React, { useEffect, useRef, useState } from 'react';
import { EditOutlined, EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons';
import { decryptSensitive } from '@src/services/common';
import { SensitiveTypeEnum } from '@src/services/common/type';
import { useRequest } from 'ahooks';
import { Button, FormItemProps, Input, InputProps, InputRef } from 'antd';

export interface EncryptInputProps extends Omit<InputProps, 'suffix' | 'value' | 'onChange'> {
  // 加密的值
  value?: string;
  // 带 * 号的值
  sensitiveValue?: string;
  // 加密: 脱敏 的映射，如果有多个就使用此熟悉，否则用 sensitiveValue 即可
  encryptSensitiveMap?: Record<string, string | undefined>;
  sensitiveType?: SensitiveTypeEnum;
  onChange?: (value: string) => void;
}

export interface EncryptFormItemProps {
  formItemProps?: FormItemProps;
  fieldProps?: EncryptInputProps;
}

const EncryptInput: React.FC<EncryptInputProps> = ({
  sensitiveValue: propsSensitiveValue,
  encryptSensitiveMap,
  sensitiveType = SensitiveTypeEnum.PHONE,
  onChange,
  ...props
}) => {
  const sensitiveValue =
    propsSensitiveValue || (props.value ? encryptSensitiveMap?.[props.value] : undefined);

  const [showReal, setShowReal] = useState(false);
  const [disabled, setDisabled] = useState(true);
  const inputRef = useRef<InputRef>(null);
  const [editClicked, setEditClicked] = useState(false);
  const [value, setValue] = useState(sensitiveValue);

  const {
    data: realValue,
    loading,
    runAsync: getRealValue,
    mutate: mutateRealValue,
  } = useRequest(decryptSensitive, { manual: true });

  // 同步内部显示值
  useEffect(() => {
    setValue(sensitiveValue);
    setShowReal(false);
    mutateRealValue(undefined);
  }, [mutateRealValue, sensitiveValue]);

  // 初始就有值
  const originHasValue = !!sensitiveValue;

  const handleEditClick = () => {
    setDisabled(false);
    setEditClicked(true);
    setValue('');
    onChange?.('');
    setTimeout(() => {
      inputRef.current?.focus();
    });
  };

  const handleClickEye = async () => {
    if (showReal) {
      setShowReal(false);
      setValue(sensitiveValue);
    } else {
      if (realValue) {
        setValue(realValue);
      } else {
        const val = await getRealValue({ encrypted: props.value!, sensitiveType });

        setValue(val);
      }

      setShowReal(true);
    }
  };

  return (
    <Input
      ref={inputRef}
      {...props}
      value={value}
      readOnly={props.readOnly || (originHasValue && disabled)}
      onChange={(e) => {
        const val = e.target.value;

        setValue(val);
        onChange?.(val);
      }}
      suffix={
        <>
          {!props.disabled && disabled && !!props.value && originHasValue && (
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={handleEditClick}
              className="!mr-0"
            />
          )}
          {sensitiveValue && !editClicked && (
            <Button
              type="text"
              size="small"
              loading={loading}
              icon={showReal ? <EyeInvisibleOutlined /> : <EyeOutlined />}
              onClick={handleClickEye}
            />
          )}
        </>
      }
    />
  );
};

export default EncryptInput;
