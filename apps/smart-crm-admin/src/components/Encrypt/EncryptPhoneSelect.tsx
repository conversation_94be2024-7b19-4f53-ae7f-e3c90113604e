import React, { useState } from 'react';
import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons';
import ExclamationTooltip from '@src/pages/standard-settings/template/components/QuestionConfig/ExclamationTooltip';
import { decryptSensitive } from '@src/services/common';
import { SensitiveTypeEnum } from '@src/services/common/type';
import { useRequest } from 'ahooks';
import { Button, Select, SelectProps } from 'antd';

const RenderOption: React.FC<{ label?: React.ReactNode; value?: string }> = ({ label, value }) => {
  const [showReal, setShowReal] = useState(false);

  const {
    data: realValue,
    runAsync: getRealValue,
    loading,
  } = useRequest(decryptSensitive, { manual: true });

  return (
    <div className="flex justify-between items-center">
      {showReal ? realValue : label}
      <Button
        type="text"
        size="small"
        loading={loading}
        icon={showReal ? <EyeInvisibleOutlined /> : <EyeOutlined />}
        onClick={async (e) => {
          // 点击不关闭弹窗
          e.stopPropagation();

          if (!showReal && !realValue) {
            await getRealValue({ encrypted: value || '', sensitiveType: SensitiveTypeEnum.PHONE });
            setShowReal(!showReal);
          } else {
            setShowReal(!showReal);
          }
        }}
      />
    </div>
  );
};

interface EncryptPhoneSelectProps extends SelectProps {
  // 加密：脱敏
  encryptSensitiveMap?: Record<string, React.ReactNode>;
}

const EncryptPhoneSelect: React.FC<EncryptPhoneSelectProps> = ({
  encryptSensitiveMap,
  ...props
}) => {
  return (
    <Select
      optionRender={({ label, value }) => <RenderOption label={label} value={value as string} />}
      labelRender={({ label, value }) => {
        if (label) {
          return label;
        }

        const mapValue = encryptSensitiveMap?.[value];

        if (mapValue) {
          return (
            <div className="flex justify-between items-center">
              {mapValue} <ExclamationTooltip title="此号码已被删除，建议重新选择" />
            </div>
          );
        }

        return value;
      }}
      {...props}
    />
  );
};

export default EncryptPhoneSelect;
