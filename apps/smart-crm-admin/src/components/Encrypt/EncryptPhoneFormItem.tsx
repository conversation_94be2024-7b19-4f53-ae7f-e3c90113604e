import { phonePattern } from '@src/common/constants';
import { SensitiveTypeEnum } from '@src/services/common/type';
import { Form } from 'antd';
import EncryptInput, { EncryptFormItemProps } from './EncryptInput';

const EncryptPhoneFormItem: React.FC<EncryptFormItemProps> = ({ formItemProps, fieldProps }) => {
  return (
    <Form.Item
      validateFirst
      validateTrigger={['onChange', 'onBlur']}
      {...formItemProps}
      rules={[
        {
          validateTrigger: 'onBlur',
          validator: (_, value) => {
            if (
              !value ||
              (typeof value === 'string' &&
                // 11 是平常的长度，24 是加密后的长度
                ((value.length === 11 && phonePattern.test(value)) || value.length === 24))
            ) {
              return Promise.resolve();
            }

            return Promise.reject(new Error('请输入正确的电话号码'));
          },
        },
        ...(formItemProps?.rules || []),
      ]}
    >
      <EncryptInput
        maxLength={11}
        sensitiveType={SensitiveTypeEnum.PHONE}
        placeholder={`请输入${typeof formItemProps?.label === 'string' ? formItemProps.label : ''}`}
        {...fieldProps}
      />
    </Form.Item>
  );
};

export default EncryptPhoneFormItem;
