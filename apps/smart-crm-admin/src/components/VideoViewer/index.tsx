import { CloseOutlined } from '@ant-design/icons';
import { createPortal } from 'react-dom';

interface VideoViewerProps {
  open?: boolean;
  url?: string;
  onClose?: () => void;
}

const VideoViewer: React.FC<VideoViewerProps> = ({ open, url, onClose }) => {
  if (!open) {
    return null;
  }

  return createPortal(
    <div
      className="fixed flex justify-center items-center top-0 left-0 h-full w-full bg-black/50 z-[9999]"
      onClick={onClose}
    >
      <CloseOutlined
        className="absolute top-6 right-6 rounded-full p-2 bg-gray-200 cursor-pointer hover:opacity-60"
        onClick={onClose}
      />
      <video
        src={url}
        controls
        style={{
          maxWidth: '80%',
          maxHeight: '80%',
        }}
        onClick={(e) => e.stopPropagation()}
      />
    </div>,
    document.body,
  );
};

export default VideoViewer;
