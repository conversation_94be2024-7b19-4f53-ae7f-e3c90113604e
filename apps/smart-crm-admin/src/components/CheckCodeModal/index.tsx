import { useEffect } from 'react';
import { checkCaptcha, getCaptcha } from '@src/services';
import { useRequest } from 'ahooks';
import { Form, Input, Modal, ModalProps } from 'antd';

interface CheckCodeModalProps extends ModalProps {
  messageInfo?: () => React.ReactNode;
  onSuccess: () => void;
}

const CheckCodeModal = ({ open, messageInfo, onSuccess, ...restProps }: CheckCodeModalProps) => {
  const [form] = Form.useForm();

  const {
    data: checkCodeData,
    run: runCheckCode,
    mutate,
  } = useRequest(getCaptcha, {
    ready: open,
  });
  const { runAsync: check, loading: checkLoading } = useRequest(checkCaptcha, { manual: true });

  useEffect(() => {
    if (open) {
      mutate(undefined);
    }
  }, [mutate, open]);

  const handleSave = async (values: any) => {
    await check({
      checkCode: values.checkCode.trim(),
      checkCodeKey: checkCodeData?.checkCodeKey || '',
    });

    onSuccess();
  };

  return (
    <Modal
      destroyOnHidden
      open={open}
      confirmLoading={checkLoading}
      onOk={form.submit}
      {...restProps}
    >
      {open && messageInfo?.()}
      <Form form={form} clearOnDestroy onFinish={handleSave}>
        <div className="flex">
          <Form.Item
            name="checkCode"
            rules={[{ required: true, message: '请输入图片验证码' }]}
            extra="若要继续，请输入图片验证码后点击确定"
          >
            <Input placeholder="请输入图片验证码" size="large" />
          </Form.Item>
          <img
            className="cursor-pointer h-10 ml-4"
            src={`data:image/png;base64,${checkCodeData?.checkCode}`}
            alt="图片验证码"
            onClick={runCheckCode}
          />
        </div>
      </Form>
    </Modal>
  );
};

export default CheckCodeModal;
