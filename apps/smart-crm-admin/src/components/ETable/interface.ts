import { HTMLAttributes } from 'react';
import { FormItemProps, TableProps } from 'antd';
import { ColumnType } from 'antd/lib/table';

export enum ValueType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  PHONE = 'PHONE',
  IDENTITY_CARD = 'IDENTITY_CARD',
  DATE = 'DATE',
  DATE_RANGE = 'DATE_RANGE',
  DATE_TIME = 'DATE_TIME',
  PERSON = 'PERSON',
  SINGLE = 'SINGLE',
  MULTIPLE = 'MULTIPLE',
  ATTACHMENT = 'ATTACHMENT',
  TAG = 'TAG',
  REGION = 'REGION',
  TEXT_ARRAY = 'TEXT_ARRAY',
  POSITION = 'POSITION',
}

export enum SearchRelationEnum {
  AND = 'AND',
  OR = 'OR',
}

export interface ETableColumn<RecordType> extends ColumnType<RecordType> {
  /** 是否可编辑 */
  editable?: boolean | ((value: any, record: RecordType, index: number) => boolean);
  /** 类型，为函数时可以判断在筛选中或在表格中 */
  valueType?: ValueType | ((type: 'filter' | 'table') => ValueType);
  /** 在显示配置中隐藏 */
  hideInSettings?: boolean;
  /** 在筛选中隐藏 */
  hideInFilters?: boolean;
  /** 在表格中的 formItem 属性 */
  formItemProps?:
    | FormItemProps
    | ((value: any, record: RecordType, index: number) => FormItemProps);
  /** 在表格中的控件属性 */
  fieldProps?:
    | Record<string, any>
    | ((value: any, record: RecordType, index: number) => Record<string, any>);
  /** 在筛选中的控件属性 */
  filterProps?: Record<string, any>;
  /** 在筛选中的标题 */
  filterTitle?: string;
  /** 转换筛选的值 */
  transform?: (value: {
    field: string;
    condition: ConditionEnum;
    value: any;
    valueType: ValueType;
  }) => {
    field: string;
    condition: ConditionEnum;
    value: any;
    valueType: ValueType;
    [key: string]: any;
  };
  /** 在筛选中的 options 请求 */
  request?: () => Promise<Record<string, any>[]>;
  /** 转换值到表格中 */
  valueFormatter?: (value: any, record: RecordType, index: number) => any;
  /** 自定义表格渲染 */
  renderCell?: (props: EditableCellProps<RecordType>) => React.ReactNode;
}

export interface ETableActionType<RecordType = any> {
  dataSource: RecordType[];
  getFilters: () => FiltersType;
  reload: () => Promise<void>;
  setDataSource: (value: React.SetStateAction<RecordType[]>) => void;
  setCurrent: (value: React.SetStateAction<number>) => void;
  getShowFields: () => { key: string; title: string }[];
}

export type SaveInfoType<RecordType = any> = {
  /**
   * field = String(col.key ?? col.dataIndex)
   */
  field: string;
  value: any;
  prevValue: any;
  record: RecordType;
  prevRecord: RecordType;
  index: number;
  valueType: ValueType;
};

export interface SettingColumnType {
  field: string;
  fixed?: boolean;
  hidden?: boolean;
}

export interface SettingColumnFullType extends SettingColumnType {
  label: string;
}

export enum ConditionEnum {
  /** 等于 */
  EQ = 'EQ',
  /** 不等于 */
  NE = 'NE',
  /** 包含 */
  LIKE = 'LIKE',
  /** 不包括 */
  NOT_LIKE = 'NOT_LIKE',
  /** 大于等于 */
  GE = 'GE',
  /** 小于等于 */
  LE = 'LE',
  /** 区间 */
  BETWEEN = 'BETWEEN',
  /** 包含 */
  IN = 'IN',
  /** 不包含 */
  NOT_IN = 'NOT_IN',
  /** 为空 */
  IS_NULL = 'IS_NULL',
  /** 不为空 */
  IS_NOT_NULL = 'IS_NOT_NULL',
}

export type FiltersType = {
  searchRelation?: SearchRelationEnum;
  value?: { field: string; condition: ConditionEnum; value: any; valueType: ValueType }[];
};

export type ETableViewItemType = {
  id: string | number;
  name: string;
  filters?: FiltersType;
  settings?: SettingColumnType[];
};

export interface ETableViewsType {
  items?: ETableViewItemType[];
  activeKey?: string | number;
  onChange?: (activeKey: string | number) => void;
  onCreate?: (
    name: string,
    filters: FiltersType,
    settings: SettingColumnType[],
  ) => void | Promise<void>;
  onDelete?: (id: string | number) => void | Promise<void>;
  onRename?: (id: string | number, name: string) => void | Promise<void>;
  onUpdate?: (
    id: string | number,
    filters: FiltersType,
    settings: SettingColumnType[],
  ) => void | Promise<void>;
}

export interface ETableProps<RecordType> extends TableProps<RecordType> {
  request?: (
    params: {
      current?: number;
      pageSize?: number;
      [key: string]: any;
    },
    filters: FiltersType,
  ) => Promise<{ data?: RecordType[]; total?: number }>;
  onSave?: (info: SaveInfoType<RecordType>) => void;
  columns?: ETableColumn<RecordType>[];
  actionRef?: React.Ref<ETableActionType<RecordType>>;
  views?: ETableViewsType;
  header?: React.ReactNode;
  options?:
    | false
    | {
        views?: boolean;
        filters?: boolean;
        settings?: boolean;
        reload?: boolean;
      };
  params?: Record<string, any>;
  skeletonRow?: boolean;
  defaultColumnWidth?: number;
}

export interface EditableCellProps<RecordType = any> extends HTMLAttributes<HTMLTableCellElement> {
  editable: boolean;
  value?: any;
  record: RecordType;
  children: React.ReactNode[];
  formItemProps?: FormItemProps;
  fieldProps?: Record<string, any>;
  valueType: ValueType;
  index: number;
  field: string;
  onSave: (info: Omit<SaveInfoType<RecordType>, 'record' | 'prevRecord'>) => void;
}
