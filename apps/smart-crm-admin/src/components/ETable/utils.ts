import React from 'react';
import { ColumnType } from 'antd/lib/table';
import { isEqual, omit } from 'lodash-es';
import { ETableColumn, SettingColumnType } from './interface';

export function memoEqual<T extends Record<string, any>>(
  objA: T,
  objB: T,
  options: {
    omit?: (keyof T)[];
  } = {},
) {
  const tempObjA = omit(objA, options.omit || []);
  const tempObjB = omit(objB, options.omit || []);

  return isEqual(tempObjA, tempObjB);
}

export function getField(col: ColumnType<any>) {
  return String(col.key ?? col.dataIndex ?? col.title);
}

export function getSettingColumns(columns: ETableColumn<any>[], settings: SettingColumnType[]) {
  return columns.map((col) => {
    const field = getField(col);
    const label = col.title as string;
    const settingCol = settings.find((i) => i.field === field);

    return settingCol
      ? { ...settingCol, label }
      : // 视图里没有，说明是新增的字段。所以在默认视图中看 column 的 hidden，保存过的视图中要 hidden
        {
          field,
          label,
          fixed: col.fixed === 'left' || col.fixed === true,
          hidden: settings.length > 0 ? true : col.hidden,
        };
  }, []);
}

/**
 * antd 自身在省略和阴影存在时会加一层 ant-table-cell-content 来处理省略和滚动的阴影冲突的问题
 * 这里也进行处理一下
 * https://github.com/react-component/table/blob/master/src/Cell/index.tsx#L233
 */
export function getReadCellContent(children: React.ReactNode[], content: React.ReactNode) {
  /**
   * 取第一位，第 0 位是展开符，目前先不支持
   * https://github.com/react-component/table/blob/master/src/Cell/index.tsx#L254
   */
  const [, child] = children;

  if (React.isValidElement(child) && child.props.className?.includes('ant-table-cell-content')) {
    const finalContent = (content as any)?.[1]?.props?.className?.includes('ant-table-cell-content')
      ? (content as any)[1].props.children
      : content;

    return React.cloneElement(child, {}, finalContent);
  } else {
    return content;
  }
}

// 从 value 得到对应的文本
export function getRegionSingleLabel(value?: string, data?: { code: string; name: string }[]) {
  const array = (value || '').split('/').filter((v) => v && v !== 'null' && v !== 'undefined');

  return array.map((code) => data?.find((i) => i.code === code)?.name || code).join(' / ');
}

export function getRegionMultipleLabelArr(value?: string, data?: { code: string; name: string }[]) {
  const array = (value || '')
    .split(',')
    .map((i) => i.split('/').filter((v) => v && v !== 'null' && v !== 'undefined'));

  return array.map((codes) =>
    codes.map((code) => data?.find((i) => i.code === code)?.name || code).join(' / '),
  );
}
