import React, {
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
  useTransition,
} from 'react';
import { ReloadOutlined } from '@ant-design/icons';
import { useEventEmitter } from 'ahooks';
import { Button, Table } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import ColumnsSetting from './components/ColumnsSetting';
import EditableCell from './components/EditableCell';
import Filters from './components/Filters';
import { filterConditionOptionsMap } from './components/Filters/constants';
import ResizableHeader from './components/ResizableHeader';
import Row from './components/Row';
import ViewTabs from './components/ViewTabs';
import ETableContext from './context';
import useColumns from './hooks/useColumns';
import usePagination from './hooks/usePagination';
import styles from './index.module.scss';
import { ETableProps, FiltersType, SettingColumnType, ValueType } from './interface';
import { getField, getSettingColumns } from './utils';

const ETable = <RecordType extends Record<string, any> = any>({
  request,
  columns: propsColumns = [],
  actionRef,
  views,
  header,
  options,
  params,
  skeletonRow,
  defaultColumnWidth,
  onSave,
  ...props
}: ETableProps<RecordType>) => {
  const [loading, setLoading] = useState(false);
  const [internalDataSource, setDataSource] = useState<RecordType[]>([]);
  const [isPending, startTransition] = useTransition();
  // 获取数据的次数
  const fetchDataSourceRef = useRef(0);

  const dataSource = (props.dataSource || internalDataSource) as RecordType[];

  // ------------------------------------ 显示配置 ------------------------------------
  const allSettingColumns = propsColumns.filter((col) => !col.hideInSettings);
  const defaultSettingColumns = getSettingColumns(allSettingColumns, []);

  // ------------------------------------ 视图 Tab ------------------------------------
  // 处理 settings 为空的视图（处理成全部显示）
  const items = (views?.items || []).map((item) => ({
    ...item,
    settings: !item.settings?.length ? defaultSettingColumns : item.settings,
  }));

  const [settings, setSettings] = useState<SettingColumnType[]>(items[0]?.settings || []);

  // TODO: 初始过滤当前不存在的
  const [filters, setFilters] = useState<FiltersType>(items[0]?.filters || {});

  const { pagination, current, pageSize, setCurrent, setTotal } = usePagination(
    dataSource.length,
    setDataSource,
    props.pagination,
  );

  const [rowSelectionPageInfo, setRowSelectionPageInfo] = useState({
    current,
    pageSize,
  });

  const showViews = options !== false && options?.views !== false;
  const showFilters = options !== false && options?.filters !== false;
  const showSettings = options !== false && options?.settings !== false;
  const showReload = options !== false && options?.reload !== false;

  const columns = useColumns(
    propsColumns,
    dataSource,
    settings,
    showSettings,
    defaultColumnWidth,
    onSave,
  );

  const scrollX = columns.reduce((total, cur) => total + Number(cur.width || 0), 0);

  const getTransformFilters = () => {
    // 过滤掉已经不存在的字段的筛选
    const value = filters.value?.reduce<Required<FiltersType>['value']>((total, item) => {
      const column = filterColumns.find((col) => getField(col) === item.field);

      if (column) {
        let valueType = filterColumns.find((col) => getField(col) === item.field)?.valueType;

        if (typeof valueType === 'function') {
          valueType = valueType('filter');
        }

        // 兼容旧数据（例如单选的旧数据用的是 EQ，但是新版本移除了 EQ，使用默认 condition）
        if (valueType) {
          const conditionOptions = filterConditionOptionsMap[valueType];

          if (
            conditionOptions &&
            !conditionOptions.some((option) => option.value === item.condition)
          ) {
            item.condition = conditionOptions[0].value;
          }

          // 兼容旧数据，以前为时间戳，现在后端需要为 YYYY 这种格式
          if ([ValueType.DATE, ValueType.DATE_TIME].includes(valueType)) {
            if (Array.isArray(item.value)) {
              item.value = item.value.map((i) => dayjs(i).format('YYYY-MM-DD HH:mm:ss'));
            } else {
              const dayjsValue = dayjs(item.value);

              if (dayjsValue.isValid()) {
                item.value = dayjsValue.format('YYYY-MM-DD HH:mm:ss');
              }
            }
          }
        }

        // transform
        total.push(column.transform ? column.transform(item) : item);
      }

      return total;
    }, []);

    return {
      ...filters,
      value,
    };
  };

  const getData = async () => {
    if (request) {
      setLoading(true);
      // 请求竞态处理
      fetchDataSourceRef.current += 1;

      const fetchId = fetchDataSourceRef.current;

      try {
        const res = await request(
          { ...(props.pagination === false ? {} : { current, pageSize }), ...params },
          getTransformFilters(),
        );

        if (fetchId === fetchDataSourceRef.current) {
          const data = res.data || [];

          if (!data.length && current > 1) {
            setCurrent((prev) => prev - 1);
          } else {
            startTransition(() => {
              setLoading(false);
              setRowSelectionPageInfo({ current, pageSize });
              setTotal(res.total ?? data.length);
              setDataSource(data);
            });
          }
        }
      } catch (error) {
        setLoading(false);
        setTotal(0);
        setDataSource([]);
      }
    }
  };

  useEffect(() => {
    getData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [current, pageSize, filters, JSON.stringify(params)]);

  const getShowFields = () =>
    getSettingColumns(settingColumns, settings).reduce<{ key: string; title: string }[]>(
      (total, cur) =>
        cur.hidden
          ? total
          : total.concat({
              key: cur.field,
              title: columns.find((col) => getField(col) === cur.field)?.title as string,
            }),
      [],
    );

  useImperativeHandle(actionRef, () => ({
    dataSource,
    setDataSource,
    setCurrent,
    getFilters: getTransformFilters,
    reload: getData,
    getShowFields,
  }));

  const filterColumns = propsColumns.filter((col) => !col.hideInFilters);
  const settingColumns = [...allSettingColumns].sort((a, b) => {
    const aIndex = settings.findIndex((i) => i.field === getField(a));
    const bIndex = settings.findIndex((i) => i.field === getField(b));

    if (aIndex > -1 && bIndex > -1) {
      return aIndex - bIndex;
    }

    return bIndex - aIndex;
  });

  const onActiveKeyChange = (key: string | number) => {
    setCurrent(1);
    views?.onChange?.(key);

    const item = items.find((i) => i.id === key);

    if (item) {
      setFilters(item.filters || {});
      setSettings(item.settings);
    }
  };

  const perfEvent = useEventEmitter();

  const contextValue = useMemo(() => ({ perfEvent }), [perfEvent]);

  return (
    <ETableContext.Provider value={contextValue}>
      {showViews && (
        <ViewTabs
          activeKey={views?.activeKey}
          items={items}
          filters={filters}
          settings={settings}
          views={views}
          onActiveKeyChange={onActiveKeyChange}
        />
      )}
      {(showFilters || showSettings || showReload) && (
        <div className="flex mb-3 gap-2">
          {showFilters && (
            <Filters
              initialValues={filters}
              columns={filterColumns}
              onSave={(val) => {
                setCurrent(1);
                setFilters(val);
              }}
            />
          )}
          {showSettings && (
            <ColumnsSetting
              columns={getSettingColumns(settingColumns, settings)}
              defaultColumns={defaultSettingColumns}
              onSave={setSettings}
            />
          )}
          {showReload && (
            <Button type="text" size="small" icon={<ReloadOutlined />} onClick={getData}>
              刷新
            </Button>
          )}
        </div>
      )}
      {header}

      <Table
        size="small"
        bordered
        loading={loading || isPending}
        {...props}
        dataSource={dataSource}
        className={classNames(styles.ETable, props.className)}
        scroll={{ x: scrollX, ...props.scroll }}
        columns={columns}
        pagination={pagination}
        components={{
          ...props.components,
          header: {
            cell: ResizableHeader,
            ...props.components?.header,
          },
          body: {
            row: skeletonRow ? Row : undefined,
            ...props.components?.body,
            cell: dataSource.length ? EditableCell : undefined,
          },
        }}
        rowSelection={
          props.rowSelection
            ? {
                columnWidth: 80,
                renderCell: (checked, _, index, originNode) => (
                  <>
                    <span className={classNames('group-hover:hidden', { hidden: checked })}>
                      {(rowSelectionPageInfo.current - 1) * rowSelectionPageInfo.pageSize +
                        index +
                        1}
                    </span>
                    {React.cloneElement(originNode as React.ReactElement, {
                      className: classNames('group-hover:inline-flex', {
                        hidden: !checked,
                      }),
                    })}
                  </>
                ),
                ...props.rowSelection,
                onCell: (...rest) => {
                  const propsOnCell = props.rowSelection?.onCell?.(...rest);

                  return {
                    ...propsOnCell,
                    className: classNames('group', propsOnCell?.className),
                  };
                },
              }
            : undefined
        }
      />
    </ETableContext.Provider>
  );
};

export default ETable;
