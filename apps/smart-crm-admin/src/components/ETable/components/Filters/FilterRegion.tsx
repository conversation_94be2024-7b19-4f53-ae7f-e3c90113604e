import { RegionCascader } from '@src/components';
import { RegionCascaderProps } from '@src/components/RegionCascader';
import { Form, Select } from 'antd';
import { filterConditionOptionsMap } from './constants';
import { FilterFieldProps } from './FilterItem';
import useNormalize from '../../hooks/useNormalize';
import { ConditionEnum, ValueType } from '../../interface';

interface FilterRegionProps extends FilterFieldProps {
  filterProps?: RegionCascaderProps;
}

const FilterRegion: React.FC<FilterRegionProps> = ({ name, filterProps }) => {
  const normalize = useNormalize(name, ValueType.REGION);

  return (
    <>
      <Form.Item name={[name, 'condition']} className="mb-0" normalize={normalize}>
        <Select style={{ width: 100 }} options={filterConditionOptionsMap[ValueType.REGION]} />
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) => {
          const condition = getFieldValue(['value', name, 'condition']);

          if ([ConditionEnum.IS_NULL, ConditionEnum.IS_NOT_NULL].includes(condition)) {
            return null;
          }

          return (
            <Form.Item
              name={[name, 'value']}
              className="mb-0"
              getValueProps={(val: string) => ({
                value: val ? val.split(',').map((i) => i.split('/')) : undefined,
              })}
              normalize={(val: number[][]) => val.map((i) => i.join('/')).join(',')}
            >
              <RegionCascader
                multiple
                unlimited={{ options: true, onChange: true }}
                placeholder="请选择目标值"
                {...(filterProps as any)}
                style={{ width: 200, ...filterProps?.style }}
              />
            </Form.Item>
          );
        }}
      </Form.Item>
    </>
  );
};

export default FilterRegion;
