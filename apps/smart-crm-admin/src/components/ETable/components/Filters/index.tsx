import { useEffect, useState } from 'react';
import { CloseCircleFilled, FilterFilled, PlusOutlined } from '@ant-design/icons';
import { App, Button, Form, Modal, Segmented } from 'antd';
import classNames from 'classnames';
import { isEqual } from 'lodash-es';
import FilterItem from './FilterItem';
import { ETableColumn, FiltersType, SearchRelationEnum, ValueType } from '../../interface';
import { getField } from '../../utils';

interface FiltersProps<RecordType = any> {
  initialValues?: FiltersType;
  columns: ETableColumn<RecordType>[];
  onSave: (value: FiltersType) => void;
}

const Filters: React.FC<FiltersProps> = ({ initialValues, columns, onSave }) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [searchRelation, setSearchRelation] = useState(
    initialValues?.searchRelation || SearchRelationEnum.AND,
  );

  useEffect(() => {
    if (open) {
      form.setFieldValue('value', initialValues?.value?.length ? initialValues.value : [{}]);
      setSearchRelation(initialValues?.searchRelation || SearchRelationEnum.AND);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  const value = initialValues?.value;

  const hasFilters = value && value.length > 0;

  return (
    <>
      <Button
        type="text"
        size="small"
        className={classNames({
          'text-primary hover:!text-primary hover:!bg-primary/5 active:!text-primary active:!bg-primary/10':
            hasFilters,
        })}
        icon={<FilterFilled />}
        onClick={() => setOpen(true)}
      >
        {hasFilters && <span>{value.length}</span>}
        筛选
      </Button>

      <Modal
        open={open}
        title="设置筛选条件"
        width={900}
        destroyOnHidden
        onOk={form.submit}
        onCancel={() => setOpen(false)}
      >
        <Form
          form={form}
          className="relative"
          onFinish={(values: Pick<FiltersType, 'value'>) => {
            setOpen(false);
            onSave({
              searchRelation,
              // 过滤掉空值
              value: values.value
                ?.filter((i) => ![null, undefined, '', []].some((item) => isEqual(item, i.value)))
                .map((i) => {
                  const column = columns.find((col) => getField(col) === i.field);
                  const colValueType = column?.valueType;
                  const valueType =
                    typeof colValueType === 'function'
                      ? colValueType('filter')
                      : colValueType || ValueType.TEXT;

                  return {
                    ...i,
                    valueType,
                  };
                }),
            });
          }}
        >
          <Form.List name="value">
            {(fields, { add, remove }) => (
              <>
                <div className="relative flex items-center mt-3">
                  {fields.length > 1 && (
                    <>
                      <Segmented
                        size="small"
                        className="relative z-10"
                        value={searchRelation}
                        options={[
                          { label: '且', value: SearchRelationEnum.AND },
                          { label: '或', value: SearchRelationEnum.OR },
                        ]}
                        onChange={setSearchRelation}
                      />
                      <div className="absolute top-4 bottom-4 left-[30px] w-8 rounded-xl rounded-r-none border-2 border-r-0" />
                    </>
                  )}
                  <div className="flex flex-col gap-3 overflow-auto ml-3">
                    {fields.map((field) => (
                      <div className="flex" key={field.key}>
                        <FilterItem name={field.name} columns={columns} />
                        <Button
                          icon={<CloseCircleFilled className="text-gray-400" />}
                          type="text"
                          className="ml-2"
                          onClick={() => remove(field.name)}
                        />
                      </div>
                    ))}
                  </div>
                </div>
                <div className="absolute -bottom-11">
                  <Button
                    type="text"
                    icon={<PlusOutlined />}
                    className="!text-primary"
                    onClick={() => {
                      if (fields.length > 4) {
                        message.error('最多只能存在 5 个筛选条件');
                      } else {
                        add(undefined);
                      }
                    }}
                  >
                    添加筛选条件
                  </Button>
                  {fields.length > 0 && (
                    <Button
                      type="text"
                      className="ml-5"
                      onClick={() => form.setFieldValue('value', undefined)}
                    >
                      清空全部条件
                    </Button>
                  )}
                </div>
              </>
            )}
          </Form.List>
        </Form>
      </Modal>
    </>
  );
};

export default Filters;
