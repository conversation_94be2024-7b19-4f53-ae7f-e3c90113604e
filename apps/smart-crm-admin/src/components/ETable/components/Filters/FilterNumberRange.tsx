import { useRef, useState } from 'react';
import { useClickAway } from 'ahooks';
import { Form, InputNumber, InputNumberProps, Select, theme } from 'antd';
import classNames from 'classnames';
import { filterConditionOptionsMap } from './constants';
import { FilterFieldProps } from './FilterItem';
import useNormalize from '../../hooks/useNormalize';
import { ConditionEnum, ValueType } from '../../interface';

interface NumberRangeProps extends Omit<InputNumberProps, 'value' | 'onChange'> {
  value?: null | [number, number];
  onChange?: (value?: null | [number, number]) => void;
}

type Value = undefined | null | [number | null, number | null];

const NumberRange: React.FC<NumberRangeProps> = ({ value: propsValue, onChange, ...props }) => {
  const { token } = theme.useToken();
  const [value, setValue] = useState<Value>(propsValue);
  const ref = useRef<HTMLDivElement>(null);
  const [focus, setFocus] = useState(false);

  useClickAway(() => {
    setFocus(false);
  }, ref);

  return (
    <div
      ref={ref}
      className={classNames(`border rounded-md h-8 flex items-center px-2 hover:border-primary`, {
        [`border-[${token.colorBorder}`]: true,
        'border-primary': focus,
      })}
      style={{
        boxShadow: focus ? `0 0 0 2px ${token.controlOutline}` : undefined,
        transition: `all ${token.motionDurationMid} ${token.motionEaseInOut}`,
      }}
      onClick={() => setFocus(true)}
    >
      <InputNumber
        size="small"
        variant="borderless"
        controls={false}
        placeholder="起始数值"
        {...props}
        value={value?.[0]}
        onChange={(val) => {
          const newValue = [val, value?.[1] ?? null] as Value;

          setValue(newValue);

          if (newValue?.every((i) => i !== null)) {
            onChange?.(newValue as [number, number]);
          } else if (newValue?.some((i) => i === null)) {
            onChange?.(null);
          }
        }}
      />
      <span className="mx-2 text-gray-300">~</span>
      <InputNumber
        size="small"
        variant="borderless"
        controls={false}
        placeholder="结束数值"
        {...props}
        value={value?.[1]}
        onChange={(val) => {
          const newValue = [value?.[0] ?? null, val] as Value;

          setValue(newValue);

          if (newValue?.every((i) => i !== null)) {
            onChange?.(newValue as [number, number]);
          } else if (newValue?.some((i) => i === null)) {
            onChange?.(null);
          }
        }}
      />
    </div>
  );
};

const FilterNumberRange: React.FC<FilterFieldProps> = ({ name, filterProps }) => {
  const normalize = useNormalize(name, ValueType.NUMBER);

  return (
    <>
      <Form.Item name={[name, 'condition']} className="mb-0" normalize={normalize}>
        <Select style={{ width: 100 }} options={filterConditionOptionsMap[ValueType.NUMBER]} />
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) => {
          const condition = getFieldValue(['value', name, 'condition']);

          if (condition === ConditionEnum.BETWEEN) {
            return (
              <Form.Item
                name={[name, 'value']}
                className="mb-0"
                rules={[
                  {
                    validator: (_, value) => {
                      if (Array.isArray(value) && value[0] > value[1]) {
                        return Promise.reject(new Error('前面的值不能大于后面的值'));
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <NumberRange {...filterProps} />
              </Form.Item>
            );
          }

          if ([ConditionEnum.EQ, ConditionEnum.LE, ConditionEnum.GE].includes(condition)) {
            return (
              <Form.Item name={[name, 'value']} className="mb-0">
                <InputNumber
                  controls={false}
                  placeholder="请输入目标值"
                  {...filterProps}
                  style={{ width: 200, ...filterProps?.style }}
                />
              </Form.Item>
            );
          }

          return null;
        }}
      </Form.Item>
    </>
  );
};

export default FilterNumberRange;
