import { UserSelect } from '@src/components';
import { Form, Select } from 'antd';
import { filterConditionOptionsMap } from './constants';
import { FilterFieldProps } from './FilterItem';
import useNormalize from '../../hooks/useNormalize';
import { ConditionEnum, ValueType } from '../../interface';

const FilterPerson: React.FC<FilterFieldProps> = ({ name }) => {
  const normalize = useNormalize(name, ValueType.PERSON);

  return (
    <>
      <Form.Item name={[name, 'condition']} className="mb-0" normalize={normalize}>
        <Select style={{ width: 100 }} options={filterConditionOptionsMap[ValueType.PERSON]} />
      </Form.Item>

      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) => {
          const condition = getFieldValue(['value', name, 'condition']);

          if ([ConditionEnum.IS_NULL, ConditionEnum.IS_NOT_NULL].includes(condition)) {
            return null;
          }

          return (
            <Form.Item name={[name, 'value']} className="mb-0">
              <UserSelect mode="multiple" style={{ width: 200 }} maxTagCount="responsive" />
            </Form.Item>
          );
        }}
      </Form.Item>
    </>
  );
};

export default FilterPerson;
