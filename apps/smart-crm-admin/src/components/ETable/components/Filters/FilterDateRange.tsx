import { DatePicker, DatePickerProps, Form, Select } from 'antd';
import { RangePickerProps } from 'antd/lib/date-picker';
import dayjs, { Dayjs } from 'dayjs';
import { filterConditionOptionsMap } from './constants';
import { FilterFieldProps } from './FilterItem';
import useNormalize from '../../hooks/useNormalize';
import { ConditionEnum, ValueType } from '../../interface';

interface FilterDateRangeProps extends FilterFieldProps {
  filterProps?: DatePickerProps | RangePickerProps;
}

const FilterDateRange: React.FC<FilterDateRangeProps> = ({ name, filterProps }) => {
  const normalize = useNormalize(name, ValueType.DATE);

  return (
    <>
      <Form.Item name={[name, 'condition']} className="mb-0" normalize={normalize}>
        <Select style={{ width: 100 }} options={filterConditionOptionsMap[ValueType.DATE]} />
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) => {
          const condition = getFieldValue(['value', name, 'condition']);

          if (condition === ConditionEnum.BETWEEN) {
            return (
              <Form.Item
                name={[name, 'value']}
                className="mb-0"
                getValueProps={(value) => ({
                  value: Array.isArray(value) ? value.map((i) => dayjs(i)) : undefined,
                })}
                normalize={(value) => {
                  if (value) {
                    return [
                      (value[0] as Dayjs).startOf('d').format('YYYY-MM-DD HH:mm:ss'),
                      (value[1] as Dayjs).endOf('d').format('YYYY-MM-DD HH:mm:ss'),
                    ];
                  }

                  return value;
                }}
              >
                <DatePicker.RangePicker {...(filterProps as RangePickerProps)} />
              </Form.Item>
            );
          }

          if ([ConditionEnum.LE, ConditionEnum.GE].includes(condition)) {
            return (
              <Form.Item
                name={[name, 'value']}
                className="mb-0"
                getValueProps={(value) => ({
                  value: value ? dayjs(value) : undefined,
                })}
                normalize={(value) => {
                  if (value) {
                    return condition === ConditionEnum.LE
                      ? value.endOf('d').format('YYYY-MM-DD HH:mm:ss')
                      : value.startOf('d').format('YYYY-MM-DD HH:mm:ss');
                  }

                  return value;
                }}
              >
                <DatePicker {...(filterProps as DatePickerProps)} />
              </Form.Item>
            );
          }

          return null;
        }}
      </Form.Item>
    </>
  );
};

export default FilterDateRange;
