import { Form, Input, InputProps, Select } from 'antd';
import { filterConditionOptionsMap } from './constants';
import { FilterFieldProps } from './FilterItem';
import useNormalize from '../../hooks/useNormalize';
import { ConditionEnum, ValueType } from '../../interface';

interface FilterInputProps extends FilterFieldProps {
  filterProps?: InputProps;
}

const FilterInput: React.FC<FilterInputProps> = ({ name, filterProps }) => {
  const normalize = useNormalize(name, ValueType.TEXT);

  return (
    <>
      <Form.Item name={[name, 'condition']} className="mb-0" normalize={normalize}>
        <Select style={{ width: 100 }} options={filterConditionOptionsMap[ValueType.TEXT]} />
      </Form.Item>

      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) => {
          const condition = getFieldValue(['value', name, 'condition']);

          if ([ConditionEnum.IS_NULL, ConditionEnum.IS_NOT_NULL].includes(condition)) {
            return null;
          }

          return (
            <Form.Item name={[name, 'value']} className="mb-0">
              <Input
                allowClear
                placeholder="请填写目标值"
                {...filterProps}
                style={{ width: 200, ...filterProps?.style }}
              />
            </Form.Item>
          );
        }}
      </Form.Item>
    </>
  );
};

export default FilterInput;
