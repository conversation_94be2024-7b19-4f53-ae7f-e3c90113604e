import { useEffect, useState } from 'react';
import { getTagList } from '@src/services/standard-settings/info';
import { TagGroupItem, TagStatusEnum } from '@src/services/standard-settings/info/type';
import { useRequest } from 'ahooks';
import { Form, Popover, Select, Tag } from 'antd';
import { filterConditionOptionsMap } from './constants';
import { FilterFieldProps } from './FilterItem';
import useNormalize from '../../hooks/useNormalize';
import { ConditionEnum, ValueType } from '../../interface';

interface TagGroupProps {
  value?: number[];
  options?: TagGroupItem[];
  loading?: boolean;
  onChange?: (value?: number[]) => void;
}

const fieldNames = { label: 'name', value: 'id' };

const TagGroup: React.FC<TagGroupProps> = ({ value, options, loading, onChange }) => {
  const [groupId, setGroupId] = useState<number>();

  // 初始化时没有 groupId，要设置上
  useEffect(() => {
    if (!groupId) {
      setGroupId(options?.find((option) => option.tags.some((i) => value?.includes(i.id)))?.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  const tagOptions = options?.find((i) => i.id === groupId)?.tags;

  return (
    <div className="flex gap-2">
      <Select
        loading={loading}
        value={groupId}
        style={{ width: 200 }}
        placeholder="请选择标签组"
        showSearch
        optionFilterProp="name"
        options={options}
        fieldNames={fieldNames}
        onChange={(val) => {
          setGroupId(val);
          onChange?.([]);
        }}
      />
      <Select
        allowClear
        loading={loading}
        mode="multiple"
        value={value}
        style={{ width: 200 }}
        placeholder="请选择标签"
        fieldNames={fieldNames}
        optionFilterProp="name"
        maxTagCount="responsive"
        options={tagOptions}
        onChange={(val) => onChange?.(val)}
        maxTagPlaceholder={(omittedOptions) =>
          omittedOptions.length > 0 && (
            <Popover
              classNames={{
                body: 'max-h-[400px] overflow-auto',
              }}
              content={
                <div className="flex gap-2 flex-wrap max-w-[220px]">
                  {omittedOptions.map((option) => (
                    <Tag
                      key={option.value}
                      className="mr-0"
                      closable
                      onClose={() => {
                        onChange?.(value?.filter((i) => i !== option.value));
                      }}
                    >
                      {option.label}
                    </Tag>
                  ))}
                </div>
              }
            >
              +{omittedOptions.length}
            </Popover>
          )
        }
      />
    </div>
  );
};

const FilterTag: React.FC<FilterFieldProps> = ({ name }) => {
  const normalize = useNormalize(name, ValueType.TAG);

  const { data, loading } = useRequest(() => getTagList({ status: TagStatusEnum.AVAILABLE }), {
    cacheKey: 'customer-tags',
  });

  return (
    <>
      <Form.Item name={[name, 'condition']} className="mb-0" normalize={normalize}>
        <Select style={{ width: 100 }} options={filterConditionOptionsMap[ValueType.TAG]} />
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) => {
          const condition = getFieldValue(['value', name, 'condition']);

          if ([ConditionEnum.IS_NULL, ConditionEnum.IS_NOT_NULL].includes(condition)) {
            return (
              <Form.Item name={[name, 'value']} className="mb-0">
                <Select
                  options={data?.result}
                  loading={loading}
                  showSearch
                  style={{ width: 200 }}
                  placeholder="请选择标签组"
                  optionFilterProp="name"
                  fieldNames={fieldNames}
                />
              </Form.Item>
            );
          }

          return (
            <Form.Item name={[name, 'value']} className="mb-0">
              <TagGroup options={data?.result} loading={loading} />
            </Form.Item>
          );
        }}
      </Form.Item>
    </>
  );
};

export default FilterTag;
