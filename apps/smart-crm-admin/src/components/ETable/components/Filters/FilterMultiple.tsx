import { useEffect, useState } from 'react';
import { Form, Popover, Select, SelectProps, Tag } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import { filterConditionOptionsMap } from './constants';
import { FilterFieldProps } from './FilterItem';
import useNormalize from '../../hooks/useNormalize';
import { ConditionEnum, ValueType } from '../../interface';

interface FilterMultipleProps extends FilterFieldProps {
  filterProps?: SelectProps;
}

const FilterMultiple: React.FC<FilterMultipleProps> = ({ name, request, filterProps }) => {
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<Record<string, any>[]>([]);

  const normalize = useNormalize(name, ValueType.MULTIPLE);

  useEffect(() => {
    if (request) {
      setLoading(true);
      request()
        .then(setOptions)
        .finally(() => {
          setLoading(false);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Form.Item name={[name, 'condition']} className="mb-0" normalize={normalize}>
        <Select style={{ width: 100 }} options={filterConditionOptionsMap[ValueType.MULTIPLE]} />
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue, setFieldValue }) => {
          const condition = getFieldValue(['value', name, 'condition']);

          if ([ConditionEnum.IS_NULL, ConditionEnum.IS_NOT_NULL].includes(condition)) {
            return null;
          }

          return (
            <Form.Item name={[name, 'value']} className="mb-0">
              <Select
                allowClear
                mode="multiple"
                placeholder="请填写目标值"
                options={options as DefaultOptionType[]}
                loading={loading}
                maxTagCount="responsive"
                maxTagPlaceholder={(omittedOptions) =>
                  omittedOptions.length > 0 && (
                    <Popover
                      classNames={{
                        body: 'max-h-[400px] overflow-auto',
                      }}
                      content={
                        <div className="flex gap-2 flex-wrap max-w-[220px]">
                          {omittedOptions.map((option) => (
                            <Tag
                              key={option.value}
                              className="mr-0"
                              closable
                              onClose={() => {
                                setFieldValue(
                                  ['value', name, 'value'],
                                  getFieldValue(['value', name, 'value']).filter(
                                    (i: string) => i !== option.value,
                                  ),
                                );
                              }}
                            >
                              {option.label}
                            </Tag>
                          ))}
                        </div>
                      }
                    >
                      +{omittedOptions.length}
                    </Popover>
                  )
                }
                {...filterProps}
                style={{ width: 200, ...filterProps?.style }}
              />
            </Form.Item>
          );
        }}
      </Form.Item>
    </>
  );
};

export default FilterMultiple;
