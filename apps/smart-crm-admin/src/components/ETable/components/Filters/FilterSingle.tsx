import { useEffect, useState } from 'react';
import { Form, Popover, Select, SelectProps, Tag } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import { filterConditionOptionsMap } from './constants';
import { FilterFieldProps } from './FilterItem';
import useNormalize from '../../hooks/useNormalize';
import { ConditionEnum, ValueType } from '../../interface';

interface FilterSingleProps extends FilterFieldProps {
  filterProps?: SelectProps;
}

const FilterSingle: React.FC<FilterSingleProps> = ({ name, request, filterProps }) => {
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<Record<string, any>[]>([]);

  const normalize = useNormalize(name, ValueType.SINGLE);

  useEffect(() => {
    if (request) {
      setLoading(true);
      request()
        .then(setOptions)
        .finally(() => {
          setLoading(false);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Form.Item name={[name, 'condition']} className="mb-0" normalize={normalize}>
        <Select style={{ width: 100 }} options={filterConditionOptionsMap[ValueType.SINGLE]} />
      </Form.Item>

      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue, setFieldValue }) => {
          const condition = getFieldValue(['value', name, 'condition']);

          if ([ConditionEnum.IN, ConditionEnum.NOT_IN].includes(condition)) {
            return (
              <Form.Item name={[name, 'value']} className="mb-0">
                <Select
                  allowClear
                  placeholder="请选择目标值"
                  mode="multiple"
                  loading={loading}
                  options={options as DefaultOptionType[]}
                  optionFilterProp="label"
                  maxTagCount="responsive"
                  maxTagPlaceholder={(omittedOptions) =>
                    omittedOptions.length > 0 && (
                      <Popover
                        classNames={{
                          body: 'max-h-[400px] overflow-auto',
                        }}
                        content={
                          <div className="flex gap-2 flex-wrap max-w-[220px]">
                            {omittedOptions.map((option) => (
                              <Tag
                                key={option.value}
                                className="mr-0"
                                closable
                                onClose={() => {
                                  setFieldValue(
                                    ['value', name, 'value'],
                                    getFieldValue(['value', name, 'value']).filter(
                                      (i: string) => i !== option.value,
                                    ),
                                  );
                                }}
                              >
                                {option.label}
                              </Tag>
                            ))}
                          </div>
                        }
                      >
                        +{omittedOptions.length}
                      </Popover>
                    )
                  }
                  {...filterProps}
                  style={{ width: 200, ...filterProps?.style }}
                />
              </Form.Item>
            );
          }

          return null;
        }}
      </Form.Item>
    </>
  );
};

export default FilterSingle;
