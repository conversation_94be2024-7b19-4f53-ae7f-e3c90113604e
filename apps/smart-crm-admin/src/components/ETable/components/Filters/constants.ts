import { ConditionEnum, ValueType } from '../../interface';

const nullOrNotType = Symbol();
const nullOrNotTypeOptions = [
  {
    label: '为空',
    value: ConditionEnum.IS_NULL,
    searchValueType: nullOrNotType,
    initialValue: ConditionEnum.IS_NULL,
  },
  {
    label: '不为空',
    value: ConditionEnum.IS_NOT_NULL,
    searchValueType: nullOrNotType,
    initialValue: ConditionEnum.IS_NOT_NULL,
  },
];

/**
 *  searchValueType 用来区分值类型，为了切换 condition 时，判断需不需要需要清空筛选值
 */
export const filterConditionOptionsMap: Partial<
  Record<
    ValueType,
    { label: string; value: ConditionEnum; searchValueType?: number | symbol; initialValue?: any }[]
  >
> = {
  [ValueType.TEXT]: [
    { label: '等于', value: ConditionEnum.EQ },
    { label: '包含', value: ConditionEnum.LIKE },
    { label: '不包含', value: ConditionEnum.NOT_LIKE },
    ...nullOrNotTypeOptions,
  ],
  [ValueType.TEXT_ARRAY]: [
    { label: '包含', value: ConditionEnum.LIKE },
    { label: '不包含', value: ConditionEnum.NOT_LIKE },
    ...nullOrNotTypeOptions,
  ],
  [ValueType.SINGLE]: [
    { label: '包含', value: ConditionEnum.IN },
    { label: '不包含', value: ConditionEnum.NOT_IN },
    ...nullOrNotTypeOptions,
  ],
  [ValueType.NUMBER]: [
    { label: '在区间', value: ConditionEnum.BETWEEN, searchValueType: 1 },
    { label: '等于', value: ConditionEnum.EQ, searchValueType: 2 },
    { label: '小于等于', value: ConditionEnum.LE, searchValueType: 2 },
    { label: '大于等于', value: ConditionEnum.GE, searchValueType: 2 },
    ...nullOrNotTypeOptions,
  ],
  [ValueType.DATE]: [
    { label: '在区间', value: ConditionEnum.BETWEEN, searchValueType: 1 },
    { label: '早于', value: ConditionEnum.LE, searchValueType: 2 },
    { label: '晚于', value: ConditionEnum.GE, searchValueType: 2 },
    ...nullOrNotTypeOptions,
  ],
  [ValueType.DATE_TIME]: [
    { label: '在区间', value: ConditionEnum.BETWEEN, searchValueType: 1 },
    { label: '早于', value: ConditionEnum.LE, searchValueType: 2 },
    { label: '晚于', value: ConditionEnum.GE, searchValueType: 2 },
    ...nullOrNotTypeOptions,
  ],
  [ValueType.MULTIPLE]: [
    { label: '包含', value: ConditionEnum.IN },
    { label: '不包含', value: ConditionEnum.NOT_IN },
    ...nullOrNotTypeOptions,
  ],
  [ValueType.IDENTITY_CARD]: [{ label: '等于', value: ConditionEnum.EQ }, ...nullOrNotTypeOptions],
  [ValueType.PHONE]: [{ label: '等于', value: ConditionEnum.EQ }, ...nullOrNotTypeOptions],
  [ValueType.PERSON]: [
    { label: '包含', value: ConditionEnum.IN },
    { label: '不包含', value: ConditionEnum.NOT_IN },
    ...nullOrNotTypeOptions,
  ],
  [ValueType.ATTACHMENT]: nullOrNotTypeOptions,
  [ValueType.TAG]: [
    { label: '等于', value: ConditionEnum.EQ, searchValueType: 1 },
    { label: '不等于', value: ConditionEnum.NE, searchValueType: 1 },
    { label: '包含', value: ConditionEnum.IN, searchValueType: 1 },
    { label: '不包含', value: ConditionEnum.NOT_IN, searchValueType: 1 },
    ...nullOrNotTypeOptions,
  ],
  [ValueType.REGION]: [
    { label: '包含', value: ConditionEnum.IN },
    { label: '不包含', value: ConditionEnum.NOT_IN },
    ...nullOrNotTypeOptions,
  ],
};
