import { phonePattern } from '@src/common/constants';
import { Form, Input, InputProps, Select } from 'antd';
import { filterConditionOptionsMap } from './constants';
import { FilterFieldProps } from './FilterItem';
import useNormalize from '../../hooks/useNormalize';
import { ConditionEnum, ValueType } from '../../interface';

interface FilterPhoneProps extends FilterFieldProps {
  filterProps?: InputProps;
}

const FilterPhone: React.FC<FilterPhoneProps> = ({ name, filterProps }) => {
  const normalize = useNormalize(name, ValueType.PHONE);

  return (
    <>
      <Form.Item name={[name, 'condition']} className="mb-0" normalize={normalize}>
        <Select style={{ width: 100 }} options={filterConditionOptionsMap[ValueType.PHONE]} />
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) => {
          const condition = getFieldValue(['value', name, 'condition']);

          if (condition === ConditionEnum.EQ) {
            return (
              <Form.Item
                name={[name, 'value']}
                className="mb-0"
                rules={[
                  {
                    pattern: phonePattern,
                    message: '格式不正确',
                  },
                ]}
              >
                <Input
                  allowClear
                  placeholder="请填写手机号"
                  {...filterProps}
                  style={{ width: 200, ...filterProps?.style }}
                />
              </Form.Item>
            );
          }

          return null;
        }}
      </Form.Item>
    </>
  );
};

export default FilterPhone;
