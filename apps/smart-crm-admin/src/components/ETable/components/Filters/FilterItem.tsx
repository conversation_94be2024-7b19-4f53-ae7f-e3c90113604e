import { Form, Select } from 'antd';
import { filterConditionOptionsMap } from './constants';
import FilterAttachment from './FilterAttachment';
import FilterDateRange from './FilterDateRange';
import FilterIdentityCard from './FilterIdentityCard';
import FilterInput from './FilterInput';
import FilterMultiple from './FilterMultiple';
import FilterNumberRange from './FilterNumberRange';
import Filter<PERSON>erson from './FilterPerson';
import FilterPhone from './FilterPhone';
import FilterRegion from './FilterRegion';
import FilterSingle from './FilterSingle';
import FilterTag from './FilterTag';
import FilterTextArray from './FilterTextArray';
import { ETableColumn, ValueType } from '../../interface';
import { getField } from '../../utils';

export interface FilterFieldProps {
  name: string;
  filterProps?: Record<string, any>;
  request?: () => Promise<Record<string, any>[]>;
}

interface FilterItemProps<RecordType = any> {
  name: number;
  columns: ETableColumn<RecordType>[];
  fieldOptions?: {
    label: string;
    value: string;
    valueType?: ValueType;
  }[];
}

const componentsMap: Partial<Record<ValueType, React.FunctionComponent<any>>> = {
  [ValueType.TEXT]: FilterInput,
  [ValueType.SINGLE]: FilterSingle,
  [ValueType.NUMBER]: FilterNumberRange,
  [ValueType.DATE]: FilterDateRange,
  [ValueType.DATE_TIME]: FilterDateRange,
  [ValueType.MULTIPLE]: FilterMultiple,
  [ValueType.IDENTITY_CARD]: FilterIdentityCard,
  [ValueType.PHONE]: FilterPhone,
  [ValueType.PERSON]: FilterPerson,
  [ValueType.ATTACHMENT]: FilterAttachment,
  [ValueType.TAG]: FilterTag,
  [ValueType.REGION]: FilterRegion,
  [ValueType.TEXT_ARRAY]: FilterTextArray,
};

const getValueType = (valueType?: ValueType | ((type: 'filter' | 'table') => ValueType)) => {
  return typeof valueType === 'function' ? valueType('filter') : valueType || ValueType.TEXT;
};

const FilterItem: React.FC<FilterItemProps> = ({ name, columns }) => {
  const form = Form.useFormInstance();

  const options = columns.map((col) => ({
    label: col.filterTitle || col.title,
    value: getField(col),
  }));

  return (
    <div className="flex gap-2">
      <Form.Item name={[name, 'field']} className="mb-0">
        <Select
          allowClear={false}
          style={{ width: 200 }}
          placeholder="请选择筛选项"
          showSearch
          optionFilterProp="label"
          options={options}
          onChange={(value) => {
            const valueType = getValueType(
              columns?.find((col) => getField(col) === value)?.valueType,
            );

            form.setFieldValue(
              ['value', name, 'condition'],
              filterConditionOptionsMap[valueType]?.[0].value,
            );
            form.setFieldValue(['value', name, 'value'], undefined);
          }}
        />
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) => {
          const value = getFieldValue(['value', name, 'field']);
          const item = columns?.find((col) => getField(col) === value);

          if (!item) {
            return null;
          }

          const valueType = getValueType(item.valueType);
          const Component = componentsMap[valueType];

          if (!Component) {
            return null;
          }

          return <Component name={name} filterProps={item.filterProps} request={item.request} />;
        }}
      </Form.Item>
    </div>
  );
};

export default FilterItem;
