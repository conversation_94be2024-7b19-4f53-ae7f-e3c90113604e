import { Form, Input, Select } from 'antd';
import { filterConditionOptionsMap } from './constants';
import { FilterFieldProps } from './FilterItem';
import { ValueType } from '../../interface';

const FilterAttachment: React.FC<FilterFieldProps> = ({ name }) => {
  return (
    <>
      <Form.Item name={[name, 'condition']} className="mb-0">
        <Select style={{ width: 100 }} options={filterConditionOptionsMap[ValueType.ATTACHMENT]} />
      </Form.Item>
      {/* 后端需要 value 为 true */}
      <Form.Item name={[name, 'value']} className="mb-0" hidden initialValue={true}>
        <Input />
      </Form.Item>
    </>
  );
};

export default FilterAttachment;
