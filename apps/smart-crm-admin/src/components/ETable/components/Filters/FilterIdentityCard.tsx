import { idCardPattern } from '@src/common/constants';
import { Form, Input, InputProps, Select } from 'antd';
import { filterConditionOptionsMap } from './constants';
import { FilterFieldProps } from './FilterItem';
import useNormalize from '../../hooks/useNormalize';
import { ConditionEnum, ValueType } from '../../interface';

interface FilterIdentityCardProps extends FilterFieldProps {
  filterProps?: InputProps;
}

const FilterIdentityCard: React.FC<FilterIdentityCardProps> = ({ name, filterProps }) => {
  const normalize = useNormalize(name, ValueType.IDENTITY_CARD);

  return (
    <>
      <Form.Item name={[name, 'condition']} className="mb-0" normalize={normalize}>
        <Select
          style={{ width: 100 }}
          options={filterConditionOptionsMap[ValueType.IDENTITY_CARD]}
        />
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) => {
          const condition = getFieldValue(['value', name, 'condition']);

          if (condition === ConditionEnum.EQ) {
            return (
              <Form.Item
                name={[name, 'value']}
                className="mb-0"
                rules={[
                  {
                    pattern: idCardPattern,
                    message: '格式不正确',
                  },
                ]}
              >
                <Input
                  allowClear
                  placeholder="请填写身份证号"
                  {...filterProps}
                  style={{ width: 200, ...filterProps?.style }}
                />
              </Form.Item>
            );
          }

          return null;
        }}
      </Form.Item>
    </>
  );
};

export default FilterIdentityCard;
