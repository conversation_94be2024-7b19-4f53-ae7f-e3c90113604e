import {
  HolderOutlined,
  VerticalAlignMiddleOutlined,
  VerticalAlignTopOutlined,
} from '@ant-design/icons';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button, Checkbox, Tooltip } from 'antd';
import classNames from 'classnames';
import { SettingColumnFullType } from '../../interface';

interface SortableItemProps {
  item: SettingColumnFullType;
  onChange: (hidden: boolean) => void;
  onFixed: (fixed: boolean) => void;
}

const SortableItem: React.FC<SortableItemProps> = ({ item, onChange, onFixed }) => {
  const {
    active,
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: item.field,
    data: {
      fixed: !!item.fixed,
    },
  });

  const style = {
    transform:
      active?.data.current?.fixed === !!item.fixed ? CSS.Translate.toString(transform) : undefined,
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      className={classNames('p-2 pr-3 border rounded-md bg-white flex items-center', {
        'relative z-10': isDragging,
      })}
      style={style}
    >
      <Button
        ref={setActivatorNodeRef}
        size="small"
        type="text"
        className={classNames('mr-2 cursor-grab', { 'cursor-grabbing': isDragging })}
        icon={<HolderOutlined />}
        {...attributes}
        {...listeners}
      />
      <Checkbox
        className="overflow-hidden [&>*:nth-child(2)]:overflow-hidden"
        checked={!item.hidden}
        onChange={(e) => onChange(!e.target.checked)}
      >
        {item.label}
      </Checkbox>

      <div className="flex-1 flex justify-end">
        {!item.fixed && (
          <Tooltip title="固定在列首">
            <Button
              type="text"
              size="small"
              icon={<VerticalAlignTopOutlined />}
              onClick={() => onFixed(true)}
            />
          </Tooltip>
        )}
        {item.fixed && (
          <Tooltip title="取消固定">
            <Button
              type="text"
              size="small"
              icon={<VerticalAlignMiddleOutlined />}
              onClick={() => onFixed(false)}
            />
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default SortableItem;
