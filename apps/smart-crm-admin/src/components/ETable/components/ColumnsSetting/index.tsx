import React, { useContext, useEffect, useRef, useState } from 'react';
import { EnterOutlined, SettingFilled } from '@ant-design/icons';
import { DndContext, DragEndEvent } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { Button, Checkbox, Drawer, Select } from 'antd';
import { Virtuoso, VirtuosoHandle } from 'react-virtuoso';
import SortableItem from './SortableItem';
import ETableContext from '../../context';
import { SettingColumnFullType, SettingColumnType } from '../../interface';

interface ColumnsSettingProps {
  columns: SettingColumnFullType[];
  defaultColumns: SettingColumnFullType[];
  onSave: (value: SettingColumnType[]) => void;
}

const ColumnsSetting: React.FC<ColumnsSettingProps> = ({ columns, defaultColumns, onSave }) => {
  const { perfEvent } = useContext(ETableContext);
  const [open, setOpen] = useState(false);
  const [items, setItems] = useState<SettingColumnFullType[]>([]);
  const bodyRef = useRef<HTMLDivElement>(null);
  const [customScrollParent, setCustomScrollParen] = useState<HTMLElement | null>();
  const virtuosoRef = useRef<VirtuosoHandle>(null);

  useEffect(() => {
    if (open) {
      setItems(columns);
      setCustomScrollParen(bodyRef.current?.parentElement);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  const handleSave = () => {
    setOpen(false);
    // 隐藏不可见的行
    perfEvent.emit();
    onSave(items);
  };

  function handleDragEnd({ active, over }: DragEndEvent) {
    if (over && active.id !== over?.id) {
      if (!!active.data.current?.fixed !== !!over.data.current?.fixed) {
        return;
      }

      setItems((prev) => {
        const activeIndex = prev.findIndex((i) => i.field === active?.id);
        const overIndex = prev.findIndex((i) => i.field === over?.id);

        return arrayMove(prev, activeIndex, overIndex);
      });
    }
  }

  const showItems = items.filter((i) => !i.hidden);

  return (
    <>
      <Button type="text" size="small" icon={<SettingFilled />} onClick={() => setOpen(true)}>
        显示配置
      </Button>
      <Drawer
        title="显示配置"
        open={open}
        destroyOnHidden
        classNames={{ body: 'flex flex-col !p-0' }}
        footer={
          <div className="flex justify-between">
            <Button type="primary" ghost onClick={() => setItems(defaultColumns)}>
              恢复默认
            </Button>
            <div className="flex gap-2">
              <Button onClick={() => setOpen(false)}>取消</Button>
              <Button type="primary" onClick={handleSave}>
                确定
              </Button>
            </div>
          </div>
        }
        onClose={() => setOpen(false)}
      >
        <div className="p-6 pb-4 border-b border-gray-100">
          <Select
            value={[]}
            showSearch
            placeholder="输入名称搜索进行快速定位"
            className="w-full mb-4"
            optionRender={(option) => (
              <div className="flex justify-between">
                <div className="truncate mr-1">{option.label}</div>
                <div>
                  <EnterOutlined className="text-primary mr-2" />
                  <span className="text-xs">到这去</span>
                </div>
              </div>
            )}
            optionFilterProp="label"
            fieldNames={{ value: 'field' }}
            options={items}
            onSelect={(value) => {
              const index = items.findIndex((i) => i.field === value);

              virtuosoRef.current?.scrollToIndex(index);
            }}
          />
          <Checkbox
            className="font-medium"
            checked={showItems.length === items.length}
            indeterminate={showItems.length > 0 && showItems.length < items.length}
            onChange={(e) =>
              setItems((prev) => prev.map((i) => ({ ...i, hidden: !e.target.checked })))
            }
          >
            列展示
          </Checkbox>
        </div>
        <div className="flex-1 overflow-auto p-6 pt-4">
          <div ref={bodyRef}>
            <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={handleDragEnd}>
              <SortableContext
                items={items.map((i) => i.field)}
                strategy={verticalListSortingStrategy}
              >
                <Virtuoso
                  ref={virtuosoRef}
                  useWindowScroll
                  increaseViewportBy={500}
                  data={items}
                  customScrollParent={customScrollParent!}
                  components={{
                    List: React.forwardRef(({ style, children }, ref) => (
                      <div ref={ref} style={style} className="flex flex-col gap-2">
                        {children}
                      </div>
                    )),
                  }}
                  itemContent={(index, item) => {
                    const isFirstFixed = index === 0 && item.fixed;
                    const isFirstNoFixed = index === items.findIndex((i) => !i.fixed);

                    return (
                      <>
                        {isFirstFixed && <p className="mb-2 text-gray-500">固定在左侧</p>}
                        {isFirstNoFixed && <p className="mb-2 text-gray-500">不固定</p>}
                        <SortableItem
                          key={item.field}
                          item={item}
                          onChange={(hidden) => {
                            setItems((prev) => {
                              const result = [...prev];

                              result[index] = { ...result[index], hidden };

                              return result;
                            });
                          }}
                          onFixed={(fixed) => {
                            setItems((prev) => {
                              // 第一个不固定的
                              const idx = prev.findIndex((i) => !i.fixed);

                              if (fixed) {
                                const result = arrayMove(prev, index, idx);

                                result[idx] = { ...result[idx], fixed: true };

                                return result;
                              } else {
                                const result = arrayMove(prev, index, idx - 1);

                                result[idx - 1] = { ...result[idx - 1], fixed: false };

                                return result;
                              }
                            });
                          }}
                        />
                      </>
                    );
                  }}
                />
              </SortableContext>
            </DndContext>
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default ColumnsSetting;
