import { useEffect, useRef, useState } from 'react';
import { Form, Input, InputRef, Modal, ModalProps } from 'antd';

interface CreateOrEditViewModalProps extends ModalProps {
  initialValue?: string;
  onSave: (name: string) => Promise<void>;
}

const CreateOrEditViewModal: React.FC<CreateOrEditViewModalProps> = ({
  initialValue,
  open,
  onSave,
  ...props
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const inputRef = useRef<InputRef | null>(null);

  useEffect(() => {
    if (open) {
      form.setFieldValue('name', initialValue);
      setTimeout(() => {
        inputRef.current?.focus();
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  return (
    <Modal
      open={open}
      confirmLoading={loading}
      destroyOnHidden
      title={initialValue ? '重命名视图' : '新建视图'}
      onOk={form.submit}
      {...props}
    >
      <Form
        form={form}
        onFinish={async (values) => {
          setLoading(true);

          try {
            await onSave(values.name);
          } catch (error) {}

          setLoading(false);
        }}
      >
        <Form.Item
          initialValue={initialValue}
          label="名称"
          name="name"
          rules={[
            { required: true, message: '请输入视图名称' },
            { max: 40, message: '最多输入 40 个字符' },
          ]}
        >
          <Input ref={inputRef} placeholder="最多输入 40 个字符" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateOrEditViewModal;
