import { useState } from 'react';
import {
  DeleteOutlined,
  EditOutlined,
  MoreOutlined,
  PlusOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import { App, ConfigProvider, Dropdown, Segmented, Tooltip } from 'antd';
import CreateOrEditViewModal from './CreateOrEditViewModal';
import {
  ETableViewItemType,
  ETableViewsType,
  FiltersType,
  SettingColumnType,
} from '../../interface';

interface ViewTabsProps {
  activeKey?: string | number;
  filters: FiltersType;
  settings: SettingColumnType[];
  items: ETableViewItemType[];
  views?: ETableViewsType;
  onActiveKeyChange: (activeKey: string | number) => void;
}

const ADD_BUTTON_ID = 'VIEWS_ADD_BUTTON_ID';

const ViewTabs: React.FC<ViewTabsProps> = ({
  activeKey,
  filters,
  settings,
  views,
  items,
  onActiveKeyChange,
}) => {
  const { modal } = App.useApp();
  const [createOrEditViewModalOpen, setCreateOrEditViewModalOpen] = useState(false);
  const [currentEditId, setCurrentEditId] = useState<string | number>();

  const handleUpdate = (id: string | number, name: string) => {
    modal.confirm({
      title: '更新视图',
      content: `确定要将当前筛选和显示配置更新到视图 “${name}” 吗？`,
      onOk: () => views?.onUpdate?.(id, filters, settings),
    });
  };

  const handleDelete = (id: string | number, name: string) => {
    modal.confirm({
      title: '删除视图',
      content: `确定要删除视图 “${name}” 吗？`,
      onOk: async () => {
        await views?.onDelete?.(id);
        onActiveKeyChange(items[0]?.id);
      },
    });
  };

  return (
    <>
      <ConfigProvider theme={{ token: { motion: false } }}>
        <Segmented
          className="mb-3 max-w-full overflow-auto"
          value={activeKey}
          options={[
            ...items.map((item, index) => ({
              label:
                index === 0 ? (
                  item.name
                ) : (
                  <div className="flex items-center">
                    {item.name}
                    {activeKey === item.id && (
                      <Dropdown
                        menu={{
                          items: [
                            {
                              label: '重命名视图',
                              key: 'rename',
                              icon: <EditOutlined />,
                              onClick: () => {
                                setCreateOrEditViewModalOpen(true);
                                setCurrentEditId(item.id);
                              },
                            },
                            {
                              label: '更新视图',
                              key: 'update',
                              icon: <SaveOutlined />,
                              onClick: () => handleUpdate(item.id, item.name),
                            },
                            {
                              label: '删除视图',
                              key: 'delete',
                              icon: <DeleteOutlined />,
                              danger: true,
                              onClick: () => handleDelete(item.id, item.name),
                            },
                          ],
                        }}
                        align={{
                          offset: [0, 10],
                        }}
                        trigger={['click']}
                        placement="bottom"
                      >
                        <div className="flex p-1 rounded ml-2 hover:bg-gray-100 active:bg-gray-200">
                          <MoreOutlined />
                        </div>
                      </Dropdown>
                    )}
                  </div>
                ),
              value: item.id,
            })),
            {
              label: (
                <Tooltip title="添加视图">
                  {/* 样式是为了增大 Tooltip 交互区域 */}
                  <div
                    className="-mx-[11px] px-[11px]"
                    onClick={() => {
                      setCreateOrEditViewModalOpen(true);
                      setCurrentEditId(undefined);
                    }}
                  >
                    <PlusOutlined />
                  </div>
                </Tooltip>
              ),
              value: ADD_BUTTON_ID,
            },
          ]}
          onChange={(value) => {
            if (value !== ADD_BUTTON_ID) {
              onActiveKeyChange(value);
            }
          }}
        />
      </ConfigProvider>

      <CreateOrEditViewModal
        open={createOrEditViewModalOpen}
        initialValue={items.find((i) => i.id === currentEditId)?.name}
        onCancel={() => setCreateOrEditViewModalOpen(false)}
        onSave={async (name) => {
          if (currentEditId) {
            await views?.onRename?.(currentEditId, name);
          } else {
            await views?.onCreate?.(name, filters, settings);
          }

          setCreateOrEditViewModalOpen(false);
        }}
      />
    </>
  );
};

export default ViewTabs;
