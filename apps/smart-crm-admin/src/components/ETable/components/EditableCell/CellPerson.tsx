import useAllUsers from '@src/hooks/useAllUsers';
import { SelectProps } from 'antd';
import CellMultiple, { CellMultipleProps } from './CellMultiple';
import CellSingle from './CellSingle';

const fieldNames = { label: 'name', value: 'id' };

const CellPerson: React.FC<CellMultipleProps> = ({ value, ...props }) => {
  const { data, loading } = useAllUsers();

  const arrayValue = Array.isArray(value)
    ? value
    : typeof value === 'number'
    ? [value]
    : value?.split(',').map(Number);

  if (!props.editable && Array.isArray(arrayValue) && arrayValue.length === 1) {
    return (
      <CellSingle
        {...props}
        value={arrayValue[0]}
        fieldProps={{ loading, fieldNames, options: data, ...props.fieldProps } as SelectProps}
      />
    );
  }

  return (
    <CellMultiple
      {...props}
      value={arrayValue}
      formItemProps={{
        getValueProps: (val?: string) => ({
          value: Array.isArray(val) ? val : val ? val.split(',').map(Number) : undefined,
        }),
        ...props.formItemProps,
      }}
      fieldProps={
        {
          loading,
          showSearch: true,
          fieldNames,
          optionFilterProp: 'name',
          options: data,
          ...props.fieldProps,
        } as SelectProps
      }
    />
  );
};

export default CellPerson;
