import React, { FC, useEffect, useRef } from 'react';
import { EditOutlined } from '@ant-design/icons';
import { autoUpdate, offset, useFloating } from '@floating-ui/react';
import PopoverValidator from '@src/components/PopoverValidator';
import { Input } from 'antd';
import { TextAreaProps } from 'antd/lib/input';
import useEditableCell from '../../hooks/useEditableCell';
import { EditableCellProps } from '../../interface';

interface PopoverTextAreaProps extends TextAreaProps {
  cellRef: React.RefObject<HTMLTableCellElement>;
}

const PopoverTextArea: React.FC<PopoverTextAreaProps> = ({ cellRef, ...props }) => {
  const rect = cellRef.current?.getBoundingClientRect();
  const containerRef = useRef<HTMLDivElement | null>(null);

  const { refs, floatingStyles } = useFloating({
    strategy: 'fixed',
    whileElementsMounted: autoUpdate,
    middleware: [offset({ mainAxis: -(rect?.height || 0) / 2 })],
  });

  // 不用 autoFocus，因为 textarea 的 autoFocus 会聚焦在起始位置
  // https://github.com/ant-design/ant-design/issues/28840
  useEffect(() => {
    const textareaEle = containerRef.current?.querySelector('textarea');

    if (textareaEle) {
      textareaEle.setSelectionRange(-1, -1);
    }
  }, []);

  return (
    <PopoverValidator>
      <div ref={refs.setReference} />
      <div
        ref={(ref) => {
          containerRef.current = ref;
          refs.setFloating(ref);
        }}
        style={{
          ...floatingStyles,
          zIndex: 3,
          width: (rect?.width || 0) - 1,
          minHeight: (rect?.height || 0) - 1,
        }}
        className="border-primary border-2 rounded-none bg-white"
      >
        <Input.TextArea
          autoSize
          variant="borderless"
          {...props}
          styles={{
            ...props.styles,
            textarea: {
              maxHeight: 120,
              ...props.styles?.textarea,
            },
          }}
        />
      </div>
    </PopoverValidator>
  );
};

export interface CellInputProps extends EditableCellProps {
  fieldProps?: TextAreaProps;
}

const CellInput: FC<CellInputProps> = (props) => {
  const { fieldProps, value } = props;
  const { mode, render, form, setSelected, cellProps } = useEditableCell(props, {
    icon: <EditOutlined />,
    hideSelectedStyleWhenEdit: true,
  });

  let childrenNode: React.ReactNode;

  if (mode === 'read') {
    childrenNode = value;
  } else if (mode === 'edit') {
    childrenNode = (
      <PopoverTextArea
        cellRef={cellProps.ref}
        size="small"
        autoComplete="off"
        variant="borderless"
        autoFocus
        {...fieldProps}
        onPressEnter={(e) => {
          form.submit();
          setSelected(false);
          fieldProps?.onPressEnter?.(e);
        }}
        onBlur={(e) => {
          form.submit();
          fieldProps?.onBlur?.(e);
        }}
      />
    );
  }

  return (
    <td {...cellProps} title={mode === 'read' ? value : ''}>
      {render(childrenNode)}
    </td>
  );
};

export default CellInput;
