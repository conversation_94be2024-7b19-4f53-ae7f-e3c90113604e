import { FC } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { PopoverValidatorSelect } from '@src/components/PopoverValidator';
import { SelectProps } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import classNames from 'classnames';
import useEditableCell from '../../hooks/useEditableCell';
import styles from '../../index.module.scss';
import { EditableCellProps } from '../../interface';

export interface CellSingleProps extends EditableCellProps {
  fieldProps?: SelectProps;
}

const CellSingle: FC<CellSingleProps> = (props) => {
  const { fieldProps, value } = props;
  const { mode, render, form, setSelected, cellProps } = useEditableCell(props, {
    icon: <CaretDownOutlined />,
  });

  let childrenNode: React.ReactNode;
  let title = '';

  const fieldNameLabel = fieldProps?.fieldNames?.label || 'label';
  const fieldNameValue = fieldProps?.fieldNames?.value || 'value';

  if (mode === 'read') {
    title = fieldProps?.options?.find((i: DefaultOptionType) => i[fieldNameValue] === value)?.[
      fieldNameLabel
    ];
    childrenNode = title;
  } else {
    childrenNode = (
      <PopoverValidatorSelect
        allowClear
        defaultOpen
        autoFocus
        size="small"
        suffixIcon={null}
        showSearch
        optionFilterProp={fieldNameLabel}
        variant="borderless"
        {...fieldProps}
        className={classNames(styles['cell-select'], fieldProps?.className)}
        style={{ width: '100%', ...fieldProps?.style }}
        onClick={(e) => {
          // 解决 React.portal 的问题，否则点击 option 或点击滚动条后 click 事件会冒泡，触发 useClickAway
          e.stopPropagation();
          fieldProps?.onClick?.(e);
        }}
        onChange={(...rest) => {
          fieldProps?.onChange?.(...rest);
          setSelected(false);
          form.submit();
        }}
      />
    );
  }

  return (
    <td {...cellProps} title={title}>
      {render(childrenNode)}
    </td>
  );
};

export default CellSingle;
