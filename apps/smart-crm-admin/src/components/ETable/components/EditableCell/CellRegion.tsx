import { FC } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { PopoverValidatorRegionCascader } from '@src/components/PopoverValidator';
import useDistricts from '@src/hooks/useDistricts';
import { CascaderProps, Popover, Tag } from 'antd';
import classNames from 'classnames';
import Overflow from 'rc-overflow';
import useEditableCell from '../../hooks/useEditableCell';
import styles from '../../index.module.scss';
import { EditableCellProps } from '../../interface';
import { getRegionMultipleLabelArr, getRegionSingleLabel } from '../../utils';

interface CellRegionProps extends EditableCellProps {
  fieldProps?: CascaderProps & { regionLevel?: 1 | 2 | 3 | 4 };
  value?: string;
}

const CellRegion: FC<CellRegionProps> = (props) => {
  const { value, fieldProps, formItemProps } = props;

  const { data, loading } = useDistricts({ streets: fieldProps?.regionLevel === 4 });

  const isMultiple = fieldProps?.multiple;

  const { mode, render, form, setSelected, cellProps } = useEditableCell(
    {
      ...props,
      formItemProps: {
        getValueProps: (val: string) => {
          let result;

          if (val) {
            if (isMultiple) {
              result = val.split(',').map((i) => i.split('/'));
            } else {
              result = val.split('/');
            }
          }

          return {
            value: result,
          };
        },
        normalize: (val) => {
          if (val) {
            if (isMultiple) {
              return val.map((item: number[]) => item.join('/')).join(',');
            }

            return val.join('/');
          }

          return val;
        },
        ...formItemProps,
      },
    },
    {
      icon: <CaretDownOutlined />,
    },
  );

  let childrenNode: React.ReactNode;
  let title = '';

  if (mode === 'read') {
    if (value) {
      if (isMultiple) {
        const labelArray = getRegionMultipleLabelArr(value, data);

        childrenNode = (
          <Overflow
            data={labelArray}
            className="flex flex-nowrap"
            renderItem={(item) => {
              const isLast = labelArray.findIndex((i) => i === item) === labelArray.length - 1;

              return <Tag className={isLast ? 'mr-0' : ''}>{item}</Tag>;
            }}
            maxCount="responsive"
            renderRest={(items) => (
              <Popover
                destroyOnHidden
                content={
                  <div className="flex gap-2 flex-wrap max-w-[240px]">
                    {items.map((item) => (
                      <Tag key={item} className="mr-0">
                        {item}
                      </Tag>
                    ))}
                  </div>
                }
              >
                <span className="cursor-pointer text-gray-500">+{items.length}</span>
              </Popover>
            )}
          />
        );
      } else {
        title = getRegionSingleLabel(value, data);
        childrenNode = title;
      }
    }
  } else {
    childrenNode = (
      <PopoverValidatorRegionCascader
        defaultOpen
        autoFocus
        size="small"
        variant="borderless"
        loading={false}
        {...(fieldProps as any)}
        className={classNames(styles['cell-select'], fieldProps?.className)}
        style={{ width: '100%', ...fieldProps?.style }}
        onClick={(e) => {
          // 解决 React.portal 的问题，否则点击 option 或点击滚动条后 click 事件会冒泡，触发 useClickAway
          e.stopPropagation();
          fieldProps?.onClick?.(e);
        }}
        onChange={(val: any, option) => {
          if (!isMultiple) {
            setSelected(false);
            form.submit();
          }

          fieldProps?.onChange?.(val, option);
        }}
        onBlur={(...rest) => {
          if (isMultiple) {
            setSelected(false);
            form.submit();
          }

          fieldProps?.onBlur?.(...rest);
        }}
      />
    );
  }

  return (
    <td {...cellProps} title={title}>
      {loading && value ? <div className="bg-[#f1f1f1] h-full rounded" /> : render(childrenNode)}
    </td>
  );
};

export default CellRegion;
