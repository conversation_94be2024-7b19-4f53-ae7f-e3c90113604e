import React, { FC } from 'react';
import { parserJSON } from '@pkg/utils';
import useEditableCell from '../../hooks/useEditableCell';
import { EditableCellProps } from '../../interface';

interface CellPositionProps extends EditableCellProps {
  // 后端返回的是 json 格式，要 parse 一下
  value?: string;
}

type ValueType = {
  name: string;
  longitude: number;
  latitude: number;
};

// 暂时表格没有编辑
const CellPosition: FC<CellPositionProps> = (props) => {
  const { value } = props;
  const { mode, render, cellProps } = useEditableCell(props);

  let childrenNode: React.ReactNode;
  let title = '';

  if (mode === 'read') {
    if (value) {
      const obj: ValueType | undefined = parserJSON(value);

      if (obj) {
        title = `${obj.name}（经度：${obj.longitude}；纬度：${obj.latitude}）`;
        childrenNode = title;
      }
    }
  } else {
    childrenNode = null;
  }

  return (
    <td {...cellProps} title={title}>
      {render(childrenNode)}
    </td>
  );
};

export default CellPosition;
