import { useState } from 'react';
import { EditOutlined, EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons';
import { idCardPattern } from '@src/common/constants';
import { PopoverValidatorInput } from '@src/components';
import { decryptSensitive } from '@src/services/common';
import { SensitiveTypeEnum } from '@src/services/common/type';
import { useRequest } from 'ahooks';
import { Button, InputProps } from 'antd';
import useEditableCell from '../../hooks/useEditableCell';
import { EditableCellProps } from '../../interface';

interface CellIdentityCardProps extends EditableCellProps {
  fieldProps?: InputProps & { sensitiveValue?: string };
}

const CellIdentityCard: React.FC<CellIdentityCardProps> = (props) => {
  const { fieldProps, value } = props;
  const { mode, render, form, setSelected, cellProps } = useEditableCell(
    {
      ...props,
      formItemProps: {
        ...props.formItemProps,
        rules: [
          {
            pattern: idCardPattern,
            message: '格式不正确',
          },
          ...(props.formItemProps?.rules || []),
        ],
      },
    },
    {
      icon: <EditOutlined />,
    },
  );
  const [showReal, setShowReal] = useState(false);

  const {
    data: realValue,
    runAsync: getRealValue,
    loading,
  } = useRequest(decryptSensitive, { manual: true });

  let childrenNode: React.ReactNode;
  let title: string | undefined;

  if (mode === 'read') {
    const { sensitiveValue } = fieldProps || {};

    if (sensitiveValue) {
      const showText = showReal ? realValue : sensitiveValue;

      title = showText;
      childrenNode = (
        <div>
          {showText}
          <Button
            type="text"
            size="small"
            loading={loading}
            className="ml-1"
            icon={showReal ? <EyeInvisibleOutlined /> : <EyeOutlined />}
            onClick={async () => {
              if (!showReal && !realValue) {
                await getRealValue({
                  encrypted: value || '',
                  sensitiveType: SensitiveTypeEnum.ID_CARD,
                });
                setShowReal(!showReal);
              } else {
                setShowReal(!showReal);
              }
            }}
          />
        </div>
      );
    } else {
      childrenNode = value;
      title = value;
    }
  } else if (mode === 'edit') {
    childrenNode = (
      <PopoverValidatorInput
        size="small"
        autoComplete="off"
        variant="borderless"
        autoFocus
        {...fieldProps}
        onPressEnter={(e) => {
          form.submit();
          setSelected(false);
          fieldProps?.onPressEnter?.(e);
        }}
        onBlur={(e) => {
          form.submit();
          fieldProps?.onBlur?.(e);
        }}
      />
    );
  }

  return (
    <td {...cellProps} title={title}>
      {render(childrenNode)}
    </td>
  );
};

export default CellIdentityCard;
