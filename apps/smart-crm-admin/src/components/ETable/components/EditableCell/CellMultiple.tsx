import { FC } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { PopoverValidatorSelect } from '@src/components/PopoverValidator';
import { Popover, SelectProps, Tag } from 'antd';
import classNames from 'classnames';
import Overflow from 'rc-overflow';
import useEditableCell from '../../hooks/useEditableCell';
import styles from '../../index.module.scss';
import { EditableCellProps } from '../../interface';

export interface CellMultipleProps extends EditableCellProps {
  fieldProps?: SelectProps;
  value?: string | (string | number)[];
}

const CellMultiple: FC<CellMultipleProps> = (props) => {
  const { fieldProps, formItemProps, value: propsValue } = props;
  const { mode, render, form, cellProps } = useEditableCell(
    {
      ...props,
      formItemProps: {
        getValueProps: (val?: string | (string | number)[]) => ({
          // 如果外面传进来的是数组，直接用，否则逗号切开成数组
          value: Array.isArray(val) ? val : val ? val.split(',') : undefined,
        }),
        normalize: (val) => val.join(','),
        ...formItemProps,
      },
    },
    {
      icon: <CaretDownOutlined />,
    },
  );

  const value = Array.isArray(propsValue) ? propsValue : propsValue ? propsValue.split(',') : [];

  let childrenNode: React.ReactNode;

  const fieldNames = fieldProps?.fieldNames;
  const fieldNameLabel = fieldNames?.label || 'label';
  const fieldNameValue = fieldNames?.value || 'value';

  if (mode === 'read') {
    const getLabel = (val: any) =>
      fieldProps?.options?.find((option: any) => option[fieldNameValue] === val)?.[fieldNameLabel];

    childrenNode = (
      <Overflow
        className="flex flex-nowrap"
        data={value}
        renderItem={(item) => {
          const isLast = value.findIndex((i: any) => i === item) === value.length - 1;
          const label = getLabel(item);

          return label && <Tag className={isLast ? 'mr-0' : ''}>{label}</Tag>;
        }}
        maxCount="responsive"
        renderRest={(items) => (
          <Popover
            destroyOnHidden
            content={
              <div className="flex gap-2 flex-wrap max-w-[240px]">
                {items.map((item) => (
                  <Tag key={item} className="mr-0">
                    {getLabel(item)}
                  </Tag>
                ))}
              </div>
            }
          >
            <span className="cursor-pointer text-gray-500">+{items.length}</span>
          </Popover>
        )}
      />
    );
  } else {
    childrenNode = (
      <PopoverValidatorSelect
        allowClear
        autoFocus
        size="small"
        defaultOpen
        suffixIcon={null}
        showSearch
        mode="multiple"
        maxTagCount="responsive"
        optionFilterProp={fieldNameLabel}
        variant="borderless"
        {...fieldProps}
        className={classNames(styles['cell-select'], fieldProps?.className)}
        style={{ width: '100%', ...fieldProps?.style }}
        onClick={(e) => {
          // 解决 React.portal 的问题，否则点击 option 或点击滚动条后 click 事件会冒泡，触发 useClickAway
          e.stopPropagation();
          fieldProps?.onClick?.(e);
        }}
        onBlur={(e) => {
          form.submit();
          fieldProps?.onBlur?.(e);
        }}
      />
    );
  }

  return (
    <td {...cellProps} title="">
      {render(childrenNode)}
    </td>
  );
};

export default CellMultiple;
