import React from 'react';
import { useEvent } from 'rc-util';
import CellAttachment from './CellAttachment';
import CellDate from './CellDate';
import CellDateTime from './CellDateTime';
import CellDefault from './CellDefault';
import CellIdentityCard from './CellIdentityCard';
import CellInput from './CellInput';
import CellMultiple from './CellMultiple';
import CellNumber from './CellNumber';
import CellPerson from './CellPerson';
import CellPhone from './CellPhone';
import CellPosition from './CellPosition';
import CellRegion from './CellRegion';
import CellSingle from './CellSingle';
import CellTag from './CellTag';
import CellTextArray from './CellTextArray';
import { EditableCellProps, ValueType } from '../../interface';
import { memoEqual } from '../../utils';

export interface IEditableCellProps<RecordType = any> extends EditableCellProps {
  render?: (value: any, record: RecordType, index: number) => React.ReactNode;
  renderCell?: (props: EditableCellProps<RecordType>) => React.ReactNode;
}

const componentsMap: Partial<Record<ValueType, React.FunctionComponent<any>>> = {
  [ValueType.TEXT]: CellInput,
  [ValueType.SINGLE]: CellSingle,
  [ValueType.MULTIPLE]: CellMultiple,
  [ValueType.DATE]: CellDate,
  [ValueType.DATE_TIME]: CellDateTime,
  [ValueType.NUMBER]: CellNumber,
  [ValueType.IDENTITY_CARD]: CellIdentityCard,
  [ValueType.PHONE]: CellPhone,
  [ValueType.PERSON]: CellPerson,
  [ValueType.ATTACHMENT]: CellAttachment,
  [ValueType.TAG]: CellTag,
  [ValueType.REGION]: CellRegion,
  [ValueType.TEXT_ARRAY]: CellTextArray,
  [ValueType.POSITION]: CellPosition,
};

const ValueTypeCell = React.memo(
  (props: EditableCellProps) => {
    const Component = componentsMap[props.valueType];

    if (!Component) {
      return null;
    }

    return <Component {...props} />;
  },
  (prev, next) =>
    memoEqual(prev, next, {
      omit: ['children', 'record'],
    }),
);

const RenderCell = React.memo(
  ({ renderCell, ...props }: Omit<IEditableCellProps, 'render'>) => {
    return renderCell?.(props);
  },
  (prev, next) =>
    memoEqual(prev, next, {
      omit: ['children', 'record'],
    }),
);

const EditableCell: React.FC<IEditableCellProps> = ({ renderCell, render, ...props }) => {
  const memoRenderCell = useEvent((_props: EditableCellProps) => renderCell?.(_props));

  if (renderCell) {
    return <RenderCell renderCell={memoRenderCell} {...props} />;
  }

  /**
   *  如果有 render， 忽略 valueType，用 CellDefault
   */
  if (!render && props.valueType) {
    return <ValueTypeCell {...props} />;
  }

  return <CellDefault {...props} />;
};

export default EditableCell;
