import { FC } from 'react';
import { Popover, Tag } from 'antd';
import Overflow from 'rc-overflow';
import useEditableCell from '../../hooks/useEditableCell';
import { EditableCellProps } from '../../interface';

interface CellTagProps extends Omit<EditableCellProps, 'value'> {
  value?: { id: number; name: string }[];
}

// 暂无编辑需求
const CellTag: FC<CellTagProps> = ({ value, ...restProps }) => {
  const { render, cellProps } = useEditableCell({ value, ...restProps, editable: false });

  const childrenNode = value && value.length > 0 && (
    <Overflow
      className="flex flex-nowrap"
      data={value}
      renderItem={(item) => {
        const isLast = value.findIndex((i) => i.id === item.id) === value.length - 1;

        return <Tag className={isLast ? 'mr-0' : ''}>{item.name}</Tag>;
      }}
      maxCount="responsive"
      renderRest={(items) => (
        <Popover
          destroyOnHidden
          content={
            <div className="flex gap-2 flex-wrap max-w-[240px]">
              {items.map((i) => (
                <Tag key={i.id} className="mr-0">
                  {i.name}
                </Tag>
              ))}
            </div>
          }
        >
          <span className="cursor-pointer text-gray-500">+{items.length}</span>
        </Popover>
      )}
    />
  );

  return (
    <td {...cellProps} title="">
      {render(childrenNode)}
    </td>
  );
};

export default CellTag;
