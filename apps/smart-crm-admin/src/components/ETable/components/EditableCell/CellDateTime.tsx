import { FC } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { PopoverValidatorDatePicker } from '@src/components/PopoverValidator';
import { DatePickerProps } from 'antd';
import dayjs from 'dayjs';
import useEditableCell from '../../hooks/useEditableCell';
import { EditableCellProps } from '../../interface';

interface CellDateTimeProps extends EditableCellProps {
  fieldProps?: DatePickerProps;
}

const CellDateTime: FC<CellDateTimeProps> = (props) => {
  const { formItemProps, fieldProps, value } = props;
  const { mode, render, form, cellProps } = useEditableCell(
    {
      ...props,
      formItemProps: {
        initialValue: value ? dayjs(value) : undefined,
        getValueProps: (val) => ({
          value: val ? dayjs(val) : undefined,
        }),
        normalize: (val: dayjs.Dayjs) => (val ? val.format('YYYY-MM-DD HH:mm:ss') : undefined),
        ...formItemProps,
      },
    },
    { icon: <CaretDownOutlined /> },
  );

  let childrenNode: React.ReactNode;
  let title = '';

  if (mode === 'read') {
    if (value) {
      title = dayjs(value).format('YYYY-MM-DD HH:mm:ss');
      childrenNode = title;
    }
  } else {
    childrenNode = (
      <PopoverValidatorDatePicker
        variant="borderless"
        defaultOpen
        autoFocus
        size="small"
        placeholder=""
        showTime
        panelRender={(originNode) => (
          // 处理 React.portal 的冒泡问题，否则点击面板会直接触发 useClickAway 关闭面板
          <div onClick={(e) => e.stopPropagation()}>{originNode}</div>
        )}
        {...fieldProps}
        style={{ width: '100%', paddingInline: 0, ...fieldProps?.style }}
        onChange={(...rest) => {
          form.submit();
          fieldProps?.onChange?.(...rest);
        }}
      />
    );
  }

  return (
    <td {...cellProps} title={title}>
      {render(childrenNode)}
    </td>
  );
};

export default CellDateTime;
