import { FC } from 'react';
import useEditableCell from '../../hooks/useEditableCell';
import { EditableCellProps } from '../../interface';

const CellTextArray: FC<EditableCellProps> = (props) => {
  const { value } = props;
  const { render, cellProps } = useEditableCell(props);

  const text = value?.split(',').join('、');

  // 暂时不需要编辑

  return (
    <td {...cellProps} title={text}>
      {render(text)}
    </td>
  );
};

export default CellTextArray;
