import { FC } from 'react';
import { EditOutlined } from '@ant-design/icons';
import { PopoverValidatorInputNumber } from '@src/components/PopoverValidator';
import { InputNumberProps } from 'antd';
import classNames from 'classnames';
import useEditableCell from '../../hooks/useEditableCell';
import styles from '../../index.module.scss';
import { EditableCellProps } from '../../interface';

interface CellNumberProps extends EditableCellProps {
  fieldProps?: InputNumberProps;
}

const CellNumber: FC<CellNumberProps> = (props) => {
  const { fieldProps, value } = props;
  const { mode, render, form, cellProps, setSelected } = useEditableCell(props, {
    icon: <EditOutlined />,
  });

  let childrenNode: React.ReactNode;

  if (mode === 'read') {
    childrenNode = value;
  } else {
    childrenNode = (
      <PopoverValidatorInputNumber
        size="small"
        variant="borderless"
        autoFocus
        {...fieldProps}
        className={classNames(styles['cell-number'], fieldProps?.className)}
        style={{ width: '100%', ...fieldProps?.style }}
        onPressEnter={(e) => {
          form.submit();
          setSelected(false);
          fieldProps?.onPressEnter?.(e);
        }}
        onBlur={(e) => {
          form.submit();
          fieldProps?.onBlur?.(e);
        }}
      />
    );
  }

  return <td {...cellProps}>{render(childrenNode)}</td>;
};

export default CellNumber;
