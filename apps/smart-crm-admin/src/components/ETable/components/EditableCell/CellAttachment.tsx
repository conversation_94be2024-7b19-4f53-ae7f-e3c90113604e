import { FC, useMemo, useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { parserJSON } from '@pkg/utils';
import { EditableCellProps } from '@src/components/ETable/interface';
import PopoverUpload from '@src/components/PopoverUpload';
import { FileDTO } from '@src/services/common/type';
import { useUpdateEffect } from 'ahooks';
import { Form, message, UploadFile } from 'antd';
import classNames from 'classnames';
import { omit } from 'lodash-es';
import useCellSkeleton from '../../hooks/useCellSkeleton';
import useClickAway from '../../hooks/useClickAway';

interface CellAttachmentProps extends Omit<EditableCellProps, 'value'> {
  value?: string;
}

const CellAttachment: FC<CellAttachmentProps> = ({
  index,
  field,
  value,
  editable,
  fieldProps,
  formItemProps,
  valueType,
  onSave,
  ...restProps
}) => {
  const [form] = Form.useForm();
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [selected, setSelected] = useState(false);
  const cellRef = useRef<HTMLTableCellElement>(null);

  // 附件进入到可视范围才展示，减轻请求资源负担
  const show = useCellSkeleton(cellRef);

  useClickAway(cellRef, () => {
    setSelected(false);
  });

  const initialValue = useMemo(
    (): UploadFile<{ key: string }>[] =>
      value
        ? (parserJSON(value) || []).map(
            (file: FileDTO) =>
              ({
                uid: file.fileKey,
                url: file.fileUrl,
                name: file.fileName,
                type: file.fileType,
                status: 'done',
                response: {
                  key: file.fileKey,
                },
              } as UploadFile<any>),
          )
        : [],
    [value],
  );

  // 外部 value 发生更改，这里需要更新
  useUpdateEffect(() => {
    const formValue: UploadFile<{ key: string }>[] = form.getFieldValue('name') || [];
    const result: UploadFile<{ key: string }>[] = [];

    formValue.forEach((item) => {
      if (item.status === 'uploading') {
        result.push(item);
      } else {
        const key = item.response?.key;

        if (initialValue.some((i) => i.response?.key === key)) {
          result.push(item);
        }
      }
    });
    initialValue.forEach((item) => {
      if (!result.some((i) => i.response?.key === item.response?.key)) {
        result.push(item);
      }
    });
    form.setFieldValue('name', result);
  }, [value]);

  const childrenNode = (
    <Form
      component={false}
      preserve={false}
      form={form}
      onFinish={(values) => {
        const files = (Object.values(values)[0] as UploadFile<{ key: string; url: string }>[])
          .filter((i) => i.status === 'done')
          .map((i) => ({
            fileKey: i.response?.key,
            fileUrl: i.response?.url || i.url,
            fileType: i.type || i.name.split('.').pop(),
            fileName: i.name,
          }));

        onSave({ field, value: JSON.stringify(files), prevValue: value, index, valueType });
      }}
      onFinishFailed={(errorInfo) => {
        message.error(errorInfo.errorFields[0].errors[0]);
      }}
    >
      <Form.Item // name 任意
        name="name"
        initialValue={initialValue}
        noStyle
        valuePropName="fileList"
        getValueFromEvent={(e) => {
          const fileList: UploadFile<{ url: string }>[] = Array.isArray(e) ? e : e.fileList;

          return fileList
            .filter((i) => i.status !== 'error')
            .map((i) => ({
              ...i,
              url: i.url || i.response?.url,
            }));
        }}
        {...formItemProps}
      >
        <PopoverUpload
          showUploadIcon={editable && selected}
          editable={editable}
          popoverProps={{
            open: popoverOpen,
            onOpenChange: (op) => {
              if (!op) {
                setPopoverOpen(op);
              }
            },
          }}
          {...fieldProps}
        />
      </Form.Item>
    </Form>
  );

  const shouldShowPopover = editable || initialValue.length > 0;

  return (
    <td
      ref={cellRef}
      {...omit(restProps, ['record'])}
      title=""
      className={classNames(
        'group',
        {
          'before:block before:absolute before:inset-0 before:border-primary before:border-2 before:pointer-events-none':
            selected,
        },
        restProps.className,
        'p-0',
      )}
      onClick={(e) => {
        if (selected && shouldShowPopover) {
          setPopoverOpen(!popoverOpen);
        }

        setSelected(true);
        restProps.onClick?.(e);
      }}
    >
      {show || initialValue.length === 0 ? (
        <>
          {shouldShowPopover && childrenNode}
          {shouldShowPopover && !popoverOpen && (
            <CaretDownOutlined
              className="hidden group-hover:inline-flex bg-white hover:bg-gray-100 p-1 border rounded-md absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer"
              onClick={() => {
                setPopoverOpen(true);
              }}
            />
          )}
        </>
      ) : (
        <div className="bg-[#f1f1f1] h-full rounded" />
      )}
    </td>
  );
};

export default CellAttachment;
