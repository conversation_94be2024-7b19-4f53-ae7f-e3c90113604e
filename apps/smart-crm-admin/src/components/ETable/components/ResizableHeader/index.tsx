import { HTMLAttributes, useRef, useState } from 'react';
import classnames from 'classnames';
import { createPortal } from 'react-dom';
import { Resizable } from 'react-resizable';

interface ResizableHeaderProps extends Omit<HTMLAttributes<HTMLTableCellElement>, 'onResize'> {
  width: number;
  onResize: (size: number) => void;
  children: React.ReactNode[];
}

// 使表格列头可拖拽
const ResizableHeader: React.FC<ResizableHeaderProps> = (props) => {
  const { onResize, width, children, ...restProps } = props;

  // 偏移量
  const [offset, setOffset] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const thRef = useRef<HTMLTableCellElement>(null);
  const [position, setPosition] = useState({ left: 0, top: 0 });
  const [tableRect, setTableRect] = useState({ top: 0, bottom: 0 });

  if (!width) {
    return <th {...restProps}>{children}</th>;
  }

  // 当前列的宽度
  const currentWidth = width + offset;

  return (
    <Resizable
      axis="x"
      handle={
        <span
          className={classnames('absolute w-[10px] h-full bottom-0 right-0 cursor-col-resize')}
        />
      }
      height={0}
      width={currentWidth}
      onResizeStart={(e) => {
        // lock scroll
        e.preventDefault();
        setIsDragging(true);

        const _tableRect = thRef.current?.closest('.ant-table')?.getBoundingClientRect();
        const thRect = thRef.current?.getBoundingClientRect();

        setTableRect({ top: _tableRect?.top || 0, bottom: _tableRect?.bottom || 0 });
        setPosition({ left: thRect?.right || 0, top: thRect?.top || 0 });
      }}
      onResize={(_, { size }: { size: { width: number } }) => {
        // 只更新偏移量，数据列表其实并没有伸缩
        setOffset(size.width - width);
      }}
      onResizeStop={(_, data) => {
        setIsDragging(false);
        // 拖拽结束以后偏移量归零
        setOffset(0);
        onResize(data.size.width);
      }}
    >
      <th ref={thRef} {...restProps}>
        {children}
        {isDragging &&
          // createPortal 解决遮挡问题
          createPortal(
            <div
              className="fixed w-[2px] bg-primary z-[9999]"
              style={{
                top: position.top,
                left: position.left + offset - 1,
                height: tableRect.top < 0 ? tableRect.bottom : tableRect.bottom - tableRect.top,
              }}
            />,
            document.body,
          )}
      </th>
    </Resizable>
  );
};

export default ResizableHeader;
