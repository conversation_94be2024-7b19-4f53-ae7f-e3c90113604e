import React, { HTMLAttributes, useContext, useMemo, useRef, useState } from 'react';
import { useUpdateEffect } from 'ahooks';
import RowContext from './context';
import ETableContext from '../../context';
import useInViewport from '../../hooks/useInViewport';

interface RowProps extends HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string | number;
}

const Row: React.FC<RowProps> = ({ children, ...props }) => {
  const { perfEvent } = useContext(ETableContext);
  const trRef = useRef<HTMLTableRowElement>(null);
  const [show, setShow] = useState(false);
  const latestInViewport = useRef(false);

  perfEvent.useSubscription(() => {
    if (!latestInViewport.current) {
      setShow(false);
    }
  });

  useInViewport(trRef, {
    callback: (inViewport) => {
      latestInViewport.current = inViewport;

      // 在可视范围并且是隐藏的
      if (inViewport && !show) {
        setShow(true);
      }
    },
  });

  const rowKey = props['data-row-key'];

  // rowKey 变化，切换分页，重新检测
  useUpdateEffect(() => {
    setShow(false);
  }, [rowKey]);

  const contextValue = useMemo(() => ({ rowKey }), [rowKey]);

  return (
    <RowContext.Provider value={contextValue}>
      <tr ref={trRef} {...props}>
        {/* 为 React 元素时是展示的初始 Table 占位 ant-table-placeholder */}
        {show || React.isValidElement(children) ? (
          children
        ) : (
          <td colSpan={React.Children.count(children)}>
            <div className="h-full bg-[#f1f1f1] rounded-md" />
          </td>
        )}
      </tr>
    </RowContext.Provider>
  );
};

export default Row;
