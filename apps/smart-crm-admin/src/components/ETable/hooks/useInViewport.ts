import 'intersection-observer';
import { useEffect } from 'react';
import { useEvent } from 'rc-util';

function useInViewport(
  target: React.RefObject<HTMLElement>,
  options?: { disabled?: boolean; callback?: (inViewport: boolean) => void },
) {
  const cb = useEvent(options?.callback || (() => {}));

  useEffect(() => {
    const ele = target.current;

    if (!ele || options?.disabled) {
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        for (const entry of entries) {
          cb(entry.isIntersecting);
        }
      },
      { rootMargin: '100px' },
    );

    observer.observe(ele);

    return () => {
      observer.disconnect();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [options?.disabled, target.current]);
}

export default useInViewport;
