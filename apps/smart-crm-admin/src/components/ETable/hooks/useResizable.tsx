import { useState } from 'react';
import { ETableColumn, ETableProps } from '../interface';
import { getField } from '../utils';

const useResizable = <RecordType extends Record<string, any>>(
  columns: ETableProps<RecordType>['columns'],
  defaultColumnWidth = 180,
) => {
  const [resizeWidthMap, setResizeWidthMap] = useState<Map<string, number>>(new Map());

  const handleResize = (key: string) => (width: number) => {
    setResizeWidthMap((prev) => {
      const result = new Map(prev);

      result.set(key, width);

      return result;
    });
  };

  const columnsWithWidth = (columns || []).map((col) => {
    const key = getField(col);
    const width = resizeWidthMap.get(key) || col.width || defaultColumnWidth;

    return {
      ...col,
      width,
      onHeaderCell: () =>
        ({
          width,
          onResize: handleResize(key),
        } as any),
    } as ETableColumn<RecordType>;
  });

  return columnsWithWidth;
};

export default useResizable;
