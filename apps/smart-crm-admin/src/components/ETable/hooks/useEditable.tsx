import { get } from 'lodash-es';
import { useEvent } from 'rc-util';
import { IEditableCellProps } from '../components/EditableCell';
import { ETableColumn, SaveInfoType, ValueType } from '../interface';
import { getField } from '../utils';

const useEditable = <RecordType extends Record<string, any>>({
  columns,
  dataSource,
  onSave,
}: {
  columns: ETableColumn<RecordType>[];
  dataSource: RecordType[];
  onSave?: (info: SaveInfoType) => void;
}) => {
  // 保持引用不变，可以 memo
  const handleSave = useEvent((info: Omit<SaveInfoType, 'record'>) => {
    if (onSave) {
      // 因为 record 被 memoEqual 函数 omit 了，所以要在这里取最新的 record
      const saveInfo = {
        ...info,
        prevRecord: dataSource[info.index],
        record: { ...dataSource[info.index], [info.field]: info.value },
      };

      onSave(saveInfo);
    }
  });

  return columns.map(
    (col) =>
      ({
        ellipsis: true,
        ...col,
        onCell: (record, index: number) => {
          let value = col.dataIndex ? get(record, col.dataIndex) : undefined;

          if (col.valueFormatter) {
            value = col.valueFormatter(value, record, index);
          }

          const editable =
            typeof col.editable === 'function' ? col.editable(value, record, index) : col.editable;
          const field = getField(col);
          const colOnCell = col.onCell?.(record, index);
          const valueType =
            typeof col.valueType === 'function' ? col.valueType('table') : col.valueType;
          const formItemProps =
            typeof col.formItemProps === 'function'
              ? col.formItemProps(value, record, index)
              : col.formItemProps;
          const fieldProps =
            typeof col.fieldProps === 'function'
              ? col.fieldProps(value, record, index)
              : col.fieldProps;

          return {
            ...colOnCell,
            editable,
            valueType: valueType || ValueType.TEXT,
            formItemProps,
            fieldProps,
            record,
            value,
            render: col.render,
            renderCell: col.renderCell,
            index,
            field,
            onSave: handleSave,
          } as IEditableCellProps;
        },
      } as ETableColumn<RecordType>),
  );
};

export default useEditable;
