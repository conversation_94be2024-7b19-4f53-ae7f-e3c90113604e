import { useContext, useState } from 'react';
import { useUpdateEffect } from 'ahooks';
import useInViewport from './useInViewport';
import RowContext from '../components/Row/context';

const useCellSkeleton = (
  cellRef: React.RefObject<HTMLTableCellElement>,
  options: { disabled?: boolean } = {},
) => {
  const rowContextValue = useContext(RowContext);
  const [show, setShow] = useState(false);
  const { disabled } = options;

  useInViewport(cellRef, {
    disabled,
    callback: (inViewport) => {
      // 在可视范围并且是隐藏的，已经展示过的离开可视范围不隐藏
      if (inViewport && !show) {
        setShow(true);
      }
    },
  });

  // rowKey 变化，切换分页，重新检测是否可视范围
  useUpdateEffect(() => {
    if (!disabled) {
      setShow(false);
    }
  }, [rowContextValue?.rowKey]);

  return show;
};

export default useCellSkeleton;
