import { isMobile } from '@src/common/constants';
import useEditable from './useEditable';
import useResizable from './useResizable';
import { ETableColumn, SaveInfoType, SettingColumnType } from '../interface';
import { getField } from '../utils';

const useColumns = <RecordType extends Record<string, any>>(
  columns: ETableColumn<RecordType>[],
  dataSource: RecordType[],
  settings: SettingColumnType[],
  showSettings: boolean,
  defaultColumnWidth?: number,
  onSave?: (info: SaveInfoType) => void,
) => {
  const showColumns =
    !settings.length || !showSettings
      ? columns
      : settings
          .reduce<ETableColumn<RecordType>[]>((total, setting) => {
            const column = columns.find((col) => getField(col) === setting.field);

            if (column && !setting.hidden) {
              total.push({
                ...column,
                hidden: false,
                // 移动端除了操作列，都不固定
                fixed: isMobile && column.key !== 'action' ? false : setting?.fixed ?? column.fixed,
              });
            }

            return total;
          }, [])
          // 在 setting 中隐藏，但是在 table 中显示的列
          .concat(columns.filter((col) => col.hideInSettings && !col.hidden));

  // 表头可拖拽
  const resizableColumns = useResizable(showColumns, defaultColumnWidth);

  // 表格可编辑
  const editableColumns = useEditable({
    columns: resizableColumns,
    dataSource,
    onSave,
  });

  return editableColumns;
};

export default useColumns;
