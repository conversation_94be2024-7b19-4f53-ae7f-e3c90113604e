import { useRef, useState } from 'react';
import { App, Form, FormProps } from 'antd';
import classNames from 'classnames';
import { omit } from 'lodash-es';
import useCellSkeleton from './useCellSkeleton';
import useClickAway from './useClickAway';
import { EditableCellProps } from '..';
import { getReadCellContent } from '../utils';

type UseEditableCellProps = Partial<EditableCellProps> &
  Pick<
    EditableCellProps,
    | 'value'
    | 'editable'
    | 'children'
    | 'field'
    | 'className'
    | 'formItemProps'
    | 'index'
    | 'valueType'
    | 'onClick'
    | 'onSave'
  > & { formProps?: FormProps };

const useEditableCell = (
  {
    value,
    editable,
    children,
    field,
    className,
    formItemProps,
    index,
    valueType,
    formProps,
    onClick,
    onSave,
    ...restProps
  }: UseEditableCellProps,
  options: { lazy?: boolean; icon?: React.ReactNode; hideSelectedStyleWhenEdit?: boolean } = {},
) => {
  const { lazy, icon, hideSelectedStyleWhenEdit } = options;
  const { message } = App.useApp();
  const [selected, setSelected] = useState(false);
  const [mode, setMode] = useState<'read' | 'edit'>('read');
  const cellRef = useRef<HTMLTableCellElement>(null);
  const show = useCellSkeleton(cellRef, { disabled: !lazy });
  const [form] = Form.useForm();

  useClickAway(cellRef, () => {
    setSelected(false);
    setMode('read');
  });

  const startEdit = () => {
    if (editable) {
      setMode('edit');
    }
  };

  const render = (content: React.ReactNode) => {
    if (lazy && !show) {
      return <div className="bg-[#f1f1f1] h-full rounded" />;
    }

    const node =
      mode === 'read' ? (
        getReadCellContent(children, content)
      ) : (
        <Form
          form={form}
          component={false}
          preserve={false}
          onFinish={(values) => {
            const newValue = Object.values(values)[0];

            if (form.isFieldTouched(formItemProps?.name || 'name') && newValue !== value) {
              onSave({
                field,
                value: newValue,
                prevValue: value,
                index,
                valueType,
              });
            }

            setMode('read');
          }}
          onFinishFailed={(errorInfo) => {
            message.error(errorInfo.errorFields[0].errors[0]);
            setMode('read');
          }}
          {...formProps}
        >
          <Form.Item
            // name 任意
            name="name"
            initialValue={value}
            noStyle
            {...formItemProps}
          >
            {content}
          </Form.Item>
        </Form>
      );

    return (
      <>
        {node}
        {icon && editable && mode === 'read' && (
          <div
            className="hidden group-hover:inline-flex bg-white hover:bg-gray-100 p-1 border rounded-md absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer"
            onClick={startEdit}
          >
            {icon}
          </div>
        )}
      </>
    );
  };

  return {
    mode,
    selected,
    setMode,
    setSelected,
    render,
    form,
    cellProps: {
      ...omit(restProps, ['record', 'fieldProps']),
      ref: cellRef,
      className: classNames(
        {
          group: editable,
          'before:block before:absolute before:inset-0 before:border-primary before:border-2 before:pointer-events-none':
            hideSelectedStyleWhenEdit && mode === 'edit' ? false : selected,
        },
        className,
      ),
      onClick: (e: React.MouseEvent<HTMLTableCellElement, MouseEvent>) => {
        if (selected) {
          startEdit();
        }

        setSelected(true);
        onClick?.(e);
      },
    },
  };
};

export default useEditableCell;
