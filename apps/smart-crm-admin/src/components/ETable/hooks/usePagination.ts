import { TablePaginationConfig } from 'antd';
import useMergedState from 'rc-util/lib/hooks/useMergedState';

const usePagination = (
  dataSourceLength: number,
  setDataSource: React.Dispatch<React.SetStateAction<any[]>>,
  pagination?: false | TablePaginationConfig,
) => {
  const isPaginationObj = typeof pagination === 'object';

  const [pageSize, setPageSize] = useMergedState(20, {
    defaultValue: isPaginationObj ? pagination.defaultPageSize : undefined,
    value: isPaginationObj ? pagination.pageSize : undefined,
  });

  const [current, setCurrent] = useMergedState(1, {
    defaultValue: isPaginationObj ? pagination.defaultCurrent : undefined,
    value: isPaginationObj ? pagination.current : undefined,
  });

  const [total, setTotal] = useMergedState(0, {
    value: isPaginationObj ? pagination.total : undefined,
  });

  const _pagination: false | TablePaginationConfig =
    pagination === false
      ? false
      : {
          ...pagination,
          current,
          pageSize,
          total,
          onChange: (page, size) => {
            // 如果外部没传 current
            if (pagination?.current === undefined) {
              // 相等说明切换的是 pageSize，需要重置到第一页
              setCurrent(current === page ? 1 : page);
            }

            if (pagination?.pageSize === undefined) {
              setPageSize(size);

              // 解决 Pagination warning
              if (pageSize !== size && dataSourceLength > size) {
                setDataSource((prev) => prev.slice(0, size));
              }
            }

            pagination?.onChange?.(page, size);
          },
        };

  return {
    pagination: _pagination,
    current,
    pageSize,
    setCurrent,
    setTotal,
  };
};

export default usePagination;
