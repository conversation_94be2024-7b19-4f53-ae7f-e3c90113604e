import { Form, FormItemProps } from 'antd';
import { filterConditionOptionsMap } from '../components/Filters/constants';
import { ValueType } from '../interface';

/**
 * 这个 hook 的作用是为了切换 condition 时，判断需不需要需要清空筛选值
 */
const useNormalize = (name: string, valueType: ValueType) => {
  const form = Form.useFormInstance();
  const options = filterConditionOptionsMap[valueType];

  const normalize: FormItemProps['normalize'] = (value, prevValue) => {
    const prevOption = options?.find((option) => option.value === prevValue);
    const currentOption = options?.find((option) => option.value === value);

    if (prevOption?.searchValueType !== currentOption?.searchValueType) {
      form.setFieldValue(['value', name, 'value'], currentOption?.initialValue);
    }

    return value;
  };

  return normalize;
};

export default useNormalize;
