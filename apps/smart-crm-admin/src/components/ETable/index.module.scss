.cell-select:global(.ant-select-single.ant-select-sm:not(.ant-select-customize-input)) {
  :global {
    .ant-select-selector {
      padding: 0;
    }

    .ant-select-selection-search {
      inset-inline-start: 0;
      inset-inline-end: 0;
    }
  }
}

.cell-select:global(.ant-select-multiple.ant-select-sm) {
  :global {
    .ant-select-selector {
      padding-inline: 0;
      font-size: 12px;
    }

    .ant-select-selection-item {
      padding-top: 2px;
      padding-bottom: 2px;
      height: 20px;
      margin-top: 0;
      margin-bottom: 0;
    }
  }
}

.cell-number {
  :global {
    input.ant-input-number-input {
      padding-inline: 0 !important;
    }
  }
}

.ETable:global(.ant-table-wrapper) {
  :global {
    .ant-table.ant-table-small .ant-table-tbody tr.ant-table-row > td {
      // min-height（td 的min-height 不起作用，但是 height 相当于 min-height，不足时会自动撑起来）
      height: 41px;
    }
  }
}
