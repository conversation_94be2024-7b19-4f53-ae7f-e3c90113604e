import { memo, useState } from 'react';
import { But<PERSON>, Divider, Form, FormListFieldData, Input } from 'antd';
import Encrypt from '../Encrypt';

enum PersonTypeEnum {
  SHOP_MANAGER = 'SHOP_MANAGER',
  EMPLOYEE = 'EMPLOYEE',
}

export type TrainingPersonsReplaceProps = {
  editable?: boolean;
  name?: string;
  defaultShow?: boolean;
};

type IProps = TrainingPersonsReplaceProps;

type TrainingPersonsReplaceItemProps = {
  defaultShow?: boolean;
  editable?: boolean;
  field: FormListFieldData;
  fields: FormListFieldData[];
  index: number;
  name: string;
};

const TrainingPersonsReplaceItem = memo(
  ({ field, fields, index, editable, name, defaultShow }: TrainingPersonsReplaceItemProps) => {
    const [show, setShow] = useState(defaultShow);
    const form = Form.useFormInstance();
    const values = form.getFieldValue([name]);

    const value = values?.[field.name];

    const hasShopManager = values?.some(
      (e: { personType: PersonTypeEnum }) => e.personType === PersonTypeEnum.SHOP_MANAGER,
    );

    const label =
      value?.personType === PersonTypeEnum.SHOP_MANAGER
        ? '店长'
        : `员工 ${hasShopManager ? index : index + 1}`;

    return (
      <div className="border border-solid border-gray-100 rounded-lg p-3 mb-3">
        <div>
          <Form.Item label={`原${label}`}>
            <div className="flex items-center justify-between">
              <div>{value.originalName}</div>
              {defaultShow ? null : (
                <Button size={'small'} onClick={() => setShow(!show)}>
                  {show ? '取消' : '更换'}
                </Button>
              )}
            </div>
          </Form.Item>
          <Encrypt.PhoneFormItem
            formItemProps={{
              label: `原${label}联系电话`,
            }}
            fieldProps={{
              disabled: true,
              value: value?.originalPhone,
              encryptSensitiveMap: {
                [value?.originalPhone || '']: value?.originalPhoneSensitive,
              },
            }}
          />
          <Encrypt.IdCardFormItem
            formItemProps={{
              label: `原${label}身份证号码`,
            }}
            fieldProps={{
              disabled: true,
              placeholder: '选择客户后自动填充',
              value: value?.originalIdentityCard,
              encryptSensitiveMap: {
                [value?.originalIdentityCard || '']: value?.originalIdentityCardSensitive,
              },
            }}
          />
        </div>
        {show ? (
          <div>
            <Divider>新人员选择</Divider>
            <Form.Item
              label={`新${label}姓名`}
              name={[field.name, 'targetName']}
              rules={[{ required: true, message: '请输入姓名' }]}
            >
              <Input placeholder="请输入姓名" />
            </Form.Item>
            <Encrypt.PhoneFormItem
              formItemProps={{
                label: `新${label}联系电话`,
                name: [field.name, 'targetPhone'],
                rules: [
                  { required: true, message: '请输入联系电话' },
                  {
                    validator: (_, _value) => {
                      if (
                        fields
                          .filter((i) => i.name !== field.name)
                          .some((i) => {
                            return _value === form.getFieldValue([name, i.name, 'targetPhone']);
                          })
                      ) {
                        return Promise.reject(new Error('联系电话不能重复'));
                      }

                      return Promise.resolve();
                    },
                  },
                ],
              }}
              fieldProps={{
                disabled: !editable,
                placeholder: '请输入联系电话',
                encryptSensitiveMap: {
                  [value.targetPhone]: value.targetPhoneSensitive,
                },
              }}
            />
            <Encrypt.IdCardFormItem
              formItemProps={{
                label: `新${label}身份证号码`,
                name: [field.name, 'targetIdentityCard'],
                rules: [
                  { required: true, message: '请输入身份证号码' },
                  {
                    validator: (_, _value) => {
                      if (
                        fields
                          .filter((i) => i.name !== field.name)
                          .some((i) => {
                            return (
                              _value === form.getFieldValue([name, i.name, 'targetIdentityCard'])
                            );
                          })
                      ) {
                        return Promise.reject(new Error('身份证号码不能重复'));
                      }

                      return Promise.resolve();
                    },
                  },
                ],
              }}
              fieldProps={{
                disabled: !editable,
                placeholder: '请输入身份证号码',
                encryptSensitiveMap: {
                  [value?.targetIdentityCard || '']: value?.targetIdentityCardSensitive,
                },
              }}
            />
            <Form.Item name={[field.name, 'trainingPersonRecordId']} hidden>
              <Input />
            </Form.Item>
            <Form.Item name={[field.name, 'personType']} hidden>
              <Input />
            </Form.Item>
            <Form.Item name={[field.name, 'originalPhone']} hidden>
              <Input />
            </Form.Item>
            <Form.Item name={[field.name, 'originalIdentityCard']} hidden>
              <Input />
            </Form.Item>
            <Form.Item name={[field.name, 'originalName']} hidden>
              <Input />
            </Form.Item>
          </div>
        ) : null}
      </div>
    );
  },
);

export const TrainingPersonsReplace = ({
  editable = true,
  defaultShow,
  name = 'trainingPersonRecordItems',
}: IProps) => {
  return (
    <Form.List name={name}>
      {(fields) =>
        fields.map((field, index) => (
          <TrainingPersonsReplaceItem
            name={name}
            key={field.key}
            index={index}
            editable={editable}
            field={field}
            fields={fields}
            defaultShow={defaultShow}
          />
        ))
      }
    </Form.List>
  );
};
