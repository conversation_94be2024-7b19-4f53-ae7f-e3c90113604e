import { useEffect, useRef } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import { App } from 'antd';
import { createPortal } from 'react-dom';

interface DocxViewerProps {
  open?: boolean;
  url?: string;
  onClose?: () => void;
}

const DocxViewer: React.FC<DocxViewerProps> = ({ url, open, onClose }) => {
  const { message } = App.useApp();
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (open && url) {
      // 动态导入，减小初始体积
      Promise.all([import('docx-preview'), fetch(url).then((res) => res.blob())]).then(
        ([{ renderAsync }, blob]) => {
          if (blob.size > 0) {
            renderAsync(blob, containerRef.current!);
          } else {
            // 空内容用 docx-preview 预览的话会报错
            message.error('文件内容为空');
            onClose?.();
          }
        },
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  if (!open) {
    return null;
  }

  return createPortal(
    <div className="fixed flex flex-col top-0 left-0 h-full w-full z-[9999] overflow-auto">
      <CloseOutlined
        className="fixed top-4 right-4 z-10 rounded-full p-2 m-2 bg-gray-200 cursor-pointer hover:opacity-60"
        onClick={onClose}
      />
      <div ref={containerRef} />
    </div>,
    document.body,
  );
};

export default DocxViewer;
