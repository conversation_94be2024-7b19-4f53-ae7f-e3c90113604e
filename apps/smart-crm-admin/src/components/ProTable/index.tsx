import React, { useRef } from 'react';
import {
  ActionType,
  ProTable as AntdProTable,
  ProTableProps as AntdProTableProps,
  ParamsType,
} from '@ant-design/pro-components';
import { closestCenter, DndContext, DragEndEvent } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { isMobile } from '@src/common/constants';
import { TableProps } from 'antd';
import classNames from 'classnames';
import SortableRow from './components/SortableRow';
import './index.css';

export interface ProTableProps<DataType, Params, ValueType>
  extends AntdProTableProps<DataType, Params, ValueType> {
  sortable?: {
    items: (number | string)[];
    onDragEnd?: (event: DragEndEvent) => void;
  };
  rowSelection?:
    | (TableProps<DataType>['rowSelection'] & {
        alwaysShowAlert?: boolean;
        /** 是否展示序号 */
        showSort?: boolean;
      })
    | false;
}

const ProTable = <
  DataType extends Record<string, any>,
  Params extends ParamsType = ParamsType,
  ValueType = 'text',
>({
  sortable,
  search,
  columns,
  actionRef: propsActionRef,
  onDataSourceChange,
  ...props
}: ProTableProps<DataType, Params, ValueType>) => {
  const actionRef = useRef<ActionType | null | undefined>(null);
  const dataSourceRef = useRef<DataType[]>([]);
  const rowSelectionPageInfo = useRef({ current: 1, pageSize: 50 });

  const tableNode = (
    <AntdProTable<DataType, Params, ValueType>
      size="small"
      columnEmptyText=""
      options={false}
      tableAlertRender={false}
      {...props}
      // 移动端不固定，否则看不了
      columns={
        isMobile
          ? columns?.map((col) => ({ ...col, fixed: col.fixed === 'right' ? col.fixed : false }))
          : columns
      }
      search={
        search === false ? false : { defaultCollapsed: isMobile, labelWidth: 'auto', ...search }
      }
      components={{
        ...props.components,
        body: {
          row: sortable ? SortableRow : undefined,
          ...props.components?.body,
        },
      }}
      actionRef={(ref) => {
        actionRef.current = ref;

        if (propsActionRef) {
          (propsActionRef as React.MutableRefObject<ActionType>).current = ref!;
        }
      }}
      onDataSourceChange={(data) => {
        dataSourceRef.current = data;
        onDataSourceChange?.(data);
      }}
      rowSelection={
        props.rowSelection && props.rowSelection.showSort
          ? {
              columnWidth: 80,
              renderCell: (checked, _, index, originNode) => (
                <>
                  <span className={classNames('group-hover:hidden', { hidden: checked })}>
                    {(rowSelectionPageInfo.current.current - 1) *
                      rowSelectionPageInfo.current.pageSize +
                      index +
                      1}
                  </span>
                  {React.cloneElement(originNode as React.ReactElement, {
                    className: classNames('group-hover:inline-flex', {
                      hidden: !checked,
                    }),
                  })}
                </>
              ),
              ...props.rowSelection,
              onCell: (...rest) => {
                const propsOnCell = props.rowSelection && props.rowSelection.onCell?.(...rest);

                return {
                  ...propsOnCell,
                  className: classNames('group', propsOnCell && propsOnCell.className),
                };
              },
            }
          : props.rowSelection
      }
      {...(props.request
        ? {
            request: async (params, ...rest) => {
              const trimParams: any = { ...params };

              Object.keys(trimParams).forEach((key) => {
                if (typeof trimParams[key] === 'string') {
                  trimParams[key] = trimParams[key].trim();
                }
              });

              const res = await props.request!(trimParams, ...rest);

              rowSelectionPageInfo.current = {
                current: params.current!,
                pageSize: params.pageSize!,
              };

              //
              if (!res.data?.length && params.current && params.current > 1) {
                actionRef.current?.setPageInfo?.({ current: params.current - 1 });

                return {
                  success: false,
                };
              }

              return res;
            },
          }
        : {})}
    />
  );

  if (sortable) {
    return (
      <DndContext
        modifiers={[restrictToVerticalAxis]}
        collisionDetection={closestCenter}
        onDragEnd={sortable.onDragEnd}
      >
        <SortableContext items={sortable.items} strategy={verticalListSortingStrategy}>
          {tableNode}
        </SortableContext>
      </DndContext>
    );
  }

  return tableNode;
};

export { DragHandle } from './components/SortableRow';

export default ProTable;
