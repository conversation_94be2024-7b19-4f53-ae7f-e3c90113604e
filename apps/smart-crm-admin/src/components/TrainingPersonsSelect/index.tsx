import { PersonTypeEnum } from '@src/services/process/train-staff/type';
import { Divider, Form, FormListFieldData, Input } from 'antd';
import { defaultTrainingPersonList } from './const';
import Encrypt from '../Encrypt';

export type TrainingPersonsSelectProps = {
  editable?: boolean;
  name?: string;
};

type TrainingPersonsSelectItemProps = {
  editable?: boolean;
  field: FormListFieldData;
  fields: FormListFieldData[];
  index: number;
  name: string;
};

const TrainingPersonsSelectItem = ({
  field,
  fields,
  index,
  editable,
  name,
}: TrainingPersonsSelectItemProps) => {
  const form = Form.useFormInstance();
  const values = form.getFieldValue([name, field.name]);

  const label = values.personType === PersonTypeEnum.SHOP_MANAGER ? '店长' : `员工 ${index}`;

  return (
    <>
      <Form.Item label={`${label}姓名`} name={[field.name, 'name']} rules={[{ required: true }]}>
        <Input disabled={!editable} placeholder="请输入姓名" />
      </Form.Item>
      <Encrypt.PhoneFormItem
        formItemProps={{
          label: `${label}联系电话`,
          name: [field.name, 'phone'],
          rules: [
            { required: true, message: '请输入联系电话' },
            {
              validator: (_, value) => {
                if (
                  fields
                    .filter((i) => i.name !== field.name)
                    .some((i) => {
                      return value === form.getFieldValue([name, i.name, 'phone']);
                    })
                ) {
                  return Promise.reject(new Error('联系电话不能重复'));
                }

                return Promise.resolve();
              },
            },
          ],
        }}
        fieldProps={{
          disabled: !editable,
          placeholder: '请输入联系电话',
          encryptSensitiveMap: {
            [values.phone]: values.phoneSensitive,
          },
        }}
      />
      <Encrypt.IdCardFormItem
        formItemProps={{
          label: `${label}身份证号码`,
          name: [field.name, 'identityCard'],
          rules: [
            { required: true, message: '请输入身份证号码' },
            {
              validator: (_, value) => {
                if (
                  fields
                    .filter((i) => i.name !== field.name)
                    .some((i) => {
                      return value === form.getFieldValue([name, i.name, 'identityCard']);
                    })
                ) {
                  return Promise.reject(new Error('身份证号码不能重复'));
                }

                return Promise.resolve();
              },
            },
          ],
        }}
        fieldProps={{
          disabled: !editable,
          placeholder: '请输入身份证号码',
          encryptSensitiveMap: {
            [values.identityCard]: values.identityCardSensitive,
          },
        }}
      />
      <Form.Item name={[field.name, 'personType']} hidden>
        <Input />
      </Form.Item>
      {index !== fields.length - 1 && <Divider />}
    </>
  );
};

export const TrainingPersonsSelect = ({
  editable = true,
  name = 'trainingPersons',
}: TrainingPersonsSelectProps) => (
  <Form.List name={name} initialValue={defaultTrainingPersonList}>
    {(fields) =>
      fields.map((field, index) => (
        <TrainingPersonsSelectItem
          name={name}
          key={field.key}
          index={index}
          editable={editable}
          field={field}
          fields={fields}
        />
      ))
    }
  </Form.List>
);
