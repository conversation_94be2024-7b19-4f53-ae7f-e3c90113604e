import { useEffect, useMemo, useRef, useState } from 'react';
import { Popover, Select, SelectProps, Spin, Tag } from 'antd';
import { BaseOptionType, DefaultOptionType } from 'antd/lib/select';
import { debounce } from 'lodash-es';
import { useEvent } from 'rc-util';

interface LoadMoreSelectProps<
  ValueType = any,
  OptionType extends DefaultOptionType | BaseOptionType = DefaultOptionType,
  ParamsType = Record<string, any>,
> extends SelectProps<ValueType, OptionType> {
  /** 值变化会触发初始化的 request */
  params?: ParamsType;
  request: (
    params: {
      pageNum: number;
      keyword?: string;
    } & ParamsType,
  ) => Promise<void | { total: number; result: OptionType[] }>;
}

export default function LoadMoreSelect<
  ValueType = any,
  OptionType extends DefaultOptionType | BaseOptionType = DefaultOptionType,
  ParamsType = Record<string, any>,
>({
  params = {} as ParamsType,
  request,
  ...props
}: LoadMoreSelectProps<ValueType, OptionType, ParamsType>) {
  const pageNum = useRef(1);
  const searchValue = useRef('');
  const [loading, setLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [options, setOptions] = useState<OptionType[]>([]);
  const cacheOptionsRef = useRef<Map<string | number, OptionType>>(new Map());
  const paramsRef = useRef<ParamsType>(params);

  paramsRef.current = params;

  // 返回最新引用，onSearch 中不会出现闭包问题
  const latestRequest = useEvent(request);

  useEffect(
    () => {
      pageNum.current = 1;
      latestRequest({ pageNum: pageNum.current, ...paramsRef.current })
        .then((res) => {
          setTotal(res?.total || 0);
          setOptions(res?.result || []);
        })
        .finally(() => {
          setLoading(false);
        });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [...Object.values(params || {})].map((i) => JSON.stringify(i)),
  );

  useEffect(() => {
    options.forEach((option) => {
      cacheOptionsRef.current.set(option[props.fieldNames?.value || 'value'], option);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [options]);

  const onSearch = useMemo(() => {
    const fetchOptions = (value: string) => {
      setOptions([]);
      searchValue.current = value;
      pageNum.current = 1;
      setLoading(true);
      latestRequest({ pageNum: pageNum.current, keyword: value, ...paramsRef.current })
        .then((res) => {
          if (searchValue.current === value) {
            setOptions(res?.result || []);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    };

    return debounce(fetchOptions, 1000);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Select
      allowClear
      placeholder="请选择"
      filterOption={false}
      showSearch
      options={options}
      notFoundContent={
        loading ? (
          <div className="flex justify-center items-center h-10">
            <Spin />
          </div>
        ) : undefined
      }
      {...(props.mode === 'multiple'
        ? {
            maxTagCount: 'responsive',
            maxTagPlaceholder: (omittedValues) =>
              omittedValues.length > 0 && (
                <Popover
                  classNames={{ body: '!p-0' }}
                  content={
                    <div
                      className="flex flex-wrap gap-2 max-w-[250px] p-2"
                      onMouseDown={(e) => e.stopPropagation()}
                      onClick={(e) => e.stopPropagation()}
                      onFocus={(e) => e.stopPropagation()}
                    >
                      {omittedValues.map((i) => (
                        <Tag
                          key={i.value}
                          className="mr-0"
                          closable
                          onClose={() => {
                            const resultValue = (props.value as any[]).filter(
                              (val) => val !== i.value,
                            );

                            props.onChange?.(
                              resultValue as ValueType,
                              resultValue.map((val) => cacheOptionsRef.current.get(val)!),
                            );
                          }}
                        >
                          {i.label}
                        </Tag>
                      ))}
                    </div>
                  }
                >
                  +{omittedValues.length}
                </Popover>
              ),
          }
        : {})}
      onSearch={onSearch}
      onPopupScroll={({ currentTarget }) => {
        if (
          currentTarget.scrollHeight - currentTarget.scrollTop - currentTarget.offsetHeight < 10 &&
          !loading &&
          options.length < total
        ) {
          pageNum.current += 1;
          setLoading(true);
          latestRequest({
            pageNum: pageNum.current,
            keyword: searchValue.current,
            ...paramsRef.current,
          })
            .then((res) => {
              setOptions((prev) => prev.concat(res?.result || []));
            })
            .finally(() => {
              setLoading(false);
            });
        }
      }}
      {...props}
    />
  );
}
