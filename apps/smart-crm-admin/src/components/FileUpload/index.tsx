import { useMemo } from 'react';
import { parserJSON } from '@pkg/utils';
import { Form, FormItemProps, UploadFile } from 'antd';
import { Rule } from 'antd/lib/form';
import InternalUpload, { InternalUploadProps } from './InternalUpload';

interface FileUploadProps {
  formItemProps?: FormItemProps;
  uploadProps?: InternalUploadProps;
}

const FileUpload: React.FC<FileUploadProps> = ({ formItemProps, uploadProps }) => {
  const format = uploadProps?.format;

  const { ...restUploadProps } = uploadProps || {};

  const getValueProps = useMemo((): FormItemProps['getValueProps'] => {
    const getFileList = (val: any) => {
      if (format === 'urlArray') {
        if (Array.isArray(val)) {
          return val.map((url) => ({
            uid: url,
            url,
            name: url,
            type: 'image/png',
            status: 'done',
          }));
        }
      } else if (format === 'url') {
        if (val) {
          return [
            {
              uid: val,
              url: val,
              name: val,
              type: 'image/png',
              status: 'done',
            },
          ];
        }
      } else {
        if (val) {
          return (format === 'json' ? parserJSON(val) : val)?.map((file: any) => ({
            uid: file.uid || file.fileKey,
            url: file.fileUrl,
            name: file.fileName,
            type: file.fileType,
            status: file.status || 'done',
            percent: file.percent,
            response: {
              key: file.fileKey,
              url: file.fileUrl,
            },
          }));
        }
      }
    };

    return (val) => ({
      fileList: getFileList(val),
    });
  }, [format]);

  const getValueFromEvent = useMemo((): FormItemProps['getValueFromEvent'] => {
    if (format === 'urlArray') {
      return undefined;
    }

    if (format === 'url') {
      return (e) => (Array.isArray(e) ? e[0] : e);
    }

    return (e) => {
      const fileList: UploadFile<{ url: string; key: string }>[] = Array.isArray(e)
        ? e
        : e.fileList;

      const result = fileList.map((i) => ({
        uid: i.uid,
        status: i.status,
        percent: i.percent,
        fileType: i.type || i.name?.split('.').pop(),
        fileName: i.name,
        fileUrl: i.response?.url,
        fileKey: i.response?.key,
      }));

      // JSON 时，空数组为 "[]" 字符串，form 必填校验中会被认为有值，所以长度为 0 时设置为 undefined
      return format === 'json' ? (result.length > 0 ? JSON.stringify(result) : undefined) : result;
    };
  }, [format]);

  return (
    <Form.Item
      label="附件"
      name="attachments"
      valuePropName="fileList"
      getValueProps={getValueProps}
      getValueFromEvent={getValueFromEvent}
      validateTrigger={['onChange', 'onSubmit']}
      {...formItemProps}
      rules={[
        ...(formItemProps?.rules || []),
        ...(format === 'url' || format === 'urlArray'
          ? []
          : ([
              {
                validateTrigger: 'onSubmit',
                validator: (_, val) => {
                  const value = format === 'json' ? parserJSON(val) : val;

                  if (Array.isArray(value) && value.some((i) => i.status === 'uploading')) {
                    return Promise.reject(new Error('文件正在上传中'));
                  }

                  return Promise.resolve();
                },
              },
            ] as Rule[])),
      ]}
    >
      <InternalUpload {...restUploadProps} />
    </Form.Item>
  );
};

export default FileUpload;
