import { useEffect, useRef, useState } from 'react';
import { EyeOutlined, UploadOutlined } from '@ant-design/icons';
import { docxFileType, FILE_UPLOAD_MAX_SIZE, pdfFileType } from '@src/common/constants';
import useUploadFile from '@src/hooks/useUploadFile';
import { downloadUrl, isImageUrl, previewPdf } from '@src/utils';
import { useLatest } from 'ahooks';
import { App, Image, Upload, UploadFile, UploadProps } from 'antd';
import { UploadRef } from 'antd/es/upload/Upload';
import { RcFile } from 'antd/lib/upload';
import { nanoid } from 'nanoid';
import { useEvent } from 'rc-util';
import DocxViewer from '../DocxViewer';
import VideoViewer from '../VideoViewer';

const DEFAULT_MAX_COUNT = 10;

export interface InternalUploadProps extends Omit<UploadProps, 'fileList' | 'onChange'> {
  /** 大小限制（M） */
  maxSize?: number;
  /**
   * 默认: fileList 为 UploadFile[]
   * json: fileList 为 JSON.stringify(value)
   * urlArray: fileList 为 string[]
   * url: fileList 为 string[]，但会在 Form.Item 里处理
   */
  format?: 'json' | 'urlArray' | 'url';
  fileList?: UploadFile<{ url: string; key: string }>[];
  onChange?: (value: string[] | UploadFile<{ url: string; key: string }>[]) => void;
}

const InternalUpload: React.FC<InternalUploadProps> = ({
  maxSize = FILE_UPLOAD_MAX_SIZE,
  maxCount = DEFAULT_MAX_COUNT,
  format,
  fileList: propsFileList,
  onChange: propsOnChange,
  ...props
}) => {
  const { message } = App.useApp();
  const [previewImgSrc, setPreviewImgSrc] = useState('');
  const [previewImgVisible, setPreviewImgVisible] = useState(false);
  const [docxViewerUrl, setDocxViewerUrl] = useState('');
  const [videoUrl, setVideoUrl] = useState('');
  const uploadRef = useRef<UploadRef>(null);

  const isStringFormat = format === 'urlArray' || format === 'url';

  const [stringFormatFileList, setStringFormatFileList] = useState(() =>
    isStringFormat ? propsFileList || [] : [],
  );
  const fileList = isStringFormat ? stringFormatFileList : propsFileList;

  const latestFileList = useLatest(fileList);
  const isMouseEnterRef = useRef(false);

  const { uploadFile } = useUploadFile();

  const filesLength = fileList?.length || 0;

  if (format === 'url') {
    maxCount = 1;
  }

  const showUpload = filesLength < maxCount;

  const handleBeforeUpload: UploadProps['beforeUpload'] = (file, files) => {
    if (filesLength + files.length > maxCount) {
      message.error(`不能超过 ${maxCount} 个文件`);

      return Upload.LIST_IGNORE;
    }

    if (file.size / 1024 / 1024 > maxSize) {
      message.error(`文件大小不能大于 ${maxSize} M`);

      return Upload.LIST_IGNORE;
    }

    const accept = props.accept?.trim();

    const acceptArray = accept ? accept.split(',') : [];

    // 检查后缀或者 fileType
    if (
      acceptArray.length &&
      !acceptArray.some((item) => file.name.endsWith(item) || item === file.type)
    ) {
      // 特殊情况
      if (acceptArray.includes('image/*') && file.type.startsWith('image')) {
        return true;
      }

      // 特殊情况
      if (acceptArray.includes('video/*') && file.type.startsWith('video')) {
        return true;
      }

      message.error('不支持上传该类型的文件');

      return Upload.LIST_IGNORE;
    }

    return true;
  };

  const isUndefinedOrEmptyArray = (value: any) => {
    return value === undefined || (Array.isArray(value) && !value.length);
  };

  const onChange = (value: UploadFile<{ url: string; key: string }>[]) => {
    const filterList = value.filter((i) => i.status !== 'error');

    if (isStringFormat) {
      setStringFormatFileList(filterList);

      const result = filterList
        .filter((i) => i.status === 'done')
        .map((i) => i.response?.url || i.url || '');

      // 防止上传第一张时触发必填检验提示（否则触发 onChange 但 value 是空的就会有提示）
      if (isUndefinedOrEmptyArray(result) && isUndefinedOrEmptyArray(propsFileList)) {
        return;
      }

      propsOnChange?.(result);
    } else {
      propsOnChange?.(filterList);
    }
  };

  const handlePaste = useEvent(async (e: ClipboardEvent) => {
    if (!isMouseEnterRef.current) {
      return;
    }

    const file = e.clipboardData?.items[0]?.getAsFile();

    if (!file) {
      return;
    }

    const canUpload = await handleBeforeUpload(file as RcFile, [file] as RcFile[]);

    if (canUpload !== true) {
      return;
    }

    const uid = nanoid();

    const rcFile: UploadFile<any> = {
      uid,
      status: 'uploading',
      percent: 0,
      name: file.name,
      type: file.type,
      size: file.size,
    };

    onChange?.([...(latestFileList.current || []), rcFile]);

    const setPercent = (percent: number) => {
      const newFileList = latestFileList.current || [];
      const index = newFileList.findIndex((i) => i.uid === uid);

      if (index > -1) {
        newFileList[index].percent = percent;
        onChange?.(newFileList);
      }
    };

    try {
      const response = await uploadFile(file, (percent) => setPercent(percent));

      const newFileList = latestFileList.current || [];
      const index = newFileList.findIndex((i) => i.uid === uid);

      if (index > -1) {
        newFileList[index].response = response;
        newFileList[index].status = 'done';
        newFileList[index].url = response.url;
      }

      onChange?.(newFileList);
    } catch (error) {
      const newFileList = latestFileList.current || [];
      const index = newFileList.findIndex((i) => i.uid === uid);

      if (index > -1) {
        newFileList[index].status = 'error';
        onChange?.(newFileList);
      }
    }
  });

  useEffect(() => {
    let uploadDom: HTMLElement | null = null;

    const onMouseEnter = () => {
      isMouseEnterRef.current = true;
    };

    const onMouseLeave = () => {
      isMouseEnterRef.current = false;
    };

    if (showUpload) {
      document.addEventListener('paste', handlePaste);
      uploadDom = uploadRef.current?.nativeElement?.querySelector('.ant-upload-select')!;

      uploadDom?.addEventListener('mouseenter', onMouseEnter);
      uploadDom?.addEventListener('mouseleave', onMouseLeave);
    }

    return () => {
      document.removeEventListener('paste', handlePaste);
      uploadDom?.removeEventListener('mouseenter', onMouseEnter);
      uploadDom?.removeEventListener('mouseleave', onMouseLeave);
    };
  }, [handlePaste, showUpload]);

  return (
    <>
      <Upload<{ key: string; url: string }>
        ref={uploadRef}
        maxCount={maxCount}
        fileList={fileList}
        multiple
        isImageUrl={isImageUrl}
        showUploadList={{
          showDownloadIcon: true,
          // 只有这些类型支持预览
          previewIcon: (file) =>
            file.type?.startsWith('image') ||
            file.type?.startsWith('video') ||
            [pdfFileType, docxFileType].includes(file.type!) ? (
              <EyeOutlined />
            ) : null,
        }}
        listType="picture-card"
        beforeUpload={handleBeforeUpload}
        customRequest={async ({ file, onSuccess, onError, onProgress }) => {
          try {
            const res = await uploadFile(file as File, (percent) => onProgress?.({ percent }));

            onSuccess?.(res);
          } catch (error) {
            message.error('上传失败，请重新上传');
            onError?.(error as Error);
          }
        }}
        onPreview={({ url: _url, status, type, response }) => {
          const url = response?.url || _url;

          if (status === 'done' && url) {
            if (type?.startsWith('image')) {
              setPreviewImgSrc(url);
              setPreviewImgVisible(true);
            } else if (type?.startsWith('video')) {
              setVideoUrl(url);
            } else if (type === pdfFileType) {
              previewPdf(url);
            } else if (type === docxFileType) {
              setDocxViewerUrl(url);
            } else {
              message.error('该类型文件不支持预览');
            }
          }
        }}
        onDownload={(file) => downloadUrl(file.url)}
        {...props}
        onChange={(info) => onChange?.(info.fileList)}
      >
        {showUpload && (
          <div className="flex flex-col justify-center items-center h-full w-full">
            <div className="text-gray-300 mb-2">
              鼠标移入 <br /> 即可粘贴
            </div>
            <UploadOutlined className="text-xl" />
          </div>
        )}
      </Upload>

      <Image
        wrapperStyle={{ display: 'none' }}
        src={previewImgSrc}
        preview={{ visible: previewImgVisible, onVisibleChange: setPreviewImgVisible }}
      />

      <DocxViewer open={!!docxViewerUrl} url={docxViewerUrl} onClose={() => setDocxViewerUrl('')} />

      <VideoViewer open={!!videoUrl} url={videoUrl} onClose={() => setVideoUrl('')} />
    </>
  );
};

export default InternalUpload;
