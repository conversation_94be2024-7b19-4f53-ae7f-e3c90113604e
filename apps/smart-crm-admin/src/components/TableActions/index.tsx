import React from 'react';
import { CaretDownOutlined, EllipsisOutlined } from '@ant-design/icons';
import { isMobile } from '@src/common/constants';
import { Button, Dropdown, Typography } from 'antd';

interface TableActionButtonType {
  key?: React.Key;
  label: React.ReactNode | ((isMobile: boolean) => React.ReactNode);
  danger?: boolean;
  disabled?: boolean;
  show?: boolean;
  onClick?: () => void;
}

interface TableActionsProps {
  shortcuts?: TableActionButtonType[];
  moreItems?: TableActionButtonType[];
}

const TableActions: React.FC<TableActionsProps> = ({ shortcuts = [], moreItems = [] }) => {
  const mergedMoreItems = (isMobile ? [...shortcuts, ...moreItems] : moreItems).reduce<
    (Omit<TableActionButtonType, 'show'> & { label?: React.ReactNode; key: React.Key })[]
  >(
    (total, { show, ...item }, index) =>
      show === undefined || show
        ? total.concat({
            key: index,
            ...item,
            label: typeof item.label === 'function' ? item.label(isMobile) : item.label,
          })
        : total,
    [],
  );

  if (isMobile) {
    if (mergedMoreItems.length === 0) {
      return '-';
    }

    return (
      <div className="flex justify-center gap-2">
        <Dropdown
          trigger={['click']}
          placement="bottom"
          menu={{
            items: mergedMoreItems,
          }}
        >
          <Button icon={<EllipsisOutlined />} size="small" />
        </Dropdown>
      </div>
    );
  }

  const mergedShortcuts = shortcuts.reduce<TableActionButtonType[]>(
    (total, { show, ...item }) => (show === undefined || show ? total.concat(item) : total),
    [],
  );

  if (mergedShortcuts.length + mergedMoreItems.length === 0) {
    return '-';
  }

  return (
    <div className="flex justify-center gap-2">
      {mergedShortcuts.map((i, index) => {
        if (typeof i.label === 'function') {
          return <React.Fragment key={index}>{i.label(isMobile)}</React.Fragment>;
        }

        return (
          <Typography.Link
            key={index}
            type={i.danger ? 'danger' : undefined}
            disabled={i.disabled}
            onClick={i.onClick}
          >
            {i.label}
          </Typography.Link>
        );
      })}
      {mergedMoreItems.length > 0 && (
        <Dropdown
          trigger={['click']}
          placement="bottom"
          menu={{
            items: mergedMoreItems,
          }}
        >
          <a>
            更多 <CaretDownOutlined />
          </a>
        </Dropdown>
      )}
    </div>
  );
};

export default TableActions;
