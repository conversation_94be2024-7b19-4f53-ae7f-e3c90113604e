type Node = { id: number; parentId: number; name: string; code: string };

type NodeWithChild = Node & { children: NodeWithChild[] };

type RegionItemType = {
  label: string;
  value: string;
  children?: RegionItemType[];
};

/**
 * 转换省市区数据
 * @param nodes 后端返回数据
 * @param region 是否需要区的数据
 * @returns 级联选择需要的数据格式
 */
export function generateRegionData(nodes: Node[], level: 1 | 2 | 3 | 4 = 3) {
  const tree: Record<string, NodeWithChild> = {};
  const map: Record<string, NodeWithChild> = {};

  nodes.forEach((node) => {
    map[node.id] = { ...node, children: [] };
  });

  nodes.forEach((node) => {
    if (node.parentId) {
      if (map[node.parentId]) {
        map[node.parentId].children.push(map[node.id]);
      }
    } else {
      tree[node.id] = map[node.id];
    }
  });

  const loop = (children: NodeWithChild[], currentLevel: number): RegionItemType[] | undefined => {
    if (level >= currentLevel) {
      return children.map((i) => ({
        label: i.name,
        value: i.code,
        children: loop(i.children, currentLevel + 1),
      }));
    }
  };

  const regionData = Object.values(tree).map((node) => ({
    label: node.name,
    value: node.code,
    children: loop(node.children, 2),
  }));

  return regionData;
}
