import { useMemo } from 'react';
import useDistricts from '@src/hooks/useDistricts';
import { DistrictDTO } from '@src/services/common/type';
import { App, Cascader, Popover, Tag } from 'antd';
import { CascaderAutoProps, DefaultOptionType } from 'antd/lib/cascader';
import classNames from 'classnames';
import { isEqual } from 'lodash-es';
import styles from './index.module.scss';
import { generateRegionData } from './utils';

export type RegionCascaderProps<
  OptionType extends DefaultOptionType = DefaultOptionType,
  ValueField extends keyof OptionType = keyof OptionType,
> = CascaderAutoProps<OptionType, ValueField> & {
  // 省、市、区
  regionLevel?: 1 | 2 | 3 | 4;
  unlimited?: {
    // 选项无限制，展示全部
    options?: boolean;
    // 值发生改变时无限制，不限制同一城市或同一区县
    onChange?: boolean;
  };
  transformDistricts?: (data: DistrictDTO[]) => DistrictDTO[];
};

function RegionCascader<
  OptionType extends DefaultOptionType = DefaultOptionType,
  ValueField extends keyof OptionType = keyof OptionType,
>({
  regionLevel = 3,
  unlimited,
  transformDistricts,
  ...props
}: RegionCascaderProps<OptionType, ValueField>) {
  const { message } = App.useApp();

  const streets = regionLevel === 4;

  const { data, loading } = useDistricts({
    streets,
  });

  const options = useMemo(
    () =>
      generateRegionData(
        transformDistricts ? transformDistricts(data || []) : data || [],
        regionLevel,
      ),
    [data, regionLevel, transformDistricts],
  );

  const getOptions = () => {
    if (unlimited?.options) {
      return options;
    }

    if (props.multiple) {
      if (regionLevel === 1) {
        return options;
      } else {
        return options.map((item) => ({
          ...item,
          disableCheckbox: true,
        }));
      }
    } else {
      return options;
    }
  };

  return (
    <Cascader
      allowClear
      showSearch
      maxTagCount="responsive"
      loading={loading}
      options={getOptions() as OptionType[]}
      // 数据是 4 级，但下拉列表只有 3 级，兼容展示
      displayRender={(label) =>
        label
          .map((item) => {
            if (Number.isNaN(Number(item))) {
              return item;
            }

            return data?.find((i) => i.code === item)?.name;
          })
          .join(' / ')
      }
      maxTagPlaceholder={(omittedOptions) =>
        omittedOptions.length > 0 && (
          <Popover
            destroyOnHidden
            classNames={{
              body: 'max-h-[400px] overflow-auto',
            }}
            content={
              <div className="flex gap-2 flex-wrap max-w-[220px]">
                {omittedOptions.map((option) => (
                  <Tag
                    key={option.value}
                    className="mr-0"
                    closable
                    onClose={() => {
                      props.onChange?.(
                        props.value?.filter(
                          (i: any) => !isEqual(i, (option as any).valueCells),
                        ) as any,
                        [],
                      );
                    }}
                  >
                    {option.label}
                  </Tag>
                ))}
              </div>
            }
          >
            +{omittedOptions.length}
          </Popover>
        )
      }
      {...props}
      onChange={(val: any, option: any) => {
        if (unlimited?.onChange) {
          props.onChange?.(val, option);
        } else {
          if (props.multiple && [2, 3, 4].includes(regionLevel as number)) {
            const provinceUniqCodeArr = [
              ...new Set((val as string[][]).map((i: string[]) => i[0])),
            ];

            if (provinceUniqCodeArr.length > 1) {
              message.error('只能选择同一省份里的地区');
            } else {
              props.onChange?.(val, option);
            }
          } else {
            props.onChange?.(val, option);
          }
        }
      }}
      popupClassName={classNames(styles.cascaderDropdown, props.popupClassName)}
    />
  );
}

export default RegionCascader;
