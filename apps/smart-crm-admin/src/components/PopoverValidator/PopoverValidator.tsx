import React, { useEffect, useState } from 'react';
import { Form, Popover } from 'antd';

// 解决有校验时输入时会闪，因为每次触发校验时 errors 会先变成空
function useDelayStatus(errors: React.ReactNode[]) {
  const [delayErrors, setDelayErrors] = useState(errors);

  useEffect(() => {
    if (errors.length) {
      setDelayErrors(errors);
    } else {
      const timeout = setTimeout(() => {
        setDelayErrors(errors);
      });

      return () => {
        clearTimeout(timeout);
      };
    }
  }, [errors]);

  return delayErrors;
}

const PopoverValidator = ({ children }: { children?: React.ReactNode }) => {
  const { errors } = Form.Item.useStatus();
  const delayErrors = useDelayStatus(errors);

  return (
    <Popover
      content={<span className="text-red-400">{delayErrors[0]}</span>}
      placement="top"
      destroyOnHidden
      open={delayErrors.length > 0}
    >
      {children}
    </Popover>
  );
};

export default PopoverValidator;
