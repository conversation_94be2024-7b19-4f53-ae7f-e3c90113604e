import { Popover, Select, SelectProps, Tag } from 'antd';
import PopoverValidator from './PopoverValidator';

interface PopoverValidatorSelectProps extends SelectProps {
  popover?: boolean;
}

export const PopoverValidatorSelect: React.FC<PopoverValidatorSelectProps> = ({
  popover = true,
  ...props
}) => {
  const selectNode = (
    <Select
      {...(props.mode
        ? {
            maxTagPlaceholder: (omittedOptions) =>
              omittedOptions.length > 0 && (
                <Popover
                  classNames={{
                    body: 'max-h-[400px] overflow-auto',
                  }}
                  content={
                    <div className="flex gap-2 flex-wrap max-w-[220px]">
                      {omittedOptions.map((option) => (
                        <Tag
                          key={option.value}
                          className="mr-0"
                          closable
                          onClose={() => {
                            props.onChange?.(
                              props.value.filter((i: any) => i !== option.value),
                              [],
                            );
                          }}
                        >
                          {option.label}
                        </Tag>
                      ))}
                    </div>
                  }
                >
                  +{omittedOptions.length}
                </Popover>
              ),
          }
        : {})}
      {...props}
    />
  );

  if (!popover) {
    return selectNode;
  }

  return <PopoverValidator>{selectNode}</PopoverValidator>;
};
