import img403 from '@src/assets/403.png';
import rocketImg from '@src/assets/rocket.svg';
import { Button, Result } from 'antd';
import { useRouteError } from 'react-router-dom';

export default function ErrorBoundary() {
  const error = useRouteError();

  if (
    error instanceof Error &&
    error.message.startsWith('Failed to fetch dynamically imported module')
  ) {
    return (
      <Result
        title={<span className="text-xl">正在刷新页面升级系统...</span>}
        icon={
          <div className="flex justify-center">
            <img
              src={rocketImg}
              alt=""
              style={{ height: '50vh' }}
              onLoad={() => {
                setTimeout(() => {
                  window.location.reload();
                }, 1000);
              }}
            />
          </div>
        }
      />
    );
  }

  return (
    <Result
      status="error"
      icon={<img src={img403} alt="" className="m-auto w-[160px]" />}
      title="发生错误，请保存错误信息并联系管理员"
      subTitle={
        <>
          <div>地址：{window.location.href}</div>
          <div>错误信息：{(error as Error)?.message || ''}</div>
        </>
      }
      extra={
        <Button type="primary" onClick={() => window.location.reload()}>
          刷新页面
        </Button>
      }
    />
  );
}
