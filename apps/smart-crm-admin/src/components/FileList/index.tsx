import { useState } from 'react';
import { DeleteOutlined, DownloadOutlined, EyeOutlined, FileTwoTone } from '@ant-design/icons';
import { previewPdf } from '@src/utils';
import { Button, Image, message, Popconfirm, Space, Typography } from 'antd';
import VideoViewer from '../VideoViewer';

type DataItem = {
  fileUrl: string;
  fileType: string;
  fileName: string;
};

interface FileListProps {
  data?: DataItem[];
  listType?: 'text' | 'picture';
  allowPreviewVideo?: boolean;
  removeFile?: (e: any) => void;
}

const FileList: React.FC<FileListProps> = ({
  data,
  listType = 'picture',
  allowPreviewVideo = true,
  removeFile,
}) => {
  const [videoUrl, setVideoUrl] = useState('');

  if (!data?.length) {
    return null;
  }

  const handlePreview = (item: DataItem) => {
    if (item.fileType === 'application/pdf') {
      previewPdf(item.fileUrl);
    }

    if (item.fileType.startsWith('video')) {
      if (allowPreviewVideo) {
        setVideoUrl(item.fileUrl);
      } else {
        message.info('不支持预览');
      }
    }
  };

  const renderPreview = (item: DataItem) => {
    if (item.fileType.startsWith('image')) {
      return <Image src={item.fileUrl} height={50} preview={{ mask: <EyeOutlined /> }} />;
    }

    if (item.fileType.startsWith('audio')) {
      return <audio src={item.fileUrl} controls className="h-8" />;
    }

    return (
      <div className="h-[50px] w-[50px] flex justify-center items-center rounded border relative">
        <FileTwoTone className="text-[30px]" />
        {(item.fileType === 'application/pdf' || item.fileType.startsWith('video')) && (
          <div
            className="absolute flex justify-center items-center w-full h-full bg-black/50 opacity-0 hover:opacity-100 cursor-pointer"
            onClick={() => {
              handlePreview(item);
            }}
          >
            <EyeOutlined className="text-white" />
          </div>
        )}
      </div>
    );
  };

  const ListPicture = () => (
    <div className="flex gap-2 flex-wrap py-2">
      {data.map((item, index) => (
        <div key={index} className="flex p-2 bg-white rounded">
          {renderPreview(item)}
          {!item.fileType.startsWith('audio') && (
            <div className="ml-3 flex flex-col justify-between items-end">
              <Typography.Text ellipsis={{ tooltip: true }} style={{ maxWidth: 150 }}>
                {item.fileName}
              </Typography.Text>
              <Button type="text" icon={<DownloadOutlined />} size="small" href={item.fileUrl} />
            </div>
          )}
        </div>
      ))}
    </div>
  );

  const ListText = () => (
    <div className="space-y-2">
      {data.map((item, index) => (
        <div key={index} className="flex items-center overflow-hidden space-x-2">
          {renderPreview(item)}
          {!item.fileType.startsWith('audio') && (
            <>
              <Typography.Text
                ellipsis={{ tooltip: true }}
                className="flex-1 text-blue-500 cursor-pointer"
                onClick={() => {
                  handlePreview(item);
                }}
              >
                {item.fileName}
              </Typography.Text>
              <Space>
                <Button type="text" icon={<DownloadOutlined />} size="small" href={item.fileUrl} />
                <Popconfirm
                  title="删除"
                  description="确认要删除该合同文件吗？"
                  onConfirm={() => {
                    removeFile && removeFile(item);
                  }}
                >
                  <Button type="text" icon={<DeleteOutlined />} size="small" />
                </Popconfirm>
              </Space>
            </>
          )}
        </div>
      ))}
    </div>
  );

  const listTypeMap = {
    picture: ListPicture,
    text: ListText,
  };

  const ListContent = listTypeMap[listType] || ListPicture;

  return (
    <>
      <ListContent />

      <VideoViewer open={!!videoUrl} url={videoUrl} onClose={() => setVideoUrl('')} />
    </>
  );
};

export default FileList;
