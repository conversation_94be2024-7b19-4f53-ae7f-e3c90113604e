import { useEffect, useMemo, useRef, useState } from 'react';
import { getShopList } from '@src/services/shop/list';
import { ShopListItemDTO } from '@src/services/shop/list/type';
import { useRequest } from 'ahooks';
import { App, Button, Select, SelectProps, Spin, Tooltip } from 'antd';
import { debounce } from 'lodash-es';

interface ShopSelectProps extends SelectProps {
  showRefresh?: boolean;
  onRefresh?: () => Promise<void>;
}

const ShopSelect: React.FC<ShopSelectProps> = ({ showRefresh, onRefresh, ...props }) => {
  const { message } = App.useApp();
  const pageNum = useRef(1);
  const searchValue = useRef('');
  const [shopOptions, setShopOptions] = useState<ShopListItemDTO[]>([]);
  const [refreshLoading, setRefreshLoading] = useState(false);

  const {
    data,
    runAsync: getShopOptions,
    loading,
    cancel,
  } = useRequest(getShopList, { manual: true });

  useEffect(() => {
    getShopOptions({ pageNum: pageNum.current, pageSize: 20 }).then((res) => {
      setShopOptions(res.result);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onSearch = useMemo(() => {
    const fetchOptions = (shopId: string) => {
      cancel();
      setShopOptions([]);
      searchValue.current = shopId;
      pageNum.current = 1;
      getShopOptions({ pageNum: pageNum.current, pageSize: 20, shopId }).then((res) => {
        setShopOptions(res.result);
      });
    };

    return debounce(fetchOptions, 1000);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="flex items-center gap-2">
      <Select
        allowClear
        className="flex-1"
        placeholder="输入门店编号进行搜索"
        filterOption={false}
        showSearch
        fieldNames={{ label: 'shopId', value: 'shopId' }}
        options={shopOptions}
        notFoundContent={
          loading ? (
            <div className="flex justify-center items-center h-10">
              <Spin />
            </div>
          ) : undefined
        }
        onSearch={onSearch}
        onPopupScroll={({ currentTarget }) => {
          if (
            currentTarget.scrollHeight - currentTarget.scrollTop - currentTarget.offsetHeight <
              10 &&
            !loading &&
            data?.total &&
            shopOptions.length < data.total
          ) {
            pageNum.current += 1;
            getShopOptions({
              pageNum: pageNum.current,
              pageSize: 20,
              shopId: searchValue.current,
            }).then((res) => {
              setShopOptions((prev) => prev.concat(res.result));
            });
          }
        }}
        {...props}
      />
      {showRefresh && (
        <Tooltip title="重新获取门店最新数据">
          <Button
            className="p-0"
            type="link"
            disabled={refreshLoading}
            onClick={async () => {
              setRefreshLoading(true);

              try {
                await onRefresh?.();
                message.success('更新成功');
              } catch (error) {}

              setRefreshLoading(false);
            }}
          >
            刷新
          </Button>
        </Tooltip>
      )}
    </div>
  );
};

export default ShopSelect;
