import React, { useEffect, useMemo, useRef, useState } from 'react';
import { parserJSON } from '@pkg/utils';
import EditableAttachment from '@src/components/Editable/EditableAttachment';
import { ValueType } from '@src/components/ETable';
import { getOperationLog } from '@src/services/common';
import { ActivityItem, OperationTypeEnum } from '@src/services/common/type';
import { useRequest } from 'ahooks';
import { Empty, Skeleton, Timeline } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import InfiniteScroll from 'react-infinite-scroll-component';

const operationTypeLabelMap: Partial<Record<OperationTypeEnum, string>> = {
  [OperationTypeEnum.CREATE_CUSTOMER]: '创建客户',
  [OperationTypeEnum.CUSTOMER_INFO_CHANGE]: '变更信息',
  [OperationTypeEnum.CREATE_PAYMENT]: '创建打款',
  [OperationTypeEnum.PAYMENT_INFO_CHANGE]: '变更信息',
  [OperationTypeEnum.CREATE_FISSION_APPLY]: '创建裂变申请',
  [OperationTypeEnum.FISSION_APPLY_INFO_CHANGE]: '变更信息',
};

const getTimeLineItem = (item: ActivityItem): React.ReactNode => {
  const getInfoChangeNode = () => (
    <>
      <p>
        {item.userName}更新了{item.updateFieldName}
      </p>
      <div className="flex">
        更新前：
        <div className="flex-1">
          {item.valueType === ValueType.ATTACHMENT ? (
            parserJSON(item.updateBefore)?.length ? (
              <EditableAttachment value={item.updateBefore} />
            ) : null
          ) : (
            item.updateBefore
          )}
        </div>
      </div>
      <div className="flex">
        更新后：
        <div className="flex-1">
          {item.valueType === ValueType.ATTACHMENT ? (
            parserJSON(item.updateAfter)?.length ? (
              <EditableAttachment value={item.updateAfter} />
            ) : null
          ) : (
            item.updateAfter
          )}
        </div>
      </div>
    </>
  );

  const mapObj: Partial<Record<OperationTypeEnum, () => React.ReactNode>> = {
    [OperationTypeEnum.CREATE_CUSTOMER]: () => (
      <>
        <p>{item.userName}创建了客户</p>
      </>
    ),
    [OperationTypeEnum.CUSTOMER_INFO_CHANGE]: getInfoChangeNode,
    [OperationTypeEnum.CREATE_PAYMENT]: () => (
      <>
        <p>{item.userName}创建了打款</p>
      </>
    ),
    [OperationTypeEnum.PAYMENT_INFO_CHANGE]: getInfoChangeNode,
    [OperationTypeEnum.CREATE_FISSION_APPLY]: () => (
      <>
        <p>{item.userName}创建了裂变申请</p>
      </>
    ),
    [OperationTypeEnum.FISSION_APPLY_INFO_CHANGE]: getInfoChangeNode,
    [OperationTypeEnum.CREATE_FRANCHISE_AREA_QUOTA]: () => (
      <>
        <p>{item.userName}开启了区域名额</p>
      </>
    ),
    [OperationTypeEnum.FRANCHISE_AREA_QUOTA_INFO_CHANGE]: getInfoChangeNode,
  };

  return mapObj[item.operationType]?.();
};

interface OperationLogProps {
  id: number;
  businessType: 'CUSTOMER' | 'PAYMENT' | 'FISSION_APPLY' | 'FRANCHISE_AREA_QUOTA';
  className?: string;
  pageSize?: number;
}

export interface OperationLogRef {
  reload: () => void;
}

const OperationLog = React.forwardRef<OperationLogRef, OperationLogProps>(
  ({ id, businessType, className, pageSize = 5 }, ref) => {
    const pageNum = useRef(1);
    const [data, setData] = useState<ActivityItem[]>([]);

    const {
      data: { total } = { total: 0 },
      runAsync: getLog,
      loading,
    } = useRequest(getOperationLog, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      reload: () => {
        pageNum.current = 1;
        getData('reset');
      },
    }));

    const getData = async (type: 'reset' | 'concat') => {
      const res = await getLog({
        pageNum: pageNum.current,
        pageSize,
        businessId: id,
        businessType,
      });

      if (type === 'reset') {
        setData(res.result);
      } else {
        setData((prev) => prev.concat(res.result));
      }
    };

    useEffect(() => {
      getData('reset');
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // 下拉加载
    const loadMoreData = async () => {
      if (loading) {
        return;
      }

      pageNum.current += 1;

      getData('concat');
    };

    // 根据日期进行分组
    const groupData = useMemo(() => {
      const result: Record<string, ActivityItem[]> = {};

      data.forEach((item) => {
        const date = dayjs(item.activityTime).format('YYYY-MM-DD');

        if (result[date]) {
          result[date].push(item);
        } else {
          result[date] = [item];
        }
      });

      return result;
    }, [data]);

    if (!data.length) {
      if (loading) {
        return <Skeleton paragraph={{ rows: 5 }} title={false} active />;
      }

      return <Empty />;
    }

    return (
      <div className={classNames('overflow-auto h-[500px]', className)} id="scrollableDiv">
        <InfiniteScroll
          dataLength={data.length}
          next={loadMoreData}
          hasMore={data.length < total}
          loader={<Skeleton paragraph={{ rows: 3 }} title={false} active className="px-6" />}
          scrollableTarget="scrollableDiv"
          // 去掉 overflow，下面的 sticky 才有效
          style={{ overflow: undefined }}
        >
          {Object.keys(groupData).map((date) => (
            <div key={date}>
              <p className="mb-2 sticky top-0 bg-white z-10">{date}</p>
              <Timeline
                items={groupData[date].map((item) => ({
                  children: (
                    <>
                      <p className="mb-1">{dayjs(item.activityTime).format('HH:mm:ss')}</p>
                      <div className="px-4 py-2 rounded bg-[#fafafa]">
                        <p className="text-gray-500 mb-1">
                          {operationTypeLabelMap[item.operationType]}
                        </p>
                        {getTimeLineItem(item)}
                      </div>
                    </>
                  ),
                }))}
              />
            </div>
          ))}
        </InfiniteScroll>
      </div>
    );
  },
);

export default OperationLog;
