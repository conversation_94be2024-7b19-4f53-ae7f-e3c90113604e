import { useMemo } from 'react';
import useAllUsers from '@src/hooks/useAllUsers';
import { IdentityTypeEnum } from '@src/services/mini-program/identity/type';
import { Popover, Select, SelectProps, Tag } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';

interface Option {
  id: number;
  name: string;
  identityType: IdentityTypeEnum;
}

export interface UserSelectProps extends SelectProps {
  transformOptions?: (options: Option[]) => Record<string, any>[];
}

const UserSelect: React.FC<UserSelectProps> = ({ transformOptions, ...props }) => {
  const { data: originData, loading } = useAllUsers();

  const data = useMemo(() => (originData || []).filter((i) => i.status === 'NORMAL'), [originData]);

  return (
    <Select
      showSearch
      allowClear
      loading={loading}
      optionFilterProp="name"
      options={(transformOptions ? transformOptions(data) : data) as DefaultOptionType[]}
      fieldNames={{ label: 'name', value: 'id' }}
      placeholder="请选择人员"
      // 回显时，若人员是 DISABLED 或 DELETED，会显示不出名称，这里做处理，使得可以显示出名称
      labelRender={(option) => option.label || originData?.find((i) => i.id === option.value)?.name}
      maxTagPlaceholder={(omittedOptions) =>
        omittedOptions.length > 0 && (
          <Popover
            classNames={{
              body: 'max-h-[400px] overflow-auto',
            }}
            content={
              <div className="flex gap-2 flex-wrap max-w-[220px]">
                {omittedOptions.map((option) => (
                  <Tag
                    key={option.value}
                    className="mr-0"
                    closable
                    onClose={() => {
                      props.onChange?.(
                        props.value.filter((i: any) => i !== option.value),
                        [],
                      );
                    }}
                  >
                    {option.label}
                  </Tag>
                ))}
              </div>
            }
          >
            +{omittedOptions.length}
          </Popover>
        )
      }
      {...props}
    />
  );
};

export default UserSelect;
