import { getBasename } from '@ice/stark-app';
import { wrapCreateBrowserRouterV6 } from '@sentry/react';
import loadingImg from '@src/assets/loading.gif';
import { App as AntdApp, ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { themeConfig } from './common/theme';
import { CheckVersion } from './components';
import { routes } from './routes';

const originConsoleError = console.error;

console.error = (...rest) => {
  if (
    typeof rest[0] === 'string' &&
    [
      'Warning: findDOMNode is deprecated',
      "Warning: Can't perform a React state update on a component that hasn't mounted yet",
    ].some((i) => rest[0].startsWith(i))
  ) {
    return;
  }

  originConsoleError(...rest);
};

dayjs.locale('zh-cn');

const sentryCreateBrowserRouter = wrapCreateBrowserRouterV6(createBrowserRouter);
const router = sentryCreateBrowserRouter(routes, { basename: getBasename() });

function App() {
  return (
    <ConfigProvider theme={themeConfig} locale={zhCN}>
      <AntdApp>
        <CheckVersion />
        <RouterProvider
          router={router}
          fallbackElement={
            <div className="h-full w-full flex flex-col items-center justify-center">
              <img className="size-20" src={loadingImg} alt="" />
              <span className="mt-3 text-[#86909C]">加载中...</span>
            </div>
          }
        />
      </AntdApp>
    </ConfigProvider>
  );
}

export default App;
