import React, { useRef, useState } from 'react';
import { evaluationAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { ButtonGroup } from '@src/components';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import BusinessDetailDrawer from '@src/pages/business-opportunity/components/BusinessDetailDrawer';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import InterviewNoticeDetailDrawer from '@src/pages/process/interview-notice/components/InterviewNoticeDetailDrawer';
import VisitDetailDrawer from '@src/pages/visit/components/VisitDetailDrawer';
import { PermissionsMap, usePermission } from '@src/permissions';
import { confirmVisit, getAppointmentRecordDetail } from '@src/services/appointment-record';
import {
  AppointmentRecordDTO,
  VisitPurposeEnum,
  VisitResultsEnum,
} from '@src/services/appointment-record/type';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { EvaluationRejectReasonEnum, SuggestionEnum } from '@src/services/visit/type';
import useUserStore from '@src/store/useUserStore';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Card, Drawer } from 'antd';
import ConfirmVisitModal from './ConfirmVisitModal';
import CreateEditAppointmentRecordModal from './CreateEditAppointmentRecordModal';

interface AppointmentRecordDetailDrawerProps {
  getId: () => number | null | undefined;
  onUpdate?: () => void;
}

const AppointmentRecordDetailDrawer = React.forwardRef<
  ModalRef,
  AppointmentRecordDetailDrawerProps
>(({ getId, onUpdate, ...drawerProps }, ref) => {
  const { modal, message } = App.useApp();
  const [open, setOpen] = useState(false);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const businessOpportunityDrawerRef = useRef<ModalRef>(null);
  const confirmVisitModalRef = useRef<ModalRef>(null);
  const visitDetailDrawerRef = useRef<ModalRef>(null);
  const createEditAppointmentRecordModalRef = useRef<ModalRef>(null);
  const customerIdRef = useRef<number | null>(null);
  const interviewDrawerRef = useRef<ModalRef>(null);

  const id = getId()!;

  const checkPermission = usePermission();
  const {
    user: { shUserId },
  } = useUserStore();
  const { data: users } = useAllUsers({ ready: open });
  const { data, loading, refresh } = useRequest(() => getAppointmentRecordDetail(id), {
    ready: open,
  });

  React.useImperativeHandle(ref, () => ({
    open: () => setOpen(true),
    close: () => setOpen(false),
  }));

  const baseInfoItems: DescriptionFieldType<AppointmentRecordDTO>[] = [
    {
      label: '预约编号',
      field: 'code',
    },
    {
      label: '商机名称',
      field: 'bizOpportunityName',
      children: (
        <a onClick={() => businessOpportunityDrawerRef.current?.open()}>
          {data?.bizOpportunityName}
        </a>
      ),
    },
    {
      label: '预约时间',
      field: 'scheduleTime',
    },
    {
      label: '到访目的',
      field: 'visitPurpose',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(VisitPurposeEnum),
      },
    },
    {
      label: '开店计划表',
      field: 'openStoreScheduleAttachments',
      valueType: ValueType.ATTACHMENT,
    },
    {
      label: '商机负责人',
      field: 'bizDirectUserName',
    },
    {
      label: '面审负责人',
      field: 'interviewerName',
    },
    {
      label: '意向区域',
      field: 'intentionRegionCode',
      valueType: ValueType.REGION,
      fieldProps: {
        regionLevel: 4,
      },
    },
    {
      label: '意向省份',
      field: 'intentionProvinceCode',
      valueType: ValueType.REGION,
      fieldProps: {
        regionLevel: 1,
      },
    },
    {
      label: '所属大区',
      field: 'belongWarZone',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(BelongWarZoneEnum),
      },
    },
    {
      label: '运营负责人',
      field: 'operationsCustomerName',
      hidden: data?.visitPurpose !== VisitPurposeEnum.加盟面审,
      children: (
        <a
          onClick={() => {
            customerIdRef.current = data?.operationsCustomerId!;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {data?.operationsCustomerName}
        </a>
      ),
    },
    {
      label: '合伙人',
      field: 'partnerCustomerName',
      hidden: data?.visitPurpose !== VisitPurposeEnum.加盟面审,
      children: (
        <a
          onClick={() => {
            customerIdRef.current = data?.partnerCustomerId!;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {data?.partnerCustomerName}
        </a>
      ),
    },
    {
      label: '意向点位',
      field: 'intentionPositions',
      children: (
        <div>
          {data?.intentionPositions?.map((i, index) => (
            <p key={index}>
              {index + 1}. {i.name}（经度：{i.longitude}；纬度：{i.latitude}）
            </p>
          ))}
        </div>
      ),
    },
    {
      label: '到访结果',
      field: 'visitResults',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(VisitResultsEnum),
      },
    },
    {
      label: '到访记录编号',
      field: 'visitRecordCode',
      children: <a onClick={() => visitDetailDrawerRef.current?.open()}>{data?.visitRecordCode}</a>,
    },
    {
      label: '到访日期时间',
      field: 'visitTime',
    },
    {
      label: '到访信息是否一致',
      field: 'infoUnanimous',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: yesOrNoOptions,
      },
    },
    // {
    //   label: '是否通过线上面审',
    //   field: 'onlineReviewStatus',
    //   valueType: ValueType.SINGLE,
    //   fieldProps: {
    //     options: yesOrNoOptions,
    //   },
    // },
    {
      label: '面谈前告知函',
      field: 'interviewNoticeCode',
      children: (
        <a onClick={() => interviewDrawerRef.current?.open()}>{data?.interviewNoticeCode}</a>
      ),
    },
    {
      label: '面审建议',
      field: 'suggestion',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(SuggestionEnum),
      },
    },
    {
      label: '面审最终结果',
      field: 'auditStatus',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: evaluationAuditStatusOptions,
      },
    },
    {
      label: '未通过原因',
      field: 'rejectReasons',
      valueType: ValueType.MULTIPLE,
      fieldProps: {
        options: enum2Options(EvaluationRejectReasonEnum),
      },
    },
    {
      label: '顾问描述客户简介',
      field: 'description',
    },
  ];

  return (
    <Drawer
      title="预约记录详情"
      width={800}
      open={open}
      push={false}
      destroyOnHidden
      onClose={() => setOpen(false)}
      {...drawerProps}
    >
      <Card loading={loading}>
        <div className="flex justify-between">
          <div className="flex flex-col gap-2">
            <p>
              客户名称：
              <a
                onClick={() => {
                  customerIdRef.current = data?.customerId!;
                  customerDetailDrawerRef.current?.open();
                }}
              >
                {data?.customerName}
              </a>
            </p>
            <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
            <p>创建时间：{data?.createTime}</p>
            <p>更新人：{users?.find((i) => i.id === data?.updateUserId)?.name}</p>
            <p>更新时间：{data?.updateTime}</p>
          </div>
          <ButtonGroup
            items={[
              {
                label: '编辑',
                onClick: () => createEditAppointmentRecordModalRef.current?.open(),
              },
              {
                label: '取消预约',
                show:
                  !data?.visitResults && checkPermission(PermissionsMap.AppointmentRecordCancel),
                onClick: () =>
                  modal.confirm({
                    title: '取消预约',
                    content: '确定取消预约吗？',
                    onOk: async () => {
                      await confirmVisit({ id, visitResults: VisitResultsEnum.面审改期 });
                      message.success('取消预约成功');
                      refresh();
                      onUpdate?.();
                    },
                  }),
              },
              {
                label: '确认到访结果',
                show: !data?.visitResults && shUserId === data?.interviewer,
                onClick: () => confirmVisitModalRef.current?.open(),
              },
            ]}
          />
        </div>
      </Card>

      <Card title="详细资料" loading={loading} className="mt-5">
        {renderDescriptions(baseInfoItems, data)}
      </Card>

      <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />
      <BusinessDetailDrawer
        ref={businessOpportunityDrawerRef}
        getId={() => data?.bizOpportunityId}
      />
      <VisitDetailDrawer ref={visitDetailDrawerRef} getId={() => data?.visitRecordId} />
      <ConfirmVisitModal
        ref={confirmVisitModalRef}
        getId={() => id}
        onSuccess={() => {
          refresh();
          onUpdate?.();
        }}
      />
      <CreateEditAppointmentRecordModal
        ref={createEditAppointmentRecordModalRef}
        getId={() => id}
        onSuccess={() => {
          refresh();
          onUpdate?.();
        }}
      />
      <InterviewNoticeDetailDrawer
        ref={interviewDrawerRef}
        getCode={() => data?.interviewNoticeCode!}
      />
    </Drawer>
  );
});

export default AppointmentRecordDetailDrawer;
