import React, { useEffect, useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { ChooseMapPoints, FileUpload, ImportFileFormItem } from '@src/components';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useProvinceWarZoneMap from '@src/hooks/useProvinceWarZoneMap';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import {
  createAppointmentRecord,
  downloadAppointmentRecordExcelTemplate,
  getAppointmentRecordDetail,
  getAppointmentRecordSchedule,
  getBusinessOpportunityByCustomerId,
  importAppointmentRecordExcel,
  updateAppointmentRecord,
} from '@src/services/appointment-record';
import { VisitPurposeEnum } from '@src/services/appointment-record/type';
import {
  BelongWarZoneEnum,
  BusinessOpportunityTypeEnum,
  ProgressNodeEnum,
} from '@src/services/business-opportunity/type';
import { getInterviewNoticeList } from '@src/services/process/interview-notice';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps, Select, Tabs } from 'antd';
import dayjs from 'dayjs';

interface CreateEditAppointmentRecordModalProps extends ModalProps {
  getId: () => number | null;
  onSuccess: () => void;
}

const CreateEditAppointmentRecordModal = React.forwardRef<
  ModalRef,
  CreateEditAppointmentRecordModalProps
>(({ getId, onSuccess, ...props }, ref) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [activeKey, setActiveKey] = useState<'manual' | 'excel'>('manual');

  const id = getId();

  const isEdit = !!id;

  const customer_id = Form.useWatch('customerId', form);
  const appointmentDay = Form.useWatch('appointmentDay', form);
  const appointmentTime = Form.useWatch('appointmentTime', form);

  const { showImportPrompt } = ImportFileFormItem.usePrompt();
  const { data: interviewNoticeList, mutate: mutateInterviewNoticeList } = useRequest(
    async () => {
      if (!customer_id || !appointmentDay || !appointmentTime) {
        return undefined;
      }

      const res = await getInterviewNoticeList({
        customerId: customer_id,
        referenceTime: `${appointmentDay} ${appointmentTime}`,
      });

      return res.map((item: string) => ({ label: item, value: item }));
    },
    {
      ready: open && !isEdit,
      refreshDeps: [customer_id, appointmentDay, appointmentTime],
    },
  );

  const { data, loading } = useRequest(() => getAppointmentRecordDetail(id!), {
    ready: open && isEdit,
    onSuccess: (res) => {
      form.setFieldsValue({
        ...res,
        ...(res.scheduleTime
          ? {
              appointmentDay: dayjs(res.scheduleTime).format('YYYY-MM-DD'),
              appointmentTime: dayjs(res.scheduleTime).format('HH:mm'),
            }
          : {}),
      });
    },
  });
  const {
    data: scheduleData,
    runAsync: getSchedule,
    mutate: mutateSchedule,
  } = useRequest(getAppointmentRecordSchedule, {
    manual: true,
  });
  const {
    run: getBusinessOpportunityList,
    data: businessOpportunityList,
    loading: getBusinessOpportunityListLoading,
    mutate: mutateBusinessOpportunityList,
  } = useRequest(getBusinessOpportunityByCustomerId, {
    manual: true,
  });
  const { runAsync: create, loading: createLoading } = useRequest(createAppointmentRecord, {
    manual: true,
  });
  const { runAsync: update, loading: updateLoading } = useRequest(updateAppointmentRecord, {
    manual: true,
  });
  const { runAsync: importExcel, loading: importExcelLoading } = useRequest(
    importAppointmentRecordExcel,
    {
      manual: true,
    },
  );
  const provinceWarZoneMap = useProvinceWarZoneMap({ ready: open });

  useEffect(() => {
    if (open && !isEdit) {
      getSchedule({ businessOpportunityType: BusinessOpportunityTypeEnum.SOCIETY });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  React.useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true);
      mutateBusinessOpportunityList(undefined);
    },
    close: () => setOpen(false),
  }));

  const handleSave = async (values: any) => {
    if (isEdit) {
      await update({
        id,
        intentionProvinceCode: values.intentionRegionCode.split('/')[0],
        ...values,
      });
      message.success('修改成功');
      onSuccess();
    } else {
      if (activeKey === 'manual') {
        await create({
          intentionProvinceCode: values.intentionRegionCode.split('/')[0],
          ...values,
        });

        message.success('创建成功');
        onSuccess();
      } else {
        const res = await importExcel(values);

        showImportPrompt(res, onSuccess);
      }
    }

    setOpen(false);
  };

  const manualFormNode = (
    <>
      <Form.Item label="预约编号" required>
        <Input disabled placeholder="系统自动生成" value={data?.code} />
      </Form.Item>
      <Form.Item
        label="客户名称"
        name="customerId"
        rules={[{ required: true, message: '请选择客户' }]}
      >
        <RelCustomer
          editable={!isEdit}
          allowClear={!isEdit}
          defaultCustomerIdToNameMap={{ [data?.customerId || '']: data?.customerName }}
          onChange={(customerId) => {
            mutateBusinessOpportunityList(undefined);
            mutateInterviewNoticeList(undefined);
            form.setFieldsValue({
              bizOpportunityId: undefined,
              appointmentDay: undefined,
              appointmentTime: undefined,
              interviewNoticeCode: undefined,
            });

            if (customerId) {
              getBusinessOpportunityList({
                id: customerId,
                node: ProgressNodeEnum.预约面谈,
              });
            }
          }}
        />
      </Form.Item>
      <Form.Item
        label="到访目的"
        name="visitPurpose"
        rules={[{ required: true, message: '请选择到访目的' }]}
      >
        <Select
          disabled={isEdit}
          placeholder="请选择到访目的"
          options={enum2Options(VisitPurposeEnum)}
        />
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) => {
          const isVisitPurposeJoin = getFieldValue('visitPurpose') === VisitPurposeEnum.加盟面审;

          return (
            <>
              {isVisitPurposeJoin && (
                <FileUpload
                  formItemProps={{
                    label: '开店计划表',
                    name: 'openStoreScheduleAttachments',
                  }}
                  uploadProps={{ maxCount: 1 }}
                />
              )}
              <Form.Item
                label="商机名称"
                name="bizOpportunityId"
                dependencies={['visitPurpose']}
                rules={isVisitPurposeJoin ? [{ required: true, message: '请选择商机' }] : undefined}
                extra={
                  !isEdit &&
                  getFieldValue('customerId') &&
                  !businessOpportunityList?.length &&
                  !getBusinessOpportunityListLoading &&
                  '您选择的客户没有处于 “预约面谈” 进展的商机'
                }
              >
                <Select
                  allowClear
                  disabled={isEdit}
                  loading={getBusinessOpportunityListLoading}
                  placeholder="请选择商机"
                  options={
                    isEdit
                      ? [{ label: data?.bizOpportunityName, value: data?.bizOpportunityId }]
                      : businessOpportunityList?.map((i) => ({ label: i.name, value: i.id }))
                  }
                  onChange={(value) => {
                    mutateSchedule(undefined);
                    form.setFieldsValue({
                      appointmentDay: undefined,
                      appointmentTime: undefined,
                    });

                    const businessOpportunity = businessOpportunityList?.find(
                      (i) => i.id === value,
                    );

                    if (value) {
                      form.setFieldsValue({
                        intentionRegionCode: businessOpportunity?.intentionRegion?.split(',')[0],
                        belongWarZone: businessOpportunity?.belongWarZone,
                        intentionPositions: businessOpportunity?.intentionPositions,
                      });
                    }

                    getSchedule({
                      businessOpportunityType:
                        businessOpportunity?.businessOpportunityType ||
                        BusinessOpportunityTypeEnum.SOCIETY,
                    });
                  }}
                />
              </Form.Item>
            </>
          );
        }}
      </Form.Item>
      <Form.Item
        label="预约日期"
        name="appointmentDay"
        tooltip="只展示对应商机类型往后 30 天的可预约日期"
        rules={[{ required: true, message: '请选择预约日期' }]}
      >
        <Select
          disabled={isEdit}
          placeholder="请选择预约日期"
          fieldNames={{ label: 'day', value: 'day' }}
          options={scheduleData}
          onChange={() => form.setFieldValue('appointmentTime', undefined)}
        />
      </Form.Item>
      <Form.Item noStyle shouldUpdate={(prev, next) => prev.appointmentDay !== next.appointmentDay}>
        {({ getFieldValue }) => (
          <Form.Item
            label="预约时间"
            name="appointmentTime"
            rules={[{ required: true, message: '请选择预约时间' }]}
          >
            <Select
              disabled={isEdit}
              placeholder="请选择预约时间"
              options={scheduleData
                ?.find((item) => item.day === getFieldValue('appointmentDay'))
                ?.timeFrames.map((i) => ({
                  label: i,
                  value: i,
                }))}
            />
          </Form.Item>
        )}
      </Form.Item>
      <Form.Item label="面审负责人" required>
        <Input disabled placeholder="依据预约时间自动分配" value={data?.interviewerName} />
      </Form.Item>
      <EditableRegion
        editable
        formItemProps={{
          label: '意向区域',
          name: 'intentionRegionCode',
          rules: [{ required: true, message: '请选择意向区域' }],
        }}
        fieldProps={{
          placeholder: '请选择意向区域',
          // regionLevel: 4,
          // transformDistricts,
          onChange: (value: any) => {
            form.setFieldValue('belongWarZone', provinceWarZoneMap[value?.[0]]);
          },
        }}
      />
      <Form.Item
        label="所属大区"
        name="belongWarZone"
        rules={[{ required: true, message: '请选择所属大区' }]}
      >
        <Select
          disabled
          placeholder="根据意向区域自动匹配"
          options={enum2Options(BelongWarZoneEnum)}
        />
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) => {
          const isVisitPurposeJoin = getFieldValue('visitPurpose') === VisitPurposeEnum.加盟面审;
          const businessOpportunityType = isEdit
            ? data?.bizOpportunityType
            : businessOpportunityList?.find((i) => i.id === getFieldValue('bizOpportunityId'))
                ?.businessOpportunityType;

          return (
            isVisitPurposeJoin && (
              <>
                <Form.Item
                  label="运营负责人"
                  name="operationsCustomerId"
                  dependencies={['bizOpportunityId']}
                  rules={
                    // 社会或渠道都必填
                    businessOpportunityType ? [{ required: true, message: '请选择运营负责人' }] : []
                  }
                >
                  <RelCustomer
                    editable
                    allowClear
                    placeholder="请选择运营负责人"
                    defaultCustomerIdToNameMap={{
                      [data?.operationsCustomerId || '']: data?.operationsCustomerName,
                    }}
                  />
                </Form.Item>
                <Form.Item
                  label="合伙人"
                  name="partnerCustomerId"
                  dependencies={['bizOpportunityId']}
                  rules={
                    // 社会店必填
                    businessOpportunityType === BusinessOpportunityTypeEnum.SOCIETY
                      ? [{ required: true, message: '请选择合伙人' }]
                      : []
                  }
                >
                  <RelCustomer
                    editable
                    allowClear
                    placeholder="请选择合伙人"
                    defaultCustomerIdToNameMap={{
                      [data?.partnerCustomerId || '']: data?.partnerCustomerName,
                    }}
                  />
                </Form.Item>
              </>
            )
          );
        }}
      </Form.Item>
      <Form.Item label="意向点位" name="intentionPositions">
        <ChooseMapPoints />
      </Form.Item>
      <Form.Item
        label="面谈前告知函"
        name="interviewNoticeCode"
        rules={[{ required: true, message: '请选择面谈前告知函' }]}
      >
        <Select placeholder="请选择面谈前告知函" options={interviewNoticeList} disabled={isEdit} />
      </Form.Item>
      <Form.Item label="顾问描述客户简介" name="description">
        <Input placeholder="请输入顾问描述客户简介" />
      </Form.Item>
    </>
  );

  return (
    <Modal
      title={data ? '编辑预约记录' : '新建预约记录'}
      open={open}
      loading={loading}
      destroyOnHidden
      confirmLoading={createLoading || updateLoading || importExcelLoading}
      styles={{
        body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
      }}
      modalRender={(node) => (
        <Form
          form={form}
          labelCol={{ span: 7 }}
          clearOnDestroy
          scrollToFirstError
          onFinish={handleSave}
        >
          {node}
        </Form>
      )}
      onOk={form.submit}
      onCancel={() => setOpen(false)}
      {...props}
    >
      {isEdit ? (
        manualFormNode
      ) : (
        <Tabs
          activeKey={activeKey}
          destroyOnHidden
          onChange={(key) => {
            setActiveKey(key as typeof activeKey);
          }}
          items={[
            {
              label: '手动新建',
              key: 'manual',
              children: manualFormNode,
            },
            {
              label: '通过 EXCEL 导入',
              key: 'excel',
              children: (
                <ImportFileFormItem downloadTemplate={downloadAppointmentRecordExcelTemplate} />
              ),
            },
          ]}
        />
      )}
    </Modal>
  );
});

export default CreateEditAppointmentRecordModal;
