import React, { useState } from 'react';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { batchConfirmVisit, confirmVisit } from '@src/services/appointment-record';
import { VisitResultsEnum } from '@src/services/appointment-record/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, Modal, Select } from 'antd';

interface ConfirmVisitModalProps {
  getId: () => number | null;
  selectedRowKeys?: number[];
  onSuccess: () => void;
}

const ConfirmVisitModal = React.forwardRef<ModalRef, ConfirmVisitModalProps>(
  ({ getId, selectedRowKeys, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);

    const { runAsync: confirm, loading: confirmLoading } = useRequest(confirmVisit, {
      manual: true,
    });
    const { runAsync: batchConfirm, loading: batchConfirmLoading } = useRequest(batchConfirmVisit, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      const id = getId();

      if (id) {
        await confirm({ id: getId(), ...values });
      } else {
        await batchConfirm({ ids: selectedRowKeys, ...values });
      }

      message.success('确认到访成功');
      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        title="确认到访结果"
        destroyOnHidden
        confirmLoading={confirmLoading || batchConfirmLoading}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
        {...props}
        open={open}
      >
        <Form form={form} clearOnDestroy labelCol={{ span: 8 }} onFinish={handleSave}>
          <Form.Item
            name="visitResults"
            label="到访结果"
            rules={[{ required: true, message: '请选择到访结果' }]}
          >
            <Select placeholder="请选择到访结果" options={enum2Options(VisitResultsEnum)} />
          </Form.Item>
          <Form.Item noStyle shouldUpdate>
            {({ getFieldValue }) =>
              getFieldValue('visitResults') === VisitResultsEnum.已到访 && (
                <Form.Item
                  name="infoUnanimous"
                  label="到访人信息是否一致"
                  rules={[{ required: true, message: '请确认到访人信息是否一致' }]}
                >
                  <Select placeholder="请确认到访人信息是否一致" options={yesOrNoOptions} />
                </Form.Item>
              )
            }
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default ConfirmVisitModal;
