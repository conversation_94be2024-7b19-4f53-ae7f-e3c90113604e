import { useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { evaluationAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ExportButton, ProTable, TableActions, UserSelect } from '@src/components';
import EditableAttachment from '@src/components/Editable/EditableAttachment';
import EditableMultiple from '@src/components/Editable/EditableMultiple';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useCheckVisibleAuth from '@src/hooks/useCheckVisibleAuth';
import useDistricts from '@src/hooks/useDistricts';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import {
  confirmVisit,
  exportAppointmentRecords,
  getAppointmentRecords,
} from '@src/services/appointment-record';
import {
  AppointmentRecordDTO,
  ExportAppointmentRecordsReq,
  VisitPurposeEnum,
  VisitResultsEnum,
} from '@src/services/appointment-record/type';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { StoreCategoryEnum } from '@src/services/contract/formal/type';
import { EvaluationRejectReasonEnum, SuggestionEnum } from '@src/services/visit/type';
import {
  compatibleTableActionWidth,
  enum2Options,
  enum2ValueEnum,
  optionsToValueEnum,
} from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Dropdown, Typography } from 'antd';
import AppointmentRecordDetailDrawer from './components/AppointmentRecordDetailDrawer';
import ConfirmVisitModal from './components/ConfirmVisitModal';
import CreateEditAppointmentRecordModal from './components/CreateEditAppointmentRecordModal';
import BusinessDetailDrawer from '../business-opportunity/components/BusinessDetailDrawer';
import CustomerDetailDrawer from '../customers/components/CustomerDetailDrawer';
import InterviewNoticeDetailDrawer from '../process/interview-notice/components/InterviewNoticeDetailDrawer';
import VisitDetailDrawer from '../visit/components/VisitDetailDrawer';

const AppointmentRecord = () => {
  const { modal, message } = App.useApp();
  const currentIdRef = useRef<number | null>(null);
  const actionRef = useRef<ActionType>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const customerIdRef = useRef<number | null>(null);
  const businessDetailDrawerRef = useRef<ModalRef>(null);
  const businessIdRef = useRef<number | null>(null);
  const visitDetailDrawerRef = useRef<ModalRef>(null);
  const visitIdRef = useRef<number | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const filterParamsRef = useRef<ExportAppointmentRecordsReq>({});
  const confirmVisitModalRef = useRef<ModalRef>(null);
  const appointmentRecordDetailDrawerRef = useRef<ModalRef>(null);
  const createEditAppointmentRecordModalRef = useRef<ModalRef>(null);
  const interviewNoticeDetailDrawerRef = useRef<ModalRef>(null);

  const checkPermission = usePermission();
  const checkVisibleAuth = useCheckVisibleAuth();
  const { data: districts } = useDistricts();
  const { data, runAsync: getList } = useRequest(getAppointmentRecords, { manual: true });

  const columns: ProColumns<AppointmentRecordDTO>[] = [
    {
      title: '预约编号',
      dataIndex: 'code',
      fixed: 'left',
      search: false,
      renderText: (value, { id }) => (
        <a
          onClick={() => {
            currentIdRef.current = id;
            appointmentRecordDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      renderText: (value, { customerId }) => (
        <Typography.Text
          style={{ color: 'var(--ant-color-link)', cursor: 'pointer' }}
          ellipsis={{ tooltip: true }}
          onClick={() => {
            customerIdRef.current = customerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </Typography.Text>
      ),
    },
    {
      title: '客户性质',
      dataIndex: 'customerNature',
      search: false,
      valueEnum: enum2ValueEnum(StoreCategoryEnum),
    },
    {
      title: '商机名称',
      dataIndex: 'bizOpportunityName',
      renderText: (value, { bizOpportunityId }) => (
        <Typography.Text
          style={{ color: 'var(--ant-color-link)', cursor: 'pointer' }}
          ellipsis={{ tooltip: true }}
          onClick={() => {
            businessIdRef.current = bizOpportunityId;
            businessDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </Typography.Text>
      ),
    },
    {
      title: '商机编号',
      dataIndex: 'bizOpportunityCode',
      search: false,
    },
    {
      title: '商机负责人',
      dataIndex: 'bizDirectUserName',
      search: {
        transform: (bizDirectUserId) => ({
          bizDirectUserId,
        }),
      },
      renderFormItem: () => <UserSelect />,
    },
    {
      title: '预约时间',
      dataIndex: 'scheduleTime',
      valueType: (_, type) => (type === 'table' ? 'dateTime' : 'dateRange'),
      search: {
        transform: (value) => ({
          beginDate: value[0],
          endDate: value[1],
        }),
      },
    },
    {
      title: '到访目的',
      dataIndex: 'visitPurpose',
      search: false,
      valueEnum: enum2ValueEnum(VisitPurposeEnum),
    },
    {
      title: '开店计划表',
      dataIndex: 'openStoreScheduleAttachments',
      search: false,
      onCell: () => ({
        className: '!py-0',
      }),
      renderText: (value) => <EditableAttachment value={value} />,
    },
    {
      title: '面审负责人',
      dataIndex: 'interviewerName',
      search: {
        transform: (interviewer) => ({
          interviewer,
        }),
      },
      renderFormItem: () => <UserSelect />,
    },
    {
      title: '意向区域',
      dataIndex: 'intentionRegionCode',
      search: false,
      ellipsis: true,
      renderText: (value) => <EditableRegion value={value} fieldProps={{ regionLevel: 4 }} />,
    },
    {
      title: '意向省份',
      dataIndex: 'intentionProvinceCode',
      search: false,
      renderText: (value) => districts?.find((i) => i.code === value)?.name,
    },
    {
      title: '所属大区',
      dataIndex: 'belongWarZone',
      search: false,
      valueEnum: enum2ValueEnum(BelongWarZoneEnum),
    },
    {
      title: '运营负责人',
      dataIndex: 'operationsCustomerName',
      search: false,
      renderText: (value, { operationsCustomerId }) => (
        <Typography.Text
          style={{ color: 'var(--ant-color-link)', cursor: 'pointer' }}
          ellipsis={{ tooltip: true }}
          onClick={() => {
            customerIdRef.current = operationsCustomerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </Typography.Text>
      ),
    },
    {
      title: '合伙人',
      dataIndex: 'partnerCustomerName',
      search: false,
      renderText: (value, { partnerCustomerId }) => (
        <Typography.Text
          style={{ color: 'var(--ant-color-link)', cursor: 'pointer' }}
          ellipsis={{ tooltip: true }}
          onClick={() => {
            customerIdRef.current = partnerCustomerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </Typography.Text>
      ),
    },
    {
      title: '到访结果',
      dataIndex: 'visitResults',
      fieldProps: {
        mode: 'multiple',
      },
      valueEnum: enum2ValueEnum(VisitResultsEnum),
    },
    {
      title: '到访记录编号',
      dataIndex: 'visitRecordCode',
      search: false,
      renderText: (value, { visitRecordId }) => (
        <a
          onClick={() => {
            visitIdRef.current = visitRecordId;
            visitDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '到访日期时间',
      dataIndex: 'visitTime',
      search: false,
    },
    {
      title: '到访信息是否一致',
      dataIndex: 'infoUnanimous',
      search: false,
      valueEnum: optionsToValueEnum(yesOrNoOptions, true),
    },
    // {
    //   title: '是否通过线上面审',
    //   dataIndex: 'onlineReviewStatus',
    //   search: false,
    //   valueEnum: optionsToValueEnum(yesOrNoOptions, true),
    // },
    {
      title: '面谈前告知函',
      dataIndex: 'interviewNoticeCode',
      search: false,
      renderText: (value, { interviewNoticeCode }) => (
        <a
          onClick={() => {
            currentIdRef.current = interviewNoticeCode as unknown as number;
            interviewNoticeDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '面审建议',
      dataIndex: 'suggestion',
      search: false,
      valueEnum: enum2ValueEnum(SuggestionEnum),
    },
    {
      title: '面审最终结果',
      dataIndex: 'auditStatus',
      search: false,
      valueEnum: optionsToValueEnum(evaluationAuditStatusOptions),
    },
    {
      title: '未通过原因',
      dataIndex: 'rejectReasons',
      search: false,
      renderText: (value) => (
        <EditableMultiple
          value={value}
          fieldProps={{ options: enum2Options(EvaluationRejectReasonEnum) }}
        />
      ),
    },
    {
      title: '顾问描述客户简介',
      dataIndex: 'description',
      search: false,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      width: compatibleTableActionWidth(220),
      search: false,
      renderText: (_, { id, visitResults, interviewer }) => (
        <TableActions
          shortcuts={[
            {
              label: '编辑',
              onClick: () => {
                currentIdRef.current = id;
                createEditAppointmentRecordModalRef.current?.open();
              },
            },
            {
              label: '取消预约',
              show: !visitResults && checkPermission(PermissionsMap.AppointmentRecordCancel),
              onClick: () =>
                modal.confirm({
                  title: '取消预约',
                  content: '确定取消预约吗？',
                  onOk: async () => {
                    await confirmVisit({ id, visitResults: VisitResultsEnum.面审改期 });
                    message.success('取消预约成功');
                    actionRef.current?.reload();
                  },
                }),
            },
            {
              label: '确认到访结果',
              show: !visitResults && checkVisibleAuth(interviewer),
              onClick: () => {
                currentIdRef.current = id;
                confirmVisitModalRef.current?.open();
              },
            },
          ]}
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        options={false}
        bordered
        sticky
        cardProps={false}
        columnEmptyText=""
        actionRef={actionRef}
        size="small"
        rowKey="id"
        pagination={{ showSizeChanger: true, showQuickJumper: true }}
        columns={columns}
        tableAlertRender={false}
        rowSelection={{
          fixed: true,
          showSort: true,
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        toolbar={{
          subTitle: (
            <span className="text-black">
              共 {data?.total || 0} 条记录
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 条
                  <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            <Permission value={PermissionsMap.AppointmentRecordBatchConfirmVisit}>
              <Dropdown
                disabled={!selectedRowKeys.length}
                menu={{
                  items: [
                    {
                      label: '确认到访结果',
                      key: 'confirm-visit',
                      onClick: () => {
                        currentIdRef.current = null;
                        confirmVisitModalRef.current?.open();
                      },
                    },
                  ],
                }}
              >
                <Button type="text" className="!flex items-center">
                  批量操作 <CaretDownOutlined />
                </Button>
              </Dropdown>
            </Permission>,
            <Button
              type="text"
              onClick={() => {
                currentIdRef.current = null;
                createEditAppointmentRecordModalRef.current?.open();
              }}
            >
              新建
            </Button>,
            <Permission value={PermissionsMap.AppointmentRecordExport}>
              <ExportButton
                total={selectedRowKeys.length || data?.total}
                request={() =>
                  exportAppointmentRecords(
                    selectedRowKeys.length ? { ids: selectedRowKeys } : filterParamsRef.current,
                  )
                }
              />
            </Permission>,
          ],
        }}
        scroll={{ x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0) }}
        request={async ({ current, pageSize, ...params }) => {
          filterParamsRef.current = params as ExportAppointmentRecordsReq;

          const res = await getList({
            pageNum: current,
            pageSize,
            ...params,
          });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />
      <BusinessDetailDrawer ref={businessDetailDrawerRef} getId={() => businessIdRef.current} />
      <VisitDetailDrawer ref={visitDetailDrawerRef} getId={() => visitIdRef.current} />
      <ConfirmVisitModal
        ref={confirmVisitModalRef}
        selectedRowKeys={selectedRowKeys}
        getId={() => currentIdRef.current}
        onSuccess={() => {
          actionRef.current?.reload();

          // 批量操作成功时清空选择
          if (!currentIdRef.current) {
            setSelectedRowKeys([]);
          }
        }}
      />
      <AppointmentRecordDetailDrawer
        ref={appointmentRecordDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
      />
      <CreateEditAppointmentRecordModal
        ref={createEditAppointmentRecordModalRef}
        getId={() => currentIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
      <InterviewNoticeDetailDrawer
        ref={interviewNoticeDetailDrawerRef}
        getCode={() => currentIdRef.current}
      />
    </PageContainer>
  );
};

export default AppointmentRecord;
