import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import EditableDate from '@src/components/Editable/EditableDate';
import { issueShopLicenseTraceTask } from '@src/services/process/abnormal-certificate';
import {
  AbnormalCertificateDTO,
  ExceptionTypeEnum,
} from '@src/services/process/abnormal-certificate/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps, Select } from 'antd';

interface IssueTaskModalProps extends ModalProps {
  getRecord: () => AbnormalCertificateDTO | null;
  onSuccess: () => void;
}

const IssueTaskModal = React.forwardRef<ModalRef, IssueTaskModalProps>(
  ({ getRecord, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();

    const record = getRecord();

    const { runAsync: issue, loading: issueLoading } = useRequest(issueShopLicenseTraceTask, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      await issue(values);
      message.success('下发成功');
      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        title="下发任务"
        open={open}
        destroyOnHidden
        confirmLoading={issueLoading}
        modalRender={(node) => (
          <Form form={form} clearOnDestroy onFinish={handleSave}>
            {node}
          </Form>
        )}
        onCancel={() => setOpen(false)}
        onOk={form.submit}
        {...props}
      >
        <Form.Item label="门店编号" name="shopId" required initialValue={record?.businessId}>
          <Input disabled value={record?.businessId} />
        </Form.Item>
        <Form.Item
          label="异常原因"
          name="exceptionTypes"
          rules={[{ required: true, message: '请选择请选择异常原因' }]}
        >
          <Select
            mode="multiple"
            placeholder="请选择异常原因"
            options={enum2Options(ExceptionTypeEnum)}
          />
        </Form.Item>
        <Form.Item
          label="异常说明"
          name="description"
          rules={[{ required: true, message: '请输入异常说明' }]}
        >
          <Input.TextArea placeholder="请输入异常说明" maxLength={255} />
        </Form.Item>
        <EditableDate
          editable
          formItemProps={{
            label: '过期日期',
            name: 'expireDate',
            initialValue: record?.expireTime,
            rules: [{ required: true, message: '请选择过期日期' }],
          }}
        />
      </Modal>
    );
  },
);

export default IssueTaskModal;
