import { useRef, useState } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ModalRef } from '@src/common/interface';
import { ExportButton, ProTable, TableActions } from '@src/components';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import {
  checkShopLicenseTrace,
  completeAbnormalCertificateTask,
  exportAbnormalCertificate,
  getAbnormalCertificateList,
  processedAbnormalCertificate,
} from '@src/services/process/abnormal-certificate';
import {
  AbnormalCertificateDTO,
  AbnormalCertificateTaskStatusEnum,
  AbnormalCertificateTaskTypeEnum,
  ExportAbnormalCertificateReq,
} from '@src/services/process/abnormal-certificate/type';
import { calcTableWidth, compatibleTableActionWidth, enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { App } from 'antd';
import dayjs from 'dayjs';
import IssueTaskModal from './components/IssueTaskModal';

const AbnormalCertificate = () => {
  const checkPermission = usePermission();
  const { modal, message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const shopIdRef = useRef<string | null>(null);
  const shopDetailDrawer = useRef<ModalRef>(null);
  const filterParamsRef = useRef<ExportAbnormalCertificateReq>({});
  const currentRecordRef = useRef<AbnormalCertificateDTO | null>(null);
  const issueTaskModalRef = useRef<ModalRef>(null);

  const { data, runAsync: getList } = useRequest(getAbnormalCertificateList, { manual: true });

  const columns: ProColumns<AbnormalCertificateDTO>[] = [
    {
      title: '门店编号',
      dataIndex: 'businessId',
      fixed: 'left',
      renderText: (value) => (
        <a
          onClick={() => {
            shopIdRef.current = value;
            shopDetailDrawer.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '所属大区',
      dataIndex: 'belongWarZone',
      valueEnum: enum2ValueEnum(BelongWarZoneEnum),
      search: false,
    },
    {
      title: '门店冠名',
      dataIndex: 'shopName',
      width: 210,
      search: false,
    },
    {
      title: '省市区',
      dataIndex: 'provinceName',
      width: 210,
      search: false,
      render: (_, { province, city, district }) =>
        [province, city, district].filter(Boolean).join(''),
    },
    {
      title: '异常类型',
      dataIndex: 'taskType',
      width: 260,
      valueEnum: enum2ValueEnum(AbnormalCertificateTaskTypeEnum),
      fieldProps: {
        mode: 'multiple',
      },
    },
    {
      title: '异常原因',
      dataIndex: 'exceptionReason',
      width: 210,
      ellipsis: true,
      search: false,
    },
    {
      title: '到期时间',
      dataIndex: 'expireTime',
      valueType: (_, type) => (type === 'table' ? 'date' : 'dateRange'),
      search: {
        transform: ([expireBeginTime, expireEndTime]) => ({
          expireBeginTime,
          expireEndTime,
        }),
      },
    },
    {
      title: '处理状态',
      dataIndex: 'taskStatus',
      valueEnum: {
        [AbnormalCertificateTaskStatusEnum.NOT_COMPLETE]: {
          text: '未完成',
          status: 'default',
        },
        [AbnormalCertificateTaskStatusEnum.COMPLETE]: {
          text: '完成',
          status: 'success',
        },
      },
    },
    {
      title: '完成时间',
      dataIndex: 'completeTime',
      search: false,
      width: 180,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 180,
      valueType: (_, type) => (type === 'table' ? 'dateTime' : 'dateRange'),
      search: {
        transform: (value) => ({
          createBeginTime: dayjs(value[0]).startOf('d').format('YYYY-MM-DD HH:mm:ss'),
          createEndTime: dayjs(value[1]).endOf('d').format('YYYY-MM-DD HH:mm:ss'),
        }),
      },
    },
    {
      title: '操作',
      key: 'action',
      search: false,
      fixed: 'right',
      align: 'center',
      width: compatibleTableActionWidth(150),
      render: (_, record) => {
        const { id, taskStatus, businessId, taskType, issued } = record;

        return (
          <TableActions
            shortcuts={[
              {
                label: '已处理',
                show: taskStatus === AbnormalCertificateTaskStatusEnum.NOT_COMPLETE,
                onClick: () =>
                  modal.confirm({
                    title: '提示',
                    content:
                      taskType === AbnormalCertificateTaskTypeEnum.现场巡检不一致
                        ? '确认后会将处理状态标记为已完成。'
                        : '确认后将重新获取门店信息判断任务是否处理。',
                    onOk: async () => {
                      if (taskType === AbnormalCertificateTaskTypeEnum.现场巡检不一致) {
                        await completeAbnormalCertificateTask(id);
                      } else {
                        await processedAbnormalCertificate(id);
                      }

                      message.success('操作成功');
                      actionRef.current?.reload();
                    },
                  }),
              },
              {
                label: '下发任务',
                show: issued && checkPermission(PermissionsMap.ProcessAbnormalCertificateIssueTask),
                onClick: () =>
                  modal.confirm({
                    title: '下发任务',
                    content: '确定要下发任务吗？',
                    onOk: async () => {
                      await checkShopLicenseTrace(businessId);
                      currentRecordRef.current = record;
                      issueTaskModalRef.current?.open();
                    },
                  }),
              },
            ]}
          />
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        cardProps={false}
        bordered
        rowKey="id"
        sticky
        size="small"
        columnEmptyText=""
        columns={columns}
        scroll={{ x: calcTableWidth(columns) }}
        options={false}
        tableAlertRender={false}
        toolbar={{
          subTitle: (
            <span className="text-black">
              共 {data?.total || 0} 条记录
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 条
                  <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            <Permission value={PermissionsMap.ProcessAbnormalCertificateExport}>
              <ExportButton
                total={selectedRowKeys.length || data?.total}
                request={() =>
                  exportAbnormalCertificate(
                    selectedRowKeys.length
                      ? { ids: selectedRowKeys, findAll: true }
                      : filterParamsRef.current,
                  )
                }
              />
            </Permission>,
          ],
        }}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        rowSelection={{
          fixed: true,
          showSort: true,
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        request={async ({ current, pageSize, ...restParams }) => {
          // 查所有人的
          const params: ExportAbnormalCertificateReq = { findAll: true, ...restParams };

          filterParamsRef.current = params;

          const res = await getList({ pageNum: current, pageSize, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />
      <ShopDetailDrawer ref={shopDetailDrawer} getId={() => shopIdRef.current} />
      <IssueTaskModal
        ref={issueTaskModalRef}
        getRecord={() => currentRecordRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
    </PageContainer>
  );
};

export default AbnormalCertificate;
