import { useEffect, useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { intendedContractExtensionAuditStatusOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ExportButton, ProTable, RegionCascader, TableActions, UserSelect } from '@src/components';
import EditableAttachment from '@src/components/Editable/EditableAttachment';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useAllUsers from '@src/hooks/useAllUsers';
import { useNoticeEventSubscription } from '@src/layout';
import IntentionDetailDrawer from '@src/pages/contract/intention/components/IntentionDetailDrawer';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { AuditStatusEnum } from '@src/services/common/type';
import {
  batchDeleteIntendedContractExtension,
  deleteIntendedContractExtension,
  exportIntendedContractExtension,
  getIntendedContractExtensionList,
} from '@src/services/process/intended-contract-extension';
import {
  ExportIntendedContractExtensionReq,
  IntendedContractExtensionDTO,
} from '@src/services/process/intended-contract-extension/type';
import { compatibleTableActionWidth, optionsToValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Dropdown, MenuProps, Typography } from 'antd';
import dayjs from 'dayjs';
import { useSearchParams } from 'react-router-dom';
import IntendedContractExtensionDetailDrawer from './components/IntendedContractExtensionDetailDrawer';
import IntendedContractExtensionModal from './components/IntendedContractExtensionModal';

const IntendedContractExtension = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { modal, message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const customerIdRef = useRef<number | null>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const intendedContractExtensionModalRef = useRef<ModalRef>(null);
  const currentIdRef = useRef<number | null>(null);
  const contractIdRef = useRef<number | null>(null);
  const filterParamsRef = useRef<ExportIntendedContractExtensionReq>({});
  const detailDrawerRef = useRef<ModalRef>(null);
  const intentionDetailDrawerRef = useRef<ModalRef>(null);

  const checkPermission = usePermission();
  const { data: users } = useAllUsers();
  const { data, runAsync: getList } = useRequest(getIntendedContractExtensionList, {
    manual: true,
  });

  useNoticeEventSubscription((event) => {
    if (
      event.type === 'intended-contract-extension' &&
      data?.result.some((i) => i.id === event.payload.id)
    ) {
      actionRef.current?.reload();
    }
  });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      currentIdRef.current = Number(id);
      detailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: ProColumns<IntendedContractExtensionDTO>[] = [
    {
      title: '申请编号',
      dataIndex: 'code',
      fixed: 'left',
      search: false,
      renderText: (value, { id }) => (
        <a
          onClick={() => {
            currentIdRef.current = id;
            detailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '意向合同编号',
      dataIndex: 'contractCode',
      renderText: (value, { contractId }) => (
        <a
          onClick={() => {
            contractIdRef.current = contractId;
            intentionDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      valueEnum: optionsToValueEnum(intendedContractExtensionAuditStatusOptions),
      fieldProps: {
        mode: 'multiple',
      },
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      renderText: (value, { customerId }) => (
        <Typography.Text
          style={{ color: 'var(--ant-color-link)', cursor: 'pointer' }}
          ellipsis={{ tooltip: true }}
          onClick={() => {
            customerIdRef.current = customerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </Typography.Text>
      ),
    },
    {
      title: '意向区域',
      dataIndex: 'regionCode',
      ellipsis: true,
      renderText: (value) => <EditableRegion value={value} fieldProps={{ regionLevel: 4 }} />,
      renderFormItem: () => (
        <RegionCascader
          placeholder="请选择"
          multiple
          unlimited={{ options: true, onChange: true }}
        />
      ),
      search: {
        transform: (value) => ({
          regionCode: value.map((item: number[]) => item.join('/')).join(','),
        }),
      },
    },
    {
      title: '意向生效日期',
      dataIndex: 'effectiveDate',
      search: false,
    },
    {
      title: '意向到期日期',
      dataIndex: 'expirationDate',
      search: false,
    },
    {
      title: '延期天数',
      dataIndex: 'postponeDayNum',
      search: false,
    },
    {
      title: '延期到期日期',
      dataIndex: 'postponeExpirationDate',
      search: false,
    },
    {
      title: '申请说明',
      dataIndex: 'description',
      search: false,
      ellipsis: true,
    },
    {
      title: '申请附件',
      dataIndex: 'applyAttachments',
      search: false,
      onCell: () => ({
        className: '!py-0',
      }),
      renderText: (value) => <EditableAttachment value={value} />,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      renderText: (value) => users?.find((i) => i.id === value)?.name,
      renderFormItem: () => <UserSelect />,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: (_, type) => (type === 'table' ? 'dateTime' : 'dateRange'),
      search: {
        transform: (value) => ({
          createBeginTime: dayjs(value[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          createEndTime: dayjs(value[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        }),
      },
    },
    {
      title: '操作',
      key: 'action',
      search: false,
      align: 'center',
      fixed: 'right',
      width: compatibleTableActionWidth(100),
      render: (_, { id, auditStatus }) => (
        <TableActions
          shortcuts={[
            {
              label: '编辑',
              show:
                checkPermission(PermissionsMap.ProcessIntendedContractExtensionEdit) &&
                [AuditStatusEnum.REVOKE, AuditStatusEnum.NOT_PASS].includes(auditStatus),
              onClick: () => {
                currentIdRef.current = id;
                intendedContractExtensionModalRef.current?.open();
              },
            },
            {
              label: '删除',
              danger: true,
              show:
                checkPermission(PermissionsMap.ProcessIntendedContractExtensionDelete) &&
                auditStatus === AuditStatusEnum.REVOKE,
              onClick: () =>
                modal.confirm({
                  title: '删除',
                  content: '确定要删除该意向合同延期申请吗？',
                  onOk: async () => {
                    await deleteIntendedContractExtension(id);
                    message.success('删除成功');
                    actionRef.current?.reload();
                    setSelectedRowKeys((prev) => prev.filter((i) => i !== id));
                  },
                }),
            },
          ]}
        />
      ),
    },
  ];

  const batchMenuItems: MenuProps['items'] = [
    {
      key: 'delete',
      label: '删除',
      danger: true,
      auth: PermissionsMap.ProcessIntendedContractExtensionDelete,
      onClick: () =>
        modal.confirm({
          title: '删除',
          content: `确定要删除选中的 ${selectedRowKeys.length} 条意向合同延期申请吗？`,
          onOk: async () => {
            await batchDeleteIntendedContractExtension(selectedRowKeys);
            message.success('删除成功');
            setSelectedRowKeys([]);
            actionRef.current?.reload();
          },
        }),
    },
  ].filter((i) => checkPermission(i.auth));

  return (
    <PageContainer>
      <ProTable
        bordered
        actionRef={actionRef}
        sticky
        size="small"
        rowKey="id"
        cardProps={false}
        options={false}
        columnEmptyText=""
        tableAlertRender={false}
        search={{
          labelWidth: 100,
        }}
        columns={columns}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        scroll={{
          x: columns.reduce(
            (total, cur) => total + (cur.hidden ? 0 : (cur.width as number) || 180),
            0,
          ),
        }}
        rowSelection={{
          fixed: true,
          showSort: true,
          selectedRowKeys,
          preserveSelectedRowKeys: true,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        toolbar={{
          subTitle: (
            <span className="text-black">
              共 {data?.total || 0} 条记录
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 条
                  <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            batchMenuItems.length > 0 && (
              <Dropdown
                disabled={!selectedRowKeys.length}
                menu={{
                  items: batchMenuItems,
                }}
              >
                <Button type="text" className="!flex items-center">
                  批量操作 <CaretDownOutlined />
                </Button>
              </Dropdown>
            ),
            <Permission value={PermissionsMap.ProcessIntendedContractExtensionCreate}>
              <Button
                type="text"
                onClick={() => {
                  currentIdRef.current = null;
                  intendedContractExtensionModalRef.current?.open();
                }}
              >
                新建
              </Button>
            </Permission>,
            <Permission value={PermissionsMap.ProcessIntendedContractExtensionExport}>
              <ExportButton
                total={selectedRowKeys.length || data?.total}
                request={() =>
                  exportIntendedContractExtension(
                    selectedRowKeys.length ? { ids: selectedRowKeys } : filterParamsRef.current,
                  )
                }
              />
            </Permission>,
          ],
        }}
        request={async ({ current, pageSize, ...params }) => {
          filterParamsRef.current = params as ExportIntendedContractExtensionReq;

          const res = await getList({ pageNum: current, pageSize, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />
      <IntendedContractExtensionModal
        ref={intendedContractExtensionModalRef}
        getId={() => currentIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
      <IntendedContractExtensionDetailDrawer
        ref={detailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
        onDelete={() => {
          actionRef.current?.reload();
          setSelectedRowKeys((prev) => prev.filter((i) => i !== currentIdRef.current));
        }}
      />
      <IntentionDetailDrawer ref={intentionDetailDrawerRef} getId={() => contractIdRef.current} />
    </PageContainer>
  );
};

export default IntendedContractExtension;
