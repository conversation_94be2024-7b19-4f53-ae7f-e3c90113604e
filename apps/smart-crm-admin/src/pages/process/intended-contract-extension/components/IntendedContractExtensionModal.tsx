import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { FileUpload } from '@src/components';
import EditableRegion from '@src/components/Editable/EditableRegion';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import { getIntentionContractDetail } from '@src/services/contract/intention';
import { IntentionContractDTO } from '@src/services/contract/intention/type';
import {
  createIntendedContractExtension,
  getIntendedContractExtensionDetail,
  updateIntendedContractExtension,
} from '@src/services/process/intended-contract-extension';
import { useRequest } from 'ahooks';
import { App, Form, Input, InputNumber, Modal, ModalProps } from 'antd';
import dayjs from 'dayjs';
import IntentionContractSelect from '../../components/IntentionContractSelect';

interface IntendedContractExtensionModalProps extends ModalProps {
  getId?: () => number | null;
  /** 快捷创建时的合同 id */
  getCreateId?: () => number | null;
  onSuccess?: () => void;
}

const postponeDayNumInitialValue = 15;

const IntendedContractExtensionModal = React.forwardRef<
  ModalRef,
  IntendedContractExtensionModalProps
>(({ getId, getCreateId, onSuccess, ...props }, ref) => {
  const { message } = App.useApp();
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  // 新建弹窗才会有值
  const [currentIntentionContractInCreate, setCurrentIntentionContractInCreate] =
    useState<IntentionContractDTO>();

  const id = getId?.();
  const isEdit = !!id;

  const setFieldsWhenContractChange = (item: IntentionContractDTO, postponeDayNum?: number) => {
    setCurrentIntentionContractInCreate(item);

    const expirationDate = item.intentionExpirationDate;

    form.setFieldsValue({
      contractCode: item.code,
      customerId: item.customerId,
      effectiveDate: item.effectiveDate,
      expirationDate,
      regionCode: item.region,
      postponeExpirationDate:
        expirationDate && (postponeDayNum || postponeDayNum === 0)
          ? dayjs(expirationDate).add(postponeDayNum, 'day').format('YYYY-MM-DD')
          : undefined,
    });
  };

  const { runAsync: create, loading: createLoading } = useRequest(createIntendedContractExtension, {
    manual: true,
  });
  const { runAsync: update, loading: updateLoading } = useRequest(updateIntendedContractExtension, {
    manual: true,
  });
  const { data, loading } = useRequest(() => getIntendedContractExtensionDetail(id!), {
    ready: open && isEdit,
    onSuccess: (res) => form.setFieldsValue(res),
  });
  const createId = getCreateId?.();
  const { loading: getIntentionContractDetailLoading } = useRequest(
    () => getIntentionContractDetail(createId!),
    {
      ready: open && !!createId,
      onSuccess: (res) => setFieldsWhenContractChange(res, postponeDayNumInitialValue),
    },
  );

  React.useImperativeHandle(ref, () => ({
    open: () => setOpen(true),
    close: () => setOpen(false),
  }));

  const handleSave = async (values: any) => {
    if (isEdit) {
      await update({ id, ...values });
    } else {
      await create(values);
    }

    message.success('提交审核成功');
    setOpen(false);
    onSuccess?.();
  };

  return (
    <Modal
      title={isEdit ? '编辑意向合同延期' : '新增意向合同延期'}
      open={open}
      destroyOnHidden
      okText="提交审核"
      loading={loading || getIntentionContractDetailLoading}
      confirmLoading={createLoading || updateLoading}
      styles={{
        body: { maxHeight: '60vh', margin: '0 -24px', padding: '0 24px', overflow: 'auto' },
      }}
      modalRender={(node) => (
        <Form form={form} clearOnDestroy labelCol={{ span: 6 }} onFinish={handleSave}>
          {node}
        </Form>
      )}
      onCancel={() => setOpen(false)}
      onOk={form.submit}
      {...props}
    >
      <Form.Item
        label="意向合同"
        name="contractCode"
        rules={[{ required: true, message: '请选择意向合同' }]}
      >
        <IntentionContractSelect
          disabled={isEdit}
          onChange={(_, option) => {
            setFieldsWhenContractChange(
              option as IntentionContractDTO,
              form.getFieldValue('postponeDayNum'),
            );
          }}
        />
      </Form.Item>
      <Form.Item
        label="客户名称"
        name="customerId"
        rules={[{ required: true, message: '请选择客户' }]}
      >
        <RelCustomer
          placeholder="选择意向合同自动填充"
          defaultCustomerIdToNameMap={{
            [currentIntentionContractInCreate?.customerId || '']:
              currentIntentionContractInCreate?.customerName,
            [data?.customerId || '']: data?.customerName,
          }}
        />
      </Form.Item>
      <EditableRegion
        editable
        formItemProps={{
          label: '意向区域',
          name: 'regionCode',
          rules: [{ required: true, message: '请选择原意向区域' }],
        }}
        fieldProps={{
          disabled: true,
          regionLevel: 4,
          placeholder: '选择意向合同自动填充',
        }}
      />
      <Form.Item
        label="意向生效日期"
        name="effectiveDate"
        rules={[{ required: true, message: '请选择意向生效日期' }]}
      >
        <Input disabled placeholder="选择意向合同自动填充" />
      </Form.Item>
      <Form.Item
        label="意向到期日期"
        name="expirationDate"
        rules={[
          { required: true, message: '请选择意向到期日期' },
          {
            validator(_rule, value) {
              if (value && dayjs(value).isBefore(dayjs().subtract(15, 'day').startOf('D'))) {
                return Promise.reject('意向合同已过期');
              }

              return Promise.resolve();
            },
          },
        ]}
      >
        <Input disabled placeholder="选择意向合同自动填充" />
      </Form.Item>
      <Form.Item
        label="延期天数"
        name="postponeDayNum"
        initialValue={postponeDayNumInitialValue}
        rules={[{ required: true, message: '请输入延期天数' }]}
      >
        <InputNumber
          min={1}
          disabled
          precision={0}
          placeholder="请输入"
          onChange={(postponeDayNum) => {
            const expirationDate = form.getFieldValue('expirationDate');

            form.setFieldValue(
              'postponeExpirationDate',
              expirationDate && (postponeDayNum || postponeDayNum === 0)
                ? dayjs(expirationDate).add(postponeDayNum, 'day').format('YYYY-MM-DD')
                : undefined,
            );
          }}
        />
      </Form.Item>
      <Form.Item
        label="延期到期日期"
        name="postponeExpirationDate"
        rules={[{ required: true, message: '请选择延期到期日期' }]}
      >
        <Input disabled placeholder="根据意向到期日期和延期天数自动计算" />
      </Form.Item>
      <Form.Item
        label="申请说明"
        name="description"
        rules={[{ required: true, message: '请输入申请说明' }]}
      >
        <Input.TextArea maxLength={500} placeholder="请输入变更说明" />
      </Form.Item>
      <FileUpload formItemProps={{ name: 'applyAttachments', label: '申请附件' }} />
    </Modal>
  );
});

export default IntendedContractExtensionModal;
