import React, { useState } from 'react';
import { AuditFlow } from '@src/components';
import useSimpleForm from '@src/hooks/useSimpleFormModal';
import { AuditStatusEnum } from '@src/services/common/type';
import {
  approveIntendedContractExtensionTask,
  getIntendedContractExtensionAuditHistory,
  rejectIntendedContractExtensionTask,
  revokeIntendedContractExtensionAudit,
} from '@src/services/process/intended-contract-extension';
import useUserStore from '@src/store/useUserStore';
import { useRequest } from 'ahooks';
import { App, Input } from 'antd';

interface IntendedContractExtensionAuditCardProps {
  id: number;
  loading: boolean;
  auditStatus?: AuditStatusEnum;
  onSuccess: () => void;
  onTodoSuccess?: () => void;
}

export interface IntendedContractExtensionAuditCardRef {
  refresh: () => void;
}

const IntendedContractExtensionAuditCard = React.forwardRef<
  IntendedContractExtensionAuditCardRef,
  IntendedContractExtensionAuditCardProps
>(({ id, loading, auditStatus, onSuccess, onTodoSuccess }, ref) => {
  const { modal, message } = App.useApp();
  const [auditModalType, setAuditModalType] = useState<'approve' | 'reject'>('approve');

  const {
    user: { shUserId },
  } = useUserStore();
  const {
    data: auditHistory = [],
    loading: getAuditHistoryLoading,
    refresh,
  } = useRequest(() => getIntendedContractExtensionAuditHistory(id));

  const { runAsync: approve, loading: approveLoading } = useRequest(
    approveIntendedContractExtensionTask,
    {
      manual: true,
    },
  );
  const { runAsync: reject, loading: rejectLoading } = useRequest(
    rejectIntendedContractExtensionTask,
    {
      manual: true,
    },
  );

  const { modalNode: auditModalNode, setOpen: setAuditModalOpen } = useSimpleForm({
    modalProps: {
      title: { approve: '通过', reject: '拒绝' }[auditModalType],
      confirmLoading: approveLoading || rejectLoading,
    },
    formProps: {
      onFinish: async (values) => {
        if (auditModalType === 'approve') {
          await approve({
            id,
            ...values,
          });
        } else {
          await reject({
            id,
            ...values,
          });
        }

        message.success('审批成功');
        setAuditModalOpen(false);
        refresh();
        onSuccess();
        onTodoSuccess?.();
      },
    },
    formItems: [
      {
        name: 'description',
        label: '说明',
        rules: [{ required: true, message: '请输入说明' }],
        children: <Input.TextArea placeholder="请输入说明" maxLength={500} showCount />,
      },
    ],
  });

  React.useImperativeHandle(ref, () => ({
    refresh,
  }));

  const openApproveAuditModal = () => {
    setAuditModalOpen(true);
    setAuditModalType('approve');
  };

  const handleRevoke = () => {
    modal.confirm({
      title: '撤回审核',
      content: '确定要撤回该审核吗？',
      onOk: async () => {
        await revokeIntendedContractExtensionAudit(id);
        message.success('撤回成功');
        refresh();
        onSuccess();
      },
    });
  };

  // 是否是审批人
  const isAuditUser = !!auditHistory[auditHistory.length - 1]?.auditUserIds?.includes(shUserId);
  const isAuditStatus = auditStatus === AuditStatusEnum.AUDIT;
  // 是否是提交人
  const isSubmitter = shUserId === auditHistory[0]?.auditUserId;

  return (
    <>
      <AuditFlow.OperateCard
        loading={loading || getAuditHistoryLoading}
        data={auditHistory}
        operateButtons={[
          {
            show: isAuditUser && isAuditStatus,
            children: '通过',
            onClick: openApproveAuditModal,
          },
          {
            show: isAuditUser && isAuditStatus,
            children: '拒绝',
            danger: true,
            onClick: () => {
              setAuditModalOpen(true);
              setAuditModalType('reject');
            },
          },
          {
            show: isAuditStatus && isSubmitter,
            children: '撤回',
            className: 'ml-auto',
            onClick: handleRevoke,
          },
        ]}
      />

      {auditModalNode}
    </>
  );
});

export default IntendedContractExtensionAuditCard;
