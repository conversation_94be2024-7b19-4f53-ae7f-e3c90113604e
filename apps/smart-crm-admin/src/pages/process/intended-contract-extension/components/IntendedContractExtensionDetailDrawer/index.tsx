import React, { useRef, useState } from 'react';
import { intendedContractExtensionAuditStatusOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { ButtonGroup } from '@src/components';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import IntentionDetailDrawer from '@src/pages/contract/intention/components/IntentionDetailDrawer';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import { PermissionsMap, usePermission } from '@src/permissions';
import { AuditStatusEnum } from '@src/services/common/type';
import {
  deleteIntendedContractExtension,
  getIntendedContractExtensionDetail,
} from '@src/services/process/intended-contract-extension';
import { IntendedContractExtensionDTO } from '@src/services/process/intended-contract-extension/type';
import { renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Card, Col, Drawer, DrawerProps, Row, Tabs } from 'antd';
import IntendedContractExtensionAuditCard, {
  IntendedContractExtensionAuditCardRef,
} from './IntendedContractExtensionAuditCard';
import IntendedContractExtensionModal from '../IntendedContractExtensionModal';

interface IntendedContractExtensionDetailDrawerProps extends DrawerProps {
  getId: () => number | null;
  onUpdate?: () => void;
  onDelete?: () => void;
  onTodoSuccess?: () => void;
}

const IntendedContractExtensionDetailDrawer = React.forwardRef<
  ModalRef,
  IntendedContractExtensionDetailDrawerProps
>(({ getId, onUpdate, onDelete, onTodoSuccess, ...props }, ref) => {
  const { modal, message } = App.useApp();
  const [open, setOpen] = useState(false);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const intendedContractExtensionModalRef = useRef<ModalRef>(null);
  const intentionDetailDrawerRef = useRef<ModalRef>(null);
  const auditCardRef = useRef<IntendedContractExtensionAuditCardRef>(null);

  const id = getId()!;

  const checkPermission = usePermission();
  const { data: users } = useAllUsers({ ready: open });
  const { data, loading, refresh, mutate } = useRequest(
    () => getIntendedContractExtensionDetail(id),
    {
      ready: open,
      onBefore: () => mutate(undefined),
    },
  );

  React.useImperativeHandle(ref, () => ({
    open: () => setOpen(true),
    close: () => setOpen(false),
  }));

  const baseInfoItems: DescriptionFieldType<IntendedContractExtensionDTO>[] = [
    {
      label: '申请编号',
      field: 'code',
    },
    {
      label: '意向合同编号',
      field: 'contractCode',
      children: (
        <a onClick={() => intentionDetailDrawerRef.current?.open()}>{data?.contractCode}</a>
      ),
    },
    {
      label: '审核状态',
      field: 'auditStatus',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: intendedContractExtensionAuditStatusOptions,
      },
    },
    {
      label: '意向区域',
      field: 'regionCode',
      valueType: ValueType.REGION,
      fieldProps: {
        regionLevel: 4,
      },
    },
    {
      label: '意向生效日期',
      field: 'effectiveDate',
    },
    {
      label: '意向到期日期',
      field: 'expirationDate',
    },
    {
      label: '延期天数',
      field: 'postponeDayNum',
    },
    {
      label: '延期到期日期',
      field: 'postponeExpirationDate',
    },
    {
      label: '申请说明',
      field: 'description',
    },
    {
      label: '申请附件',
      field: 'applyAttachments',
      valueType: ValueType.ATTACHMENT,
    },
  ];

  return (
    <Drawer
      title="意向合同延期详情"
      width={1200}
      open={open}
      push={false}
      destroyOnHidden
      onClose={() => setOpen(false)}
      {...props}
    >
      <Card loading={loading}>
        <div className="flex justify-between">
          <div className="flex flex-col gap-2">
            <p>
              客户名称：
              <a onClick={() => customerDetailDrawerRef.current?.open()}>{data?.customerName}</a>
            </p>
            <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
            <p>创建时间：{data?.createTime}</p>
          </div>
          <ButtonGroup
            items={[
              {
                label: '编辑',
                show:
                  checkPermission(PermissionsMap.ProcessIntendedContractExtensionEdit) &&
                  [AuditStatusEnum.REVOKE, AuditStatusEnum.NOT_PASS].includes(data?.auditStatus!),
                onClick: () => intendedContractExtensionModalRef.current?.open(),
              },
              {
                label: '删除',
                danger: true,
                show:
                  checkPermission(PermissionsMap.ProcessIntendedContractExtensionDelete) &&
                  data?.auditStatus === AuditStatusEnum.REVOKE,
                onClick: () =>
                  modal.confirm({
                    title: '删除',
                    content: '确定要删除该意向合同延期申请吗？',
                    onOk: async () => {
                      await deleteIntendedContractExtension(id);
                      message.success('删除成功');
                      setOpen(false);
                      onDelete?.();
                    },
                  }),
              },
            ]}
          />
        </div>
      </Card>
      <Row gutter={[20, 20]} className="mt-5">
        <Col span={24} xl={16}>
          <Card loading={loading}>
            <Tabs
              className="-mt-5"
              items={[
                {
                  label: '详情',
                  key: 'info',
                  children: renderDescriptions(baseInfoItems, data),
                },
              ]}
            />
          </Card>
        </Col>
        <Col span={24} xl={8}>
          <IntendedContractExtensionAuditCard
            ref={auditCardRef}
            id={id}
            auditStatus={data?.auditStatus}
            loading={loading}
            onTodoSuccess={onTodoSuccess}
            onSuccess={() => {
              refresh();
              onUpdate?.();
              auditCardRef.current?.refresh();
            }}
          />
        </Col>
      </Row>

      <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => data?.customerId} />
      <IntendedContractExtensionModal
        ref={intendedContractExtensionModalRef}
        getId={() => id}
        onSuccess={() => {
          refresh();
          onUpdate?.();
          auditCardRef.current?.refresh();
        }}
      />
      <IntentionDetailDrawer ref={intentionDetailDrawerRef} getId={() => data?.contractId!} />
    </Drawer>
  );
});

export default IntendedContractExtensionDetailDrawer;
