import React, { useState } from 'react';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { Encrypt } from '@src/components';
import EditableRegion from '@src/components/Editable/EditableRegion';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import { getCustomerDetail } from '@src/services/customers';
import { saveInterviewNotice } from '@src/services/process/interview-notice';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps, Select } from 'antd';

interface InterviewNoticeModalProps extends ModalProps {
  getId?: () => number | null;
  /** 快捷创建时的合同 id */
  getCreateId?: () => number | null;
  onSuccess?: () => void;
}

const InterviewNoticeCreateModal = React.forwardRef<ModalRef, InterviewNoticeModalProps>(
  ({ getId, getCreateId, onSuccess, ...props }, ref) => {
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    const { message } = App.useApp();
    // 新建弹窗才会有值
    const id = getId?.();
    const isDetail = !!id;

    const { runAsync: create, loading: createLoading } = useRequest(saveInterviewNotice, {
      manual: true,
    });

    const {
      data: newCustomer,
      loading: getNewCustomerLoading,
      mutate: mutateNewCustomer,
    } = useRequest(getCustomerDetail, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
      },
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      const { area, ...rest } = values;
      const [provinceCode, cityCode, regionCode] = area.split('/');

      const params = {
        provinceCode,
        cityCode,
        regionCode,
        ...rest,
      };

      await create(params);
      message.success('保存成功');
      setOpen(false);
      onSuccess?.();
    };

    return (
      <Modal
        title={isDetail ? '面谈前告知函详情' : '新建面谈前告知函'}
        footer={isDetail ? false : undefined}
        open={open}
        destroyOnHidden
        confirmLoading={createLoading}
        okText="提交审核"
        styles={{
          body: { maxHeight: '60vh', margin: '0 -24px', padding: '0 24px', overflow: 'auto' },
        }}
        width={800}
        modalRender={(node) => (
          <Form
            form={form}
            clearOnDestroy
            scrollToFirstError
            labelCol={{ span: 8 }}
            onFinish={handleSave}
            disabled={isDetail}
          >
            {node}
          </Form>
        )}
        onCancel={() => setOpen(false)}
        onOk={!isDetail ? form.submit : undefined}
        {...props}
      >
        <Form.Item
          label="签约人姓名"
          name="customerId"
          rules={[{ required: true, message: '请选择新业主姓名' }]}
        >
          <RelCustomer
            editable={!isDetail}
            allowClear={!isDetail}
            onChange={(_, info) => {
              mutateNewCustomer(info!);
              form.setFieldsValue({
                signerPhone: info?.phones?.[0],
                signerIdentityCard: info?.identityCard,
              });
            }}
          />
        </Form.Item>
        <Form.Item
          name="signerPhone"
          label="签约人联系方式"
          validateFirst
          validateTrigger={['onChange', 'onBlur']}
          rules={[{ required: true, message: '请选择' }]}
        >
          <Encrypt.PhoneSelect
            loading={getNewCustomerLoading}
            placeholder="请选择手机号"
            options={newCustomer?.phones?.map((phone, index) => ({
              label: newCustomer.phonesSensitive?.[index],
              value: phone,
            }))}
          />
        </Form.Item>
        <Encrypt.IdCardFormItem
          formItemProps={{
            label: '签约人身份证号码',
            name: 'signerIdentityCard',
            rules: [{ required: true, message: '请选择身份证号码' }],
          }}
          fieldProps={{
            disabled: true,
            placeholder: '选择加盟商后自动填充',
            encryptSensitiveMap: {
              [newCustomer?.identityCard || '']: newCustomer?.identityCardSensitive,
            },
          }}
        />
        <Form.Item
          name="toPublic"
          label="是否对公"
          initialValue={false}
          rules={[{ required: true, message: '请选择是否对公' }]}
        >
          <Select options={yesOrNoOptions} placeholder="请选择是否对公" />
        </Form.Item>
        <Form.Item noStyle dependencies={['toPublic']}>
          {({ getFieldValue }) =>
            getFieldValue('toPublic') && (
              <>
                <Form.Item
                  label="签约主体"
                  name="signSubject"
                  rules={[
                    { required: true, message: '请输入签约主体' },
                    { max: 200, message: '最大长度为200个字符' },
                  ]}
                >
                  <Input placeholder="请输入签约主体" />
                </Form.Item>
                <Form.Item
                  label="统一社会信用代码"
                  name="socialCreditCode"
                  rules={[
                    { required: true, message: '请输入统一社会信用代码' },
                    { max: 200, message: '最大长度为200个字符' },
                  ]}
                >
                  <Input placeholder="请输入统一社会信用代码" />
                </Form.Item>
              </>
            )
          }
        </Form.Item>
        <EditableRegion
          editable
          fieldProps={{
            regionLevel: 3,
          }}
          formItemProps={{
            label: '意向区域',
            name: 'area',
            validateFirst: true,
            rules: [{ required: true, message: '请选择意向区域' }],
          }}
        />
      </Modal>
    );
  },
);

export default InterviewNoticeCreateModal;
