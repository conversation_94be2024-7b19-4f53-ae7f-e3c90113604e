import React, { useRef, useState } from 'react';
import { ModalRef } from '@src/common/interface';
import EditableRegion from '@src/components/Editable/EditableRegion';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import {
  getInterviewNoticeDetail,
  getInterviewNoticeDetailByCode,
} from '@src/services/process/interview-notice';
import { renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { Card, Drawer, DrawerProps, Form } from 'antd';

interface InterviewNoticeDetailDrawerProps extends DrawerProps {
  getId?: () => number | null;
  getCode?: () => string | number | null;
}

const InterviewNoticeDetailDrawer = React.forwardRef<ModalRef, InterviewNoticeDetailDrawerProps>(
  ({ getId, getCode, ...props }, ref) => {
    const customerDetailDrawerRef = useRef<ModalRef>(null);
    const shopDetailDrawerRef = useRef<ModalRef>(null);
    const shopIdRef = useRef<string | null>(null);

    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    const [customerId, setCustomerId] = useState<number>(-1);
    // 新建弹窗才会有值
    const id = getId?.();
    const code = getCode?.();
    const { data: users } = useAllUsers({ ready: open });

    const methodName = id ? getInterviewNoticeDetail : getInterviewNoticeDetailByCode;
    const key = id ? (id as number) : (code as string);
    const isDetail = !!key;

    const { data, loading } = useRequest(() => methodName(key), {
      ready: open && isDetail,
      onSuccess: (res) => {
        form.setFieldsValue({
          ...res,
          // weaverRegion: {
          //   label: res.weaverRegionName,
          //   value: res.weaverRegionId,
          // },
          // weaverDistrict: {
          //   label: res.weaverDistrictName,
          //   value: res.weaverDistrictId,
          // },
          // weaverTargetRegion: {
          //   label: res.weaverTargetRegionName,
          //   value: res.weaverTargetRegionId,
          // },
        });
      },
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
      },
      close: () => setOpen(false),
    }));

    const items = [
      {
        field: 'customerId',
        label: '签约人',
        children: (
          <a
            onClick={() => {
              setCustomerId(data?.customerId!);
              customerDetailDrawerRef.current?.open();
            }}
          >
            {data?.customerName}
          </a>
        ),
      },
      {
        field: 'signerPhone',
        label: '签约人联系方式',
        valueType: ValueType.PHONE,
        fieldProps: {
          sensitiveValue: data?.signerPhoneSensitive,
        },
      },
      {
        field: 'signerIdentityCard',
        label: '签约人身份证号码',
        valueType: ValueType.IDENTITY_CARD,
        fieldProps: {
          sensitiveValue: data?.signerIdentityCardSensitive,
        },
      },
      {
        field: 'toPublic',
        label: '是否对公',
        children: <div>{data?.toPublic ? '是' : '否'}</div>,
      },
      {
        field: 'signSubject',
        label: '签约主体',
        valueType: ValueType.TEXT,
      },
      {
        field: 'socialCreditCode',
        label: '统一社会信用代码',
        valueType: ValueType.TEXT,
      },
      {
        field: 'regionCode',
        label: '意向区域',
        children: (
          <EditableRegion
            value={`${data?.provinceCode}/${data?.cityCode}/${data?.regionCode}`}
            fieldProps={{ regionLevel: 3 }}
          />
        ),
      },
    ];

    return (
      <Drawer
        width={800}
        title="面谈前告知函详情"
        open={open}
        destroyOnHidden
        loading={loading}
        onClose={() => setOpen(false)}
        {...props}
      >
        <Card loading={loading}>
          <div className="flex flex-col gap-2">
            <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
            <p>创建时间：{data?.createTime}</p>
          </div>
        </Card>
        <Card loading={loading} className="mt-5">
          {renderDescriptions(items, data)}
        </Card>
        <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerId!} />
        <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => shopIdRef.current} />
      </Drawer>
    );
  },
);

export default InterviewNoticeDetailDrawer;
