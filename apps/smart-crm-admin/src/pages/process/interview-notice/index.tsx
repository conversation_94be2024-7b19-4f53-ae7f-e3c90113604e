import { useRef, useState } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ModalRef } from '@src/common/interface';
import { ExportButton, ProTable } from '@src/components';
import EditableIdentityCard from '@src/components/Editable/EditableIdentityCard';
import EditablePhone from '@src/components/Editable/EditablePhone';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useAllUsers from '@src/hooks/useAllUsers';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { Permission, PermissionsMap } from '@src/permissions';
import {
  exportInterviewNotice,
  getInterviewNoticeNodes,
  getInterviewNoticePage,
} from '@src/services/process/interview-notice';
import {
  InterviewNoticeDTO,
  InterviewNoticeReq,
} from '@src/services/process/interview-notice/type';
import { useRequest } from 'ahooks';
import { Button } from 'antd';
import InterviewNoticeCreateModal from './components/InterviewNoticeCreateModal';
import InterviewNoticeDetailDrawer from './components/InterviewNoticeDetailDrawer';

const InterviewNotice = () => {
  const actionRef = useRef<ActionType>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const currentIdRef = useRef<number | null>(null);
  const shopIdRef = useRef<string | null>(null);
  const shopDetailDrawer = useRef<ModalRef>(null);
  const filterParamsRef = useRef<InterviewNoticeReq>({} as InterviewNoticeReq);
  const shopRelocationApplicationModalRef = useRef<ModalRef>(null);
  const shopRelocationApplicationDetailRef = useRef<ModalRef>(null);

  const { data, runAsync: getList } = useRequest(getInterviewNoticePage, { manual: true });

  const { data: nodesValueEnum } = useRequest(async () => {
    const res = await getInterviewNoticeNodes();

    return res.reduce((acc: { [x: string]: any }, cur: string | number) => {
      acc[cur] = cur;

      return acc;
    }, {});
  });

  const { data: users } = useAllUsers();

  const columns: ProColumns<InterviewNoticeDTO>[] = [
    {
      title: '申请编号',
      dataIndex: 'code',
      fixed: 'left',
      renderText: (value, record) => (
        <a
          onClick={() => {
            currentIdRef.current = record.id;
            shopRelocationApplicationDetailRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '是否对公',
      dataIndex: 'toPublic',
      valueType: 'select',
      valueEnum: {
        true: '是',
        false: '否',
      },
      search: false,
    },
    {
      title: '签约人姓名',
      dataIndex: 'customerName',
      width: 210,
    },
    {
      title: '签约人联系方式',
      dataIndex: 'signerPhone',
      search: false,
      renderText: (value, { signerPhoneSensitive }) => (
        <EditablePhone value={value} fieldProps={{ sensitiveValue: signerPhoneSensitive }} />
      ),
    },
    {
      title: '签约人身份证号码',
      dataIndex: 'signerIdentityCard',
      width: 210,
      search: false,
      renderText: (value, { signerIdentityCardSensitive }) => (
        <EditableIdentityCard
          value={value}
          fieldProps={{ sensitiveValue: signerIdentityCardSensitive }}
        />
      ),
    },
    {
      title: '签约主体',
      search: false,
      dataIndex: 'signSubject',
    },
    {
      title: '统一社会信用代码',
      search: false,
      dataIndex: 'socialCreditCode',
    },
    {
      title: '意向区域',
      dataIndex: 'regionCode',
      width: 210,
      search: false,
      render: (_v, { provinceCode, cityCode, regionCode }) => (
        <EditableRegion
          value={`${provinceCode}/${cityCode}/${regionCode}`}
          fieldProps={{ regionLevel: 3 }}
        />
      ),
    },
    {
      title: '流程节点',
      dataIndex: 'node',
      width: 210,
      valueEnum: nodesValueEnum,
      fieldProps: {
        mode: 'multiple',
      },
    },
    {
      title: '归档时间',
      dataIndex: 'archiveTime',
      search: false,
      width: 180,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      search: false,
      width: 180,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      search: false,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      search: false,
      width: 180,
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        cardProps={false}
        bordered
        rowKey="id"
        sticky
        size="small"
        columnEmptyText=""
        columns={columns}
        scroll={{ x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0) }}
        options={false}
        tableAlertRender={false}
        search={{
          labelWidth: 100,
        }}
        toolbar={{
          subTitle: (
            <span className="text-black">
              共 {data?.total || 0} 条记录
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 条
                  <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            <Permission value={PermissionsMap.InterviewNoticeCreate}>
              <Button
                type="text"
                onClick={() => {
                  currentIdRef.current = null;
                  shopRelocationApplicationModalRef.current?.open();
                }}
              >
                新建
              </Button>
            </Permission>,
            <Permission value={PermissionsMap.InterviewNoticeExport}>
              <ExportButton
                total={selectedRowKeys.length || data?.total}
                request={() =>
                  exportInterviewNotice(
                    selectedRowKeys.length ? { ids: selectedRowKeys } : filterParamsRef.current,
                  )
                }
              />
            </Permission>,
          ],
        }}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        rowSelection={{
          fixed: true,
          showSort: true,
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        request={async ({ current, pageSize, ...restParams }) => {
          filterParamsRef.current = restParams as InterviewNoticeReq;

          const res = await getList({ pageNum: current, pageSize, ...restParams });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />
      <InterviewNoticeCreateModal
        ref={shopRelocationApplicationModalRef}
        getId={() => currentIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
      <InterviewNoticeDetailDrawer
        ref={shopRelocationApplicationDetailRef}
        getId={() => currentIdRef.current}
      />
      <ShopDetailDrawer ref={shopDetailDrawer} getId={() => shopIdRef.current} />
    </PageContainer>
  );
};

export default InterviewNotice;
