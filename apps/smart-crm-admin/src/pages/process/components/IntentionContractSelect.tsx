import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ConditionEnum, ValueType } from '@src/components/ETable';
import { getIntentionContractList } from '@src/services/contract/intention';
import { IntentionContractDTO } from '@src/services/contract/intention/type';
import { useRequest } from 'ahooks';
import { Select, SelectProps, Spin } from 'antd';
import { debounce } from 'lodash-es';

const IntentionContractSelect: React.FC<SelectProps> = (props) => {
  const [options, setOptions] = useState<IntentionContractDTO[]>([]);
  const searchValue = useRef('');
  const pageNum = useRef(1);

  const {
    data: { total } = {},
    runAsync: getIntentionContractOptions,
    loading,
    cancel,
  } = useRequest(getIntentionContractList, {
    manual: true,
  });

  const searchIntentionContractOptions = (value?: string) => {
    return getIntentionContractOptions({
      pageNum: pageNum.current,
      pageSize: 20,
      fields: value
        ? [{ field: 'code', condition: ConditionEnum.LIKE, value, valueType: ValueType.TEXT }]
        : [],
    });
  };

  useEffect(() => {
    searchIntentionContractOptions().then((res) => {
      setOptions(res.result);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onSearch = useMemo(() => {
    const fetchOptions = (value: string) => {
      cancel();
      setOptions([]);
      searchValue.current = value;
      pageNum.current = 1;
      searchIntentionContractOptions(value).then((res) => {
        setOptions(res.result);
      });
    };

    return debounce(fetchOptions, 1000);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Select
      options={options}
      placeholder="输入意向合同编码进行搜索"
      loading={loading}
      filterOption={false}
      showSearch
      fieldNames={{ label: 'code', value: 'code' }}
      notFoundContent={
        loading ? (
          <div className="flex justify-center items-center h-10">
            <Spin />
          </div>
        ) : undefined
      }
      onSearch={onSearch}
      onPopupScroll={({ currentTarget }) => {
        if (
          currentTarget.scrollHeight - currentTarget.scrollTop - currentTarget.offsetHeight < 10 &&
          !loading &&
          total &&
          options.length < total
        ) {
          pageNum.current += 1;
          searchIntentionContractOptions(searchValue.current).then((res) => {
            setOptions((prev) => prev.concat(res.result));
          });
        }
      }}
      {...props}
    />
  );
};

export default IntentionContractSelect;
