import React, { useRef, useState } from 'react';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { ButtonGroup } from '@src/components';
import EditableAttachment from '@src/components/Editable/EditableAttachment';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { PermissionsMap, usePermission } from '@src/permissions';
import { queryFoodLicenseDetail } from '@src/services/process/store-setup-task/food-license';
import { FoodLicenseAttachmentModuleTypeEnum } from '@src/services/process/store-setup-task/food-license/type';
import { renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { Card, Drawer, DrawerProps } from 'antd';
import CreateEditFoodLicenseModal from './CreateEditFoodLicenseModal';

interface FoodLicenseDetailDrawerProps extends DrawerProps {
  getId: () => number | null;
  onUpdate?: () => void;
}

const FoodLicenseDetailDrawer = React.forwardRef<ModalRef, FoodLicenseDetailDrawerProps>(
  ({ getId, onUpdate, ...props }, ref) => {
    const [open, setOpen] = useState(false);
    const createEditFoodLicenseModalRef = useRef<ModalRef>(null);
    const shopDetailDrawerRef = useRef<ModalRef>(null);
    const checkPermission = usePermission();

    const id = getId();
    const { data, loading, refresh } = useRequest(() => queryFoodLicenseDetail(id!), {
      ready: open,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const infoItems: DescriptionFieldType[] = [
      {
        field: 'operatorName',
        label: '经营者名称',
      },
      {
        field: FoodLicenseAttachmentModuleTypeEnum.FOOD_BUSINESS_LICENSE,
        label: '食品许可证照片',
        children: (
          <EditableAttachment
            value={
              data?.attachments.find(
                (i) => i.moduleType === FoodLicenseAttachmentModuleTypeEnum.FOOD_BUSINESS_LICENSE,
              )?.attachments
            }
          />
        ),
      },
      {
        field: 'businessCategory',
        label: '主体业态',
      },
      {
        field: 'licenseNumber',
        label: '许可证编号',
      },
      {
        field: 'issueDate',
        label: '办理日期',
      },
      {
        field: 'licenseExpiryDate',
        label: '有效期至',
      },
      {
        field: 'businessAddress',
        label: '经营地址',
      },
      {
        field: 'businessScope',
        label: '经营项目',
      },
    ];

    return (
      <Drawer
        open={open}
        width={800}
        destroyOnHidden
        push={false}
        title="食品许可证详情"
        onClose={() => setOpen(false)}
        {...props}
      >
        <Card loading={loading}>
          <div className="flex justify-between">
            <div className="flex flex-col gap-1">
              <span>
                门店编号：
                <a
                  onClick={() => {
                    shopDetailDrawerRef.current?.open();
                  }}
                >
                  {data?.shopId}
                </a>
              </span>
              <span>门店冠名：{data?.shopName}</span>
              <span>创建时间：{data?.createTime}</span>
              <span>更新时间：{data?.updateTime}</span>
            </div>
            <ButtonGroup
              items={[
                {
                  show: checkPermission(PermissionsMap.ProcessStoreSetupTaskFoodLicenseEdit),
                  label: '编辑',
                  onClick: () => createEditFoodLicenseModalRef.current?.open(),
                },
              ]}
            />
          </div>
        </Card>
        <Card className="mt-5" loading={loading}>
          {renderDescriptions(infoItems, data)}
        </Card>

        <CreateEditFoodLicenseModal
          ref={createEditFoodLicenseModalRef}
          getId={() => id}
          onSuccess={() => {
            refresh();
            onUpdate?.();
          }}
        />
        <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => data?.shopId} />
      </Drawer>
    );
  },
);

export default FoodLicenseDetailDrawer;
