import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { FileUpload, ShopSelect } from '@src/components';
import EditableDate from '@src/components/Editable/EditableDate';
import { FileDTO } from '@src/services/common/type';
import {
  createFoodLicense,
  queryFoodLicenseDetail,
  updateFoodLicense,
} from '@src/services/process/store-setup-task/food-license';
import { FoodLicenseAttachmentModuleTypeEnum } from '@src/services/process/store-setup-task/food-license/type';
import { ShopDTO } from '@src/services/shop/list/type';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps } from 'antd';
import dayjs from 'dayjs';

interface CreateEditFoodLicenseModalProps extends ModalProps {
  getId: () => number | null;
  onSuccess: () => void;
}

const CreateEditFoodLicenseModal = React.forwardRef<ModalRef, CreateEditFoodLicenseModalProps>(
  ({ getId, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();

    const id = getId();
    const isEdit = !!id;

    const { runAsync: create, loading: createLoading } = useRequest(createFoodLicense, {
      manual: true,
    });
    const { runAsync: update, loading: updateLoading } = useRequest(updateFoodLicense, {
      manual: true,
    });

    const { loading } = useRequest(() => queryFoodLicenseDetail(id!), {
      ready: open && isEdit,
      onSuccess: (res) => {
        form.setFieldsValue({
          ...res,
          attachments: res.attachments.reduce<Record<string, FileDTO[]>>(
            (total, cur) => ({
              ...total,
              [cur.moduleType]: cur.attachments,
            }),
            {},
          ),
        });
      },
    });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      const params = {
        ...values,
        attachments: Object.keys(values.attachments).map((moduleType) => ({
          moduleType,
          attachments: values.attachments[moduleType],
        })),
      };

      if (isEdit) {
        await update({ ...params, id });
        message.success('修改成功');
      } else {
        await create(params);
        message.success('创建成功');
      }

      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        title={isEdit ? '编辑食品许可证' : '新建食品许可证'}
        open={open}
        destroyOnHidden
        loading={loading}
        confirmLoading={createLoading || updateLoading}
        styles={{
          body: { maxHeight: '60vh', margin: '0 -24px', padding: '0 24px', overflow: 'auto' },
        }}
        modalRender={(node) => (
          <Form
            form={form}
            clearOnDestroy
            scrollToFirstError
            labelCol={{ span: 8 }}
            onFinish={handleSave}
          >
            {node}
          </Form>
        )}
        onCancel={() => setOpen(false)}
        onOk={form.submit}
        {...props}
      >
        <Form.Item
          label="门店编号"
          name="shopId"
          rules={[{ required: true, message: '请选择门店' }]}
        >
          <ShopSelect
            disabled={isEdit}
            onChange={(_, shop) => {
              form.setFieldValue('shopName', (shop as ShopDTO | undefined)?.shopName);
            }}
          />
        </Form.Item>
        <Form.Item
          label="门店冠名"
          name="shopName"
          rules={[{ required: true, message: '请选择门店' }]}
        >
          <Input disabled placeholder="选择门店编号后自动填充" />
        </Form.Item>
        <Form.Item
          label="经营者名称"
          name="operatorName"
          rules={[{ required: true, message: '请输入经营者名称' }]}
        >
          <Input placeholder="请输入经营者名称" />
        </Form.Item>
        <FileUpload
          formItemProps={{
            label: '食品许可证照片',
            name: ['attachments', FoodLicenseAttachmentModuleTypeEnum.FOOD_BUSINESS_LICENSE],
            rules: [{ required: true, message: '请上传食品许可证照片' }],
          }}
          uploadProps={{
            accept: 'image/*',
            maxCount: 1,
          }}
        />
        <Form.Item
          label="主体业态"
          name="businessCategory"
          rules={[{ required: true, message: '请输入主体业态' }]}
        >
          <Input placeholder="请输入主体业态" />
        </Form.Item>
        <Form.Item
          label="许可证编号"
          name="licenseNumber"
          rules={[{ required: true, message: '请输入许可证编号' }]}
        >
          <Input placeholder="请输入许可证编号" />
        </Form.Item>
        <EditableDate
          editable
          formItemProps={{
            label: '办理日期',
            name: 'issueDate',
            rules: [{ required: true, message: '请选择办理日期' }],
          }}
        />
        <EditableDate
          editable
          formItemProps={{
            label: '有效期至',
            name: 'licenseExpiryDate',
            validateFirst: true,
            dependencies: ['issueDate'],
            rules: [
              { required: true, message: '请选择有效期' },
              {
                validator: (_, value) => {
                  const issueDate = form.getFieldValue('issueDate');

                  if (issueDate && value && dayjs(value).subtract(1, 'day').isBefore(issueDate)) {
                    return Promise.reject(new Error('不能早于或等于办理日期'));
                  }

                  return Promise.resolve();
                },
              },
            ],
          }}
        />
        <Form.Item
          label="经营地址"
          name="businessAddress"
          rules={[{ required: true, message: '请输入经营地址' }]}
        >
          <Input placeholder="请输入经营地址" />
        </Form.Item>
        <Form.Item
          label="经营项目"
          name="businessScope"
          rules={[{ required: true, message: '请输入经营项目' }]}
        >
          <Input.TextArea placeholder="请输入经营项目" maxLength={255} />
        </Form.Item>
      </Modal>
    );
  },
);

export default CreateEditFoodLicenseModal;
