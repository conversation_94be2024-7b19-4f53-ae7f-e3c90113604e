import { useEffect, useRef } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ModalRef } from '@src/common/interface';
import { ProTable, TableActions } from '@src/components';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { queryFoodLicensePageList } from '@src/services/process/store-setup-task/food-license';
import { compatibleTableActionWidth } from '@src/utils';
import { useRequest } from 'ahooks';
import { Button } from 'antd';
import { useSearchParams } from 'react-router-dom';
import CreateEditFoodLicenseModal from './components/CreateEditFoodLicenseModal';
import FoodLicenseDetailDrawer from './components/FoodLicenseDetailDrawer';

const FoodLicense = () => {
  const currentIdRef = useRef<number | null>(null);
  const actionRef = useRef<ActionType>(null);
  const createEditFoodLicenseModalRef = useRef<ModalRef>(null);
  const detailDrawerRef = useRef<ModalRef>(null);
  const shopDetailDrawerRef = useRef<ModalRef>(null);
  const shopIdRef = useRef<string | null>(null);
  const checkPermission = usePermission();
  const [searchParams, setSearchParams] = useSearchParams();

  const { data, runAsync: queryList } = useRequest(queryFoodLicensePageList, { manual: true });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      currentIdRef.current = Number(id);
      detailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: ProColumns[] = [
    {
      title: '门店编号',
      dataIndex: 'shopId',
      fixed: 'left',
      render: (_, { shopId }) => (
        <a
          onClick={() => {
            shopIdRef.current = shopId;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {shopId}
        </a>
      ),
    },
    {
      title: '门店冠名',
      dataIndex: 'shopName',
    },
    {
      title: '经营者名称',
      dataIndex: 'operatorName',
    },
    {
      title: '主体业态',
      dataIndex: 'businessCategory',
      search: false,
    },
    {
      title: '许可证编号',
      dataIndex: 'licenseNumber',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      search: false,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      search: false,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      width: compatibleTableActionWidth(100),
      fixed: 'right',
      search: false,
      render: (_, { id }) => (
        <TableActions
          shortcuts={[
            {
              show: checkPermission(PermissionsMap.ProcessStoreSetupTaskFoodLicenseDetail),
              label: '详情',
              onClick: () => {
                currentIdRef.current = id;
                detailDrawerRef.current?.open();
              },
            },
            {
              show: checkPermission(PermissionsMap.ProcessStoreSetupTaskFoodLicenseEdit),
              label: '编辑',
              onClick: () => {
                currentIdRef.current = id;
                createEditFoodLicenseModalRef.current?.open();
              },
            },
          ]}
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        rowKey="id"
        cardProps={false}
        bordered
        sticky
        columns={columns}
        scroll={{ x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0) }}
        toolbar={{
          subTitle: <span className="text-black">共 {data?.total || 0} 条信息</span>,
          actions: [
            <Permission value={PermissionsMap.ProcessStoreSetupTaskFoodLicenseCreate}>
              <Button
                type="text"
                onClick={() => {
                  currentIdRef.current = null;
                  createEditFoodLicenseModalRef.current?.open();
                }}
              >
                新建
              </Button>
            </Permission>,
          ],
        }}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        request={async ({ current, ...params }) => {
          const { result, total } = await queryList({ pageNum: current, ...params });

          return {
            data: result,
            total,
          };
        }}
      />

      <CreateEditFoodLicenseModal
        ref={createEditFoodLicenseModalRef}
        getId={() => currentIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
      <FoodLicenseDetailDrawer
        ref={detailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
      />
      <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => shopIdRef.current} />
    </PageContainer>
  );
};

export default FoodLicense;
