import React, { useRef, useState } from 'react';
import { yesOrNoOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { ButtonGroup } from '@src/components';
import EditableAttachment from '@src/components/Editable/EditableAttachment';
import { ValueType } from '@src/components/ETable';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { PermissionsMap, usePermission } from '@src/permissions';
import { queryBusinessLicenseDetail } from '@src/services/process/store-setup-task/business-license';
import {
  BusinessLicenseAttachmentModuleTypeEnum,
  LicenseTypeEnum,
  ValidityTypeEnum,
} from '@src/services/process/store-setup-task/business-license/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { Card, Drawer, DrawerProps } from 'antd';
import CreateEditBusinessLicenseModal from './CreateEditBusinessLicenseModal';

interface BusinessLicenseDetailDrawerProps extends DrawerProps {
  getId: () => number | null;
  onUpdate?: () => void;
}

const BusinessLicenseDetailDrawer = React.forwardRef<ModalRef, BusinessLicenseDetailDrawerProps>(
  ({ getId, onUpdate, ...props }, ref) => {
    const [open, setOpen] = useState(false);
    const createEditBusinessLicenseModalRef = useRef<ModalRef>(null);
    const shopDetailDrawerRef = useRef<ModalRef>(null);
    const checkPermission = usePermission();

    const id = getId();
    const { data, loading, refresh } = useRequest(() => queryBusinessLicenseDetail(id!), {
      ready: open,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const infoItems: DescriptionFieldType[] = [
      {
        field: 'licenseName',
        label: '营业执照名称',
      },
      {
        field: BusinessLicenseAttachmentModuleTypeEnum.BUSINESS_LICENSE,
        label: '营业执照照片',
        children: (
          <EditableAttachment
            value={
              data?.attachments.find(
                (i) => i.moduleType === BusinessLicenseAttachmentModuleTypeEnum.BUSINESS_LICENSE,
              )?.attachments
            }
          />
        ),
      },
      {
        field: 'licenseType',
        label: '营业执照类型',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(LicenseTypeEnum),
        },
      },
      {
        field: 'licenseNumber',
        label: '营业执照编号',
      },
      {
        field: 'establishmentDate',
        label: '注册日期',
      },
      {
        field: 'validityType',
        label: '有效期类型',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(ValidityTypeEnum),
        },
      },
      {
        field: 'licenseExpiryDate',
        label: '有效期至',
      },
      {
        field: 'businessAddress',
        label: '营业执照地址',
      },
      {
        field: 'legalPerson',
        label: '法人姓名',
      },
      {
        field: 'legalIdentityCard',
        label: '法人身份证号',
        valueType: ValueType.IDENTITY_CARD,
        fieldProps: {
          sensitiveValue: data?.legalIdentityCardSensitive,
        },
      },
      {
        field: 'legalContact',
        label: '法人联系方式',
        valueType: ValueType.PHONE,
        fieldProps: {
          sensitiveValue: data?.legalContactSensitive,
        },
      },
      {
        field: BusinessLicenseAttachmentModuleTypeEnum.CERTIFICATE_PHOTO,
        label: '法人身份证正面',
        children: (
          <EditableAttachment
            value={
              data?.attachments.find(
                (i) => i.moduleType === BusinessLicenseAttachmentModuleTypeEnum.CERTIFICATE_PHOTO,
              )?.attachments
            }
          />
        ),
      },
      {
        field: BusinessLicenseAttachmentModuleTypeEnum.CERTIFICATE_SEAL,
        label: '法人身份证反面',
        children: (
          <EditableAttachment
            value={
              data?.attachments.find(
                (i) => i.moduleType === BusinessLicenseAttachmentModuleTypeEnum.CERTIFICATE_SEAL,
              )?.attachments
            }
          />
        ),
      },
      {
        field: BusinessLicenseAttachmentModuleTypeEnum.HOLD_CERTIFICATE_PHOTO,
        label: '法人手持身份证正面',
        children: (
          <EditableAttachment
            value={
              data?.attachments.find(
                (i) =>
                  i.moduleType === BusinessLicenseAttachmentModuleTypeEnum.HOLD_CERTIFICATE_PHOTO,
              )?.attachments
            }
          />
        ),
      },
      {
        field: BusinessLicenseAttachmentModuleTypeEnum.HOLD_CERTIFICATE_SEAL,
        label: '法人手持身份证反面',
        children: (
          <EditableAttachment
            value={
              data?.attachments.find(
                (i) =>
                  i.moduleType === BusinessLicenseAttachmentModuleTypeEnum.HOLD_CERTIFICATE_SEAL,
              )?.attachments
            }
          />
        ),
      },
      {
        field: 'reuseFoodAccount',
        label: '是否沿用外卖账号',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        field: 'retainGrouponAccount',
        label: '是否沿用团购账号',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
    ];

    return (
      <Drawer
        open={open}
        width={800}
        title="营业执照详情"
        push={false}
        destroyOnHidden
        onClose={() => setOpen(false)}
        {...props}
      >
        <Card loading={loading}>
          <div className="flex justify-between">
            <div className="flex flex-col gap-1">
              <span>
                门店编号：
                <a
                  onClick={() => {
                    shopDetailDrawerRef.current?.open();
                  }}
                >
                  {data?.shopId}
                </a>
              </span>
              <span>门店冠名：{data?.shopName}</span>
              <span>创建时间：{data?.createTime}</span>
              <span>更新时间：{data?.updateTime}</span>
            </div>
            <ButtonGroup
              items={[
                {
                  show: checkPermission(PermissionsMap.ProcessStoreSetupTaskBusinessLicenseEdit),
                  label: '编辑',
                  onClick: () => createEditBusinessLicenseModalRef.current?.open(),
                },
              ]}
            />
          </div>
        </Card>
        <Card className="mt-5" loading={loading}>
          {renderDescriptions(infoItems, data)}
        </Card>

        <CreateEditBusinessLicenseModal
          ref={createEditBusinessLicenseModalRef}
          getId={() => id}
          onSuccess={() => {
            refresh();
            onUpdate?.();
          }}
        />
        <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => data?.shopId} />
      </Drawer>
    );
  },
);

export default BusinessLicenseDetailDrawer;
