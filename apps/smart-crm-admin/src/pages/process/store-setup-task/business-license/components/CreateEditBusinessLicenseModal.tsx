import React, { useState } from 'react';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { Encrypt, FileUpload, ShopSelect } from '@src/components';
import EditableDate from '@src/components/Editable/EditableDate';
import { FileDTO } from '@src/services/common/type';
import {
  createBusinessLicense,
  queryBusinessLicenseDetail,
  queryRelocationStatusByShop,
  updateBusinessLicense,
} from '@src/services/process/store-setup-task/business-license';
import {
  BusinessLicenseAttachmentModuleTypeEnum,
  LicenseTypeEnum,
  ValidityTypeEnum,
} from '@src/services/process/store-setup-task/business-license/type';
import { ShopDTO } from '@src/services/shop/list/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps, Select } from 'antd';
import dayjs from 'dayjs';

interface CreateEditBusinessLicenseModalProps extends ModalProps {
  getId: () => number | null;
  onSuccess: () => void;
}

const CreateEditBusinessLicenseModal = React.forwardRef<
  ModalRef,
  CreateEditBusinessLicenseModalProps
>(({ getId, onSuccess, ...props }, ref) => {
  const { message } = App.useApp();
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();

  const id = getId();
  const isEdit = !!id;

  const { runAsync: create, loading: createLoading } = useRequest(createBusinessLicense, {
    manual: true,
  });
  const { runAsync: update, loading: updateLoading } = useRequest(updateBusinessLicense, {
    manual: true,
  });
  const {
    data: isRelocation,
    runAsync: queryRelocationStatus,
    mutate: mutateRelocationStatus,
  } = useRequest(queryRelocationStatusByShop, { manual: true });

  const { data, loading } = useRequest(() => queryBusinessLicenseDetail(id!), {
    ready: open && isEdit,
    onSuccess: (res) => {
      queryRelocationStatus(res.shopId);
      form.setFieldsValue({
        ...res,
        attachments: res.attachments.reduce<Record<string, FileDTO[]>>(
          (total, cur) => ({
            ...total,
            [cur.moduleType]: cur.attachments,
          }),
          {},
        ),
      });
    },
  });

  React.useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true);
      mutateRelocationStatus(undefined);
    },
    close: () => setOpen(false),
  }));

  const handleSave = async (values: any) => {
    const params = {
      ...values,
      attachments: Object.keys(values.attachments).map((moduleType) => ({
        moduleType,
        attachments: values.attachments[moduleType],
      })),
    };

    if (isEdit) {
      await update({ ...params, id });
      message.success('修改成功');
    } else {
      await create(params);
      message.success('创建成功');
    }

    setOpen(false);
    onSuccess();
  };

  return (
    <Modal
      title={isEdit ? '编辑营业执照' : '新建营业执照'}
      open={open}
      loading={loading}
      destroyOnHidden
      confirmLoading={createLoading || updateLoading}
      styles={{
        body: { maxHeight: '60vh', margin: '0 -24px', padding: '0 24px', overflow: 'auto' },
      }}
      modalRender={(node) => (
        <Form
          form={form}
          clearOnDestroy
          scrollToFirstError
          labelCol={{ span: 8 }}
          onFinish={handleSave}
        >
          {node}
        </Form>
      )}
      onCancel={() => setOpen(false)}
      onOk={form.submit}
      {...props}
    >
      <Form.Item label="门店编号" name="shopId" rules={[{ required: true, message: '请选择门店' }]}>
        <ShopSelect
          disabled={isEdit}
          onChange={(shopId, shop) => {
            mutateRelocationStatus(undefined);
            queryRelocationStatus(shopId);
            form.setFieldValue('shopName', (shop as ShopDTO | undefined)?.shopName);
          }}
        />
      </Form.Item>
      <Form.Item
        label="门店冠名"
        name="shopName"
        rules={[{ required: true, message: '请选择门店' }]}
      >
        <Input disabled placeholder="选择门店编号后自动填充" />
      </Form.Item>
      <Form.Item
        label="营业执照名称"
        name="licenseName"
        rules={[{ required: true, message: '请输入营业执照名称' }]}
      >
        <Input placeholder="请输入营业执照名称" />
      </Form.Item>
      <FileUpload
        formItemProps={{
          label: '营业执照照片',
          name: ['attachments', BusinessLicenseAttachmentModuleTypeEnum.BUSINESS_LICENSE],
          rules: [{ required: true, message: '请上传营业执照照片' }],
        }}
        uploadProps={{
          accept: 'image/*',
          maxCount: 1,
        }}
      />
      <Form.Item
        label="营业执照类型"
        name="licenseType"
        rules={[{ required: true, message: '请选择营业执照类型' }]}
      >
        <Select placeholder="请选择营业执照类型" options={enum2Options(LicenseTypeEnum)} />
      </Form.Item>
      <Form.Item
        label="营业执照编号"
        name="licenseNumber"
        rules={[{ required: true, message: '请输入营业执照编号' }]}
      >
        <Input placeholder="请输入营业执照编号" />
      </Form.Item>
      <EditableDate
        editable
        formItemProps={{
          label: '注册日期',
          name: 'establishmentDate',
          rules: [{ required: true, message: '请选择注册日期' }],
        }}
      />
      <Form.Item
        label="有效期类型"
        name="validityType"
        rules={[{ required: true, message: '请选择有效期类型' }]}
      >
        <Select placeholder="请选择有效期类型" options={enum2Options(ValidityTypeEnum)} />
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) =>
          getFieldValue('validityType') === ValidityTypeEnum.具体期限 && (
            <EditableDate
              editable
              formItemProps={{
                label: '有效期至',
                name: 'licenseExpiryDate',
                validateFirst: true,
                dependencies: ['validityType', 'establishmentDate'],
                rules: [
                  {
                    required: true,
                    message: '请选择有效期',
                  },
                  {
                    validator: (_, value) => {
                      const establishmentDate = form.getFieldValue('establishmentDate');

                      if (
                        establishmentDate &&
                        value &&
                        dayjs(value).subtract(1, 'day').isBefore(establishmentDate)
                      ) {
                        return Promise.reject(new Error('不能早于或等于注册日期'));
                      }

                      return Promise.resolve();
                    },
                  },
                ],
              }}
            />
          )
        }
      </Form.Item>
      <Form.Item
        label="营业执照地址"
        name="businessAddress"
        rules={[{ required: true, message: '请输入营业执照地址' }]}
      >
        <Input placeholder="请输入营业执照地址" />
      </Form.Item>
      <Form.Item
        label="法人姓名"
        name="legalPerson"
        rules={[{ required: true, message: '请输入法人姓名' }]}
      >
        <Input placeholder="请输入法人姓名" />
      </Form.Item>
      <Encrypt.IdCardFormItem
        formItemProps={{
          label: '法人身份证号',
          name: 'legalIdentityCard',
          rules: [{ required: true, message: '请输入法人身份证号' }],
        }}
        fieldProps={{
          placeholder: '请输入法人身份证号',
          encryptSensitiveMap: {
            [data?.legalIdentityCard!]: data?.legalIdentityCardSensitive,
          },
        }}
      />
      <Encrypt.PhoneFormItem
        formItemProps={{
          label: '法人联系方式',
          name: 'legalContact',
          rules: [{ required: true, message: '请输入法人联系方式' }],
        }}
        fieldProps={{
          placeholder: '请输入法人联系方式',
          encryptSensitiveMap: {
            [data?.legalContact!]: data?.legalContactSensitive,
          },
        }}
      />
      <FileUpload
        formItemProps={{
          label: '法人身份证正面',
          name: ['attachments', BusinessLicenseAttachmentModuleTypeEnum.CERTIFICATE_PHOTO],
          rules: [{ required: true, message: '请上传法人身份证正面' }],
        }}
        uploadProps={{
          accept: 'image/*',
          maxCount: 1,
        }}
      />
      <FileUpload
        formItemProps={{
          label: '法人身份证反面',
          name: ['attachments', BusinessLicenseAttachmentModuleTypeEnum.CERTIFICATE_SEAL],
          rules: [{ required: true, message: '请上传法人身份证反面' }],
        }}
        uploadProps={{
          accept: 'image/*',
          maxCount: 1,
        }}
      />
      <FileUpload
        formItemProps={{
          label: '法人手持身份证正面',
          name: ['attachments', BusinessLicenseAttachmentModuleTypeEnum.HOLD_CERTIFICATE_PHOTO],
          rules: [{ required: true, message: '请上传法人手持身份证正面' }],
        }}
        uploadProps={{
          accept: 'image/*',
          maxCount: 1,
        }}
      />
      <FileUpload
        formItemProps={{
          label: '法人手持身份证反面',
          name: ['attachments', BusinessLicenseAttachmentModuleTypeEnum.HOLD_CERTIFICATE_SEAL],
          rules: [{ required: true, message: '请上传法人手持身份证反面' }],
        }}
        uploadProps={{
          accept: 'image/*',
          maxCount: 1,
        }}
      />
      {isRelocation && (
        <>
          <Form.Item
            label="是否沿用外卖账号"
            name="reuseFoodAccount"
            rules={[{ required: true, message: '请选择是否沿用外卖账号' }]}
          >
            <Select options={yesOrNoOptions} placeholder="请选择是否沿用外卖账号" />
          </Form.Item>
          <Form.Item
            label="是否沿用团购账号"
            name="retainGrouponAccount"
            rules={[{ required: true, message: '请选择是否沿用团购账号' }]}
          >
            <Select options={yesOrNoOptions} placeholder="请选择是否沿用团购账号" />
          </Form.Item>
        </>
      )}
    </Modal>
  );
});

export default CreateEditBusinessLicenseModal;
