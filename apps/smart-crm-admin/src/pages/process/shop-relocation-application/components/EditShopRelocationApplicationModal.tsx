import React, { useState } from 'react';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useDistrictsStreetOpened from '@src/hooks/useDistrictsStreetOpened';
import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';
import {
  getShopRelocationDetail,
  updateShopRelocation,
} from '@src/services/process/shop-relocation-application';
import {
  CapacityStatusEnum,
  ShopCapacityStatusEnum,
} from '@src/services/process/shop-relocation-application/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps, Select } from 'antd';

interface EditShopRelocationApplicationModalProps extends ModalProps {
  getId: () => number | null;
  onSuccess?: () => void;
}

const EditShopRelocationApplicationModal = React.forwardRef<
  ModalRef,
  EditShopRelocationApplicationModalProps
>(({ getId, onSuccess, ...props }, ref) => {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const id = getId();

  const { transformDistricts, getStreetOpenedRegionIdsLoading } = useDistrictsStreetOpened({
    ready: open,
  });
  const { loading, mutate } = useRequest(() => getShopRelocationDetail(id!), {
    ready: open,
    onSuccess: (res) => {
      form.setFieldsValue(res);
    },
  });
  const { runAsync: update, loading: updateLoading } = useRequest(updateShopRelocation, {
    manual: true,
  });

  React.useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true);
      mutate(undefined);
    },
    close: () => setOpen(false),
  }));

  const handleSave = async (values: any) => {
    await update({ id, ...values });
    message.success('保存成功');
    setOpen(false);
    onSuccess?.();
  };

  return (
    <Modal
      title="编辑门店搬迁申请"
      open={open}
      destroyOnHidden
      confirmLoading={updateLoading}
      loading={loading || getStreetOpenedRegionIdsLoading}
      styles={{
        body: { maxHeight: '60vh', margin: '0 -24px', padding: '0 24px', overflow: 'auto' },
      }}
      modalRender={(node) => (
        <Form
          form={form}
          clearOnDestroy
          scrollToFirstError
          labelCol={{ span: 10 }}
          onFinish={handleSave}
        >
          {node}
        </Form>
      )}
      onCancel={() => setOpen(false)}
      onOk={form.submit}
      {...props}
    >
      <Form.Item label="流程编号" name="shopId">
        <Input disabled />
      </Form.Item>
      <Form.Item label="门店编号" name="shopId">
        <Input disabled />
      </Form.Item>
      <Form.Item label="点位释放协议是否签署完成" name="releaseAgreementSign">
        <Select disabled options={yesOrNoOptions} />
      </Form.Item>
      <Form.Item label="搬迁原因" name="relocationReason">
        <Input.TextArea disabled autoSize />
      </Form.Item>
      <Form.Item label="渠道类型" name="channelType">
        <Select disabled options={enum2Options(ContractChannelTypeEnum)} />
      </Form.Item>
      <Form.Item label="新门店地址" name="shopAddress">
        <Input.TextArea disabled autoSize />
      </Form.Item>
      <EditableRegion
        editable
        formItemProps={{
          label: '新意向区域',
          name: 'regionCode',
          rules: [{ required: true, message: '请选择新意向区域' }],
        }}
        fieldProps={{
          placeholder: '请选择新意向区域',
          regionLevel: 4,
          transformDistricts,
        }}
      />
      <Form.Item
        label="门店容量释放状态"
        name="shopCapacityStatus"
        rules={[{ required: true, message: '请选择门店容量释放状态' }]}
      >
        <Select
          options={enum2Options(ShopCapacityStatusEnum)}
          placeholder="请选择门店容量释放状态"
        />
      </Form.Item>
      <Form.Item
        label="意向区域容量占用状态"
        name="capacityStatus"
        rules={[{ required: true, message: '请选择意向区域容量占用状态' }]}
      >
        <Select
          options={enum2Options(CapacityStatusEnum)}
          placeholder="请选择意向区域容量占用状态"
        />
      </Form.Item>
    </Modal>
  );
});

export default EditShopRelocationApplicationModal;
