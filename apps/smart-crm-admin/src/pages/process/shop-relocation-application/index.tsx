import { useRef } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ProTable, RegionCascader, TableActions } from '@src/components';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useDistricts from '@src/hooks/useDistricts';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { PermissionsMap, usePermission } from '@src/permissions';
import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';
import { getShopRelocationList } from '@src/services/process/shop-relocation-application';
import {
  CapacityStatusEnum,
  ShopCapacityStatusEnum,
  ShopRelocationDTO,
} from '@src/services/process/shop-relocation-application/type';
import { compatibleTableActionWidth, enum2ValueEnum, optionsToValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import EditShopRelocationApplicationModal from './components/EditShopRelocationApplicationModal';

const ShopRelocationApplication = () => {
  const checkPermission = usePermission();
  const actionRef = useRef<ActionType>(null);
  const currentIdRef = useRef<number | null>(null);
  const shopIdRef = useRef<string | null>(null);
  const shopDetailDrawer = useRef<ModalRef>(null);
  const editModalRef = useRef<ModalRef>(null);

  const { data, runAsync: getList } = useRequest(getShopRelocationList, { manual: true });
  const { data: districts } = useDistricts({ streets: true });

  const columns: ProColumns<ShopRelocationDTO>[] = [
    {
      title: '编号',
      dataIndex: 'code',
      fixed: 'left',
      search: false,
    },
    {
      title: '流程编号',
      dataIndex: 'requestId',
      search: false,
    },
    {
      title: '申请名称',
      dataIndex: 'name',
      search: false,
      width: 300,
      renderText: (_, { shopId, regionCode }) =>
        `${shopId}-${regionCode
          .split('/')
          .map((code) => districts?.find((d) => d.code === code)?.name)
          .join('')}`,
    },
    {
      title: '门店编号',
      dataIndex: 'shopId',
      renderText: (value) => (
        <a
          onClick={() => {
            shopIdRef.current = value;
            shopDetailDrawer.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '点位释放协议是否签署完成',
      dataIndex: 'releaseAgreementSign',
      width: 200,
      valueEnum: optionsToValueEnum(yesOrNoOptions, true),
    },
    {
      title: '搬迁原因',
      dataIndex: 'relocationReason',
      ellipsis: true,
      search: false,
    },
    {
      title: '渠道类型',
      dataIndex: 'channelType',
      search: false,
      valueEnum: enum2ValueEnum(ContractChannelTypeEnum),
    },
    {
      title: '新门店地址',
      dataIndex: 'shopAddress',
      search: false,
      ellipsis: true,
    },
    {
      title: '新意向区域',
      dataIndex: 'regionCode',
      ellipsis: true,
      renderText: (value) => <EditableRegion value={value} fieldProps={{ regionLevel: 4 }} />,
      renderFormItem: () => <RegionCascader placeholder="请选择" />,
      search: {
        transform: (value) => value.join('/'),
      },
    },
    {
      title: '门店容量释放状态',
      dataIndex: 'shopCapacityStatus',
      valueEnum: enum2ValueEnum(ShopCapacityStatusEnum),
    },
    {
      title: '意向区域容量占用状态',
      dataIndex: 'capacityStatus',
      valueEnum: enum2ValueEnum(CapacityStatusEnum),
    },
    {
      title: '搬迁是否成功',
      dataIndex: 'relocationSuccess',
      search: false,
      valueEnum: optionsToValueEnum(yesOrNoOptions, true),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      search: false,
      width: compatibleTableActionWidth(80),
      render: (_, { id }) => (
        <TableActions
          shortcuts={[
            {
              label: '编辑',
              show: checkPermission(PermissionsMap.ShopRelocationApplicationEdit),
              onClick: () => {
                currentIdRef.current = id;
                editModalRef.current?.open();
              },
            },
          ]}
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        cardProps={false}
        bordered
        rowKey="id"
        sticky
        columns={columns}
        scroll={{ x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0) }}
        toolbar={{
          subTitle: <span className="text-black">共 {data?.total || 0} 条记录</span>,
        }}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        request={async ({ current, pageSize, ...restParams }) => {
          const res = await getList({ pageNum: current, pageSize, ...restParams });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />
      <EditShopRelocationApplicationModal
        ref={editModalRef}
        getId={() => currentIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
      <ShopDetailDrawer ref={shopDetailDrawer} getId={() => shopIdRef.current} />
    </PageContainer>
  );
};

export default ShopRelocationApplication;
