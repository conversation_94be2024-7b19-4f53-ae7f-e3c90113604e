import { useEffect, useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { workflowAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ETable, TableActions } from '@src/components';
import { ETableActionType, ETableColumn, ValueType } from '@src/components/ETable';
import useTableViews from '@src/hooks/useTableViews';
import FormalDetailDrawer from '@src/pages/contract/formal/components/FormalDetailDrawer';
import RelocationFormalDetailDrawer from '@src/pages/contract/relocation-formal/components/RelocationFormalDetailDrawer';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { PermissionsMap, usePermission } from '@src/permissions';
import { OwnerTypeEnum } from '@src/services/business-opportunity/type';
import { BusinessTypeEnum } from '@src/services/common/type';
import { FormalNodeStateEnum, StoreCategoryEnum } from '@src/services/contract/formal/type';
import { ContractNodeEnum } from '@src/services/contract/renewal/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import {
  getTrainingPersonList,
  syncTrainingPersonCertInfo,
} from '@src/services/process/train-staff';
import {
  PersonTypeEnum,
  TrainingModeEnum,
  TrainingPersonListDTO,
} from '@src/services/process/train-staff/type';
import { WorkflowAuditStatusEnum } from '@src/services/workflow/type';
import { compatibleTableActionWidth, enum2Options, getParamsFromFilters } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Popover, Skeleton, Tabs } from 'antd';
import { isEqual } from 'lodash-es';
import { useSearchParams } from 'react-router-dom';
import { CreateEditTrainStaffModal, UpdateTrainingInfoModal } from './components';
import TrainingInfoDetailDrawer from './components/TrainingInfoDetailDrawer';

type IProps = {
  type: 'formal' | 'relocation-formal';
};

const TrainStaffTabsItem = ({ type }: IProps) => {
  const { message } = App.useApp();
  const [searchParams, setSearchParams] = useSearchParams();
  const checkPermission = usePermission();

  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);

  const { data, runAsync: getList } = useRequest(getTrainingPersonList, { manual: true });
  const { views, loading: getViewsLoading } = useTableViews(
    type === 'formal'
      ? BusinessTypeEnum.TRAINING_FORMAL_CONTRACT_PERSON_LIST
      : BusinessTypeEnum.TRAINING_R_FORMAL_CONTRACT_PERSON_LIST,
  );

  const actionRef = useRef<ETableActionType>(null);
  const currentRef = useRef<TrainingPersonListDTO | null>(null);
  const shopIdRef = useRef<string | null>(null);
  const shopDetailDrawerRef = useRef<ModalRef>(null);
  const contractIdRef = useRef<number | null>(null);
  const contractDetailDrawerRef = useRef<ModalRef>(null);
  const relocationFormalIdRef = useRef<number | null>(null);
  const relocationFormalDetailDrawerRef = useRef<ModalRef>(null);
  const createEditTrainStaffModalRef = useRef<ModalRef>(null);
  const updateTrainingInfoModalRef = useRef<ModalRef>(null);
  const trainingInfoDetailDrawerRef = useRef<ModalRef>(null);

  const contractType =
    type === 'formal' ? ContractTypeEnum.正式合同 : ContractTypeEnum.搬迁正式合同;

  useEffect(() => {
    const contractId = searchParams.get('contractId');
    const paramsContractType = searchParams.get('contractType');

    if (contractId && contractType === paramsContractType) {
      currentRef.current = {
        id: contractId,
        contractType: paramsContractType,
      } as TrainingPersonListDTO;
      trainingInfoDetailDrawerRef.current?.open();
      searchParams.delete('contractId');
      searchParams.delete('contractType');
      setSearchParams(searchParams, { replace: true });
    }
  }, [contractType, searchParams, setSearchParams]);

  const { run: syncTrainingPersonCertInfoRun } = useRequest(
    async (value: { contractId: string; contractType: ContractTypeEnum }) => {
      if (!value.contractId || !value.contractType) {
        return;
      }

      return await syncTrainingPersonCertInfo(value);
    },
    {
      manual: true,
      onSuccess: () => {
        message.success('同步成功');
        actionRef.current?.reload();
      },
    },
  );

  const columns: ETableColumn<TrainingPersonListDTO>[] = [
    {
      title: '合同编号',
      dataIndex: 'code',
      fixed: 'left',
      valueType: ValueType.TEXT,
      render: (value, { id }) => (
        <a
          onClick={() => {
            if (type === 'formal') {
              contractIdRef.current = +id;
              contractDetailDrawerRef.current?.open();
            }

            if (type === 'relocation-formal') {
              relocationFormalIdRef.current = +id;
              relocationFormalDetailDrawerRef.current?.open();
            }
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '门店冠名',
      dataIndex: 'storeName',
      valueType: ValueType.TEXT,
    },
    {
      title: '省市区',
      dataIndex: 'region',
      hideInFilters: true,
      render: (_, { provinceName, cityName, regionName }) =>
        [provinceName, cityName, regionName].filter(Boolean).join('/'),
    },
    {
      title: '门店编号',
      dataIndex: 'storeCode',
      hideInFilters: true,
      valueType: ValueType.TEXT,
      render: (value) => (
        <a
          onClick={() => {
            shopIdRef.current = value;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '加盟类型',
      dataIndex: 'storeCategory',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(StoreCategoryEnum),
      },
      filterProps: {
        options: enum2Options(StoreCategoryEnum),
      },
    },
    {
      title: '业主性质',
      dataIndex: 'ownerType',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(OwnerTypeEnum),
      },
      filterProps: {
        options: enum2Options(OwnerTypeEnum),
      },
    },
    {
      title: '签约人',
      dataIndex: 'signer',
      valueType: ValueType.TEXT,
    },
    {
      title: '签约人联系电话',
      dataIndex: 'signerPhone',
      valueType: ValueType.PHONE,
      fieldProps: (_, { signerPhoneSensitive }) => ({
        sensitiveValue: signerPhoneSensitive,
      }),
    },
    {
      title: '合同节点',
      dataIndex: 'nodeState',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options:
          type === 'formal' ? enum2Options(FormalNodeStateEnum) : enum2Options(ContractNodeEnum),
      },
      filterProps: {
        options:
          type === 'formal' ? enum2Options(FormalNodeStateEnum) : enum2Options(ContractNodeEnum),
      },
    },
    {
      title: '培训人员姓名',
      dataIndex: 'name',
      valueType: ValueType.TEXT,
      hidden: true,
      hideInSettings: true,
    },
    {
      title: '培训人员手机号',
      dataIndex: 'phone',
      valueType: ValueType.PHONE,
      hidden: true,
      hideInSettings: true,
    },
    {
      title: '培训人员是否证件齐全',
      dataIndex: 'certIsRefine',
      valueType: ValueType.SINGLE,
      hidden: true,
      hideInSettings: true,
      filterProps: {
        options: [
          // 后端这里不接受 Boolean
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
      },
    },
    {
      title: '培训人员',
      dataIndex: 'trainingPersons',
      hideInFilters: true,
      render: (value: TrainingPersonListDTO['trainingPersons']) => {
        const content = (
          <ETable
            dataSource={value}
            options={false}
            size="small"
            rowKey="customerId"
            pagination={false}
            columns={[
              {
                title: '人员类型',
                dataIndex: 'personType',
                render: (_, { personType }) => {
                  return personType === PersonTypeEnum.SHOP_MANAGER ? '店长' : '员工';
                },
              },
              {
                title: '姓名',
                dataIndex: 'name',
              },
              {
                title: '手机',
                dataIndex: 'phone',
                valueType: ValueType.PHONE,
                fieldProps: (_, { phoneSensitive }) => ({
                  sensitiveValue: phoneSensitive,
                }),
              },
              {
                title: '身份证',
                dataIndex: 'identityCard',
                valueType: ValueType.IDENTITY_CARD,
                fieldProps: (_, { identityCardSensitive }) => ({
                  sensitiveValue: identityCardSensitive,
                }),
              },
              {
                title: '通岗证编号',
                dataIndex: 'crossFunctionalCertCode',
                valueType: ValueType.TEXT,
              },
              {
                title: '结业证编号',
                dataIndex: 'completionCertCode',
                valueType: ValueType.TEXT,
              },
            ]}
          />
        );

        return (
          <Popover content={content}>
            <a>{(value || [])?.map((e) => e.name).join('，')}</a>
          </Popover>
        );
      },
    },
    {
      title: '通岗证编号',
      dataIndex: 'crossFunctionalCertCode',
      valueType: ValueType.TEXT,
      hidden: true,
      hideInSettings: true,
    },
    {
      title: '结业证编号',
      dataIndex: 'completionCertCode',
      valueType: ValueType.TEXT,
      hidden: true,
      hideInSettings: true,
    },
    {
      title: '人员是否需要培训',
      dataIndex: 'needTraining',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: yesOrNoOptions,
      },
      filterProps: {
        options: [
          // 后端这里不接受 Boolean
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
      },
    },

    {
      title: '培训方式',
      dataIndex: 'trainingMode',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(TrainingModeEnum),
      },
      filterProps: {
        options: enum2Options(TrainingModeEnum),
      },
    },
    {
      title: '培训门店',
      dataIndex: 'trainingShopId',
      valueType: ValueType.TEXT,
      render: (value) => (
        <a
          onClick={() => {
            shopIdRef.current = value;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '到店培训日期',
      dataIndex: 'trainingDate',
      valueType: ValueType.DATE,
    },
    {
      title: '预计鉴定日期',
      dataIndex: 'expectAppraisalDate',
      valueType: ValueType.DATE,
    },
    {
      title: '审批状态',
      dataIndex: 'auditStatus',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: workflowAuditStatusOptions,
      },
      filterProps: {
        options: workflowAuditStatusOptions,
      },
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      key: 'action',
      width: compatibleTableActionWidth(150),
      hideInFilters: true,
      hideInSettings: true,
      shouldCellUpdate: (prev, next) => !isEqual(prev, next),
      render: (_, record) => {
        return (
          <TableActions
            shortcuts={[
              {
                label: '编辑',
                show:
                  record.trainingPersons &&
                  checkPermission(PermissionsMap.TrainStaffEdit) &&
                  (!record.auditStatus ||
                    [WorkflowAuditStatusEnum.REJECT].includes(record.auditStatus)),
                onClick: () => {
                  currentRef.current = record;
                  createEditTrainStaffModalRef.current?.open();
                },
              },
              {
                label: '新建',
                show:
                  !record.trainingPersons &&
                  checkPermission(PermissionsMap.TrainStaffCreate) &&
                  (!record.auditStatus ||
                    [WorkflowAuditStatusEnum.REJECT].includes(record.auditStatus)),
                onClick: () => {
                  currentRef.current = record;
                  createEditTrainStaffModalRef.current?.open();
                },
              },
              {
                label: '详情',
                show: checkPermission(PermissionsMap.TrainStaffDetail),
                onClick: () => {
                  currentRef.current = record;
                  trainingInfoDetailDrawerRef.current?.open();
                },
              },
            ]}
            moreItems={[
              {
                label: '同步人员证件信息',
                show:
                  checkPermission(PermissionsMap.TrainStaffSync) &&
                  (!record.auditStatus ||
                    [WorkflowAuditStatusEnum.REJECT, WorkflowAuditStatusEnum.APPROVE].includes(
                      record.auditStatus,
                    )),
                onClick: () => {
                  syncTrainingPersonCertInfoRun({
                    contractId: record.id,
                    contractType: record.contractType,
                  });
                },
              },
              {
                label: '修改培训信息',
                show:
                  checkPermission(PermissionsMap.TrainStaffUpdate) &&
                  [WorkflowAuditStatusEnum.APPROVE].includes(record.auditStatus),
                onClick: () => {
                  currentRef.current = record;
                  updateTrainingInfoModalRef.current?.open();
                },
              },
            ]}
          />
        );
      },
    },
  ];

  const headerNode = (
    <div className="sm:flex justify-between mb-3">
      <div className="self-end mb-2 sm:mb-0 pl-2">
        共 {data?.total || 0} 个合同
        {selectedRowKeys.length > 0 && (
          <>
            ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 个
            <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
              清空选择
            </a>
          </>
        )}
      </div>
    </div>
  );

  return (
    <>
      {getViewsLoading ? (
        <Skeleton paragraph={{ rows: 15 }} />
      ) : (
        <ETable
          rowKey="id"
          actionRef={actionRef}
          sticky
          bordered
          size="small"
          header={headerNode}
          views={views}
          pagination={{ showQuickJumper: true, showSizeChanger: true }}
          columns={columns}
          rowSelection={{
            fixed: true,
            preserveSelectedRowKeys: true,
            selectedRowKeys,
            onChange: (keys) => setSelectedRowKeys(keys as number[]),
          }}
          request={async ({ current, pageSize }, filters) => {
            const res = await getList({
              pageNum: current,
              pageSize,
              ...getParamsFromFilters(filters),
              contractType,
            });

            return {
              data: res.result,
              total: res.total,
            };
          }}
        />
      )}

      <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => shopIdRef.current} />
      <FormalDetailDrawer
        ref={contractDetailDrawerRef}
        getId={() => contractIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
        onDelete={() => actionRef.current?.reload()}
      />

      <RelocationFormalDetailDrawer
        ref={relocationFormalDetailDrawerRef}
        getId={() => relocationFormalIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
        onDelete={() => actionRef.current?.reload()}
      />
      <CreateEditTrainStaffModal
        ref={createEditTrainStaffModalRef}
        getItem={() => currentRef.current}
        onSuccess={() => {
          actionRef.current?.reload();
        }}
      />
      <UpdateTrainingInfoModal
        ref={updateTrainingInfoModalRef}
        getItem={() => currentRef.current}
        onSuccess={() => {
          actionRef.current?.reload();
        }}
      />
      <TrainingInfoDetailDrawer
        ref={trainingInfoDetailDrawerRef}
        getItem={() => currentRef.current}
      />
    </>
  );
};

const TrainStaff = () => {
  const [searchParams] = useSearchParams();
  const contractType = searchParams.get('contractType') as ContractTypeEnum;
  const [activeKey, setActiveKey] = useState<ContractTypeEnum>(
    () => contractType || ContractTypeEnum.正式合同,
  );

  return (
    <PageContainer>
      <Tabs
        activeKey={activeKey}
        items={[
          {
            label: '正式合同',
            key: ContractTypeEnum.正式合同,
            children: <TrainStaffTabsItem type={'formal'} />,
          },
          {
            label: '搬迁正式合同',
            key: ContractTypeEnum.搬迁正式合同,
            children: <TrainStaffTabsItem type={'relocation-formal'} />,
          },
        ]}
        onChange={(key) => {
          setActiveKey(key as ContractTypeEnum);
        }}
      />
    </PageContainer>
  );
};

export default TrainStaff;
