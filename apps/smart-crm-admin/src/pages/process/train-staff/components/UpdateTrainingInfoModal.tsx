import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { getTrainingPersonDetail, updateTrainingInfo } from '@src/services/process/train-staff';
import { TrainingPersonListDTO } from '@src/services/process/train-staff/type';
import { useRequest } from 'ahooks';
import { App, Form, Modal, ModalProps } from 'antd';
import { UpdateTrainingInfoForm } from './UpdateTrainingInfoForm';

interface UpdateTrainingInfoModalProps extends ModalProps {
  getItem?: () => TrainingPersonListDTO | null;
  onSuccess: () => void;
}

export const UpdateTrainingInfoModal = React.forwardRef<ModalRef, UpdateTrainingInfoModalProps>(
  ({ getItem, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();

    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    const itemInfo = getItem?.();

    const { loading } = useRequest(
      async () => {
        if (!itemInfo?.id) {
          return;
        }

        const res = await getTrainingPersonDetail({
          contractId: itemInfo.id,
          contractType: itemInfo.contractType,
        });

        return res;
      },
      {
        ready: open && !!itemInfo?.id && !!itemInfo.contractType,
        onSuccess: (res) => {
          form.setFieldsValue(res);
        },
      },
    );

    const { runAsync: update, loading: updateLoading } = useRequest(updateTrainingInfo, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
      },
      close: () => setOpen(false),
    }));

    const handleSave = async (values: Record<string, any>) => {
      if (!itemInfo?.id || !itemInfo.trainingInfoId) return;

      const params = {
        id: itemInfo.trainingInfoId,
        contractId: itemInfo.id,
        contractType: itemInfo.contractType,
        ...values,
      };

      await update(params);
      message.success('修改成功');

      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        open={open}
        width={600}
        title={'修改培训信息'}
        loading={loading}
        destroyOnHidden
        styles={{
          body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
        }}
        confirmLoading={updateLoading}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
        {...props}
      >
        <Form
          form={form}
          clearOnDestroy
          scrollToFirstError
          labelCol={{ span: 8 }}
          onFinish={handleSave}
        >
          <UpdateTrainingInfoForm />
        </Form>
      </Modal>
    );
  },
);
