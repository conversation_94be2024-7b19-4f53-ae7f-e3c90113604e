import React, { useRef, useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { TrainingPersonsSelect } from '@src/components';
import { defaultTrainingPersonList } from '@src/components/TrainingPersonsSelect/const';
import {
  getTrainingPersonDetail,
  saveTrainingPersonList,
  updateTrainingPersonList,
} from '@src/services/process/train-staff';
import { TrainingPersonListDTO, TrainingPersonsDTO } from '@src/services/process/train-staff/type';
import { submitWorkflow } from '@src/services/workflow';
import { WorkflowAuditStatusEnum, WorkflowBusinessTypeEnum } from '@src/services/workflow/type';
import { useRequest } from 'ahooks';
import { App, Button, Form, Modal, ModalProps } from 'antd';

interface CreateEditTrainStaffModalProps extends ModalProps {
  getItem?: () => TrainingPersonListDTO | null;
  onSuccess: () => void;
}

export const CreateEditTrainStaffModal = React.forwardRef<ModalRef, CreateEditTrainStaffModalProps>(
  ({ getItem, onSuccess, ...props }, ref) => {
    const saveTypeRef = useRef<'save' | 'submit'>('save');
    const { message } = App.useApp();

    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    const itemInfo = getItem?.();

    const isEdit = !!itemInfo?.trainingPersons;

    const { data, loading } = useRequest(
      async () => {
        if (!itemInfo) {
          return;
        }

        const res = await getTrainingPersonDetail({
          contractId: itemInfo.id,
          contractType: itemInfo.contractType,
        });

        return res;
      },
      {
        ready: open && !!itemInfo?.id && !!itemInfo.contractType,
        onSuccess: (res) => {
          form.setFieldsValue({
            ...res,
            trainingPersons: res?.trainingPersons ?? defaultTrainingPersonList,
          });
        },
      },
    );

    const { runAsync: save, loading: saveLoading } = useRequest(saveTrainingPersonList, {
      manual: true,
    });

    const { runAsync: submit, loading: submitLoading } = useRequest(submitWorkflow, {
      manual: true,
    });

    const { runAsync: update, loading: updateLoading } = useRequest(updateTrainingPersonList, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
      },
      close: () => setOpen(false),
    }));

    const handleSave = async (values: { trainingPersons: TrainingPersonsDTO[] }) => {
      if (!itemInfo) return;

      const params = {
        contractId: itemInfo.id,
        contractType: itemInfo.contractType,
        ...values,
      };

      if (saveTypeRef.current === 'submit') {
        if (!data?.trainingInfoId) {
          message.error('流程id缺失');

          return;
        }

        await update(params);
        await submit({
          businessId: data?.trainingInfoId,
          businessType: WorkflowBusinessTypeEnum.SCRM_TRAINING_PERSON_ARRANGEMENT,
          businessName: data?.storeCode,
        });
        message.success('提交审核成功');
      } else {
        if (isEdit) {
          await update(params);
        } else {
          await save(params);
        }

        message.success('保存成功');
      }

      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        open={open}
        width={600}
        title={isEdit ? '编辑培训人员' : '新建培训人员'}
        loading={loading}
        destroyOnHidden
        styles={{
          body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
        }}
        modalRender={(node) => (
          <Form
            form={form}
            clearOnDestroy
            scrollToFirstError
            labelCol={{ span: 6 }}
            onFinish={handleSave}
          >
            {node}
          </Form>
        )}
        okText={isEdit ? '保存' : '提交审核'}
        okButtonProps={{ ghost: isEdit && data?.auditStatus === WorkflowAuditStatusEnum.REJECT }}
        confirmLoading={updateLoading || saveLoading || submitLoading}
        footer={(_, { OkBtn, CancelBtn }) => (
          <>
            <CancelBtn />
            <OkBtn />
            {isEdit && data?.auditStatus === WorkflowAuditStatusEnum.REJECT && (
              <Button
                loading={saveLoading || submitLoading}
                type="primary"
                onClick={() => {
                  saveTypeRef.current = 'submit';
                  form.submit();
                }}
              >
                保存并发起
              </Button>
            )}
          </>
        )}
        onOk={() => {
          saveTypeRef.current = 'save';
          form.submit();
        }}
        onCancel={() => setOpen(false)}
        {...props}
      >
        <TrainingPersonsSelect />
      </Modal>
    );
  },
);
