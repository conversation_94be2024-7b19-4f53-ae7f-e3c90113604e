import { yesOrNoOptions } from '@src/common/constants';
import { ShopSelect } from '@src/components';
import EditableDate from '@src/components/Editable/EditableDate';
import { TrainingModeEnum } from '@src/services/process/train-staff/type';
import { enum2Options } from '@src/utils';
import { Form, Select } from 'antd';

export const UpdateTrainingInfoForm = () => {
  const needTraining = Form.useWatch('needTraining');

  return (
    <>
      <Form.Item
        name="needTraining"
        label="人员是否需要培训"
        rules={[{ required: true, message: '请选择人员是否需要培训' }]}
      >
        <Select options={yesOrNoOptions} placeholder="请选择人员是否需要培训" />
      </Form.Item>
      {needTraining && (
        <>
          <Form.Item
            name="trainingMode"
            label="培训方式"
            rules={[{ required: true, message: '请选择培训方式' }]}
          >
            <Select options={enum2Options(TrainingModeEnum)} placeholder="请选择培训方式" />
          </Form.Item>
          <Form.Item
            name="trainingShopId"
            label="培训门店"
            rules={[{ required: true, message: '请选择培训门店' }]}
          >
            <ShopSelect />
          </Form.Item>
          <EditableDate
            editable
            formItemProps={{
              name: 'trainingDate',
              label: '到店培训日期',
              rules: [{ required: true, message: '请选择到店培训日期' }],
            }}
          />

          <EditableDate
            editable
            formItemProps={{
              name: 'expectAppraisalDate',
              label: '预计鉴定日期',
              rules: [{ required: true, message: '请选择预计鉴定日期' }],
            }}
          />
        </>
      )}
    </>
  );
};
