import React, { useRef } from 'react';
import { yesOrNoOptions } from '@src/common/constants';
import { DescriptionFieldGroupType, ModalRef } from '@src/common/interface';
import { AuditFlow, ButtonGroup } from '@src/components';
import { ValueType } from '@src/components/ETable';
import { PermissionsMap, usePermission, usePermissionOpen } from '@src/permissions';
import { OwnerTypeEnum } from '@src/services/business-opportunity/type';
import { FormalNodeStateEnum, StoreCategoryEnum } from '@src/services/contract/formal/type';
import { ContractNodeEnum } from '@src/services/contract/renewal/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import {
  getTrainingPersonDetail,
  syncTrainingPersonCertInfo,
  updateTrainingInfo,
} from '@src/services/process/train-staff';
import {
  PersonTypeEnum,
  TrainingModeEnum,
  TrainingPersonListDTO,
} from '@src/services/process/train-staff/type';
import {
  WorkflowAuditOperationTypeEnum,
  WorkflowAuditStatusEnum,
  WorkflowBusinessTypeEnum,
} from '@src/services/workflow/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Card, Col, Drawer, DrawerProps, Row } from 'antd';
import { omit } from 'lodash-es';
import { CreateEditTrainStaffModal } from './CreateEditTrainStaffModal';
import { UpdateTrainingInfoForm } from './UpdateTrainingInfoForm';
import { UpdateTrainingInfoModal } from './UpdateTrainingInfoModal';

interface TrainingInfoDetailDrawerProps extends DrawerProps {
  getItem: () => TrainingPersonListDTO | null;
  onUpdate?: () => void;
}

const TrainingInfoDetailDrawer = React.forwardRef<ModalRef, TrainingInfoDetailDrawerProps>(
  ({ getItem, onUpdate, ...props }, ref) => {
    const { message } = App.useApp();
    const [open, setOpen] = usePermissionOpen(PermissionsMap.TrainStaffDetail);
    const currentRef = useRef<TrainingPersonListDTO | null>(null);
    const createEditTrainStaffModalRef = useRef<ModalRef>(null);
    const updateTrainingInfoModalRef = useRef<ModalRef>(null);

    const itemInfo = getItem();

    const checkPermission = usePermission();

    const { data, loading, refresh, mutate } = useRequest(
      async () => {
        if (!itemInfo?.id) {
          return;
        }

        const res = await getTrainingPersonDetail({
          contractId: itemInfo.id,
          contractType: itemInfo.contractType,
        });

        return res;
      },
      { ready: open },
    );

    const { run: syncTrainingPersonCertInfoRun } = useRequest(
      async (value: { contractId: string; contractType: ContractTypeEnum }) => {
        if (!value.contractId || !value.contractType) {
          return;
        }

        return await syncTrainingPersonCertInfo(value);
      },
      {
        manual: true,
        onSuccess: () => {
          message.success('同步成功');
          refresh();
          onUpdate?.();
        },
      },
    );

    React.useImperativeHandle(ref, () => ({
      open: () => {
        mutate(undefined);
        setOpen(true);
      },
      close: () => setOpen(false),
    }));

    const baseInfoItems: DescriptionFieldGroupType<TrainingPersonListDTO>[] = [
      {
        label: '合同信息',
        children: [
          {
            field: 'code',
            label: '合同编号',
          },
          {
            field: data?.contractType === ContractTypeEnum.正式合同 ? 'nodeState' : 'node',
            label: '合同节点',
            valueType: ValueType.SINGLE,
            fieldProps: {
              options:
                data?.contractType === ContractTypeEnum.正式合同
                  ? enum2Options(FormalNodeStateEnum)
                  : enum2Options(ContractNodeEnum),
            },
          },
          {
            field: 'storeName',
            label: '门店冠名',
          },
          {
            label: '省市区',
            children: [data?.provinceName, data?.cityName, data?.regionName]
              .filter(Boolean)
              .join('/'),
          },
          {
            field: 'storeCode',
            label: '门店编号',
          },
          {
            field: 'storeCategory',
            label: '加盟类型',
            valueType: ValueType.SINGLE,
            fieldProps: {
              options: enum2Options(StoreCategoryEnum),
            },
          },
          {
            field: 'ownerType',
            label: '业主性质',
            valueType: ValueType.SINGLE,
            fieldProps: {
              options: enum2Options(OwnerTypeEnum),
            },
          },
          {
            field: 'signer',
            label: '签约人',
          },
          {
            label: '签约人电话',
            field: 'signerPhone',
            valueType: ValueType.PHONE,
            fieldProps: {
              sensitiveValue: data?.signerPhoneSensitive,
            },
          },
        ],
      },
      {
        label: '人员信息',
        children:
          (data?.trainingPersons || []).flatMap((item, index) => {
            const personTypeName =
              item.personType === PersonTypeEnum.SHOP_MANAGER ? '店长' : `店员${index}`;
            const isShopManager = item.personType === PersonTypeEnum.SHOP_MANAGER;

            return [
              {
                label: personTypeName,
                field: ['trainingPersons', `${index}`, 'name'],
              },
              {
                label: `${personTypeName}电话`,
                field: ['trainingPersons', `${index}`, 'phone'],
                valueType: ValueType.PHONE,
                fieldProps: {
                  sensitiveValue: item?.phoneSensitive,
                },
              },
              {
                label: `${personTypeName}身份证号`,
                field: ['trainingPersons', `${index}`, 'identityCard'],
                valueType: ValueType.IDENTITY_CARD,
                fieldProps: {
                  sensitiveValue: item?.identityCardSensitive,
                },
              },
              {
                label: `${isShopManager ? '结业证' : '通岗证'}`,
                field: [
                  'trainingPersons',
                  `${index}`,
                  isShopManager ? 'completionCertCode' : 'crossFunctionalCertCode',
                ],
              },
            ];
          }) || [],
      },
      {
        label: '培训信息',
        children: [
          {
            field: 'needTraining',
            label: '人员是否需要培训',
            valueType: ValueType.SINGLE,
            fieldProps: {
              options: yesOrNoOptions,
            },
          },
          {
            field: 'trainingMode',
            label: '培训方式',
            valueType: ValueType.SINGLE,
            fieldProps: {
              options: enum2Options(TrainingModeEnum),
            },
          },
          {
            field: 'trainingShopName',
            label: '训练门店',
          },
          {
            field: 'trainingShopDockingUserName',
            label: '训练门店对接人',
          },
          {
            field: 'trainingUserName',
            label: '门店训练大狮兄/大狮姐',
          },
          {
            field: 'trainingManagerName',
            label: '门店训练运营经理',
          },
          {
            field: 'trainingDate',
            label: '到店培训日期',
          },
          {
            field: 'expectAppraisalDate',
            label: '预计鉴定时间',
          },
        ],
      },
    ];

    return (
      <Drawer
        title="培训人员清单详情"
        open={open}
        width={1100}
        destroyOnHidden
        onClose={() => setOpen(false)}
        {...props}
      >
        <Row gutter={[20, 20]}>
          <Col span={24} xl={16}>
            <Card loading={loading}>
              <div className="flex justify-end">
                <ButtonGroup
                  items={[
                    {
                      label: '编辑',
                      show:
                        checkPermission(PermissionsMap.TrainStaffEdit) &&
                        data?.trainingPersons &&
                        (!data.auditStatus || data.auditStatus === WorkflowAuditStatusEnum.REJECT),
                      onClick: () => {
                        currentRef.current = data!;
                        createEditTrainStaffModalRef.current?.open();
                      },
                    },
                    {
                      label: '新建',
                      show:
                        checkPermission(PermissionsMap.TrainStaffCreate) &&
                        data &&
                        !data.trainingPersons &&
                        (!data.auditStatus || data.auditStatus === WorkflowAuditStatusEnum.REJECT),
                      onClick: () => {
                        currentRef.current = data!;
                        createEditTrainStaffModalRef.current?.open();
                      },
                    },
                    {
                      label: '同步人员证件信息',
                      show:
                        checkPermission(PermissionsMap.TrainStaffSync) &&
                        data &&
                        (!data.auditStatus ||
                          [
                            WorkflowAuditStatusEnum.REJECT,
                            WorkflowAuditStatusEnum.APPROVE,
                          ].includes(data.auditStatus)),
                      onClick: () =>
                        syncTrainingPersonCertInfoRun({
                          contractId: data!.id,
                          contractType: data!.contractType,
                        }),
                    },
                    {
                      label: '修改培训信息',
                      show:
                        checkPermission(PermissionsMap.TrainStaffUpdate) &&
                        data &&
                        (!data.auditStatus || data.auditStatus === WorkflowAuditStatusEnum.APPROVE),
                      onClick: () => {
                        currentRef.current = data!;
                        updateTrainingInfoModalRef.current?.open();
                      },
                    },
                  ]}
                />
              </div>
              {renderDescriptions(baseInfoItems, data)}
            </Card>
          </Col>
          <Col span={24} xl={8}>
            <AuditFlow.WorkflowCard
              loading={loading}
              auditStatus={data?.auditStatus}
              processInstanceId={data?.processInstanceId}
              businessId={data?.trainingInfoId}
              businessName={data?.storeCode}
              businessType={WorkflowBusinessTypeEnum.SCRM_TRAINING_PERSON_ARRANGEMENT}
              auditButtons={{
                [WorkflowAuditOperationTypeEnum.RETURN]: false,
                [WorkflowAuditOperationTypeEnum.REVOKE]: false,
              }}
              onBeforeSubmit={(values, type, currentAuditHistory) => {
                if (
                  currentAuditHistory?.taskDefKey === 'training_docking_node' &&
                  type === WorkflowAuditOperationTypeEnum.APPROVE
                ) {
                  return updateTrainingInfo({
                    id: data?.trainingInfoId!,
                    contractId: data?.id,
                    contractType: data?.contractType,
                    ...omit(values, ['description']),
                  });
                }
              }}
              onSuccess={() => {
                refresh();
                onUpdate?.();
              }}
            >
              {(originalNode, type, currentAuditHistory) =>
                currentAuditHistory?.taskDefKey === 'training_docking_node' &&
                type === WorkflowAuditOperationTypeEnum.APPROVE ? (
                  <Card title="培训信息">
                    <UpdateTrainingInfoForm />
                  </Card>
                ) : (
                  originalNode
                )
              }
            </AuditFlow.WorkflowCard>
          </Col>
        </Row>

        <CreateEditTrainStaffModal
          ref={createEditTrainStaffModalRef}
          getItem={() => currentRef.current}
          onSuccess={() => {
            refresh();
            onUpdate?.();
          }}
        />
        <UpdateTrainingInfoModal
          ref={updateTrainingInfoModalRef}
          getItem={() => currentRef.current}
          onSuccess={() => {
            refresh();
            onUpdate?.();
          }}
        />
      </Drawer>
    );
  },
);

export default TrainingInfoDetailDrawer;
