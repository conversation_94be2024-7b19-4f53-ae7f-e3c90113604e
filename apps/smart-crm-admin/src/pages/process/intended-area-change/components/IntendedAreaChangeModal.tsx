import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { ChooseMapPoints, FileUpload } from '@src/components';
import CheckCodeModal from '@src/components/CheckCodeModal';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useDistrictsStreetOpened from '@src/hooks/useDistrictsStreetOpened';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import { StoreCategoryEnum } from '@src/services/contract/formal/type';
import { getIntentionContractDetail } from '@src/services/contract/intention';
import {
  ContractChannelTypeEnum,
  IntentionContractDTO,
} from '@src/services/contract/intention/type';
import {
  checkIntendedAreaChangeInfo,
  createIntendedAreaChange,
  getIntendedAreaChangeDetail,
  updateIntendedAreaChange,
} from '@src/services/process/intended-area-change';
import { IntendedAreaChangeReasonEnum } from '@src/services/process/intended-area-change/type';
import { getExpansionPositionsByDistrictCode } from '@src/services/regional/expansion';
import { getAreaQuotaByDistrictCode } from '@src/services/regional/quota';
import { JoinRegionEnum } from '@src/services/regional/quota/type';
import { enum2Options, enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps, Select } from 'antd';
import IntentionContractSelect from '../../components/IntentionContractSelect';

interface IntendedAreaChangeModalProps extends ModalProps {
  getId?: () => number | null;
  /** 快捷创建时的合同 id */
  getCreateId?: () => number | null;
  onSuccess?: () => void;
}

const IntendedAreaChangeModal = React.forwardRef<ModalRef, IntendedAreaChangeModalProps>(
  ({ getId, getCreateId, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    // 新建弹窗才会有值
    const [currentIntentionContractInCreate, setCurrentIntentionContractInCreate] =
      useState<IntentionContractDTO>();
    const [checkCodeModalOpen, setCheckCodeModalOpen] = useState(false);

    const id = getId?.();
    const isEdit = !!id;

    const setFieldsWhenContractChange = (
      item: IntentionContractDTO,
      otherValues?: Record<string, any>,
    ) => {
      setCurrentIntentionContractInCreate(item);
      form.setFieldsValue({
        contractCode: item.code,
        customerId: item.customerId,
        effectiveDate: item.effectiveDate,
        expirationDate: item.intentionExpirationDate,
        originalRegionCode: item.region,
        originalPositions: item.intentionPositions,
        originalExpansionPositionId: item.expansionPosition?.id,
        originalChannelType: item.channelType,
        ...otherValues,
      });
    };

    const {
      data: checkInfoData,
      runAsync: checkInfo,
      loading: checkInfoLoading,
      mutate: mutateCheckInfoData,
    } = useRequest(checkIntendedAreaChangeInfo, { manual: true });
    const { runAsync: create, loading: createLoading } = useRequest(createIntendedAreaChange, {
      manual: true,
    });
    const { runAsync: update, loading: updateLoading } = useRequest(updateIntendedAreaChange, {
      manual: true,
    });
    const {
      data: expansionPositions,
      runAsync: getExpansionPositions,
      loading: getExpansionPositionsLoading,
      mutate: mutateExpansionPositions,
    } = useRequest(getExpansionPositionsByDistrictCode, { manual: true });
    const { data, loading } = useRequest(() => getIntendedAreaChangeDetail(id!), {
      ready: open && isEdit,
      onSuccess: (res) => {
        form.setFieldsValue({
          ...res,
          originalExpansionPositionId: res.originalExpansionPosition?.id,
          expansionPositionId: res.expansionPosition?.id,
        });
        getExpansionPositions(res.regionCode);
      },
    });
    const createId = getCreateId?.();
    const { loading: getIntentionContractDetailLoading } = useRequest(
      () => getIntentionContractDetail(createId!),
      {
        ready: open && !!createId,
        onSuccess: (res) => setFieldsWhenContractChange(res),
      },
    );
    const { transformDistricts, getStreetOpenedRegionIdsLoading } = useDistrictsStreetOpened({
      ready: open,
    });

    const {
      data: areaQuota,
      runAsync: getAreaQuota,
      mutate: mutateAreaQuota,
    } = useRequest(getAreaQuotaByDistrictCode, { manual: true });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        mutateExpansionPositions(undefined);
        mutateCheckInfoData(undefined);
        mutateAreaQuota(undefined);
      },
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      if (isEdit) {
        await update({ id, ...values });
      } else {
        await create(values);
      }

      message.success('提交审核成功');
      setOpen(false);
      onSuccess?.();
    };

    const handleFinish = async (values: any) => {
      const { customerId, originalRegionCode, regionCode } = values;

      const [checkRes, areaQuotaRes] = await Promise.all([
        checkInfo({
          customerId,
          originalRegionCode,
          regionCode,
        }),
        getAreaQuota({ districtCode: regionCode }),
      ]);

      if (checkRes.success && areaQuotaRes.remainQuotaNum > 0) {
        handleSave(values);
      } else {
        setCheckCodeModalOpen(true);
      }
    };

    // 意向合同是否有合作的特殊渠道
    const hasChannelType = isEdit
      ? data?.originalChannelType && data.originalChannelType !== ContractChannelTypeEnum.常规店
      : currentIntentionContractInCreate &&
        currentIntentionContractInCreate.channelType !== ContractChannelTypeEnum.常规店;

    return (
      <>
        <Modal
          title={isEdit ? '编辑意向区域变更' : '新建意向区域变更'}
          open={open}
          destroyOnHidden
          loading={loading || getIntentionContractDetailLoading || getStreetOpenedRegionIdsLoading}
          confirmLoading={createLoading || updateLoading || checkInfoLoading}
          okText="提交审核"
          styles={{
            body: { maxHeight: '60vh', margin: '0 -24px', padding: '0 24px', overflow: 'auto' },
          }}
          modalRender={(node) => (
            <Form
              form={form}
              clearOnDestroy
              scrollToFirstError
              labelCol={{ span: 8 }}
              onFinish={handleFinish}
            >
              {node}
            </Form>
          )}
          onCancel={() => setOpen(false)}
          onOk={form.submit}
          {...props}
        >
          <Form.Item
            label="意向合同"
            name="contractCode"
            rules={[{ required: true, message: '请选择意向合同' }]}
          >
            <IntentionContractSelect
              disabled={isEdit}
              onChange={(_, option) => {
                setFieldsWhenContractChange(option as IntentionContractDTO, {
                  regionCode: undefined,
                  positions: undefined,
                  channelType: undefined,
                });
              }}
            />
          </Form.Item>
          <Form.Item
            label="客户名称"
            name="customerId"
            rules={[{ required: true, message: '请选择客户' }]}
          >
            <RelCustomer
              placeholder="选择意向合同自动填充"
              defaultCustomerIdToNameMap={{
                [currentIntentionContractInCreate?.customerId || '']:
                  currentIntentionContractInCreate?.customerName,
                [data?.customerId || '']: data?.customerName,
              }}
            />
          </Form.Item>
          <Form.Item
            label="意向生效日期"
            name="effectiveDate"
            rules={[{ required: true, message: '请选择意向生效日期' }]}
          >
            <Input disabled placeholder="选择意向合同自动填充" />
          </Form.Item>
          <Form.Item
            label="意向到期日期"
            name="expirationDate"
            rules={[{ required: true, message: '请选择意向到期日期' }]}
          >
            <Input disabled placeholder="选择意向合同自动填充" />
          </Form.Item>
          <EditableRegion
            editable
            formItemProps={{
              label: '原意向区域',
              name: 'originalRegionCode',
              rules: [{ required: true, message: '请选择原意向区域' }],
            }}
            fieldProps={{
              disabled: true,
              regionLevel: 4,
              placeholder: '选择意向合同自动填充',
            }}
          />
          <EditableRegion
            editable
            fieldProps={{
              regionLevel: 4,
              transformDistricts,
              onChange: (val: any) => {
                form.setFieldValue('expansionPositionId', undefined);
                mutateExpansionPositions(undefined);

                if (val) {
                  getExpansionPositions(val.join('/'));
                }
              },
            }}
            formItemProps={{
              label: '变更意向区域',
              name: 'regionCode',
              validateFirst: true,
              rules: [
                { required: true, message: '请选择原意向区域' },
                {
                  validator: (_, value) => {
                    if (value && value === form.getFieldValue('originalRegionCode')) {
                      return Promise.reject(new Error('不能与原意向区域相同'));
                    }

                    return Promise.resolve();
                  },
                },
              ],
            }}
          />
          <Form.Item
            label="原意向点位"
            name="originalPositions"
            dependencies={['contractCode']}
            rules={[{ required: hasChannelType, message: '请选择原意向点位' }]}
          >
            <ChooseMapPoints multiple={false} disabled placeholder="选择意向合同自动填充" />
          </Form.Item>
          <Form.Item
            label="变更意向点位"
            name="positions"
            dependencies={['contractCode']}
            rules={[
              {
                required: hasChannelType,
                message: '请选择变更意向点位',
              },
            ]}
          >
            <ChooseMapPoints multiple={false} />
          </Form.Item>
          {/* 意向点位和扩容点位只能选择一个，所以变更意向点位必填时隐藏扩容点位 */}
          {!hasChannelType && (
            <>
              <Form.Item name="originalExpansionPositionId" hidden>
                <Input />
              </Form.Item>
              <Form.Item noStyle dependencies={['originalExpansionPositionId']}>
                {({ getFieldValue }) => {
                  const originalExpansionPositionId = getFieldValue('originalExpansionPositionId');

                  return (
                    <Form.Item label="原扩容点位">
                      {originalExpansionPositionId ? (
                        isEdit ? (
                          `${data?.originalExpansionPosition?.name}（经度：${data?.originalExpansionPosition?.longitude}；纬度：${data?.originalExpansionPosition?.latitude}）`
                        ) : (
                          `${currentIntentionContractInCreate?.expansionPosition?.name}（经度：${currentIntentionContractInCreate?.expansionPosition?.longitude}；纬度：${currentIntentionContractInCreate?.expansionPosition?.latitude}）`
                        )
                      ) : (
                        <span className="text-[#bfbfbf]">选择意向合同自动填充</span>
                      )}
                    </Form.Item>
                  );
                }}
              </Form.Item>
              <Form.Item
                label="变更扩容点位"
                name="expansionPositionId"
                dependencies={['positions']}
                rules={[
                  {
                    validator: (_, value) => {
                      const positions = form.getFieldValue('positions');

                      if (value && Array.isArray(positions) && positions.length > 0) {
                        return Promise.reject(new Error('意向点位和扩容点位只能选择其中一个'));
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Select
                  allowClear
                  placeholder="请选择变更扩容点位"
                  loading={getExpansionPositionsLoading}
                  // 编辑时如果区域一样应该要加上原来的
                  options={[
                    ...(isEdit &&
                    data?.expansionPosition &&
                    data?.regionCode === form.getFieldValue('regionCode')
                      ? [data.expansionPosition]
                      : []),
                    ...(expansionPositions || []),
                  ].map((i) => ({
                    label: `${i.name} （经度：${i.longitude}；纬度：${i.latitude}）`,
                    value: i.id,
                  }))}
                />
              </Form.Item>
            </>
          )}
          {hasChannelType && (
            <>
              <Form.Item
                label="原合作的特殊渠道"
                name="originalChannelType"
                rules={[{ required: true, message: '请选择原合作的特殊渠道' }]}
              >
                <Select
                  placeholder="选择意向合同自动填充"
                  disabled
                  options={enum2Options(ContractChannelTypeEnum)}
                />
              </Form.Item>
              <Form.Item
                label="变更合作的特殊渠道"
                name="channelType"
                rules={[{ required: true, message: '请选择变更合作的特殊渠道' }]}
              >
                <Select
                  allowClear
                  options={enum2Options(ContractChannelTypeEnum)}
                  placeholder="请选择变更合作的特殊渠道"
                />
              </Form.Item>
            </>
          )}
          <Form.Item
            label="变更原因"
            name="reason"
            rules={[{ required: true, message: '请选择变更原因' }]}
          >
            <Select
              placeholder="请选择变更原因"
              options={enum2Options(IntendedAreaChangeReasonEnum)}
            />
          </Form.Item>
          <Form.Item
            label="变更说明"
            name="description"
            rules={[{ required: true, message: '请输入变更说明' }]}
          >
            <Input.TextArea maxLength={500} placeholder="请输入变更说明" />
          </Form.Item>
          <FileUpload formItemProps={{ name: 'applyAttachments', label: '申请附件' }} />
        </Modal>
        <CheckCodeModal
          title={[
            { label: '加盟属性不匹配', show: !checkInfoData?.success },
            { label: '区域无剩余名额', show: areaQuota && areaQuota.remainQuotaNum <= 0 },
          ]
            .filter((i) => i.show)
            .map((i) => i.label)
            .join('、')}
          open={checkCodeModalOpen}
          onCancel={() => setCheckCodeModalOpen(false)}
          onSuccess={() => {
            setCheckCodeModalOpen(false);
            handleSave(form.getFieldsValue());
          }}
          messageInfo={() => {
            const joinRegionEnum = enum2ValueEnum(JoinRegionEnum);

            return (
              <div className="text-[#e10600] text-sm mb-2">
                {!checkInfoData?.success && (
                  <>
                    <div>
                      客户性质：
                      {enum2ValueEnum(StoreCategoryEnum)[checkInfoData?.customerNature!]}
                    </div>
                    <div>
                      原区域性质：
                      {joinRegionEnum[checkInfoData?.originalJoinRegion!]}
                    </div>
                    <div>
                      变更区域性质：
                      {joinRegionEnum[checkInfoData?.joinRegion!]}
                    </div>
                  </>
                )}
                {areaQuota && areaQuota.remainQuotaNum <= 0 && (
                  <div>区域剩余名额：{areaQuota.remainQuotaNum}</div>
                )}
              </div>
            );
          }}
        />
      </>
    );
  },
);

export default IntendedAreaChangeModal;
