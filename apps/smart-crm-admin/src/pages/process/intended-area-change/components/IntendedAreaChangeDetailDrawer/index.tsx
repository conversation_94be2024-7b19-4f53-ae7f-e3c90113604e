import React, { useMemo, useRef, useState } from 'react';
import { intendedAreaChangeAuditStatusOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { ButtonGroup } from '@src/components';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import IntentionDetailDrawer from '@src/pages/contract/intention/components/IntentionDetailDrawer';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import { PermissionsMap, usePermission } from '@src/permissions';
import { AuditStatusEnum } from '@src/services/common/type';
import { StoreCategoryEnum } from '@src/services/contract/formal/type';
import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';
import {
  deleteIntendedAreaChange,
  getIntendedAreaChangeDetail,
} from '@src/services/process/intended-area-change';
import {
  IntendedAreaChangeDTO,
  IntendedAreaChangeReasonEnum,
} from '@src/services/process/intended-area-change/type';
import { JoinRegionEnum } from '@src/services/regional/quota/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { Alert, App, Card, Col, Drawer, DrawerProps, Row } from 'antd';
import IntendedAreaChangeAuditCard, {
  IntendedAreaChangeAuditCardRef,
} from './IntendedAreaChangeAuditCard';
import IntendedAreaChangeModal from '../IntendedAreaChangeModal';

interface IntendedAreaChangeDetailDrawerProps extends DrawerProps {
  getId: () => number | null;
  onUpdate?: () => void;
  onDelete?: () => void;
  onTodoSuccess?: () => void;
}

const IntendedAreaChangeDetailDrawer = React.forwardRef<
  ModalRef,
  IntendedAreaChangeDetailDrawerProps
>(({ getId, onUpdate, onDelete, onTodoSuccess, ...props }, ref) => {
  const { modal, message } = App.useApp();
  const [open, setOpen] = useState(false);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const intendedAreaChangeModalRef = useRef<ModalRef>(null);
  const intentionDetailDrawerRef = useRef<ModalRef>(null);
  const auditCardRef = useRef<IntendedAreaChangeAuditCardRef>(null);

  const id = getId()!;

  const checkPermission = usePermission();
  const { data: users } = useAllUsers({ ready: open });
  const { data, loading, refresh, mutate } = useRequest(() => getIntendedAreaChangeDetail(id), {
    ready: open,
    onBefore: () => mutate(undefined),
  });

  React.useImperativeHandle(ref, () => ({
    open: () => setOpen(true),
    close: () => setOpen(false),
  }));

  const baseInfoItems: DescriptionFieldType<IntendedAreaChangeDTO>[] = [
    {
      label: '申请编号',
      field: 'code',
    },
    {
      label: '意向合同编号',
      field: 'contractCode',
      children: (
        <a onClick={() => intentionDetailDrawerRef.current?.open()}>{data?.contractCode}</a>
      ),
    },
    {
      label: '审核状态',
      field: 'auditStatus',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: intendedAreaChangeAuditStatusOptions,
      },
    },
    {
      label: '意向生效日期',
      field: 'effectiveDate',
    },
    {
      label: '意向到期日期',
      field: 'expirationDate',
    },
    {
      label: '客户性质',
      field: 'customerNature',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(StoreCategoryEnum),
      },
    },
    {
      label: '原意向区域',
      field: 'originalRegionCode',
      valueType: ValueType.REGION,
      fieldProps: {
        regionLevel: 4,
      },
    },
    {
      label: '原意向区域性质',
      field: 'originalJoinRegion',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(JoinRegionEnum),
      },
    },
    {
      label: '变更意向区域',
      field: 'regionCode',
      valueType: ValueType.REGION,
      fieldProps: {
        regionLevel: 4,
      },
    },
    {
      label: '变更意向区域性质',
      field: 'joinRegion',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(JoinRegionEnum),
      },
    },
    {
      label: '原意向点位',
      field: 'originalPositions',
      children: data?.originalPositions?.map(
        (i) => `${i.name}（经度：${i.longitude}；纬度：${i.latitude}）`,
      ),
    },
    {
      label: '变更意向点位',
      field: 'positions',
      children: data?.positions?.map(
        (i) => `${i.name}（经度：${i.longitude}；纬度：${i.latitude}）`,
      ),
    },
    {
      label: '原扩容点位',
      field: 'originalExpansionPosition',
      children:
        data?.originalExpansionPosition &&
        `${data.originalExpansionPosition.name}（经度：${data.originalExpansionPosition.longitude}；纬度：${data.originalExpansionPosition.latitude}）`,
    },
    {
      label: '变更扩容点位',
      field: 'expansionPosition',
      children:
        data?.expansionPosition &&
        `${data.expansionPosition.name}（经度：${data.expansionPosition.longitude}；纬度：${data.expansionPosition.latitude}）`,
    },
    {
      label: '原合作的特殊渠道',
      field: 'originalChannelType',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(ContractChannelTypeEnum),
      },
    },
    {
      label: '变更合作的特殊渠道',
      field: 'channelType',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(ContractChannelTypeEnum),
      },
    },
    {
      label: '变更原因',
      field: 'reason',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(IntendedAreaChangeReasonEnum),
      },
    },
    {
      label: '变更说明',
      field: 'description',
    },
    {
      label: '申请附件',
      field: 'applyAttachments',
      valueType: ValueType.ATTACHMENT,
    },
    {
      label: '变更区域剩余名额（提交时）',
      field: 'remainQuotaNum',
    },
  ];

  // 三者性质是否相同
  const isSameNature = useMemo(() => {
    if (!data) {
      return true;
    }

    // 映射，M 对 M，T 对 T，相当于值相等
    const storeToRegionMap = {
      [StoreCategoryEnum['加盟 M']]: JoinRegionEnum['公开加盟区(M)'],
      [StoreCategoryEnum['加盟 T']]: JoinRegionEnum['邀请加盟区(T)'],
    };

    const customerNature = storeToRegionMap[data.customerNature];

    return customerNature === data.originalJoinRegion && customerNature === data.joinRegion;
  }, [data]);

  return (
    <Drawer
      title="意向区域变更详情"
      width={1200}
      open={open}
      push={false}
      destroyOnHidden
      onClose={() => setOpen(false)}
      {...props}
    >
      <Card loading={loading}>
        <div className="flex justify-between">
          <div className="flex flex-col gap-2">
            <p>
              客户名称：
              <a onClick={() => customerDetailDrawerRef.current?.open()}>{data?.customerName}</a>
            </p>
            <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
            <p>创建时间：{data?.createTime}</p>
          </div>
          <ButtonGroup
            items={[
              {
                label: '编辑',
                show:
                  checkPermission(PermissionsMap.ProcessIntendedAreaChangeEdit) &&
                  [AuditStatusEnum.REVOKE, AuditStatusEnum.NOT_PASS].includes(data?.auditStatus!),
                onClick: () => intendedAreaChangeModalRef.current?.open(),
              },
              {
                label: '删除',
                danger: true,
                show:
                  checkPermission(PermissionsMap.ProcessIntendedAreaChangeDelete) &&
                  data?.auditStatus === AuditStatusEnum.REVOKE,
                onClick: () =>
                  modal.confirm({
                    title: '删除',
                    content: '确定要删除该意向区域变更申请吗？',
                    onOk: async () => {
                      await deleteIntendedAreaChange(id);
                      message.success('删除成功');
                      setOpen(false);
                      onDelete?.();
                    },
                  }),
              },
            ]}
          />
        </div>
      </Card>
      <Row gutter={[20, 20]} className="mt-5">
        <Col span={24} xl={16}>
          <Card loading={loading}>
            {!isSameNature && (
              <Alert
                type="warning"
                message="客户性质、原意向区域性质、变更意向区域存在不一致"
                showIcon
                className="mb-5"
              />
            )}
            {renderDescriptions(baseInfoItems, data)}
          </Card>
        </Col>
        <Col span={24} xl={8}>
          <IntendedAreaChangeAuditCard
            ref={auditCardRef}
            id={id}
            auditStatus={data?.auditStatus}
            loading={loading}
            onTodoSuccess={onTodoSuccess}
            onSuccess={() => {
              refresh();
              onUpdate?.();
              auditCardRef.current?.refresh();
            }}
          />
        </Col>
      </Row>

      <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => data?.customerId} />
      <IntendedAreaChangeModal
        ref={intendedAreaChangeModalRef}
        getId={() => id}
        onSuccess={() => {
          refresh();
          onUpdate?.();
          auditCardRef.current?.refresh();
        }}
      />
      <IntentionDetailDrawer ref={intentionDetailDrawerRef} getId={() => data?.contractId!} />
    </Drawer>
  );
});

export default IntendedAreaChangeDetailDrawer;
