import { useEffect, useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { intendedAreaChangeAuditStatusOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ExportButton, ProTable, RegionCascader, TableActions, UserSelect } from '@src/components';
import EditableAttachment from '@src/components/Editable/EditableAttachment';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useAllUsers from '@src/hooks/useAllUsers';
import { useNoticeEventSubscription } from '@src/layout';
import IntentionDetailDrawer from '@src/pages/contract/intention/components/IntentionDetailDrawer';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { AuditStatusEnum } from '@src/services/common/type';
import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';
import {
  batchDeleteIntendedAreaChange,
  deleteIntendedAreaChange,
  exportIntendedAreaChange,
  getIntendedAreaChangeList,
} from '@src/services/process/intended-area-change';
import {
  ExportIntendedAreaChangeReq,
  IntendedAreaChangeDTO,
  IntendedAreaChangeReasonEnum,
} from '@src/services/process/intended-area-change/type';
import { compatibleTableActionWidth, enum2ValueEnum, optionsToValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Dropdown, MenuProps, Typography } from 'antd';
import dayjs from 'dayjs';
import { useSearchParams } from 'react-router-dom';
import IntendedAreaChangeDetailDrawer from './components/IntendedAreaChangeDetailDrawer';
import IntendedAreaChangeModal from './components/IntendedAreaChangeModal';

const IntendedAreaChange = () => {
  const { modal, message } = App.useApp();
  const [searchParams, setSearchParams] = useSearchParams();
  const actionRef = useRef<ActionType>(null);
  const intendedAreaChangeModalRef = useRef<ModalRef>(null);
  const currentIdRef = useRef<number | null>(null);
  const contractIdRef = useRef<number | null>(null);
  const contractDetailDrawerRef = useRef<ModalRef>(null);
  const detailDrawerRef = useRef<ModalRef>(null);
  const customerIdRef = useRef<number | null>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const filterParamsRef = useRef<ExportIntendedAreaChangeReq>({});

  const checkPermission = usePermission();
  const { data: users } = useAllUsers();
  const { data, runAsync: getList } = useRequest(getIntendedAreaChangeList, {
    manual: true,
  });

  useNoticeEventSubscription((event) => {
    if (
      event.type === 'intended-area-change' &&
      data?.result.some((i) => i.id === event.payload.id)
    ) {
      actionRef.current?.reload();
    }
  });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      currentIdRef.current = Number(id);
      detailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: ProColumns<IntendedAreaChangeDTO>[] = [
    {
      title: '申请编号',
      dataIndex: 'code',
      search: false,
      fixed: 'left',
      renderText: (value, { id }) => (
        <a
          onClick={() => {
            currentIdRef.current = id;
            detailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '意向合同编号',
      dataIndex: 'contractCode',
      renderText: (value, { contractId }) => (
        <a
          onClick={() => {
            contractIdRef.current = contractId;
            contractDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      fieldProps: {
        mode: 'multiple',
      },
      valueEnum: optionsToValueEnum(intendedAreaChangeAuditStatusOptions),
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      renderText: (value, { customerId }) => (
        <Typography.Text
          style={{ color: 'var(--ant-color-link)', cursor: 'pointer' }}
          ellipsis={{ tooltip: true }}
          onClick={() => {
            customerIdRef.current = customerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </Typography.Text>
      ),
    },
    {
      title: '意向生效日期',
      dataIndex: 'effectiveDate',
      search: false,
    },
    {
      title: '意向到期日期',
      dataIndex: 'expirationDate',
      search: false,
    },
    {
      title: '原意向区域',
      dataIndex: 'originalRegionCode',
      ellipsis: true,
      renderText: (value) => <EditableRegion value={value} fieldProps={{ regionLevel: 4 }} />,
      renderFormItem: () => (
        <RegionCascader
          placeholder="请选择"
          multiple
          unlimited={{ options: true, onChange: true }}
        />
      ),
      search: {
        transform: (value) => ({
          originalRegionCode: value.map((item: number[]) => item.join('/')).join(','),
        }),
      },
    },
    {
      title: '变更意向区域',
      dataIndex: 'regionCode',
      search: false,
      ellipsis: true,
      renderText: (value) => <EditableRegion value={value} fieldProps={{ regionLevel: 4 }} />,
    },
    {
      title: '原意向点位',
      dataIndex: 'originalPositions',
      search: false,
      ellipsis: true,
      renderText: (_, { originalPositions }) =>
        originalPositions?.map((i) => `${i.name}（经度：${i.longitude}；纬度：${i.latitude}）`),
    },
    {
      title: '变更意向点位',
      dataIndex: 'positions',
      search: false,
      ellipsis: true,
      renderText: (_, { positions }) =>
        positions?.map((i) => `${i.name}（经度：${i.longitude}；纬度：${i.latitude}）`),
    },
    {
      title: '原扩容点位',
      dataIndex: 'originalExpansionPosition',
      search: false,
      ellipsis: true,
      renderText: (_, { originalExpansionPosition }) =>
        originalExpansionPosition &&
        `${originalExpansionPosition.name}（经度：${originalExpansionPosition.longitude}；纬度：${originalExpansionPosition.latitude}）`,
    },
    {
      title: '变更扩容点位',
      dataIndex: 'expansionPosition',
      search: false,
      ellipsis: true,
      renderText: (_, { expansionPosition }) =>
        expansionPosition &&
        `${expansionPosition.name}（经度：${expansionPosition.longitude}；纬度：${expansionPosition.latitude}）`,
    },
    {
      title: '原合作的特殊渠道',
      dataIndex: 'originalChannelType',
      search: false,
      valueEnum: enum2ValueEnum(ContractChannelTypeEnum),
    },
    {
      title: '变更合作的特殊渠道',
      dataIndex: 'channelType',
      search: false,
      valueEnum: enum2ValueEnum(ContractChannelTypeEnum),
    },
    {
      title: '变更原因',
      dataIndex: 'reason',
      search: false,
      valueEnum: enum2ValueEnum(IntendedAreaChangeReasonEnum),
    },
    {
      title: '变更说明',
      dataIndex: 'description',
      search: false,
      ellipsis: true,
    },
    {
      title: '申请附件',
      dataIndex: 'applyAttachments',
      search: false,
      onCell: () => ({
        className: '!py-0',
      }),
      renderText: (value) => <EditableAttachment value={value} />,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      renderText: (value) => users?.find((i) => i.id === value)?.name,
      renderFormItem: () => <UserSelect />,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: (_, type) => (type === 'table' ? 'dateTime' : 'dateRange'),
      search: {
        transform: (value) => ({
          createBeginTime: dayjs(value[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          createEndTime: dayjs(value[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        }),
      },
    },
    {
      title: '操作',
      key: 'action',
      search: false,
      align: 'center',
      fixed: 'right',
      width: compatibleTableActionWidth(100),
      render: (_, { id, contractId, auditStatus }) => (
        <TableActions
          shortcuts={[
            {
              label: '编辑',
              show:
                checkPermission(PermissionsMap.ProcessIntendedAreaChangeEdit) &&
                [AuditStatusEnum.REVOKE, AuditStatusEnum.NOT_PASS].includes(auditStatus),
              onClick: () => {
                currentIdRef.current = id;
                contractIdRef.current = contractId;
                intendedAreaChangeModalRef.current?.open();
              },
            },
            {
              label: '删除',
              danger: true,
              show:
                checkPermission(PermissionsMap.ProcessIntendedAreaChangeDelete) &&
                auditStatus === AuditStatusEnum.REVOKE,
              onClick: () =>
                modal.confirm({
                  title: '删除',
                  content: '确定要删除该意向区域变更申请吗？',
                  onOk: async () => {
                    await deleteIntendedAreaChange(id);
                    message.success('删除成功');
                    actionRef.current?.reload();
                    setSelectedRowKeys((prev) => prev.filter((i) => i !== id));
                  },
                }),
            },
          ]}
        />
      ),
    },
  ];

  const batchMenuItems: MenuProps['items'] = [
    {
      key: 'delete',
      label: '删除',
      danger: true,
      auth: PermissionsMap.ProcessIntendedAreaChangeDelete,
      onClick: () =>
        modal.confirm({
          title: '删除',
          content: `确定要删除选中的 ${selectedRowKeys.length} 条意向区域变更申请吗？`,
          onOk: async () => {
            await batchDeleteIntendedAreaChange(selectedRowKeys);
            message.success('删除成功');
            setSelectedRowKeys([]);
            actionRef.current?.reload();
          },
        }),
    },
  ].filter((i) => checkPermission(i.auth));

  return (
    <PageContainer>
      <ProTable
        bordered
        actionRef={actionRef}
        sticky
        size="small"
        rowKey="id"
        search={{
          labelWidth: 100,
        }}
        cardProps={false}
        options={false}
        columnEmptyText=""
        tableAlertRender={false}
        scroll={{
          x: columns.reduce(
            (total, cur) => total + (cur.hidden ? 0 : (cur.width as number) || 180),
            0,
          ),
        }}
        rowSelection={{
          fixed: true,
          showSort: true,
          selectedRowKeys,
          preserveSelectedRowKeys: true,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        toolbar={{
          subTitle: (
            <span className="text-black">
              共 {data?.total || 0} 条记录
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 条
                  <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            batchMenuItems.length > 0 && (
              <Dropdown
                disabled={!selectedRowKeys.length}
                menu={{
                  items: batchMenuItems,
                }}
              >
                <Button type="text" className="!flex items-center">
                  批量操作 <CaretDownOutlined />
                </Button>
              </Dropdown>
            ),
            <Permission value={PermissionsMap.ProcessIntendedAreaChangeCreate}>
              <Button
                type="text"
                onClick={() => {
                  currentIdRef.current = null;
                  intendedAreaChangeModalRef.current?.open();
                }}
              >
                新建
              </Button>
            </Permission>,
            <Permission value={PermissionsMap.ProcessIntendedAreaChangeExport}>
              <ExportButton
                total={selectedRowKeys.length || data?.total}
                request={() =>
                  exportIntendedAreaChange(
                    selectedRowKeys.length ? { ids: selectedRowKeys } : filterParamsRef.current,
                  )
                }
              />
            </Permission>,
          ],
        }}
        columns={columns}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        request={async ({ current, pageSize, ...params }) => {
          filterParamsRef.current = params as ExportIntendedAreaChangeReq;

          const res = await getList({ pageNum: current, pageSize, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <IntendedAreaChangeModal
        ref={intendedAreaChangeModalRef}
        getId={() => currentIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
      <IntendedAreaChangeDetailDrawer
        ref={detailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
        onDelete={() => {
          actionRef.current?.reload();
          setSelectedRowKeys((prev) => prev.filter((i) => i !== currentIdRef.current));
        }}
      />
      <IntentionDetailDrawer ref={contractDetailDrawerRef} getId={() => contractIdRef.current} />
      <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />
    </PageContainer>
  );
};

export default IntendedAreaChange;
