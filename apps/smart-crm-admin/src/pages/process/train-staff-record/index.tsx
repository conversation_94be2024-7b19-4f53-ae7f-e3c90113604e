import { useEffect, useRef } from 'react';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { workflowAuditStatusOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { TableActions } from '@src/components';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { PersonTypeEnum, TrainingPersonsDTO } from '@src/services/process/train-staff/type';
import { getTrainingPersonRecordPage } from '@src/services/process/train-staff-record';
import {
  GetTrainingPersonRecordPageReq,
  TrainingPersonRecordChangeReasonEnum,
  TrainingPersonRecordListDTO,
} from '@src/services/process/train-staff-record/type';
import { compatibleTableActionWidth, enum2ValueEnum, optionsToValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { Button } from 'antd';
import { useSearchParams } from 'react-router-dom';
import CreateEditTrainStaffRecordModal from './components/CreateEditTrainStaffRecordModal';
import TrainStaffRecordDetailDrawer from './components/TrainStaffRecordDetailDrawer';

const TrainStaffRecord = () => {
  const checkPermission = usePermission();
  const [searchParams, setSearchParams] = useSearchParams();
  const actionRef = useRef<ActionType>(null);
  const createEditModalRef = useRef<ModalRef>(null);
  const shopIdRef = useRef<string | null>(null);
  const shopDetailDrawerRef = useRef<ModalRef>(null);
  const detailDrawerRef = useRef<ModalRef>(null);
  const filterParamsRef = useRef<GetTrainingPersonRecordPageReq>({});
  const currentRef = useRef<TrainingPersonRecordListDTO | null>(null);

  const { runAsync: getList } = useRequest(getTrainingPersonRecordPage, { manual: true });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      currentRef.current = {
        id: +id,
      } as TrainingPersonRecordListDTO;
      detailDrawerRef.current?.open();
      searchParams.delete('id');
      setSearchParams(searchParams, { replace: true });
    }
  }, [searchParams, setSearchParams]);

  const columns: ProColumns<TrainingPersonRecordListDTO>[] = [
    {
      title: '门店编号',
      dataIndex: 'shopId',
      fixed: 'left',
      width: 100,
      renderText: (value: string) => (
        <a
          onClick={() => {
            shopIdRef.current = value;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '门店冠名',
      dataIndex: 'shopName',
      search: false,
      width: 200,
    },
    {
      title: '更换原因',
      dataIndex: 'changeReason',
      search: false,
      valueEnum: enum2ValueEnum(TrainingPersonRecordChangeReasonEnum),
      width: 120,
    },
    {
      title: '更换人员身份',
      dataIndex: 'trainingPersonRecordItems',
      search: false,
      width: 120,
      renderText: (value: TrainingPersonsDTO[]) => {
        return value
          ?.map((item) => `${item.personType === PersonTypeEnum.SHOP_MANAGER ? '店长' : '店员'}`)
          .join(',');
      },
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      valueEnum: optionsToValueEnum(workflowAuditStatusOptions),
      width: 100,
    },
    {
      title: '创建人',
      search: false,
      dataIndex: 'createUserName',
      width: 100,
    },
    {
      title: '创建时间',
      search: false,
      dataIndex: 'createTime',
      width: 180,
    },
    {
      title: '审核通过时间',
      search: false,
      dataIndex: 'auditPassTime',
      width: 180,
    },

    {
      title: '操作',
      key: 'action',
      width: compatibleTableActionWidth(80),
      search: false,
      fixed: 'right',
      align: 'center',
      render: (_, record) => {
        return (
          <TableActions
            shortcuts={[
              {
                label: '详情',
                show: checkPermission(PermissionsMap.TrainStaffRecordDetail),
                onClick: () => {
                  currentRef.current = record;
                  detailDrawerRef.current?.open();
                },
              },
            ]}
          />
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable
        bordered
        actionRef={actionRef}
        sticky
        size="small"
        rowKey="id"
        search={{
          labelWidth: 'auto',
        }}
        cardProps={false}
        options={false}
        columnEmptyText=""
        tableAlertRender={false}
        scroll={{
          x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0),
        }}
        toolbar={{
          actions: [
            <Permission value={PermissionsMap.TrainStaffRecordCreate}>
              <Button
                type="text"
                onClick={() => {
                  createEditModalRef.current?.open();
                }}
              >
                新建
              </Button>
            </Permission>,
          ],
        }}
        columns={columns}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        request={async ({ current, pageSize, ...params }: Record<string, any>) => {
          filterParamsRef.current = params;

          const res = await getList({ pageNum: current, pageSize, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />
      <CreateEditTrainStaffRecordModal
        ref={createEditModalRef}
        onSuccess={() => actionRef.current?.reload()}
      />
      <TrainStaffRecordDetailDrawer
        ref={detailDrawerRef}
        getItem={() => currentRef.current}
        onUpdate={() => {
          actionRef.current?.reload();
        }}
      />
      <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => shopIdRef.current} />
    </PageContainer>
  );
};

export default TrainStaffRecord;
