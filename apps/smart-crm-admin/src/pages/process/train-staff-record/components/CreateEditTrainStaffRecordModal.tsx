import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { TrainingInfoPassSelect, TrainingPersonsReplace } from '@src/components';
import { TrainingPersonsDTO } from '@src/services/process/train-staff/type';
import { saveTrainingPersonRecord } from '@src/services/process/train-staff-record';
import {
  TrainingPersonRecordChangeReasonEnum,
  TrainingPersonRecordDetailDTO,
  TrainingPersonRecordItemsDTO,
} from '@src/services/process/train-staff-record/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { Card, Form, Input, Modal, ModalProps, Select } from 'antd';
import { DefaultOptionType } from 'antd/es/select';

interface CreateEditTrainStaffModalProps extends ModalProps {
  onSuccess: () => void;
}

const CreateEditTrainStaffRecordModal = React.forwardRef<ModalRef, CreateEditTrainStaffModalProps>(
  ({ onSuccess, ...props }, ref) => {
    const [form] = Form.useForm();

    const [open, setOpen] = useState(false);
    const [trainingPersons, setTrainingPersons] = useState<
      TrainingPersonRecordItemsDTO[] | undefined
    >();

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
      },
      close: () => setOpen(false),
    }));

    const { runAsync: save, loading: saveLoading } = useRequest(saveTrainingPersonRecord, {
      manual: true,
    });

    const handleSave = async (values: TrainingPersonRecordDetailDTO) => {
      const params = {
        ...values,
        trainingPersonRecordItems: values.trainingPersonRecordItems?.filter(
          (item: TrainingPersonRecordItemsDTO) =>
            item.targetName && item.targetPhone && item.targetIdentityCard,
        ),
      };

      await save(params);
      setOpen(false);
      onSuccess();
    };

    const handleShopIdChange = (_: string, record?: DefaultOptionType) => {
      const _trainingPersons = record?.trainingPersons?.map((item: TrainingPersonsDTO) => {
        const { name, phone, phoneSensitive, identityCard, identityCardSensitive, ...restItem } =
          item || {};

        return {
          originalName: name,
          originalPhone: phone,
          originalPhoneSensitive: phoneSensitive,
          originalIdentityCard: identityCard,
          originalIdentityCardSensitive: identityCardSensitive,
          ...restItem,
        };
      });

      setTrainingPersons(_trainingPersons);
      form.setFieldValue('shopName', record?.shopName);
      form.setFieldValue('trainingInfoId', record?.id);
      form.setFieldValue('trainingPersonRecordItems', _trainingPersons);
    };

    return (
      <Modal
        open={open}
        width={600}
        title={'更换培训人员'}
        destroyOnHidden
        styles={{
          body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
        }}
        modalRender={(node) => (
          <Form
            form={form}
            clearOnDestroy
            scrollToFirstError
            labelCol={{ span: 6 }}
            onFinish={handleSave}
          >
            {node}
          </Form>
        )}
        okText="发起审核"
        confirmLoading={saveLoading}
        onOk={() => {
          form.submit();
        }}
        onCancel={() => setOpen(false)}
        {...props}
      >
        <Form form={form} className="flex flex-col space-y-3 mt-3" onFinish={handleSave}>
          <Form.Item
            label="更换原因"
            name="changeReason"
            rules={[{ required: true, message: '请选择更换原因' }]}
          >
            <Select
              placeholder={'请选择更换原因'}
              options={enum2Options(TrainingPersonRecordChangeReasonEnum).map((i) => ({
                ...i,
                disabled: i.value === TrainingPersonRecordChangeReasonEnum.鉴定多次未通过,
              }))}
            />
          </Form.Item>
          <Card title="门店信息">
            <Form.Item
              label="门店编号"
              name="shopId"
              rules={[{ required: true, message: '请选择原门店编号' }]}
            >
              <TrainingInfoPassSelect onChange={handleShopIdChange} />
            </Form.Item>
            <Form.Item label="门店冠名" name="shopName">
              <Input placeholder="选择门店编号后自动填充" disabled />
            </Form.Item>
            <Form.Item name="trainingInfoId" hidden>
              <Input />
            </Form.Item>
          </Card>
          {trainingPersons?.length ? (
            <Card title="人员信息">
              <TrainingPersonsReplace />
            </Card>
          ) : null}
        </Form>
      </Modal>
    );
  },
);

export default CreateEditTrainStaffRecordModal;
