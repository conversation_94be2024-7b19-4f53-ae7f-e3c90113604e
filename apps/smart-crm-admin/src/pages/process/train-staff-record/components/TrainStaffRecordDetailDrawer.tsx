import React, { useRef } from 'react';
import { workflowAuditStatusOptions } from '@src/common/constants';
import { DescriptionFieldGroupType, ModalRef } from '@src/common/interface';
import { AuditFlow, TrainingPersonsReplace } from '@src/components';
import { ValueType } from '@src/components/ETable';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { PermissionsMap, usePermissionOpen } from '@src/permissions';
import { PersonTypeEnum } from '@src/services/process/train-staff/type';
import {
  getTrainingPersonRecordDetail,
  updateTrainingPersonRecord,
} from '@src/services/process/train-staff-record';
import {
  TrainingPersonRecordChangeReasonEnum,
  TrainingPersonRecordListDTO,
} from '@src/services/process/train-staff-record/type';
import {
  WorkflowAuditOperationTypeEnum,
  WorkflowBusinessTypeEnum,
} from '@src/services/workflow/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { Card, Col, Drawer, DrawerProps, Row } from 'antd';

interface TrainStaffRecordDetailDrawerProps extends DrawerProps {
  getItem: () => TrainingPersonRecordListDTO | null;
  onUpdate?: () => void;
}

const TrainStaffRecordDetailDrawer = React.forwardRef<ModalRef, TrainStaffRecordDetailDrawerProps>(
  ({ getItem, onUpdate, ...props }, ref) => {
    const [open, setOpen] = usePermissionOpen(PermissionsMap.TrainStaffRecordDetail);
    const shopDetailDrawerRef = useRef<ModalRef>(null);

    const itemInfo = getItem();

    const { data, loading, refresh, mutate } = useRequest(
      async () => {
        if (!itemInfo?.id) {
          return;
        }

        const res = await getTrainingPersonRecordDetail(itemInfo.id);

        return res;
      },
      { ready: open },
    );

    React.useImperativeHandle(ref, () => ({
      open: () => {
        mutate(undefined);
        setOpen(true);
      },
      close: () => setOpen(false),
    }));

    const baseInfoItems: DescriptionFieldGroupType<TrainingPersonRecordListDTO>[] = [
      {
        label: '基础信息',
        children: [
          {
            label: '门店编号',
            field: 'shopId',
            children: (
              <a
                onClick={() => {
                  shopDetailDrawerRef.current?.open();
                }}
              >
                {data?.shopId}
              </a>
            ),
          },
          {
            label: '门店冠名',
            field: 'shopName',
          },
          {
            field: 'changeReason',
            label: '更换原因',
            valueType: ValueType.SINGLE,
            fieldProps: {
              options: enum2Options(TrainingPersonRecordChangeReasonEnum),
            },
          },
          {
            field: 'auditStatus',
            label: '审核状态',
            valueType: ValueType.SINGLE,
            fieldProps: {
              options: workflowAuditStatusOptions,
            },
          },
          {
            field: 'createUserName',
            label: '创建人',
          },
          {
            label: '创建时间',
            field: 'createTime',
            valueType: ValueType.DATE_TIME,
          },
          {
            label: '审核通过时间',
            field: 'auditPassTime',
            valueType: ValueType.DATE_TIME,
          },
        ],
      },
      {
        label: '更换人员信息',
        children:
          (data?.trainingPersonRecordItems || [])?.flatMap((item, index) => {
            const hasShopManager = data?.trainingPersonRecordItems?.some(
              (i) => i.personType === PersonTypeEnum.SHOP_MANAGER,
            );
            const personTypeName =
              item.personType === PersonTypeEnum.SHOP_MANAGER
                ? '店长'
                : `店员${hasShopManager ? index : index + 1}`;

            return [
              {
                label: `原${personTypeName}`,
                field: ['trainingPersonRecordItems', `${index}`, 'originalName'],
              },
              {
                label: `新${personTypeName}`,
                field: ['trainingPersonRecordItems', `${index}`, 'targetName'],
              },
              {
                label: `原${personTypeName}电话`,
                field: ['trainingPersonRecordItems', `${index}`, 'originalPhone'],
                valueType: ValueType.PHONE,
                fieldProps: {
                  sensitiveValue: item?.originalPhoneSensitive,
                },
              },
              {
                label: `新${personTypeName}电话`,
                field: ['trainingPersonRecordItems', `${index}`, 'targetPhone'],
                valueType: ValueType.PHONE,
                fieldProps: {
                  sensitiveValue: item?.targetPhoneSensitive,
                },
              },
              {
                label: `原${personTypeName}身份证号`,
                field: ['trainingPersonRecordItems', `${index}`, 'originalIdentityCard'],
                valueType: ValueType.IDENTITY_CARD,
                fieldProps: {
                  sensitiveValue: item?.originalIdentityCardSensitive,
                },
              },
              {
                label: `新${personTypeName}身份证号`,
                field: ['trainingPersonRecordItems', `${index}`, 'targetIdentityCard'],
                valueType: ValueType.IDENTITY_CARD,
                fieldProps: {
                  sensitiveValue: item?.targetIdentityCardSensitive,
                },
              },
            ];
          }) || [],
      },
    ];

    return (
      <Drawer
        title="培训人员更换记录详情"
        open={open}
        width={1100}
        destroyOnClose
        onClose={() => setOpen(false)}
        {...props}
      >
        <Row gutter={[20, 20]}>
          <Col span={24} xl={16}>
            <Card loading={loading}>{renderDescriptions(baseInfoItems, data)}</Card>
          </Col>
          <Col span={24} xl={8}>
            <AuditFlow.WorkflowCard
              loading={loading}
              auditStatus={data?.auditStatus}
              processInstanceId={data?.processInstanceId}
              businessId={data?.id}
              businessType={WorkflowBusinessTypeEnum.SCRM_TRAINING_PERSON_CHANGE}
              businessName={data?.shopId}
              auditButtons={{
                [WorkflowAuditOperationTypeEnum.RETURN]: false,
              }}
              formProps={{
                initialValues: {
                  trainingPersonRecordItems: data?.trainingPersonRecordItems,
                },
              }}
              onBeforeSubmit={(values, type) => {
                if (data && type === WorkflowAuditOperationTypeEnum.APPROVE) {
                  return updateTrainingPersonRecord({
                    id: data.id,
                    trainingInfoId: data.trainingInfoId,
                    shopId: data.shopId,
                    changeReason: data.changeReason,
                    trainingPersonRecordItems: values.trainingPersonRecordItems,
                  });
                }
              }}
              onSuccess={() => {
                refresh();
                onUpdate?.();
              }}
            >
              {(originalNode, type) =>
                type === WorkflowAuditOperationTypeEnum.APPROVE ? (
                  <TrainingPersonsReplace defaultShow />
                ) : (
                  originalNode
                )
              }
            </AuditFlow.WorkflowCard>
          </Col>
        </Row>
        <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => data?.shopId} />
      </Drawer>
    );
  },
);

export default TrainStaffRecordDetailDrawer;
