import React, { useRef, useState } from 'react';
import { InboxOutlined } from '@ant-design/icons';
import { ImportFileFormItem } from '@src/components';
import useUploadFile from '@src/hooks/useUploadFile';
import {
  downloadCertificateRenewalReviewTemplate,
  submitCertificateRenewalReview,
} from '@src/services/process/certificate-renewal';
import { useRequest } from 'ahooks';
import { App, Button, Divider, Form, Modal, Progress } from 'antd';
import dayjs from 'dayjs';
import { DBSchema, openDB } from 'idb';

interface ImportBatchReviewButtonProps {
  onSuccess: () => void;
}

interface FileCache {
  name: string;
  url: string;
  size: number;
  type: string;
  lastModified: number;
  day: string;
}

interface FileCacheDB extends DBSchema {
  files: {
    key: string; // 'name' 字段
    value: FileCache;
    indexes: {
      day: string;
    };
  };
}

const dbName = 'scrm-certificate-renewal-file-caches';
const dbStoreName = 'files';
const countInitValue = { success: 0, fail: 0, total: 0 };
const maxCount = 600;

const ImportBatchReviewButton: React.FC<ImportBatchReviewButtonProps> = ({ onSuccess }) => {
  const { message, modal } = App.useApp();
  const [open, setOpen] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const { getUploadToken, uploadFile } = useUploadFile();
  const [count, setCount] = useState(countInitValue);
  const cancelFn = useRef<(() => void) | null>(null);
  const [showUploadInfo, setShowUploadInfo] = useState(false);
  const [fileInfos, setFileInfos] = useState<Record<string, string>>({});
  const [form] = Form.useForm();

  const { showImportPrompt } = ImportFileFormItem.usePrompt();
  const { runAsync: submit, loading: submitLoading } = useRequest(submitCertificateRenewalReview, {
    manual: true,
  });

  const handleOpen = async () => {
    setOpen(true);
    setCount(countInitValue);
    setShowUploadInfo(false);
    setFileInfos({});
    cancelFn.current = null;
  };

  const getToady = () => dayjs().format('YYYY-MM-DD');

  const initDB = async () => {
    const db = await openDB<FileCacheDB>(dbName, 1, {
      upgrade(_db) {
        // 文件名作唯一值
        const store = _db.createObjectStore(dbStoreName, { keyPath: 'name' });

        // day 作为索引
        store.createIndex('day', 'day', { unique: false });
      },
    });

    // 清除不是今天的缓存
    async function clearCache() {
      const tx = db.transaction(dbStoreName, 'readwrite');
      const store = tx.objectStore(dbStoreName);
      const index = store.index('day');
      const today = getToady();

      let cursor = await index.openCursor();

      while (cursor) {
        if (cursor.key !== today) {
          // 删除当前记录
          await cursor.delete(); // 会删除关联 store 中该 primary key 的记录
        }

        cursor = await cursor.continue();
      }

      await tx.done;
    }

    await clearCache();

    async function getFileCache(fileName: string) {
      const tx = db.transaction(dbStoreName, 'readonly');

      // 用文件名读取 cache
      const fileCache = await tx.objectStore(dbStoreName).get(fileName);

      await tx.done;

      return fileCache;
    }

    async function setFileCache(value: FileCache) {
      const tx = db.transaction(dbStoreName, 'readwrite');

      await tx.objectStore(dbStoreName).put(value);

      await tx.done;
    }

    function closeDB() {
      db.close();
    }

    return {
      getFileCache,
      setFileCache,
      closeDB,
    };
  };

  // 用来将发送多次请求获取 OSS token 优化成一次
  const lazyOnce = <T extends any>(fn: () => Promise<T>) => {
    let promise: Promise<T> | null = null;

    return () => {
      if (!promise) {
        promise = fn();
      }

      return promise;
    };
  };

  const uploadFiles = async (files: File[]) => {
    const { getFileCache, setFileCache, closeDB } = await initDB();

    let nextIndex = 0;
    let isCancelled = false;

    const getToken = lazyOnce(getUploadToken);

    async function worker(): Promise<void> {
      while (!isCancelled) {
        const idx = nextIndex++;

        if (idx >= files.length) {
          return;
        }

        const file = files[idx];

        const suffix = `.${file.name.split('.').pop()}`;
        const fileName = file.name.slice(0, file.name.length - suffix.length);

        const fileCache = await getFileCache(fileName);

        if (
          fileCache &&
          fileCache.size === file.size &&
          fileCache.type === file.type &&
          fileCache.lastModified === file.lastModified
        ) {
          setFileInfos((prev) => ({ ...prev, [fileName]: fileCache.url }));
          setCount((prev) => ({ ...prev, success: prev.success + 1 }));
        } else {
          try {
            await getToken();

            const { url } = await uploadFile(file);

            if (isCancelled) {
              return;
            }

            setFileInfos((prev) => ({ ...prev, [fileName]: url }));
            setCount((prev) => ({ ...prev, success: prev.success + 1 }));

            setFileCache({
              name: fileName,
              size: file.size,
              type: file.type,
              lastModified: file.lastModified,
              url,
              day: getToady(),
            });
          } catch (err: any) {
            setCount((prev) => ({ ...prev, fail: prev.fail + 1 }));
          }
        }
      }
    }

    cancelFn.current = () => {
      isCancelled = true;
    };

    // 设置并发数 10
    await Promise.all(Array.from({ length: 10 }, () => worker()));
    message.success('上传图片结束！');
    closeDB();
  };

  const handleInputFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Object.values(e.target.files || []).filter((file) =>
      /\.(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico)$/i.test(file.name),
    );

    // 解决无法重复上传同一个文件的问题
    e.target.value = '';

    if (files.length) {
      if (files.length > maxCount) {
        modal.warning({
          title: '提示',
          content: `一次最多上传 ${maxCount} 张图片，请调整后重新上传`,
        });

        return;
      }

      // 单张图片最大 5 M
      const maxSize = 5;
      const largeFiles = files.filter((file) => file.size / 1024 / 1024 > maxSize);

      if (largeFiles.length > 0) {
        modal.warning({
          title: `以下图片大小超过 ${maxSize} M，请调整后重新上传`,
          content: largeFiles.map((file, index) => <div key={index}>{file.name}</div>),
        });

        return;
      }

      modal.confirm({
        title: '上传文件',
        content: `检测到 ${files.length} 张图片，是否要继续上传？`,
        onOk: () => {
          // 上传前重置这些值
          cancelFn.current?.();
          setCount({
            success: 0,
            fail: 0,
            total: files.length,
          });
          setFileInfos({});
          setShowUploadInfo(true);

          uploadFiles(files);
        },
      });
    } else {
      message.warning('文件夹里无图片文件！');
    }
  };

  const handleSave = async (values: any) => {
    if (!Object.keys(fileInfos).length) {
      message.warning('请上传图片文件夹');

      return;
    }

    const res = await submit({
      ...values,
      fileInfos: JSON.stringify(fileInfos),
    });

    setOpen(false);
    showImportPrompt(res, onSuccess);
  };

  return (
    <>
      <Button type="text" onClick={handleOpen}>
        批量发起审核
      </Button>
      <Modal
        title="批量发起审核"
        open={open}
        destroyOnHidden
        width={870}
        confirmLoading={submitLoading}
        onCancel={() => {
          setOpen(false);
          cancelFn.current?.();
        }}
        onOk={form.submit}
      >
        <div className="flex gap-5">
          <Form form={form} className="flex-1" clearOnDestroy onFinish={handleSave}>
            <ImportFileFormItem downloadTemplate={downloadCertificateRenewalReviewTemplate} />
          </Form>
          <Divider type="vertical" className="h-auto" />
          <div className="flex-1">
            <input
              className="hidden"
              ref={inputRef}
              type="file"
              // 解决 safari 仍然可以选中文件
              accept={`.${'n'.repeat(100)}`}
              // @ts-ignore 选择文件夹
              webkitdirectory=""
              onChange={handleInputFileChange}
            />
            <Form.Item
              label="文件夹中的图片名需与 excel 中填写的图片名相对应"
              required
              labelCol={{ span: 24 }}
              className="mb-0"
            >
              <div
                className="flex flex-col justify-center items-center h-[180px] border border-dashed border-gray-300 rounded-lg cursor-pointer bg-[#fafafa] hover:border-primary"
                onClick={() => inputRef.current?.click()}
              >
                <InboxOutlined className="text-primary mb-2 text-[50px]" />
                点击上传图片所在的文件夹
              </div>
            </Form.Item>
            {showUploadInfo && (
              <div className="flex flex-col items-center mt-3">
                <Progress
                  percent={Number((((count.success + count.fail) / count.total) * 100).toFixed(2))}
                />
                <div>
                  文件上传进度：
                  {count.success + count.fail} / {count.total}
                </div>
                <div>
                  成功：{count.success}； 失败：{count.fail}
                </div>
              </div>
            )}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ImportBatchReviewButton;
