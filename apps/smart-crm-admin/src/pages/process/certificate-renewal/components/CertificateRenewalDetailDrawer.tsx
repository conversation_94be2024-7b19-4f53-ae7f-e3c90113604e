import React, { useRef } from 'react';
import { workflowAuditStatusOptions } from '@src/common/constants';
import { DescriptionFieldGroupType, ModalRef } from '@src/common/interface';
import { AuditFlow, ButtonGroup } from '@src/components';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { PermissionsMap, usePermission, usePermissionOpen } from '@src/permissions';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { ExceptionTypeEnum } from '@src/services/process/abnormal-certificate/type';
import {
  deleteCertificateRenewal,
  pushWeaverCertificateRenewal,
  queryCertificateRenewalDetail,
} from '@src/services/process/certificate-renewal';
import {
  CertificateRenewalDTO,
  CertificateRenewalStatusEnum,
  CertificateRenewalWeaverNodeStatusEnum,
} from '@src/services/process/certificate-renewal/type';
import {
  WorkflowAuditOperationTypeEnum,
  WorkflowAuditStatusEnum,
  WorkflowBusinessTypeEnum,
} from '@src/services/workflow/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Card, Col, Drawer, DrawerProps, Row } from 'antd';
import EditCertificateRenewalModal from './EditCertificateRenewalModal';
import { certificateRenewalFieldItems } from '../constants';

interface CertificateRenewalDetailDrawerProps extends DrawerProps {
  getId: () => number | null;
  onUpdate?: () => void;
}

const CertificateRenewalDetailDrawer = React.forwardRef<
  ModalRef,
  CertificateRenewalDetailDrawerProps
>(({ getId, onUpdate, ...props }, ref) => {
  const { modal, message } = App.useApp();
  const [open, setOpen] = usePermissionOpen(PermissionsMap.ProcessCertificateRenewalDetail);
  const shopDetailDrawerRef = useRef<ModalRef>(null);
  const editModalRef = useRef<ModalRef>(null);

  const id = getId();

  const checkPermission = usePermission();
  const { data: users } = useAllUsers({ ready: open });
  const { data, loading, mutate, refresh } = useRequest(() => queryCertificateRenewalDetail(id!), {
    ready: open,
  });

  React.useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true);
      mutate(undefined);
    },
    close: () => setOpen(false),
  }));

  const baseInfoItems: DescriptionFieldGroupType<CertificateRenewalDTO>[] = [
    {
      label: '基本信息',
      children: [
        {
          label: '门店编号',
          field: 'shopId',
          children: <a onClick={() => shopDetailDrawerRef.current?.open()}>{data?.shopId}</a>,
        },
        {
          label: '所属大区',
          field: 'belongWarZone',
          valueType: ValueType.SINGLE,
          fieldProps: {
            options: enum2Options(BelongWarZoneEnum),
          },
        },
        {
          label: '异常原因',
          field: 'exceptionTypes',
          valueType: ValueType.MULTIPLE,
          fieldProps: {
            options: enum2Options(ExceptionTypeEnum),
          },
        },
        {
          label: '异常说明',
          field: 'description',
        },
        {
          label: '任务状态',
          field: 'status',
          valueType: ValueType.SINGLE,
          fieldProps: {
            options: enum2Options(CertificateRenewalStatusEnum),
          },
        },
        {
          label: '过期日期',
          field: 'expireDate',
        },
        {
          label: '发起审核时间',
          field: 'flowCreateTime',
        },
        {
          label: '发起 OA 流程时间',
          field: 'weaverFlowCreateTime',
        },
        {
          label: '审核状态',
          field: 'auditStatus',
          valueType: ValueType.SINGLE,
          fieldProps: {
            options: workflowAuditStatusOptions,
          },
        },
        {
          label: '证照更新流程',
          field: 'weaverNodeStatus',
          valueType: ValueType.SINGLE,
          fieldProps: {
            options: enum2Options(CertificateRenewalWeaverNodeStatusEnum),
          },
        },
      ],
    },
    ...certificateRenewalFieldItems,
  ];

  return (
    <Drawer
      title="证照更新任务详情"
      open={open}
      width={1100}
      destroyOnHidden
      onClose={() => setOpen(false)}
      {...props}
    >
      <Card loading={loading}>
        <div className="flex justify-between">
          <div className="flex flex-col gap-2">
            <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
            <p>创建时间：{data?.createTime}</p>
            <p>更新人：{users?.find((i) => i.id === data?.updateUserId)?.name}</p>
            <p>更新时间：{data?.updateTime}</p>
          </div>
          <ButtonGroup
            items={[
              {
                label: '编辑',
                show:
                  data?.status === CertificateRenewalStatusEnum.未完成 &&
                  checkPermission(PermissionsMap.ProcessCertificateRenewalEdit),
                onClick: () => editModalRef.current?.open(),
              },
              {
                label: '推送泛微',
                show:
                  data?.weaverNodeStatus === CertificateRenewalWeaverNodeStatusEnum.未发起 &&
                  data?.auditStatus === WorkflowAuditStatusEnum.APPROVE &&
                  checkPermission(PermissionsMap.ProcessCertificateRenewalPushWeaver),
                onClick: () =>
                  modal.confirm({
                    title: '推送泛微',
                    content: '确定要执行推送泛微操作吗？',
                    onOk: async () => {
                      await pushWeaverCertificateRenewal(id!);
                      message.success('推送成功');
                      refresh();
                      onUpdate?.();
                    },
                  }),
              },
              {
                label: '删除',
                danger: true,
                show:
                  data?.status === CertificateRenewalStatusEnum.未完成 &&
                  checkPermission(PermissionsMap.ProcessCertificateRenewalDelete),
                onClick: () =>
                  modal.confirm({
                    title: '删除',
                    content: '确定要删除该条证照更新任务吗？',
                    onOk: async () => {
                      await deleteCertificateRenewal(id!);
                      message.success('删除成功');
                      setOpen(false);
                      onUpdate?.();
                    },
                  }),
              },
            ]}
          />
        </div>
      </Card>
      <Row gutter={[20, 20]} className="mt-5">
        <Col span={24} xl={16}>
          <Card loading={loading}>{renderDescriptions(baseInfoItems, data)}</Card>
        </Col>
        <Col span={24} xl={8}>
          <AuditFlow.WorkflowCard
            loading={loading}
            auditStatus={data?.auditStatus}
            processInstanceId={data?.processInstanceId}
            businessId={data?.id}
            businessName={data?.shopId}
            businessType={WorkflowBusinessTypeEnum.SCRM_SHOP_LICENSE_TRACE_AUDIT}
            auditButtons={{
              [WorkflowAuditOperationTypeEnum.RETURN]: false,
            }}
            onAfterSubmit={(type, values) => {
              if (type === WorkflowAuditOperationTypeEnum.APPROVE && values?.finish) {
                return pushWeaverCertificateRenewal(id!);
              }
            }}
            onSuccess={() => {
              refresh();
              onUpdate?.();
            }}
          />
        </Col>
      </Row>

      <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => data?.shopId} />
      <EditCertificateRenewalModal
        ref={editModalRef}
        getId={() => id}
        onSuccess={() => {
          refresh();
          onUpdate?.();
        }}
      />
    </Drawer>
  );
});

export default CertificateRenewalDetailDrawer;
