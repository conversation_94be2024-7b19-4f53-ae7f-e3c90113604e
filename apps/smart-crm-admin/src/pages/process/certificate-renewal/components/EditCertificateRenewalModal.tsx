import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { Editable } from '@src/components';
import { ExceptionTypeEnum } from '@src/services/process/abnormal-certificate/type';
import {
  queryCertificateRenewalDetail,
  submitCertificateRenewal,
} from '@src/services/process/certificate-renewal';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Card, Form, Input, Modal, ModalProps, Select } from 'antd';
import { certificateRenewalFieldItems } from '../constants';

interface EditCertificateRenewalModalProps extends ModalProps {
  getId: () => number | null;
  onSuccess: () => void;
}

const EditCertificateRenewalModal = React.forwardRef<ModalRef, EditCertificateRenewalModalProps>(
  ({ getId, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();

    const id = getId();

    const { mutate, loading } = useRequest(() => queryCertificateRenewalDetail(id!), {
      ready: open,
      onSuccess: (res) => form.setFieldsValue(res),
    });
    const { runAsync: submit, loading: submitLoading } = useRequest(submitCertificateRenewal, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        mutate(undefined);
      },
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      await submit(values);
      message.success('提交审核成功');
      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        open={open}
        title="编辑证照更新任务"
        loading={loading}
        destroyOnHidden
        okText="提交审核"
        width={600}
        styles={{
          body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
        }}
        confirmLoading={submitLoading}
        modalRender={(node) => (
          <Form
            form={form}
            clearOnDestroy
            scrollToFirstError
            labelCol={{ span: 10 }}
            onFinish={handleSave}
          >
            {node}
          </Form>
        )}
        onCancel={() => setOpen(false)}
        onOk={form.submit}
        {...props}
      >
        <Form.Item label="门店编号" name="shopId" rules={[{ required: true, message: '不能为空' }]}>
          <Input disabled />
        </Form.Item>
        <Form.Item
          label="异常原因"
          name="exceptionTypes"
          rules={[{ required: true, message: '请选择请选择异常原因' }]}
        >
          <Select
            mode="multiple"
            placeholder="请选择异常原因"
            options={enum2Options(ExceptionTypeEnum)}
          />
        </Form.Item>
        <Form.Item
          label="异常说明"
          name="description"
          rules={[{ required: true, message: '请输入异常说明' }]}
        >
          <Input.TextArea placeholder="请输入异常说明" maxLength={255} />
        </Form.Item>
        {certificateRenewalFieldItems.map((item) => (
          <Card
            key={item.label}
            title={item.label}
            classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
          >
            {item.children
              .filter((i) => !i.hideInForm)
              .map((i) =>
                'children' in i ? (
                  i.children
                ) : (
                  <Editable
                    key={String(i.field)}
                    editable
                    valueType={i.valueType}
                    fieldProps={i.fieldProps}
                    formItemProps={{
                      label: i.label,
                      name: i.field,
                      ...i.formItemProps,
                      // 有 rules 就用自己的 rules，否则都必填
                      rules: i.formItemProps?.rules
                        ? i.formItemProps.rules
                        : [{ required: true, message: '不能为空' }],
                    }}
                  />
                ),
              )}
          </Card>
        ))}
      </Modal>
    );
  },
);

export default EditCertificateRenewalModal;
