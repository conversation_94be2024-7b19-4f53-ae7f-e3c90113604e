import { useEffect, useRef, useState } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { workflowAuditStatusOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import {
  ExportButton,
  ImportFileButton,
  ProTable,
  TableActions,
  UserSelect,
} from '@src/components';
import EditableMultiple from '@src/components/Editable/EditableMultiple';
import useAllUsers from '@src/hooks/useAllUsers';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { ExceptionTypeEnum } from '@src/services/process/abnormal-certificate/type';
import {
  deleteCertificateRenewal,
  downloadCertificateRenewalTaskTemplate,
  exportCertificateRenewal,
  importCertificateRenewalTask,
  pushWeaverCertificateRenewal,
  queryCertificateRenewalPageList,
} from '@src/services/process/certificate-renewal';
import {
  CertificateRenewalDTO,
  CertificateRenewalStatusEnum,
  CertificateRenewalWeaverNodeStatusEnum,
  ExportCertificateRenewalReq,
} from '@src/services/process/certificate-renewal/type';
import { WorkflowAuditStatusEnum } from '@src/services/workflow/type';
import {
  calcTableWidth,
  compatibleTableActionWidth,
  enum2Options,
  enum2ValueEnum,
  optionsToValueEnum,
} from '@src/utils';
import { useRequest } from 'ahooks';
import { App } from 'antd';
import dayjs from 'dayjs';
import { useSearchParams } from 'react-router-dom';
import CertificateRenewalDetailDrawer from './components/CertificateRenewalDetailDrawer';
import EditCertificateRenewalModal from './components/EditCertificateRenewalModal';
import ImportBatchReviewButton from './components/ImportBatchReviewButton';

const CertificateRenewal = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { modal, message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const shopIdRef = useRef<string | null>(null);
  const currentIdRef = useRef<number | null>(null);
  const shopDetailDrawerRef = useRef<ModalRef>(null);
  const detailDrawerRef = useRef<ModalRef>(null);
  const editModalRef = useRef<ModalRef>(null);
  const filterParamsRef = useRef<ExportCertificateRenewalReq>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);

  const checkPermission = usePermission();
  const { data: users } = useAllUsers();
  const { data, runAsync: queryList } = useRequest(queryCertificateRenewalPageList, {
    manual: true,
  });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      currentIdRef.current = Number(id);
      detailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: ProColumns<CertificateRenewalDTO>[] = [
    {
      title: '门店编号',
      dataIndex: 'shopId',
      fixed: 'left',
      renderText: (value) => (
        <a
          onClick={() => {
            shopIdRef.current = value;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '所属大区',
      dataIndex: 'belongWarZone',
      valueEnum: enum2ValueEnum(BelongWarZoneEnum),
      search: false,
    },
    {
      title: '异常原因',
      dataIndex: 'exceptionTypes',
      formItemProps: {
        name: 'exceptionType',
      },
      valueEnum: enum2ValueEnum(ExceptionTypeEnum),
      renderText: (value) => (
        <EditableMultiple
          value={value}
          fieldProps={{
            options: enum2Options(ExceptionTypeEnum),
          }}
        />
      ),
    },
    {
      title: '异常说明',
      dataIndex: 'description',
      search: false,
      ellipsis: true,
    },
    {
      title: '任务状态',
      dataIndex: 'status',
      valueEnum: enum2ValueEnum(CertificateRenewalStatusEnum),
    },
    {
      title: '过期日期',
      dataIndex: 'expireDate',
      search: false,
    },
    {
      title: '发起审核时间',
      dataIndex: 'flowCreateTime',
      search: false,
    },
    {
      title: '发起 OA 流程时间',
      dataIndex: 'weaverFlowCreateTime',
      search: false,
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      valueEnum: optionsToValueEnum(workflowAuditStatusOptions),
    },
    {
      title: '证照更新流程',
      dataIndex: 'weaverNodeStatus',
      valueEnum: enum2ValueEnum(CertificateRenewalWeaverNodeStatusEnum),
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      renderFormItem: () => <UserSelect />,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: (_, type) => (type === 'table' ? 'dateTime' : 'dateRange'),
      search: {
        transform: (value) => ({
          createBeginTime: dayjs(value[0]).startOf('d').format('YYYY-MM-DD HH:mm:ss'),
          createEndTime: dayjs(value[1]).endOf('d').format('YYYY-MM-DD HH:mm:ss'),
        }),
      },
    },
    {
      title: '操作',
      key: 'action',
      width: compatibleTableActionWidth(150),
      align: 'center',
      fixed: 'right',
      search: false,
      render: (_, { id, status, weaverNodeStatus, auditStatus }) => (
        <TableActions
          shortcuts={[
            {
              label: '详情',
              show: checkPermission(PermissionsMap.ProcessCertificateRenewalDetail),
              onClick: () => {
                currentIdRef.current = id;
                detailDrawerRef.current?.open();
              },
            },
            {
              label: '编辑',
              show:
                status === CertificateRenewalStatusEnum.未完成 &&
                checkPermission(PermissionsMap.ProcessCertificateRenewalEdit),
              onClick: () => {
                currentIdRef.current = id;
                editModalRef.current?.open();
              },
            },
          ]}
          moreItems={[
            {
              label: '推送泛微',
              show:
                weaverNodeStatus === CertificateRenewalWeaverNodeStatusEnum.未发起 &&
                auditStatus === WorkflowAuditStatusEnum.APPROVE &&
                checkPermission(PermissionsMap.ProcessCertificateRenewalPushWeaver),
              onClick: () =>
                modal.confirm({
                  title: '推送泛微',
                  content: '确定要执行推送泛微操作吗？',
                  onOk: async () => {
                    await pushWeaverCertificateRenewal(id!);
                    message.success('推送成功');
                    actionRef.current?.reload();
                  },
                }),
            },
            {
              label: '删除',
              danger: true,
              show:
                status === CertificateRenewalStatusEnum.未完成 &&
                checkPermission(PermissionsMap.ProcessCertificateRenewalDelete),
              onClick: () =>
                modal.confirm({
                  title: '删除',
                  content: '确定要删除该条证照更新任务吗？',
                  onOk: async () => {
                    await deleteCertificateRenewal(id);
                    message.success('删除成功');
                    actionRef.current?.reload();
                    setSelectedRowKeys((prev) => prev.filter((i) => i !== id));
                  },
                }),
            },
          ]}
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        rowKey="id"
        sticky
        columns={columns}
        cardProps={false}
        bordered
        scroll={{ x: calcTableWidth(columns) }}
        rowSelection={{
          fixed: true,
          showSort: true,
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        toolbar={{
          subTitle: (
            <span className="text-black">
              共 {data?.total || 0} 个任务
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 个
                  <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            <Permission value={PermissionsMap.ProcessCertificateRenewalImportTask}>
              <ImportFileButton
                downloadTemplate={downloadCertificateRenewalTaskTemplate}
                request={importCertificateRenewalTask}
                onSuccess={() => actionRef.current?.reload()}
              >
                批量生成任务
              </ImportFileButton>
            </Permission>,
            <Permission value={PermissionsMap.ProcessCertificateRenewalImportReview}>
              <ImportBatchReviewButton onSuccess={() => actionRef.current?.reload()} />
            </Permission>,
            <Permission value={PermissionsMap.ProcessCertificateRenewalExport}>
              <ExportButton
                total={selectedRowKeys.length || data?.total}
                request={() =>
                  exportCertificateRenewal(
                    selectedRowKeys.length ? { ids: selectedRowKeys } : filterParamsRef.current,
                  )
                }
              />
            </Permission>,
          ],
        }}
        request={async ({ current, pageSize, ...params }) => {
          filterParamsRef.current = params as ExportCertificateRenewalReq;

          const { result, total } = await queryList({
            pageNum: current,
            pageSize,
            ...params,
          });

          return {
            data: result,
            total,
          };
        }}
      />

      <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => shopIdRef.current} />
      <CertificateRenewalDetailDrawer
        ref={detailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
      />
      <EditCertificateRenewalModal
        ref={editModalRef}
        getId={() => currentIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
    </PageContainer>
  );
};

export default CertificateRenewal;
