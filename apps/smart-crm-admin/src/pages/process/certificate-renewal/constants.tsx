import { DescriptionFieldType } from '@src/common/interface';
import { Editable } from '@src/components';
import { ValueType } from '@src/components/ETable';
import { CertificateRenewalDTO } from '@src/services/process/certificate-renewal/type';
import { ValidityTypeEnum } from '@src/services/process/store-setup-task/business-license/type';
import { enum2Options } from '@src/utils';
import { Form } from 'antd';

export const certificateRenewalFieldItems: {
  label: string;
  children: (DescriptionFieldType<CertificateRenewalDTO> & { hideInForm?: boolean })[];
}[] = [
  {
    label: '营业执照信息',
    children: [
      {
        label: '营业执照统一社会信用代码',
        field: ['businessLicense', 'licenseCode'],
        formItemProps: {
          rules: [
            { required: true, message: '不能为空' },
            { len: 18, message: '需要 18 位长度' },
          ],
        },
      },
      {
        label: '营业执照图片',
        field: ['businessLicense', 'licenseUrls'],
        valueType: ValueType.ATTACHMENT,
        fieldProps: {
          accept: 'image/*',
          format: 'urlArray',
        },
      },
      {
        label: '营业执照名称',
        field: ['businessLicense', 'licenseName'],
      },
      {
        label: '营业执照类型',
        field: ['businessLicense', 'licenseType'],
      },
      {
        label: '营业执照注册日期',
        field: ['businessLicense', 'licenseRegisterDate'],
        valueType: ValueType.DATE,
      },
      {
        label: '经营场所',
        field: ['businessLicense', 'licenseAddress'],
      },
      {
        label: '营业执照有效类型',
        field: ['businessLicense', 'licenseEffectiveType'],
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(ValidityTypeEnum),
        },
      },
      {
        label: '营业执照有效截止日期',
        field: ['businessLicense', 'licenseExpireDate'],
        valueType: ValueType.DATE,
        // 编辑时隐藏
        hideInForm: true,
      },
      {
        label: '营业执照有效截止日期',
        // 详情页隐藏
        hidden: true,
        children: (
          <Form.Item noStyle shouldUpdate key="licenseEffectiveType">
            {({ getFieldValue }) =>
              getFieldValue(['businessLicense', 'licenseEffectiveType']) ===
                ValidityTypeEnum.具体期限 && (
                <Editable
                  editable
                  valueType={ValueType.DATE}
                  formItemProps={{
                    label: '营业执照有效截止日期',
                    name: ['businessLicense', 'licenseExpireDate'],
                    rules: [{ required: true, message: '不能为空' }],
                  }}
                />
              )
            }
          </Form.Item>
        ),
      },
      {
        label: '经营者',
        field: ['businessLicense', 'licenseOperator'],
      },
      {
        label: '经营范围',
        field: ['businessLicense', 'licenseBusinessScope'],
      },
      {
        label: '营业执照发证日期',
        field: ['businessLicense', 'licenseIssueDate'],
        valueType: ValueType.DATE,
      },
    ],
  },
  {
    label: '食安许可证信息',
    children: [
      {
        label: '食品经营许可证编号',
        field: ['qualification', 'qualificationCode'],
        formItemProps: {
          rules: [
            { required: true, message: '不能为空' },
            { max: 30, message: '不能超过 30 位长度' },
          ],
        },
      },
      {
        label: '食品经营许可证照',
        field: ['qualification', 'qualificationUrls'],
        valueType: ValueType.ATTACHMENT,
        fieldProps: {
          accept: 'image/*',
          format: 'urlArray',
        },
      },
      {
        label: '食品经营许可证发证日期',
        field: ['qualification', 'qualificationIssueDate'],
        valueType: ValueType.DATE,
      },
      {
        label: '食品经营许可证有效截止日期',
        field: ['qualification', 'qualificationExpireDate'],
        valueType: ValueType.DATE,
      },
      {
        label: '经营者',
        field: ['qualification', 'qualificationOperator'],
      },
      {
        label: '主体业态',
        field: ['qualification', 'qualificationMainBusiness'],
      },
      {
        label: '法定代表人',
        field: ['qualification', 'qualificationLegerPerson'],
      },
      {
        label: '经营场所',
        field: ['qualification', 'qualificationAddress'],
      },
      {
        label: '经营项目',
        field: ['qualification', 'qualificationProject'],
      },
    ],
  },
  {
    label: '法人信息',
    children: [
      {
        label: '法人姓名',
        field: ['legalPerson', 'name'],
      },
      {
        label: '法人身份证号',
        field: ['legalPerson', 'idCardNo'],
        valueType: ValueType.IDENTITY_CARD,
        fieldProps: {
          // 不脱敏
          sensitive: false,
        },
      },
      {
        label: '法人手机号',
        field: ['legalPerson', 'phone'],
        valueType: ValueType.PHONE,
        fieldProps: {
          // 不脱敏
          sensitive: false,
        },
      },
      {
        label: '法人证件有效截止日期',
        field: ['legalPerson', 'idCardExpireDate'],
        valueType: ValueType.DATE,
      },
      {
        label: '法人身份证照正面',
        field: ['legalPerson', 'idCardFrontUrl'],
        valueType: ValueType.ATTACHMENT,
        fieldProps: {
          accept: 'image/*',
          format: 'url',
        },
      },
      {
        label: '法人身份证照反面',
        field: ['legalPerson', 'idCardBackUrl'],
        valueType: ValueType.ATTACHMENT,
        fieldProps: {
          accept: 'image/*',
          format: 'url',
        },
      },
      {
        label: '法人手持身份证照正面',
        field: ['legalPerson', 'holdIdCardFrontUrl'],
        valueType: ValueType.ATTACHMENT,
        fieldProps: {
          accept: 'image/*',
          format: 'url',
        },
      },
      {
        label: '法人手持身份证照反面',
        field: ['legalPerson', 'holdIdCardBackUrl'],
        valueType: ValueType.ATTACHMENT,
        fieldProps: {
          accept: 'image/*',
          format: 'url',
        },
      },
    ],
  },
];
