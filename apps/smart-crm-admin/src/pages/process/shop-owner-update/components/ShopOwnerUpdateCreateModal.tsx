import React, { useMemo, useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { Encrypt, FileUpload, ShopSelect } from '@src/components';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import { OwnerTypeEnum } from '@src/services/business-opportunity/type';
import { CustomerDTO } from '@src/services/customers/type';
import {
  getWeaverDepartmentList,
  getWeaverProvinceList,
  getWeaverWarZoneList,
} from '@src/services/process/abnormal-certificate';
import { saveOwnerUpdate } from '@src/services/process/shop-owner-update';
import { OwnerUpdateTypeEnum } from '@src/services/process/shop-owner-update/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps, Select } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { debounce } from 'lodash-es';

interface ShopOwnerUpdateCreateModalProps extends ModalProps {
  onSuccess?: () => void;
}

const ShopOwnerUpdateCreateModal = React.forwardRef<ModalRef, ShopOwnerUpdateCreateModalProps>(
  ({ onSuccess, ...props }, ref) => {
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    const { message } = App.useApp();
    const [customerInfo, setCustomerInfo] = useState<CustomerDTO>();
    const [newCustomerInfo, setNewCustomerInfo] = useState<CustomerDTO>();

    const { runAsync: create, loading: createLoading } = useRequest(saveOwnerUpdate, {
      manual: true,
    });

    const {
      data: departmentList,
      loading: getDepartmentListLoading,
      runAsync: getDepartmentRun,
    } = useRequest(
      async (keyword) => {
        const res = await getWeaverDepartmentList(keyword);

        return res.map((item) => ({
          id: item?.id,
          departmentname: `${item?.departmentname || ''}---${item?.tlevel || ''}`,
        }));
      },
      {
        ready: open,
      },
    );

    const onSearch = useMemo(() => {
      return debounce(getDepartmentRun, 1000);
    }, [getDepartmentRun]);

    const { data: provinceList, loading: getProvinceListLoading } = useRequest(
      getWeaverProvinceList,
      {
        ready: open,
      },
    );

    const { data: warZoneList, loading: getWarZoneListLoading } = useRequest(getWeaverWarZoneList, {
      ready: open,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        setCustomerInfo(undefined);
        setNewCustomerInfo(undefined);
      },
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      const { weaverDepartmentId, weaverWarZoneId, weaverProvinceId, ...rest } = values;

      const weaverDistrict = warZoneList?.find((item) => item.belongWarZone === weaverWarZoneId);

      const weaverRegion = provinceList?.find((item) => item.belongProvince === weaverProvinceId);

      const weaverDepart = departmentList?.find((item) => item.id === weaverDepartmentId);

      const params = {
        ...rest,
        weaverDepartmentId,
        weaverDepartmentName: weaverDepart?.departmentname,
        weaverWarZoneId,
        weaverWarZoneName: weaverDistrict?.belongWarZoneName,
        weaverZoneManagerId: weaverDistrict?.zoneManagerId,
        weaverProvinceHeadId: weaverDistrict?.provinceHeadId,
        weaverProvinceId,
        weaverProvinceName: weaverRegion?.provinceName,
        weaverProvinceManagerId: weaverRegion?.provinceManagerId,
      };

      await create(params);
      message.success('保存成功');
      setOpen(false);
      onSuccess?.();
    };

    const handleShopIdChange = (_: string, shopItem?: DefaultOptionType) => {
      form.setFieldsValue({
        shopName: shopItem?.shopName,
        shopAddress: shopItem?.address,
      });
    };

    return (
      <Modal
        title="新建门店业主变更申请"
        open={open}
        destroyOnHidden
        confirmLoading={createLoading}
        okText="提交审核"
        styles={{
          body: { maxHeight: '60vh', margin: '0 -24px', padding: '0 24px', overflow: 'auto' },
        }}
        width={800}
        modalRender={(node) => (
          <Form
            form={form}
            clearOnDestroy
            scrollToFirstError
            labelCol={{ span: 8 }}
            onFinish={handleSave}
          >
            {node}
          </Form>
        )}
        onCancel={() => setOpen(false)}
        onOk={form.submit}
        {...props}
      >
        <Form.Item
          label="转让类型"
          name="transferType"
          rules={[{ required: true, message: '请选择转让类型' }]}
        >
          <Select
            allowClear
            placeholder="请选择转让类型"
            options={enum2Options(OwnerUpdateTypeEnum)}
          />
        </Form.Item>
        <Form.Item
          label="接店人类型"
          name="ownerType"
          rules={[{ required: true, message: '请选择接店人类型' }]}
        >
          <Select allowClear placeholder="请选择接店人类型" options={enum2Options(OwnerTypeEnum)} />
        </Form.Item>
        <Form.Item
          label="门店所属省份"
          name="weaverProvinceId"
          rules={[{ required: true, message: '请选择门店所属省份' }]}
        >
          <Select
            loading={getProvinceListLoading}
            allowClear
            placeholder="请选择门店所属省份"
            fieldNames={{ label: 'provinceName', value: 'belongProvince' }}
            options={provinceList}
          />
        </Form.Item>
        <Form.Item
          label="所属大区"
          name="weaverWarZoneId"
          rules={[{ required: true, message: '请选择所属大区' }]}
        >
          <Select
            placeholder="请选择所属大区"
            options={warZoneList}
            fieldNames={{ label: 'belongWarZoneName', value: 'belongWarZone' }}
            loading={getWarZoneListLoading}
          />
        </Form.Item>
        <Form.Item
          label="门店所属作战小队"
          name="weaverDepartmentId"
          rules={[{ required: true, message: '请选择门店所属作战小队' }]}
        >
          <Select
            showSearch
            placeholder="请选择门店所属作战小队"
            autoClearSearchValue={false}
            filterOption={false}
            options={departmentList}
            fieldNames={{ label: 'departmentname', value: 'id' }}
            loading={getDepartmentListLoading}
            onSearch={onSearch}
          />
        </Form.Item>
        <Form.Item
          label="门店编号"
          name="shopId"
          rules={[{ required: true, message: '请选择门店编号' }]}
        >
          <ShopSelect onChange={handleShopIdChange} />
        </Form.Item>
        <Form.Item
          label="门店冠名"
          name="shopName"
          rules={[{ required: true, message: '请输入门店冠名' }]}
        >
          <Input placeholder="请输入门店冠名" disabled />
        </Form.Item>
        <Form.Item
          label="原业主名字"
          name="originalCustomerId"
          rules={[{ required: true, message: '请选择原业主名字' }]}
        >
          <RelCustomer
            editable
            allowClear
            onChange={(_, info) => {
              setCustomerInfo(info);
              form.setFieldsValue({
                originalCustomerPhone: info?.phones?.[0],
                originalIdentityCard: info?.identityCard,
              });
            }}
          />
        </Form.Item>
        <Form.Item
          name="originalCustomerPhone"
          label="原业主电话"
          validateFirst
          validateTrigger={['onChange', 'onBlur']}
          rules={[{ required: true, message: '请输入联系方式' }]}
        >
          <Encrypt.PhoneSelect
            placeholder="请选择手机号"
            options={customerInfo?.phones?.map((phone, index) => ({
              label: customerInfo?.phonesSensitive?.[index],
              value: phone,
            }))}
          />
        </Form.Item>
        <Encrypt.IdCardFormItem
          formItemProps={{
            label: '原业主身份证号码',
            name: 'originalIdentityCard',
            rules: [{ required: true, message: '请输入身份证号码' }],
          }}
          fieldProps={{
            disabled: true,
            placeholder: '选择业主后自动填充',
            encryptSensitiveMap: {
              [customerInfo?.identityCard || '']: customerInfo?.identityCardSensitive,
            },
          }}
        />
        <Form.Item
          label="新业主姓名"
          name="customerId"
          rules={[
            { required: true, message: '请选择新业主姓名' },
            {
              validator: (_, value) => {
                if (value && value === form.getFieldValue('originalCustomerId')) {
                  return Promise.reject(new Error('变更后的业主不能与原业主相同'));
                }

                return Promise.resolve();
              },
            },
          ]}
        >
          <RelCustomer
            editable
            allowClear
            onChange={(_, info) => {
              setNewCustomerInfo(info);
              form.setFieldsValue({
                customerPhone: info?.phones?.[0],
                merchantNo: info?.phones?.[0],
                identityCard: info?.identityCard,
              });
            }}
          />
        </Form.Item>
        <Form.Item
          name="customerPhone"
          label="新业主电话"
          validateFirst
          validateTrigger={['onChange', 'onBlur']}
          rules={[{ required: true, message: '请选择' }]}
        >
          <Encrypt.PhoneSelect
            placeholder="请选择手机号"
            options={newCustomerInfo?.phones?.map((phone, index) => ({
              label: newCustomerInfo.phonesSensitive?.[index],
              value: phone,
            }))}
          />
        </Form.Item>
        <Encrypt.IdCardFormItem
          formItemProps={{
            label: '新业主身份证号码',
            name: 'identityCard',
            rules: [{ required: true, message: '请选择身份证号码' }],
          }}
          fieldProps={{
            disabled: true,
            placeholder: '选择业主后自动填充',
            encryptSensitiveMap: {
              [newCustomerInfo?.identityCard || '']: newCustomerInfo?.identityCardSensitive,
            },
          }}
        />

        <Form.Item
          name="merchantNo"
          label="新业主收银系统登录账号(手机号)"
          validateFirst
          validateTrigger={['onChange', 'onBlur']}
          rules={[{ required: true, message: '请选择新业主收银系统登录账号(手机号)' }]}
        >
          <Encrypt.PhoneSelect
            placeholder="请选择新业主收银系统登录账号(手机号)"
            options={newCustomerInfo?.phones?.map((phone, index) => ({
              label: newCustomerInfo.phonesSensitive?.[index],
              value: phone,
            }))}
          />
        </Form.Item>

        <Form.Item
          label="双方之间关系"
          name="ownerRelation"
          rules={[{ required: true, message: '请选择双方之间关系' }]}
        >
          <Select
            allowClear
            placeholder="请选择双方之间关系"
            options={[
              {
                label: '亲属',
                value: '亲属',
              },
              {
                label: '合伙',
                value: '合伙',
              },
              {
                label: '一般转让',
                value: '一般转让',
              },
            ]}
          />
        </Form.Item>
        <Form.Item
          label="变更法人的原因"
          name="reason"
          rules={[
            { required: true, message: '请输入变更法人的原因' },
            { max: 500, message: '最多输入 500 个字符' },
          ]}
        >
          <Input.TextArea placeholder="请输入变更法人的原因" />
        </Form.Item>
        <FileUpload
          formItemProps={{
            label: '双方签署正式合同的附件',
            name: 'contractAttachments',
            dependencies: ['transferType'],
            rules: [
              ({ getFieldValue }) => ({
                required: getFieldValue('transferType') === OwnerUpdateTypeEnum.加盟商内部转让,
                message: '请上传双方签署正式合同的附件',
              }),
            ],
          }}
          uploadProps={{
            maxSize: 50,
          }}
        />
        <FileUpload
          formItemProps={{
            label: '营业执照上传',
            name: 'licenseAttachments',
          }}
          uploadProps={{
            maxSize: 50,
          }}
        />
      </Modal>
    );
  },
);

export default ShopOwnerUpdateCreateModal;
