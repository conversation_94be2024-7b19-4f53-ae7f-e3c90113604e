import React, { useRef, useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { OwnerTypeEnum } from '@src/services/business-opportunity/type';
import { getOwnerUpdateDetail } from '@src/services/process/shop-owner-update';
import { OwnerUpdateTypeEnum } from '@src/services/process/shop-owner-update/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { Card, Drawer, DrawerProps } from 'antd';

interface ShopRelocationApplicationDetailDrawerProps extends DrawerProps {
  getId?: () => number | null;
}

const ShopRelocationApplicationDetailDrawer = React.forwardRef<
  ModalRef,
  ShopRelocationApplicationDetailDrawerProps
>(({ getId, ...props }, ref) => {
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const shopDetailDrawerRef = useRef<ModalRef>(null);
  const shopIdRef = useRef<string | null>(null);

  const [open, setOpen] = useState(false);
  const [customerId, setCustomerId] = useState<number>(-1);
  const id = getId?.();

  const { data: users } = useAllUsers({ ready: open });

  const { data, loading } = useRequest(() => getOwnerUpdateDetail(id!), {
    ready: open,
  });

  React.useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true);
    },
    close: () => setOpen(false),
  }));

  const items = [
    {
      field: 'transferType',
      label: '转让类型',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(OwnerUpdateTypeEnum),
      },
    },
    {
      field: 'ownerType',
      label: '接店人类型',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(OwnerTypeEnum),
      },
    },
    {
      field: 'weaverProvinceName',
      label: '门店所属省份',
      valueType: ValueType.TEXT,
    },
    {
      field: 'weaverWarZoneName',
      label: '所属大区',
      valueType: ValueType.TEXT,
    },
    {
      field: 'weaverDepartmentName',
      label: '门店所属作战小队',
      valueType: ValueType.TEXT,
    },
    {
      field: 'shopId',
      label: '门店编号',
      children: (
        <a
          onClick={() => {
            shopIdRef.current = data?.shopId!;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {data?.shopId}
        </a>
      ),
    },
    {
      field: 'shopName',
      label: '门店冠名',
      valueType: ValueType.TEXT,
    },
    {
      field: 'originalCustomerId',
      label: '原业主名字',
      children: (
        <a
          onClick={() => {
            setCustomerId(data?.originalCustomerId!);
            customerDetailDrawerRef.current?.open();
          }}
        >
          {data?.originalCustomerName}
        </a>
      ),
    },
    {
      field: 'originalCustomerPhone',
      label: '原业主电话',
      valueType: ValueType.PHONE,
      fieldProps: {
        sensitiveValue: data?.originalCustomerPhoneSensitive,
      },
    },
    {
      field: 'originalIdentityCard',
      label: '原业主身份证号码',
      valueType: ValueType.IDENTITY_CARD,
      fieldProps: {
        sensitiveValue: data?.originalIdentityCardSensitive,
      },
    },
    {
      field: 'customerId',
      label: '新业主姓名',
      children: (
        <a
          onClick={() => {
            setCustomerId(data?.customerId!);
            customerDetailDrawerRef.current?.open();
          }}
        >
          {data?.customerName}
        </a>
      ),
    },
    {
      field: 'customerPhone',
      label: '新业主电话',
      valueType: ValueType.PHONE,
      fieldProps: {
        sensitiveValue: data?.customerPhoneSensitive,
      },
    },
    {
      field: 'identityCard',
      label: '新业主身份证号码',
      valueType: ValueType.IDENTITY_CARD,
      fieldProps: {
        sensitiveValue: data?.identityCardSensitive,
      },
    },
    {
      field: 'merchantNo',
      label: '新业主收银系统登录账号(手机号)',
      valueType: ValueType.PHONE,
      fieldProps: {
        sensitiveValue: data?.merchantNoSensitive,
      },
    },
    {
      field: 'ownerRelation',
      label: '双方之间关系',
      valueType: ValueType.TEXT,
    },
    {
      field: 'reason',
      label: '变更法人的原因',
      valueType: ValueType.TEXT,
    },
    {
      field: 'contractAttachments',
      label: '双方签署正式合同的附件',
      valueType: ValueType.ATTACHMENT,
    },
    {
      field: 'licenseAttachments',
      label: '营业执照上传',
      valueType: ValueType.ATTACHMENT,
    },
  ];

  return (
    <Drawer
      width={800}
      push={false}
      title="门店业主变更申请详情"
      open={open}
      destroyOnHidden
      loading={loading}
      onClose={() => setOpen(false)}
      {...props}
    >
      <Card loading={loading}>
        <div className="flex flex-col gap-2">
          <p>
            门店编号：
            <a
              onClick={() => {
                shopIdRef.current = data?.shopId!;
                shopDetailDrawerRef.current?.open();
              }}
            >
              {data?.shopId}
            </a>
          </p>
          <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
          <p>创建时间：{data?.createTime}</p>
        </div>
      </Card>
      <Card className="mt-5" loading={loading}>
        {renderDescriptions(items, data)}
      </Card>
      <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerId!} />
      <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => shopIdRef.current} />
    </Drawer>
  );
});

export default ShopRelocationApplicationDetailDrawer;
