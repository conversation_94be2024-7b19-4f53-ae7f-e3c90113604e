import { useRef, useState } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ModalRef } from '@src/common/interface';
import { ExportButton, ProTable } from '@src/components';
import useAllUsers from '@src/hooks/useAllUsers';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { Permission, PermissionsMap } from '@src/permissions';
import { OwnerTypeEnum } from '@src/services/business-opportunity/type';
import {
  AbnormalCertificateDTO,
  ExportAbnormalCertificateReq,
} from '@src/services/process/abnormal-certificate/type';
import { exportOwnerUpdate, getOwnerUpdatePage } from '@src/services/process/shop-owner-update';
import { OwnerUpdateTypeEnum } from '@src/services/process/shop-owner-update/type';
import { enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { Button } from 'antd';
import ShopOwnerUpdateCreateModal from './components/ShopOwnerUpdateCreateModal';
import ShopOwnerUpdateDetailDrawer from './components/ShopOwnerUpdateDetailDrawer';

const ShopOwnerUpdate = () => {
  const actionRef = useRef<ActionType>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const currentIdRef = useRef<number | null>(null);
  const shopIdRef = useRef<string | null>(null);
  const shopDetailDrawer = useRef<ModalRef>(null);
  const filterParamsRef = useRef<ExportAbnormalCertificateReq>({});
  const shopRelocationApplicationModalRef = useRef<ModalRef>(null);
  const shopRelocationApplicationDetailRef = useRef<ModalRef>(null);

  const { data, runAsync: getList } = useRequest(getOwnerUpdatePage, { manual: true });

  const { data: users } = useAllUsers();

  const columns: ProColumns<AbnormalCertificateDTO>[] = [
    {
      title: '申请编号',
      dataIndex: 'code',
      fixed: 'left',
      renderText: (value, record) => (
        <a
          onClick={() => {
            currentIdRef.current = record.id;
            shopRelocationApplicationDetailRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '门店编号',
      dataIndex: 'shopId',
      renderText: (value) => (
        <a
          onClick={() => {
            shopIdRef.current = value;
            shopDetailDrawer.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      width: 210,
      search: false,
    },
    {
      title: '转让类型',
      dataIndex: 'transferType',
      valueEnum: enum2ValueEnum(OwnerUpdateTypeEnum),
    },
    {
      title: '接店人类型',
      dataIndex: 'ownerType',
      search: false,
      valueEnum: enum2ValueEnum(OwnerTypeEnum),
    },
    {
      title: '门店所属省份',
      dataIndex: 'weaverProvinceName',
      search: false,
    },
    {
      title: '所属大区',
      dataIndex: 'weaverWarZoneName',
      search: false,
      width: 180,
    },
    {
      title: '门店所属作战小队',
      dataIndex: 'weaverDepartmentName',
      search: false,
      width: 180,
    },
    {
      title: '流程节点',
      dataIndex: 'node',
      width: 210,
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      search: false,
      width: 180,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      search: false,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      search: false,
      width: 180,
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        cardProps={false}
        bordered
        rowKey="id"
        sticky
        size="small"
        columnEmptyText=""
        columns={columns}
        scroll={{ x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0) }}
        options={false}
        tableAlertRender={false}
        search={{
          labelWidth: 100,
        }}
        toolbar={{
          subTitle: (
            <span className="text-black">
              共 {data?.total || 0} 条记录
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 条
                  <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            <Permission value={PermissionsMap.ShopOwnerUpdateCreate}>
              <Button
                type="text"
                onClick={() => {
                  shopRelocationApplicationModalRef.current?.open();
                }}
              >
                新建
              </Button>
            </Permission>,
            <Permission value={PermissionsMap.ShopOwnerUpdateExport}>
              <ExportButton
                total={selectedRowKeys.length || data?.total}
                request={() =>
                  exportOwnerUpdate(
                    selectedRowKeys.length ? { ids: selectedRowKeys } : filterParamsRef.current,
                  )
                }
              />
            </Permission>,
          ],
        }}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        rowSelection={{
          fixed: true,
          showSort: true,
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        request={async ({ current, pageSize, ...restParams }) => {
          filterParamsRef.current = restParams as ExportAbnormalCertificateReq;

          const res = await getList({ pageNum: current, pageSize, ...restParams });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />
      <ShopOwnerUpdateCreateModal
        ref={shopRelocationApplicationModalRef}
        onSuccess={() => actionRef.current?.reload()}
      />
      <ShopOwnerUpdateDetailDrawer
        ref={shopRelocationApplicationDetailRef}
        getId={() => currentIdRef.current}
      />
      <ShopDetailDrawer ref={shopDetailDrawer} getId={() => shopIdRef.current} />
    </PageContainer>
  );
};

export default ShopOwnerUpdate;
