import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { Encrypt, ShopSelect } from '@src/components';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';
import {
  createRelocationApply,
  editRelocationApply,
} from '@src/services/process/relocation-intention-chat';
import {
  ChatTypeEnum,
  RelocationApplyDTO,
} from '@src/services/process/relocation-intention-chat/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps, Select } from 'antd';

interface CreateRelocationApplyModalProps extends ModalProps {
  onSuccess: () => void;
  getData: () => RelocationApplyDTO | null;
}

const CreateRelocationApplyModal = React.forwardRef<ModalRef, CreateRelocationApplyModalProps>(
  ({ onSuccess, getData, ...props }, ref) => {
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();

    const data = getData();
    const isEdit = !!data;

    React.useEffect(() => {
      if (open && !!data) {
        form.setFieldsValue(data);
      }
    }, [data, form, open]);

    const chatType = Form.useWatch('chatType', form);

    const { runAsync: create, loading } = useRequest(createRelocationApply, {
      manual: true,
    });

    const { runAsync: update, loading: updateLoading } = useRequest(editRelocationApply, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      if (isEdit) {
        await update({ ...values, id: data?.id });
        message.success('编辑成功');
      } else {
        await create(values);
        message.success('创建成功');
      }

      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        open={open}
        title={isEdit ? '编辑搬迁群名' : '新建搬迁群名'}
        destroyOnHidden
        confirmLoading={loading || updateLoading}
        modalRender={(node) => (
          <Form form={form} labelCol={{ span: 4 }} clearOnDestroy onFinish={handleSave}>
            {node}
          </Form>
        )}
        onCancel={() => setOpen(false)}
        onOk={form.submit}
        {...props}
      >
        <Form.Item
          label="群类型"
          name="chatType"
          rules={[{ required: true, message: '请选择群类型' }]}
        >
          <Select
            disabled={isEdit}
            options={enum2Options(ChatTypeEnum)}
            placeholder="请选择群类型"
          />
        </Form.Item>
        <Form.Item
          label="渠道类型"
          name="channelType"
          rules={[{ required: true, message: '请选择渠道类型' }]}
        >
          <Select placeholder="请选择渠道类型" options={enum2Options(ContractChannelTypeEnum)} />
        </Form.Item>
        {chatType === ChatTypeEnum.搬迁群名 && (
          <Form.Item
            label="门店编号"
            name="shopId"
            rules={[{ required: true, message: '请输入门店编号' }]}
          >
            <ShopSelect disabled={isEdit} />
          </Form.Item>
        )}

        <Form.Item
          label="客户"
          name="customerId"
          rules={[{ required: true, message: '请选择客户' }]}
        >
          <RelCustomer
            editable={!isEdit}
            defaultCustomerIdToNameMap={{
              [data?.customerId || '']: data?.customerName,
            }}
          />
        </Form.Item>
        <Encrypt.PhoneFormItem
          formItemProps={{
            label: '手机号',
            name: 'customerPhone',
            rules: [{ required: true, message: '请输入手机号' }],
          }}
          fieldProps={{
            placeholder: '请输入手机号',
            sensitiveValue: data?.customerPhoneSensitive,
          }}
        />
        <Form.Item
          label="搬迁群名"
          name="chatName"
          rules={[{ required: true, message: '请输入搬迁群名' }]}
        >
          <Input maxLength={200} placeholder="请输入搬迁群名" />
        </Form.Item>
      </Modal>
    );
  },
);

export default CreateRelocationApplyModal;
