import { useRef } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ModalRef } from '@src/common/interface';
import { ProTable, TableActions } from '@src/components';
import EditablePhone from '@src/components/Editable/EditablePhone';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';
import {
  deleteRelocationApply,
  getRelocationApplyList,
} from '@src/services/process/relocation-intention-chat';
import {
  ChatTypeEnum,
  RelocationApplyDTO,
} from '@src/services/process/relocation-intention-chat/type';
import { compatibleTableActionWidth, enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, But<PERSON> } from 'antd';
import CreateRelocationApplyModal from './components/CreateRelocationApplyModal';

const RelocationApply = () => {
  const { modal, message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const currentRef = useRef<RelocationApplyDTO | null>(null);
  const createModalRef = useRef<ModalRef>(null);
  const shopIdRef = useRef<string | null>(null);
  const shopDetailDrawer = useRef<ModalRef>(null);

  const checkPermission = usePermission();
  const { data, runAsync: getList } = useRequest(getRelocationApplyList, { manual: true });

  const columns: ProColumns<RelocationApplyDTO>[] = [
    { title: '群编号', dataIndex: 'chatCode' },
    { title: '群名', dataIndex: 'chatName' },
    {
      title: '群类型',
      dataIndex: 'chatType',
      valueEnum: enum2ValueEnum(ChatTypeEnum),
    },
    {
      title: '渠道类型',
      dataIndex: 'channelType',
      valueEnum: enum2ValueEnum(ContractChannelTypeEnum),
    },
    {
      title: '门店编号',
      dataIndex: 'shopId',
      search: false,
      renderText: (value) => (
        <a
          onClick={() => {
            shopIdRef.current = value;
            shopDetailDrawer.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      search: false,
      width: 210,
    },
    {
      title: '手机号',
      dataIndex: 'customerPhone',
      renderText: (value, { customerPhoneSensitive }) => (
        <EditablePhone value={value} fieldProps={{ sensitiveValue: customerPhoneSensitive }} />
      ),
    },
    { title: '创建时间', dataIndex: 'createTime', search: false },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      fixed: 'right',
      width: compatibleTableActionWidth(100),
      search: false,
      render: (_, item) => (
        <TableActions
          shortcuts={[
            {
              label: '编辑',
              show: checkPermission(PermissionsMap.ProcessRelocationApplyEdit),
              onClick: () => {
                currentRef.current = item;
                createModalRef.current?.open();
              },
            },
            {
              label: '删除',
              danger: true,
              show: checkPermission(PermissionsMap.ProcessRelocationApplyDelete),
              onClick: () =>
                modal.confirm({
                  title: '删除',
                  content: '确定要删除该搬迁群名吗？',
                  onOk: async () => {
                    await deleteRelocationApply(item.id);
                    message.success('删除成功');
                    actionRef.current?.reload();
                  },
                }),
            },
          ]}
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        bordered
        actionRef={actionRef}
        sticky
        size="small"
        rowKey="id"
        cardProps={false}
        options={false}
        columnEmptyText=""
        tableAlertRender={false}
        columns={columns}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        scroll={{
          x: columns.reduce(
            (total, cur) => total + (cur.hidden ? 0 : (cur.width as number) || 180),
            0,
          ),
        }}
        toolbar={{
          subTitle: <span className="text-black">共 {data?.total || 0} 条记录</span>,
          actions: [
            <Permission value={PermissionsMap.ProcessRelocationApplyCreate}>
              <Button
                type="text"
                onClick={() => {
                  currentRef.current = null;
                  createModalRef.current?.open();
                }}
              >
                新建
              </Button>
            </Permission>,
          ],
        }}
        request={async ({ current, pageSize, ...params }) => {
          const res = await getList({ pageNum: current, pageSize, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <CreateRelocationApplyModal
        ref={createModalRef}
        getData={() => currentRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
      <ShopDetailDrawer ref={shopDetailDrawer} getId={() => shopIdRef.current} />
    </PageContainer>
  );
};

export default RelocationApply;
