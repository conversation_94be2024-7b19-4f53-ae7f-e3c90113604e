import { useEffect, useRef } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { workflowAuditStatusOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ProTable, TableActions } from '@src/components';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useAllUsers from '@src/hooks/useAllUsers';
import useDistricts from '@src/hooks/useDistricts';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';
import { queryRelocationAreaChangeList } from '@src/services/process/relocation-area-change';
import {
  RelocationAreaChangeDTO,
  RelocationTypeEnum,
} from '@src/services/process/relocation-area-change/type';
import { WorkflowAuditStatusEnum } from '@src/services/workflow/type';
import { compatibleTableActionWidth, enum2ValueEnum, optionsToValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { Button } from 'antd';
import { useSearchParams } from 'react-router-dom';
import CreateEditRelocationAreaChangeModal from './components/CreateEditRelocationAreaChangeModal';
import RelocationAreaChangeDetailDrawer from './components/RelocationAreaChangeDetailDrawer';

const RelocationAreaChange = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const checkPermission = usePermission();
  const actionRef = useRef<ActionType>(null);
  const createEditModalRef = useRef<ModalRef>(null);
  const shopDetailDrawerRef = useRef<ModalRef>(null);
  const shopIdRef = useRef<string | null>(null);
  const currentIdRef = useRef<number | null>(null);
  const detailDrawerRef = useRef<ModalRef>(null);

  const { data: users } = useAllUsers();
  const { data, runAsync: queryList } = useRequest(queryRelocationAreaChangeList, { manual: true });
  const { data: districts } = useDistricts({ streets: true });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      currentIdRef.current = Number(id);
      detailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: ProColumns<RelocationAreaChangeDTO>[] = [
    {
      title: '申请名称',
      dataIndex: 'name',
      fixed: 'left',
      search: false,
      width: 300,
      renderText: (_, { shopId, newDistrictCode }) =>
        `${shopId}-${newDistrictCode
          .split('/')
          .map((code) => districts?.find((d) => d.code === code)?.name)
          .join('')}`,
    },
    {
      title: '门店搬迁申请编号',
      dataIndex: 'shopRelocApplyCode',
      search: false,
    },
    {
      title: '门店编号',
      dataIndex: 'shopId',
      renderText: (value) => (
        <a
          onClick={() => {
            shopIdRef.current = value;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      valueEnum: optionsToValueEnum(workflowAuditStatusOptions),
    },
    {
      title: '搬迁属性',
      dataIndex: 'relocationType',
      search: false,
      valueEnum: enum2ValueEnum(RelocationTypeEnum),
    },
    {
      title: '搬迁原因',
      dataIndex: 'relocationReason',
      search: false,
      ellipsis: true,
    },
    {
      title: '原渠道类型',
      dataIndex: 'originalChannelType',
      search: false,
      valueEnum: enum2ValueEnum(ContractChannelTypeEnum),
    },
    {
      title: '原搬迁意向区域',
      dataIndex: 'originalDistrictCode',
      search: false,
      ellipsis: true,
      renderText: (value) => <EditableRegion value={value} fieldProps={{ regionLevel: 4 }} />,
    },
    {
      title: '原搬迁门店地址',
      dataIndex: 'originalShopAddress',
      search: false,
      ellipsis: true,
    },
    {
      title: '新渠道类型',
      dataIndex: 'newChannelType',
      search: false,
      valueEnum: enum2ValueEnum(ContractChannelTypeEnum),
    },
    {
      title: '新搬迁意向区域',
      dataIndex: 'newDistrictCode',
      search: false,
      ellipsis: true,
      renderText: (value) => <EditableRegion value={value} fieldProps={{ regionLevel: 4 }} />,
    },
    {
      title: '新搬迁门店定位',
      dataIndex: 'newShopCoordinates',
      search: false,
      ellipsis: true,
      renderText: (_, { newShopCoordinates }) =>
        newShopCoordinates
          ? `${newShopCoordinates.name}（经度：${newShopCoordinates.longitude}；纬度：${newShopCoordinates.latitude}）`
          : '',
    },
    {
      title: '新搬迁门店地址',
      dataIndex: 'newShopAddress',
      search: false,
      ellipsis: true,
    },
    {
      title: '招商顾问',
      dataIndex: 'consultantUserId',
      search: false,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '操作',
      key: 'action',
      search: false,
      align: 'center',
      fixed: 'right',
      width: compatibleTableActionWidth(120),
      render: (_, { id, auditStatus }) => (
        <TableActions
          shortcuts={[
            {
              label: '详情',
              onClick: () => {
                currentIdRef.current = id;
                detailDrawerRef.current?.open();
              },
            },
            {
              label: '编辑',
              show:
                [WorkflowAuditStatusEnum.REJECT, WorkflowAuditStatusEnum.REVOKE].includes(
                  auditStatus,
                ) && checkPermission(PermissionsMap.ProcessRelocationAreaChangeEdit),
              onClick: () => {
                currentIdRef.current = id;
                createEditModalRef.current?.open();
              },
            },
          ]}
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        rowKey="id"
        sticky
        cardProps={false}
        bordered
        columns={columns}
        toolbar={{
          subTitle: <span className="text-black">共 {data?.total || 0} 条记录</span>,
          actions: [
            <Permission value={PermissionsMap.ProcessRelocationAreaChangeCreate}>
              <Button
                type="text"
                onClick={() => {
                  currentIdRef.current = null;
                  createEditModalRef.current?.open();
                }}
              >
                新建
              </Button>
            </Permission>,
          ],
        }}
        scroll={{ x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0) }}
        request={async ({ current, ...params }) => {
          const { result, total } = await queryList({ pageNum: current, ...params });

          return {
            data: result,
            total,
          };
        }}
      />
      <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => shopIdRef.current} />
      <CreateEditRelocationAreaChangeModal
        ref={createEditModalRef}
        getId={() => currentIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
      <RelocationAreaChangeDetailDrawer
        ref={detailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
      />
    </PageContainer>
  );
};

export default RelocationAreaChange;
