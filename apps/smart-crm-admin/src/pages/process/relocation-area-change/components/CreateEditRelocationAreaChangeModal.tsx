import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { ChooseMapPoints, LoadMoreSelect, UserSelect } from '@src/components';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useDistricts from '@src/hooks/useDistricts';
import useDistrictsStreetOpened from '@src/hooks/useDistrictsStreetOpened';
import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';
import {
  queryRelocationAreaChangeDetail,
  submitRelocationAreaChange,
  updateRelocationAreaChange,
} from '@src/services/process/relocation-area-change';
import { RelocationTypeEnum } from '@src/services/process/relocation-area-change/type';
import { getShopRelocationList } from '@src/services/process/shop-relocation-application';
import { ShopRelocationDTO } from '@src/services/process/shop-relocation-application/type';
import { submitWorkflow } from '@src/services/workflow';
import { WorkflowBusinessTypeEnum } from '@src/services/workflow/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps, Select } from 'antd';

interface CreateEditRelocationAreaChangeModalProps extends ModalProps {
  getId: () => number | null;
  onSuccess: () => void;
}

const CreateEditRelocationAreaChangeModal = React.forwardRef<
  ModalRef,
  CreateEditRelocationAreaChangeModalProps
>(({ getId, onSuccess, ...props }, ref) => {
  const { message } = App.useApp();
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();

  const id = getId();
  const isEdit = !!id;

  const { data: districts } = useDistricts({ ready: open, streets: true });
  const { transformDistricts, getStreetOpenedRegionIdsLoading } = useDistrictsStreetOpened({
    ready: open,
  });
  const { loading } = useRequest(() => queryRelocationAreaChangeDetail(id!), {
    ready: open && isEdit,
    onSuccess: (res) => {
      form.setFieldsValue(res);
    },
  });
  const { runAsync: submit, loading: submitLoading } = useRequest(submitRelocationAreaChange, {
    manual: true,
  });
  const { runAsync: update, loading: updateLoading } = useRequest(updateRelocationAreaChange, {
    manual: true,
  });
  const { runAsync: runSubmitWorkflow, loading: submitWorkflowLoading } = useRequest(
    submitWorkflow,
    {
      manual: true,
    },
  );

  React.useImperativeHandle(ref, () => ({
    open: () => setOpen(true),
    close: () => setOpen(false),
  }));

  const handleSave = async (values: any) => {
    const regionName = (values.newDistrictCode as string)
      .split('/')
      .map((code) => districts?.find((district) => district.code === code)?.name || '')
      .join('');
    const name = `${values.shopId}-${regionName}`;

    if (isEdit) {
      await update({
        id,
        name,
        ...values,
      });
      await runSubmitWorkflow({
        businessId: id,
        businessName: values.shopId,
        businessType: WorkflowBusinessTypeEnum.SCRM_RELOCATION_INTENTION_REGION_CHANGE,
      });
    } else {
      await submit({
        name,
        ...values,
      });
    }

    message.success('提交成功');
    setOpen(false);
    onSuccess();
  };

  return (
    <Modal
      title={isEdit ? '编辑搬迁区域变更申请' : '新建搬迁区域变更申请'}
      open={open}
      destroyOnHidden
      okText="提交审核"
      loading={loading || getStreetOpenedRegionIdsLoading}
      confirmLoading={submitLoading || updateLoading || submitWorkflowLoading}
      styles={{
        body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
      }}
      modalRender={(node) => (
        <Form
          form={form}
          clearOnDestroy
          scrollToFirstError
          labelCol={{ span: 7 }}
          onFinish={handleSave}
        >
          {node}
        </Form>
      )}
      onCancel={() => setOpen(false)}
      onOk={form.submit}
      {...props}
    >
      <Form.Item
        label="门店编号"
        name="shopRelocApplyCode"
        rules={[{ required: true, message: '请选择门店编号' }]}
      >
        <LoadMoreSelect
          disabled={isEdit}
          placeholder="请选择门店编号"
          request={async ({ pageNum, keyword }) => {
            const { result, total } = await getShopRelocationList({
              pageNum,
              pageSize: 10,
              shopId: keyword,
            });

            return {
              result: result.map((i) => ({
                label: `${i.shopId}（${i.code}）`,
                value: i.code,
                ...i,
              })),
              total,
            };
          }}
          onChange={(_, _option) => {
            const option = _option as ShopRelocationDTO | undefined;

            form.setFieldsValue({
              shopId: option?.shopId,
              originalChannelType: option?.channelType,
              originalShopAddress: option?.shopAddress,
              originalDistrictCode: option?.regionCode,
            });
          }}
        />
      </Form.Item>
      {/* 保存时需要 */}
      <Form.Item name="shopId" hidden>
        <Input />
      </Form.Item>
      <Form.Item
        label="搬迁属性"
        name="relocationType"
        rules={[{ required: true, message: '请选择搬迁属性' }]}
      >
        <Select
          allowClear
          placeholder="请选择搬迁属性"
          options={enum2Options(RelocationTypeEnum)}
        />
      </Form.Item>
      <Form.Item
        label="搬迁原因"
        name="relocationReason"
        rules={[{ required: true, message: '请输入搬迁原因' }]}
      >
        <Input.TextArea maxLength={255} placeholder="请输入搬迁原因" />
      </Form.Item>
      <Form.Item
        label="原渠道类型"
        name="originalChannelType"
        rules={[{ required: true, message: '不能为空' }]}
      >
        <Select
          disabled
          options={enum2Options(ContractChannelTypeEnum)}
          placeholder="选择门店编号后自动填充"
        />
      </Form.Item>
      <Form.Item
        label="原搬迁门店地址"
        name="originalShopAddress"
        rules={[{ required: true, message: '不能为空' }]}
      >
        <Input disabled placeholder="选择门店编号后自动填充" />
      </Form.Item>
      <EditableRegion
        editable
        formItemProps={{
          label: '原搬迁意向区域',
          name: 'originalDistrictCode',
          rules: [{ required: true, message: '不能为空' }],
        }}
        fieldProps={{
          disabled: true,
          regionLevel: 4,
          placeholder: '选择门店编号后自动填充',
        }}
      />
      <Form.Item
        label="新渠道类型"
        name="newChannelType"
        rules={[{ required: true, message: '请选择渠道类型' }]}
      >
        <Select options={enum2Options(ContractChannelTypeEnum)} placeholder="请选择渠道类型" />
      </Form.Item>
      <EditableRegion
        editable
        formItemProps={{
          label: '新搬迁意向区域',
          name: 'newDistrictCode',
          validateFirst: true,
          dependencies: ['originalDistrictCode'],
          rules: [
            { required: true, message: '请选择新搬迁意向区域' },
            {
              validator: (_, value) => {
                const originalDistrictCode = form.getFieldValue('originalDistrictCode');

                if (originalDistrictCode === value) {
                  return Promise.reject(new Error('不能与原搬迁意向区域相同'));
                }

                return Promise.resolve();
              },
            },
          ],
        }}
        fieldProps={{
          regionLevel: 4,
          transformDistricts,
          placeholder: '请选择新搬迁意向区域',
        }}
      />
      <Form.Item
        label="新搬迁门店定位"
        name="newShopCoordinates"
        getValueProps={(value) => ({
          value: value ? [value] : undefined,
        })}
        normalize={(value) => (Array.isArray(value) ? value[0] : undefined)}
        rules={[{ required: true, message: '请选择新搬迁门店定位' }]}
      >
        <ChooseMapPoints
          multiple={false}
          onChange={(value) => form.setFieldValue('newShopAddress', value[0]?.name)}
        />
      </Form.Item>
      <Form.Item
        label="新搬迁门店地址"
        name="newShopAddress"
        rules={[{ required: true, message: '请输入新搬迁门店地址' }]}
      >
        <Input placeholder="请输入新搬迁门店地址" maxLength={255} />
      </Form.Item>
      <Form.Item
        label="招商顾问"
        name="consultantUserId"
        rules={[{ required: true, message: '请选择招商顾问' }]}
      >
        <UserSelect placeholder="请选择招商顾问" />
      </Form.Item>
    </Modal>
  );
});

export default CreateEditRelocationAreaChangeModal;
