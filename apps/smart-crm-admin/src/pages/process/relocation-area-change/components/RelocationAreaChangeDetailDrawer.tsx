import React, { useRef } from 'react';
import { workflowAuditStatusOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { AuditFlow, ButtonGroup } from '@src/components';
import { ValueType } from '@src/components/ETable';
import useDistricts from '@src/hooks/useDistricts';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { PermissionsMap, usePermission, usePermissionOpen } from '@src/permissions';
import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';
import { queryRelocationAreaChangeDetail } from '@src/services/process/relocation-area-change';
import {
  RelocationAreaChangeDTO,
  RelocationTypeEnum,
} from '@src/services/process/relocation-area-change/type';
import {
  WorkflowAuditOperationTypeEnum,
  WorkflowAuditStatusEnum,
  WorkflowBusinessTypeEnum,
} from '@src/services/workflow/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { Card, Col, Drawer, DrawerProps, Row } from 'antd';
import CreateEditRelocationAreaChangeModal from './CreateEditRelocationAreaChangeModal';

interface RelocationAreaChangeDetailDrawerProps extends DrawerProps {
  getId: () => number | null;
  onUpdate?: () => void;
}

const RelocationAreaChangeDetailDrawer = React.forwardRef<
  ModalRef,
  RelocationAreaChangeDetailDrawerProps
>(({ getId, onUpdate, ...props }, ref) => {
  const checkPermission = usePermission();
  const [open, setOpen] = usePermissionOpen(PermissionsMap.ProcessRelocationAreaChangeDetail);
  const editModalRef = useRef<ModalRef>(null);
  const shopDetailDrawerRef = useRef<ModalRef>(null);

  const id = getId();

  const { data, loading, refresh } = useRequest(() => queryRelocationAreaChangeDetail(id!), {
    ready: open,
  });
  const { data: districts } = useDistricts({ ready: open, streets: true });

  React.useImperativeHandle(ref, () => ({
    open: () => setOpen(true),
    close: () => setOpen(false),
  }));

  const baseInfoItems: DescriptionFieldType<RelocationAreaChangeDTO>[] = [
    {
      label: '申请名称',
      field: 'name',
      children: `${data?.shopId}-${data?.newDistrictCode
        .split('/')
        .map((code) => districts?.find((d) => d.code === code)?.name)
        .join('')}`,
    },
    {
      label: '门店搬迁申请编号',
      field: 'shopRelocApplyCode',
    },
    {
      label: '门店编号',
      field: 'shopId',
      children: <a onClick={() => shopDetailDrawerRef.current?.open()}>{data?.shopId}</a>,
    },
    {
      label: '审核状态',
      field: 'auditStatus',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: workflowAuditStatusOptions,
      },
    },
    {
      label: '搬迁属性',
      field: 'relocationType',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(RelocationTypeEnum),
      },
    },
    {
      label: '搬迁原因',
      field: 'relocationReason',
    },
    {
      label: '渠道类型',
      field: 'originalChannelType',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(ContractChannelTypeEnum),
      },
    },
    {
      label: '原搬迁门店地址',
      field: 'originalShopAddress',
    },
    {
      label: '原搬迁意向区域',
      field: 'originalDistrictCode',
      valueType: ValueType.REGION,
      fieldProps: {
        regionLevel: 4,
      },
    },
    {
      label: '新渠道类型',
      field: 'newChannelType',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(ContractChannelTypeEnum),
      },
    },
    {
      label: '新搬迁门店定位',
      field: 'newShopCoordinates',
      children: data?.newShopCoordinates
        ? `${data.newShopCoordinates.name}（经度：${data.newShopCoordinates.longitude}；纬度：${data.newShopCoordinates.latitude}）`
        : null,
    },
    {
      label: '新搬迁门店地址',
      field: 'newShopAddress',
    },
    {
      label: '新搬迁意向区域',
      field: 'newDistrictCode',
      valueType: ValueType.REGION,
      fieldProps: {
        regionLevel: 4,
      },
    },
    {
      label: '招商顾问',
      field: 'consultantUserId',
      valueType: ValueType.PERSON,
    },
  ];

  return (
    <Drawer
      title="搬迁区域变更申请详情"
      open={open}
      width={1100}
      destroyOnHidden
      onClose={() => setOpen(false)}
      {...props}
    >
      <Row gutter={[20, 20]}>
        <Col span={24} xl={16}>
          <Card
            loading={loading}
            title="基本信息"
            extra={
              <ButtonGroup
                items={[
                  {
                    label: '编辑',
                    show:
                      [WorkflowAuditStatusEnum.REJECT, WorkflowAuditStatusEnum.REVOKE].includes(
                        data?.auditStatus!,
                      ) && checkPermission(PermissionsMap.ProcessRelocationAreaChangeEdit),
                    onClick: () => editModalRef.current?.open(),
                  },
                ]}
              />
            }
          >
            {renderDescriptions(baseInfoItems, data)}
          </Card>
        </Col>
        <Col span={24} xl={8}>
          <AuditFlow.WorkflowCard
            loading={loading}
            auditStatus={data?.auditStatus}
            processInstanceId={data?.processInstanceId}
            businessId={data?.id}
            businessName={data?.shopId}
            businessType={WorkflowBusinessTypeEnum.SCRM_RELOCATION_INTENTION_REGION_CHANGE}
            auditButtons={{
              [WorkflowAuditOperationTypeEnum.RETURN]: false,
            }}
            onSuccess={() => {
              refresh();
              onUpdate?.();
            }}
          />
        </Col>
      </Row>

      <CreateEditRelocationAreaChangeModal
        ref={editModalRef}
        getId={() => id}
        onSuccess={() => {
          refresh();
          onUpdate?.();
        }}
      />
      <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => data?.shopId} />
    </Drawer>
  );
});

export default RelocationAreaChangeDetailDrawer;
