import { useMemo, useState } from 'react';
import { Editable, Encrypt } from '@src/components';
import { ValueType } from '@src/components/ETable';
import useProvinceWarZoneMap from '@src/hooks/useProvinceWarZoneMap';
import { checkCluePhone } from '@src/services/clues/my';
import { ClueDetail, FieldItemType } from '@src/services/clues/my/type';
import { FieldTypeEnum } from '@src/services/common/type';
import { getFieldProps } from '@src/utils';
import { useRequest } from 'ahooks';
import { Form, Input, Select } from 'antd';
import { isEqual } from 'lodash-es';
import RelCustomer from './RelCustomer';

interface ClueFieldFormItemsProps {
  /** 是否是编辑 */
  isEdit?: boolean;
  fields: FieldItemType[];
  data?: ClueDetail;
}

const ClueFieldFormItems: React.FC<ClueFieldFormItemsProps> = ({ isEdit, fields, data }) => {
  // 手机号重复错误信息
  const [phoneErrorMsg, setPhoneErrorMsg] = useState('');
  // 是否需要校验手机号重复
  const [shouldValidatePhone, setShouldValidatePhone] = useState(!isEdit);
  const form = Form.useFormInstance();

  const { runAsync: validatePhone, cancel } = useRequest(checkCluePhone, { manual: true });
  const provinceWarZoneMap = useProvinceWarZoneMap();

  // 自定义字段排序，必填的放上面
  const customFields = useMemo(() => {
    const _customFields = fields.filter((i) => i.fieldType === FieldTypeEnum.CUSTOM && i.editFlag);

    return _customFields.sort((a, b) => {
      if (a.showRules?.some((rule) => rule.field === b.field)) {
        return 1;
      }

      if (b.showRules?.some((rule) => rule.field === a.field)) {
        return -1;
      }

      if (a.notNull && !b.notNull) {
        return -1;
      }

      if (!a.notNull && b.notNull) {
        return 1;
      }

      return 0;
    });
  }, [fields]);

  return (
    <>
      <Form.Item
        name="name"
        label="线索名称"
        rules={[
          { required: true, message: '请输入线索名称' },
          { max: 20, message: '最多输入 20 个字符' },
        ]}
      >
        <Input placeholder="请输入线索名称" />
      </Form.Item>
      <Encrypt.PhoneFormItem
        formItemProps={{
          name: 'phone',
          label: '手机号',
          // 解决手机号重复的错误提示太长不换行导致的布局问题
          wrapperCol: { span: 15 },
          rules: [
            { required: true, message: '请输入手机号' },
            {
              validateTrigger: 'onBlur',
              validator: async (_, value) => {
                cancel();

                if (phoneErrorMsg) {
                  return Promise.reject(new Error(phoneErrorMsg));
                }

                if (shouldValidatePhone) {
                  try {
                    await validatePhone(value);
                    setPhoneErrorMsg('');

                    return Promise.resolve();
                  } catch (error: any) {
                    setPhoneErrorMsg(error.message);

                    return Promise.reject(error.message);
                  } finally {
                    setShouldValidatePhone(false);
                  }
                }

                return Promise.resolve();
              },
            },
          ],
        }}
        fieldProps={{
          disabled: isEdit,
          placeholder: '请输入手机号',
          sensitiveValue: data?.phoneSensitive,
          onChange: () => {
            cancel();
            setPhoneErrorMsg('');
            setShouldValidatePhone(true);
          },
        }}
      />
      <Form.Item name="source" label="来源" rules={[{ required: true, message: '请选择来源' }]}>
        <Select
          allowClear
          placeholder="请选择来源"
          showSearch
          optionFilterProp="label"
          options={fields.find((field) => field.field === 'source')?.options}
        />
      </Form.Item>
      {!isEdit && (
        <Form.Item name="customerId" label="关联客户">
          <RelCustomer editable allowClear />
        </Form.Item>
      )}
      {customFields.map((i) => {
        const node = (
          <Editable
            key={i.field}
            valueType={i.valueType}
            editable
            formItemProps={{
              name: i.field,
              label: i.name,
              rules: i.notNull ? [{ required: true, message: `${i.name}不能为空` }] : undefined,
            }}
            fieldProps={{
              ...getFieldProps(i),
              ...(i.field === 'intentionDistrict'
                ? {
                    // 意向区域有值时，不允许修改
                    disabled: isEdit && !!data?.intentionDistrict,
                    onChange: (value: string[] | undefined) => {
                      form.setFieldValue(
                        'belongWarZone',
                        value ? provinceWarZoneMap[value[0]] : undefined,
                      );
                    },
                  }
                : {}),
              ...(i.field === 'belongWarZone'
                ? {
                    disabled: true,
                    placeholder: '根据意向区域自动匹配',
                  }
                : {}),
            }}
          />
        );

        const showRules = i.showRules || [];

        if (showRules.length > 0) {
          return (
            <Form.Item
              key={i.field}
              noStyle
              shouldUpdate={(prev, next) =>
                showRules.some((rule) => !isEqual(prev[rule.field], next[rule.field]))
              }
            >
              {({ getFieldValue }) =>
                showRules.some((rule) => {
                  const fieldValue = getFieldValue(rule.field);
                  const isMultiple =
                    fields.find((field) => field.field === rule.field)?.valueType ===
                    ValueType.MULTIPLE;

                  if (isMultiple) {
                    return rule.values.some((val) => fieldValue?.includes(val));
                  }

                  return rule.values.includes(fieldValue);
                }) && node
              }
            </Form.Item>
          );
        }

        return node;
      })}
      <Form.Item name="remark" label="备注" rules={[{ max: 500, message: '最多输入 500 个字符' }]}>
        <Input placeholder="请输入备注" />
      </Form.Item>
    </>
  );
};

export default ClueFieldFormItems;
