import React, { useRef, useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { getTagList } from '@src/services/standard-settings/info';
import { TagCategoryEnum, TagStatusEnum } from '@src/services/standard-settings/info/type';
import { useRequest } from 'ahooks';
import { Card, Checkbox, Modal, Skeleton, Tag } from 'antd';
import classNames from 'classnames';

interface TagsSettingProps {
  className?: string;
  value?: number[];
  editable?: boolean;
  onChange?: (value: number[]) => void | Promise<void>;
}

const TagsSetting: React.FC<TagsSettingProps> = ({
  value: propsValue,
  editable,
  className,
  onChange,
}) => {
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState<number[]>([]);
  const [showSelected, setShowSelected] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(false);

  const { data, loading: getTagListLoading } = useRequest(getTagList, {
    cacheKey: 'tag-list',
  });

  const groups = data?.result.filter(
    (group) =>
      group.status === 'AVAILABLE' || group.tags.some((i) => (propsValue || []).includes(i.id)),
  );

  const showGroups = showSelected
    ? groups?.filter((item) => item.tags.some((tag) => value.includes(tag.id)))
    : groups;

  const getTagInfo = (id: number) => {
    const group = data?.result.find((item) => item.tags.some((tag) => tag.id === id));

    return {
      isSystem: group?.category === TagCategoryEnum.系统,
      name: group?.tags.find((i) => i.id === id)?.name || '',
    };
  };

  return (
    <>
      <div className={classNames('flex gap-2 items-start flex-wrap', className)}>
        {getTagListLoading && propsValue ? (
          <Skeleton active paragraph={false} title={{ width: 120, style: { height: 22 } }} />
        ) : (
          propsValue?.map((id) => {
            const { name, isSystem } = getTagInfo(id);

            return (
              <Tag key={id} className="mr-0" color={isSystem ? 'gold' : undefined}>
                {name}
              </Tag>
            );
          })
        )}
        {!editable && !propsValue && <span className="text-gray-400">暂无标签</span>}
        {editable && (
          <Tag
            className="border-dashed cursor-pointer"
            onClick={() => {
              setOpen(true);
              setShowSelected(false);
              setValue(propsValue || []);
            }}
          >
            <PlusOutlined /> 设置标签
          </Tag>
        )}
      </div>
      <Modal
        title="设置标签"
        width={800}
        open={open}
        destroyOnHidden
        confirmLoading={loading}
        onOk={async () => {
          setLoading(true);

          try {
            await onChange?.(value);
            setOpen(false);
          } catch (error) {}

          setLoading(false);
        }}
        onCancel={() => setOpen(false)}
      >
        <Card
          className="mb-3"
          styles={getTagListLoading ? undefined : { body: { padding: 0 } }}
          loading={getTagListLoading}
        >
          <div className="flex h-[500px]">
            <div className="w-[200px] border-r overflow-auto">
              {showGroups?.map((i, index) => (
                <div
                  key={i.id}
                  className="p-3 border-b cursor-pointer hover:bg-gray-100"
                  onClick={() =>
                    (containerRef.current?.childNodes[index] as HTMLDivElement).scrollIntoView({
                      behavior: 'smooth',
                    })
                  }
                >
                  {i.name} {i.status === 'INVALID' && '(已废弃)'}
                </div>
              ))}
            </div>
            <div ref={containerRef} className="overflow-auto flex flex-col p-4 flex-1 gap-5">
              {showGroups?.map((item) => {
                // 标签可用、或 propsValue 中有时才展示
                const tags = item.tags.filter(
                  (i) => i.status === TagStatusEnum.AVAILABLE || propsValue?.includes(i.id),
                );
                const isSystemTag = item.category === TagCategoryEnum.系统;

                return (
                  <div key={item.id}>
                    <div className="flex justify-between mb-3">
                      <div>
                        {item.name}
                        {item.status === 'INVALID' && '(已废弃)'}
                      </div>
                      {isSystemTag && <Tag color="gold">系统标签，不可更改</Tag>}
                    </div>
                    {!tags.length ? (
                      <span className="text-gray-500">无标签</span>
                    ) : (
                      <div className="flex gap-2 flex-wrap">
                        {tags.map((i) => {
                          const checked = value.includes(i.id);

                          return (
                            <Tag.CheckableTag
                              checked={checked}
                              key={i.id}
                              className={classNames('py-1 px-3 mr-0', {
                                'bg-gray-100': !checked,
                                'pointer-events-none': isSystemTag,
                              })}
                              onChange={(check) => {
                                if (isSystemTag) {
                                  return;
                                }

                                if (item.type === 'RADIO') {
                                  if (check) {
                                    setValue(
                                      value
                                        .filter(
                                          (val) =>
                                            !item.tags.map((t) => t.id).some((id) => id === val),
                                        )
                                        .concat(i.id),
                                    );
                                  } else {
                                    setValue(value.filter((val) => val !== i.id));
                                  }
                                } else {
                                  setValue(
                                    check
                                      ? value.concat(i.id)
                                      : value.filter((val) => val !== i.id),
                                  );
                                }
                              }}
                            >
                              {i.name} {i.status === 'INVALID' && '(已废弃)'}
                            </Tag.CheckableTag>
                          );
                        })}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </Card>
        <Checkbox checked={showSelected} onChange={(e) => setShowSelected(e.target.checked)}>
          展示已选标签
        </Checkbox>
      </Modal>
    </>
  );
};

export default TagsSetting;
