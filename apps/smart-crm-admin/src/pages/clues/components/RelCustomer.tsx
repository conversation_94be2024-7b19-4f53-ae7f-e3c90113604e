import { useEffect, useRef, useState } from 'react';
import { EditOutlined } from '@ant-design/icons';
import { ActionType } from '@ant-design/pro-components';
import { ModalRef } from '@src/common/interface';
import { ProTable } from '@src/components';
import useCustomerColumns from '@src/hooks/useCustomerColumns';
import CreateEditCustomerModal from '@src/pages/customers/components/CreateEditCustomerModal';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import { getCustomerList } from '@src/services/customers';
import { CustomerDTO } from '@src/services/customers/type';
import { Button, Divider, Modal, Typography } from 'antd';
import { TableRowSelection } from 'antd/lib/table/interface';

interface RelCustomerProps {
  editable?: boolean;
  tip?: React.ReactNode;
  value?: number;
  // 默认的客户 id 和客户名称的映射值
  defaultCustomerIdToNameMap?: Record<number, string | undefined>;
  placeholder?: string;
  // 是否可以将已选择的客户移除，只允许在新建的时候移除
  allowClear?: boolean;
  getCheckboxProps?: TableRowSelection<CustomerDTO>['getCheckboxProps'];
  onChange?: (
    value?: number,
    info?: CustomerDTO,
  ) => void | boolean | Promise<void> | Promise<boolean>;
  onUpdate?: (info?: CustomerDTO) => void;
}

const RelCustomer: React.FC<RelCustomerProps> = ({
  editable,
  tip,
  value,
  defaultCustomerIdToNameMap,
  placeholder,
  allowClear,
  getCheckboxProps,
  onChange,
  onUpdate,
  ...props
}) => {
  const [open, setOpen] = useState(false);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const customerIdRef = useRef<number | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [showName, setShowName] = useState('');
  const [confirmLoading, setConfirmLoading] = useState(false);
  const createEditCustomerModalRef = useRef<ModalRef>(null);
  const actionRef = useRef<ActionType>(null);
  const [selectedRecord, setSelectedRecord] = useState<CustomerDTO>();

  const columns = useCustomerColumns({
    ready: open,
    onCustomerNameClick: (record) => {
      customerIdRef.current = record.id;
      customerDetailDrawerRef.current?.open();
    },
  });

  useEffect(() => {
    if (open) {
      setSelectedRowKeys(value ? [value] : []);
      setSelectedRecord({ name: showName } as CustomerDTO);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  return (
    <>
      <div className="flex items-center">
        {value ? (
          <a
            onClick={() => {
              customerIdRef.current = value!;
              customerDetailDrawerRef.current?.open();
            }}
          >
            {showName || defaultCustomerIdToNameMap?.[value]}
          </a>
        ) : (
          <span className="text-[#bfbfbf]">{placeholder || '请选择客户'}</span>
        )}
        {editable && (
          <Button
            type="text"
            icon={<EditOutlined />}
            className="ml-2"
            onClick={() => setOpen(true)}
          />
        )}
      </div>
      <Modal
        title="选择客户"
        open={open}
        width={1000}
        confirmLoading={confirmLoading}
        destroyOnHidden
        onOk={async () => {
          const id = selectedRowKeys[0];

          if (id === value) {
            setOpen(false);
          } else {
            setConfirmLoading(true);

            try {
              const res = await onChange?.(id, selectedRecord);

              if (res === false) {
                setConfirmLoading(false);

                return;
              }

              setShowName(selectedRecord?.name || '');
              setOpen(false);
            } catch (error) {}

            setConfirmLoading(false);
          }
        }}
        onCancel={() => setOpen(false)}
        {...props}
      >
        <ProTable
          columnEmptyText=""
          actionRef={actionRef}
          bordered
          rowKey="id"
          size="small"
          columns={columns}
          scroll={{
            x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0),
            y: 400,
          }}
          options={false}
          tableAlertRender={false}
          search={{
            className: '!mb-0 !px-0',
          }}
          cardProps={{
            bodyStyle: {
              padding: 0,
            },
          }}
          toolbar={{
            subTitle: (
              <div className="flex items-center" style={{ height: 22 }}>
                {tip}
                {selectedRowKeys.length > 0 ? (
                  <>
                    已选择客户：
                    <a
                      onClick={() => {
                        customerIdRef.current = selectedRowKeys[0];
                        customerDetailDrawerRef.current?.open();
                      }}
                    >
                      {selectedRecord?.name || defaultCustomerIdToNameMap?.[selectedRowKeys[0]]}
                    </a>
                    {allowClear && (
                      <>
                        <Divider type="vertical" />
                        <Typography.Link
                          type="danger"
                          onClick={() => {
                            setSelectedRowKeys([]);
                            setSelectedRecord(undefined);
                          }}
                        >
                          移除
                        </Typography.Link>
                      </>
                    )}
                  </>
                ) : (
                  '未选择客户'
                )}
              </div>
            ),
            actions: [
              <Button
                type="primary"
                onClick={() => {
                  customerIdRef.current = null;
                  createEditCustomerModalRef.current?.open();
                }}
              >
                新建客户
              </Button>,
            ],
          }}
          pagination={{ showQuickJumper: true, showSizeChanger: true }}
          rowSelection={{
            type: 'radio',
            fixed: true,
            preserveSelectedRowKeys: true,
            selectedRowKeys,
            getCheckboxProps,
            onSelect: (record) => {
              setSelectedRowKeys([record.id]);
              setSelectedRecord(record);
            },
          }}
          request={async ({ current, ...params }) => {
            const res = await getCustomerList({ pageNum: current, ...params });

            return {
              data: res.result,
              total: res.total,
            };
          }}
        />
      </Modal>

      <CreateEditCustomerModal
        ref={createEditCustomerModalRef}
        getId={() => customerIdRef.current}
        onSuccess={(info) => {
          actionRef.current?.reload();

          if (value && info?.id === value) {
            onUpdate?.(info);
          }
        }}
      />

      <CustomerDetailDrawer
        ref={customerDetailDrawerRef}
        getId={() => customerIdRef.current}
        onUpdate={(info) => {
          actionRef.current?.reload();
          onUpdate?.(info);
        }}
      />
    </>
  );
};

export default RelCustomer;
