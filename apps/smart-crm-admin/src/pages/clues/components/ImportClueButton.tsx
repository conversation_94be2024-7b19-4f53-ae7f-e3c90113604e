import { useState } from 'react';
import { ImportFileFormItem } from '@src/components';
import { createClue, downloadClueTemplate, importClueExcel } from '@src/services/clues/my';
import { AllocationTypeEnum, CreateClueReq, FieldItemType } from '@src/services/clues/my/type';
import { FieldTypeEnum } from '@src/services/common/type';
import { useRequest } from 'ahooks';
import { App, Button, Form, Modal, Segmented, Tabs } from 'antd';
import ClueFieldFormItems from './ClueFieldFormItems';
import TagsSetting from './TagsSetting';

interface ImportClueButtonProps {
  fields: FieldItemType[];
  onSuccess: () => void;
}

const ImportClueButton: React.FC<ImportClueButtonProps> = ({ fields, onSuccess }) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [activeKey, setActiveKey] = useState<'manual' | 'excel'>('manual');

  const { showImportPrompt } = ImportFileFormItem.usePrompt();
  const { runAsync: createByManual, loading: createLoading } = useRequest(createClue, {
    manual: true,
  });
  const { runAsync: importExcel, loading: importLoading } = useRequest(importClueExcel, {
    manual: true,
  });

  const handleSave = async (values: any) => {
    if (activeKey === 'manual') {
      // 所有自定义字段 field
      const allCustomFields = fields.reduce<string[]>(
        (total, cur) => (cur.fieldType === FieldTypeEnum.CUSTOM ? total.concat(cur.field) : total),
        [],
      );
      const params: CreateClueReq = { fieldValues: [], allocationType: values.allocationType };

      Object.keys(values).forEach((field) => {
        const value = values[field];

        if (allCustomFields.includes(field)) {
          params.fieldValues.push({ field, value });
        } else {
          params[field] = value;
        }
      });
      await createByManual(params);
      message.success('录入成功');
      onSuccess();
    } else {
      const res = await importExcel(values);

      showImportPrompt(res, onSuccess);
    }

    setOpen(false);
  };

  return (
    <>
      <Button type="text" onClick={() => setOpen(true)}>
        录入
      </Button>

      <Modal
        title="录入线索"
        open={open}
        destroyOnHidden
        width={900}
        confirmLoading={createLoading || importLoading}
        modalRender={(node) => (
          <Form
            form={form}
            preserve={false}
            clearOnDestroy
            labelCol={{ span: 9 }}
            scrollToFirstError
            labelWrap
            initialValues={{
              intention: 'YES_INTENTION',
              agreePolicy: 'AGREE_POLICY',
            }}
            onFinish={handleSave}
          >
            {node}
          </Form>
        )}
        footer={(originNode) => (
          <div className="flex justify-between">
            <Form.Item
              name="allocationType"
              className="mb-0"
              initialValue={AllocationTypeEnum.AUTO}
            >
              <Segmented
                options={[
                  { label: '自动分配', value: AllocationTypeEnum.AUTO },
                  { label: '分配给自己', value: AllocationTypeEnum.ONESELF },
                ]}
              />
            </Form.Item>
            <div className="flex gap-2">{originNode}</div>
          </div>
        )}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
      >
        <Tabs
          activeKey={activeKey}
          destroyOnHidden
          onChange={(key) => {
            setActiveKey(key as typeof activeKey);
          }}
          items={[
            {
              key: 'manual',
              label: '手动录入',
              children: (
                <div className="sm:flex gap-4">
                  <div className="flex-1 pr-5 h-[520px] overflow-auto border-r">
                    <ClueFieldFormItems fields={fields} />
                  </div>
                  <div className="w-[300px]">
                    <Form.Item name="tagIds" label="标签" layout="vertical">
                      <TagsSetting editable />
                    </Form.Item>
                  </div>
                </div>
              ),
            },
            {
              key: 'excel',
              label: '通过 EXCEL 导入',
              children: <ImportFileFormItem downloadTemplate={downloadClueTemplate} />,
            },
          ]}
        />
      </Modal>
    </>
  );
};

export default ImportClueButton;
