import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { getClueDetail, updateClueFieldValue } from '@src/services/clues/my';
import { FieldItemType, UpdateClueFieldValueReq } from '@src/services/clues/my/type';
import { FieldTypeEnum } from '@src/services/common/type';
import { useRequest } from 'ahooks';
import { App, Form, Modal, ModalProps } from 'antd';
import ClueFieldFormItems from './ClueFieldFormItems';

interface EditClueModalProps extends ModalProps {
  getId: () => number | null;
  fields: FieldItemType[];
  onSuccess: () => void;
}

const EditClueModal = React.forwardRef<ModalRef, EditClueModalProps>(
  ({ getId, fields, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);

    const clueId = getId()!;

    const { runAsync: save, loading: saveLoading } = useRequest(updateClueFieldValue, {
      manual: true,
    });
    const { data, loading, mutate } = useRequest(() => getClueDetail(clueId), {
      ready: open,
      onSuccess: (res) => {
        form.setFieldsValue(res);
      },
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        mutate(undefined);
      },
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      // 所有自定义字段 field
      const allCustomFields = fields.reduce<string[]>(
        (total, cur) => (cur.fieldType === FieldTypeEnum.CUSTOM ? total.concat(cur.field) : total),
        [],
      );
      const params: UpdateClueFieldValueReq = { id: clueId, fieldValues: [] };

      Object.keys(values).forEach((field) => {
        const value = values[field];

        if (allCustomFields.includes(field)) {
          params.fieldValues.push({ field, value });
        } else {
          params[field] = value;
        }
      });
      await save(params);
      setOpen(false);
      message.success('修改成功');
      onSuccess();
    };

    return (
      <Modal
        title="编辑线索"
        open={open}
        width={600}
        loading={loading}
        destroyOnHidden
        styles={{
          body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
        }}
        confirmLoading={saveLoading}
        modalRender={(node) => (
          <Form
            form={form}
            clearOnDestroy
            preserve={false}
            labelCol={{ span: 9 }}
            labelWrap
            scrollToFirstError
            onFinish={handleSave}
          >
            {node}
          </Form>
        )}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
        {...props}
      >
        <ClueFieldFormItems isEdit fields={fields} data={data} />
      </Modal>
    );
  },
);

export default EditClueModal;
