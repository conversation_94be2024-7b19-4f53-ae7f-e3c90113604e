import { useEffect, useRef, useState } from 'react';
import { callPhone } from '@src/services/clues/my';
import { useRequest } from 'ahooks';
import { Button, Popover, PopoverProps } from 'antd';

interface CallPhonePopoverProps extends PopoverProps {
  phone: string;
}

const CallPhonePopover: React.FC<CallPhonePopoverProps> = ({ phone, ...props }) => {
  const [disableTime, setDisableTime] = useState(0);
  const timer = useRef<ReturnType<typeof setTimeout>>();

  const { run, loading } = useRequest(() => callPhone(phone), {
    manual: true,
    onFinally: () => {
      setDisableTime(3);
      timer.current = setInterval(() => {
        setDisableTime((prev) => {
          if (prev === 0) {
            clearInterval(timer.current);

            return prev;
          }

          return prev - 1;
        });
      }, 1000);
    },
  });

  useEffect(() => {
    return () => {
      clearInterval(timer.current);
    };
  }, []);

  const disabled = disableTime !== 0;

  return (
    <Popover
      trigger="click"
      content={
        <div className="max-w-[220px] flex flex-col items-end gap-2">
          在拨打客户电话时，请打开 “塔塔工作台” APP进入通话助手，并保持在主页，以确保通话顺畅。
          <Button loading={loading} disabled={disabled} size="small" type="primary" onClick={run}>
            确定 {disabled && `(${disableTime}s)`}
          </Button>
        </div>
      }
      {...props}
    />
  );
};

export default CallPhonePopover;
