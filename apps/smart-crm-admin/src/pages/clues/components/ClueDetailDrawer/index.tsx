import React, { useRef, useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { ButtonGroup, Editable } from '@src/components';
import useAllUsers from '@src/hooks/useAllUsers';
import { PermissionsMap, usePermission } from '@src/permissions';
import { receiveClue } from '@src/services/clues/clue-pool';
import {
  deleteClue,
  getClueAuth,
  getClueDetail,
  getRelCustomer,
  getWXFollowUser,
  markClueTag,
  relCustomerById,
} from '@src/services/clues/my';
import { BelongCluePoolTypeEnum } from '@src/services/clues/my/type';
import { getBusinessFieldList } from '@src/services/common';
import { BusinessTypeEnum } from '@src/services/common/type';
import { getFieldProps } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Card, Descriptions, Drawer, DrawerProps, Tabs } from 'antd';
import dayjs from 'dayjs';
import ClueTimelineTab, { ClueTimelineTabRef } from './ClueTimelineTab';
import AbandonModal from '../../my/components/AbandonModal';
import FollowUpModal from '../../my/components/FollowUpModal';
import TransferClueModal from '../../my/components/TransferClueModal';
import AssignClueModal from '../../pool/components/AssignClueModal';
import TransferPoolModal from '../../pool/components/TransferPoolModal';
import CallPhonePopover from '../CallPhonePopover';
import EditClueModal from '../EditClueModal';
import RelCustomer from '../RelCustomer';
import TagsSetting from '../TagsSetting';

interface ClueDetailDrawerProps extends DrawerProps {
  getId: () => number | null;
  onUpdate?: () => void;
  // 数据从当前列表移除、可能是删除、转让、领取、放弃等
  onDecrease?: (id?: number) => void;
}

const ClueDetailDrawer = React.forwardRef<ModalRef, ClueDetailDrawerProps>(
  ({ getId, onUpdate, onDecrease, ...props }, ref) => {
    const { modal, message } = App.useApp();
    const [open, setOpen] = useState(false);
    const clueTimelineTabRef = useRef<ClueTimelineTabRef>(null);
    const transferClueModalRef = useRef<ModalRef>(null);
    const abandonModalRef = useRef<ModalRef>(null);
    const followUpModalRef = useRef<ModalRef>(null);
    const assignClueModalRef = useRef<ModalRef>(null);
    const transferPoolModalRef = useRef<ModalRef>(null);
    const editClueModalRef = useRef<ModalRef>(null);

    const clueId = getId()!;

    const checkPermission = usePermission();
    const { data: fields = [], loading: getFieldListLoading } = useRequest(
      () => getBusinessFieldList({ businessType: BusinessTypeEnum.MY_CLUE }),
      {
        ready: open,
      },
    );
    const { data: users } = useAllUsers({ ready: open });
    const { data, loading, mutate, refresh } = useRequest(() => getClueDetail(clueId), {
      ready: open,
    });
    const { runAsync: markTag } = useRequest(markClueTag, { manual: true });
    const {
      data: WXData,
      loading: getWXDataLoading,
      mutate: mutateWXData,
    } = useRequest(() => getWXFollowUser(clueId), { ready: open });
    const {
      // operable 控制右上角按钮操作权限，editable 控制标签、关联客户、基本信息编辑权限
      data: { operable, editable } = { operable: false, editable: false },
      loading: getEditableLoading,
      mutate: mutateAuth,
    } = useRequest(() => getClueAuth(clueId), {
      ready: open,
    });
    const {
      data: relCustomer,
      loading: getRelCustomerLoading,
      mutate: mutateRelCustomer,
    } = useRequest(() => getRelCustomer(clueId), { ready: open });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        mutate(undefined);
        mutateWXData(undefined);
        mutateRelCustomer(undefined);
        mutateAuth(undefined);
      },
      close: () => setOpen(false),
    }));

    const handDeleteClue = () => {
      modal.confirm({
        title: '删除线索',
        content: `确定要删除线索 “${data?.name}” 吗？`,
        onOk: async () => {
          await deleteClue({ clueId, fromCluePoolId: data?.cluePoolId });
          message.success('删除成功');
          setOpen(false);
          onDecrease?.(clueId);
        },
      });
    };

    const handleReceive = () => {
      modal.confirm({
        title: '领取线索',
        content: '确定要领取该线索吗？',
        onOk: async () => {
          await receiveClue({
            clueId,
            fromCluePoolId: data?.cluePoolId!,
          });
          message.success('领取成功');
          setOpen(false);
          onDecrease?.(clueId);
        },
      });
    };

    const renderActions = () => {
      if (!data) {
        return null;
      }

      // 我的线索
      if (data.belongCluePoolType === BelongCluePoolTypeEnum.PRIVATE_CLUE_POOL) {
        return (
          <ButtonGroup
            items={[
              {
                label: '编辑',
                show: editable,
                onClick: () => editClueModalRef.current?.open(),
              },
              {
                label: '添加跟进',
                show: operable && checkPermission(PermissionsMap.MyCluesFollow),
                onClick: () => followUpModalRef.current?.open(),
              },
              {
                label: '转让线索',
                show: operable && checkPermission(PermissionsMap.MyCluesTransfer),
                onClick: () => transferClueModalRef.current?.open(),
              },
              {
                label: (isMobile) => (
                  <CallPhonePopover phone={data.phone}>
                    {isMobile ? '拨打电话' : <Button>拨打电话</Button>}
                  </CallPhonePopover>
                ),
                show: operable && checkPermission(PermissionsMap.MyCluesCall),
              },
              {
                label: '放弃',
                show: operable && checkPermission(PermissionsMap.MyCluesAbandon),
                onClick: () => abandonModalRef.current?.open(),
              },
              {
                label: '删除',
                danger: true,
                show: operable && checkPermission(PermissionsMap.MyCluesDelete),
                onClick: handDeleteClue,
              },
            ]}
          />
        );
        // 默认线索池或指定线索池
      } else {
        return (
          operable && (
            <ButtonGroup
              items={[
                {
                  label: '领取线索',
                  show: checkPermission(PermissionsMap.CluePoolListReceive),
                  onClick: handleReceive,
                },
                {
                  label: '分配线索',
                  show: checkPermission(PermissionsMap.CluePoolListAssign),
                  onClick: () => assignClueModalRef.current?.open(),
                },
                {
                  label: '转移线索池',
                  show: checkPermission(PermissionsMap.CluePoolListTransfer),
                  onClick: () => transferPoolModalRef.current?.open(),
                },
                {
                  label: (isMobile) => (
                    <CallPhonePopover phone={data.phone}>
                      {isMobile ? '拨打电话' : <Button>拨打电话</Button>}
                    </CallPhonePopover>
                  ),
                  show: checkPermission(PermissionsMap.CluePoolListCall),
                },
                {
                  label: '删除',
                  danger: true,
                  show: checkPermission(PermissionsMap.CluePoolListDelete),
                  onClick: handDeleteClue,
                },
              ]}
            />
          )
        );
      }
    };

    return (
      <Drawer
        title="线索详情"
        push={false}
        width={1050}
        destroyOnHidden
        onClose={() => setOpen(false)}
        {...props}
        open={open}
      >
        <Card loading={!data || loading || getEditableLoading || getRelCustomerLoading}>
          <div className="flex justify-between mb-3">
            <div className="flex flex-col gap-2">
              <p> 线索名称: {data?.name}</p>
              <TagsSetting
                editable={editable}
                value={data?.tagId?.map((i) => i.id)}
                onChange={
                  editable
                    ? async (value) => {
                        const addTagIds = value.filter(
                          (val) => !data?.tagId?.some((tag) => tag.id === val),
                        );
                        const removeTagIds = (data?.tagId || [])
                          .filter((tag) => !value.includes(tag.id))
                          .map((i) => i.id);

                        await markTag({ clueId, addTagIds, removeTagIds });
                        message.success('修改成功');
                        clueTimelineTabRef.current?.reloadFollow();
                        onUpdate?.();
                        refresh();
                      }
                    : undefined
                }
              />
              {/* 在成员线索里展示负责人，线索池展示最后负责人 */}
              {data?.belongCluePoolType === BelongCluePoolTypeEnum.PRIVATE_CLUE_POOL ? (
                <p>负责人：{users?.find((user) => user.id === data?.director)?.name}</p>
              ) : (
                <p>最后负责人：{users?.find((user) => user.id === data?.finalDirector)?.name}</p>
              )}
              <div className="flex items-center">
                关联客户：
                <RelCustomer
                  editable={editable}
                  value={relCustomer?.id}
                  defaultCustomerIdToNameMap={{ [relCustomer?.id || '']: relCustomer?.name }}
                  placeholder="暂无"
                  onChange={async (customerId, info) => {
                    await relCustomerById({ clueId, customerId: customerId! });
                    mutateRelCustomer({ id: customerId!, name: info?.name! });
                    message.success('修改成功');
                  }}
                />
              </div>
            </div>

            {renderActions()}
          </div>
        </Card>
        <Card
          className="mt-5"
          loading={loading || getWXDataLoading || getFieldListLoading || getEditableLoading}
        >
          <Tabs
            className="-mt-5"
            items={[
              {
                key: 'profile',
                label: '线索概况',
                children: (
                  <>
                    <Descriptions
                      column={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2, xxl: 2 }}
                      title="添加企微客户信息"
                      items={[
                        {
                          key: '1',
                          label: '添加员工',
                          children: WXData?.map((i) => i.name).join('、'),
                        },
                        {
                          key: '2',
                          label: '首次添加时间',
                          children: WXData?.[0]?.followTime
                            ? dayjs(WXData[0].followTime).format('YYYY-MM-DD HH:mm:ss')
                            : null,
                        },
                      ]}
                    />
                    <Descriptions
                      column={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2, xxl: 2 }}
                      title="基本信息"
                      items={fields
                        .filter((i) => i.field !== 'tagId' && i.showFlag)
                        .map((item) => ({
                          key: item.field,
                          label: item.name,
                          children: (
                            <Editable
                              valueType={item.valueType}
                              fieldProps={getFieldProps({
                                ...item,
                                sensitiveValue: data?.[`${item.field}Sensitive`],
                              })}
                              initialValue={data?.[item.field]}
                            />
                          ),
                        }))}
                    />
                  </>
                ),
              },
              {
                key: 'timeline',
                label: '线索动态',
                destroyOnHidden: true,
                children: (
                  <ClueTimelineTab
                    ref={clueTimelineTabRef}
                    clueId={clueId}
                    callPhone={data?.phone || ''}
                  />
                ),
              },
            ]}
          />
        </Card>

        <TransferClueModal
          ref={transferClueModalRef}
          getId={() => clueId}
          onSuccess={() => {
            setOpen(false);
            onDecrease?.(clueId);
          }}
        />
        <AbandonModal
          ref={abandonModalRef}
          getId={() => clueId}
          onSuccess={() => {
            setOpen(false);
            onDecrease?.(clueId);
          }}
        />
        <FollowUpModal
          ref={followUpModalRef}
          getId={() => clueId}
          onSuccess={() => clueTimelineTabRef.current?.reloadFollow()}
        />
        <AssignClueModal
          ref={assignClueModalRef}
          cluePoolId={data?.cluePoolId!}
          getId={() => clueId}
          onSuccess={() => {
            setOpen(false);
            onDecrease?.(clueId);
          }}
        />
        <TransferPoolModal
          ref={transferPoolModalRef}
          cluePoolId={data?.cluePoolId!}
          getId={() => clueId}
          onSuccess={() => {
            setOpen(false);
            onDecrease?.(clueId);
          }}
        />

        <EditClueModal
          getId={() => clueId}
          fields={fields}
          ref={editClueModalRef}
          onSuccess={() => {
            refresh();
            onUpdate?.();
          }}
        />
      </Drawer>
    );
  },
);

export default ClueDetailDrawer;
