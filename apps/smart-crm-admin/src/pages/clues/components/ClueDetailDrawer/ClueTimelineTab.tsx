import React, { useEffect, useMemo, useRef, useState } from 'react';
import { parserJSON } from '@pkg/utils';
import { FileList } from '@src/components';
import EditableAttachment from '@src/components/Editable/EditableAttachment';
import { ValueType } from '@src/components/ETable';
import { getClueActivity, getClueFollowActivity } from '@src/services/clues/my';
import { getCallPhoneLog, getOperationLog } from '@src/services/common';
import { ActivityItem, OperationTypeEnum } from '@src/services/common/type';
import { useRequest } from 'ahooks';
import { Empty, Segmented, Skeleton, Timeline } from 'antd';
import dayjs from 'dayjs';
import { padStart } from 'lodash-es';
import InfiniteScroll from 'react-infinite-scroll-component';

// 秒 -> 时:分:秒
function convertSecondsToHMS(seconds: number) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  // padStart 补齐 0:0:0 为 00:00:00 两位
  return [hours, minutes, remainingSeconds].map((i) => padStart(String(i), 2, '0')).join(':');
}

enum ActivityTypeEnum {
  /** 线索动态 */
  CLUE_ACTIVITY = 'CLUE_ACTIVITY',
  /** 信息变更 */
  CLUE_INFO_CHANGE = 'CLUE_INFO_CHANGE',
  /** 跟进动态 */
  FOLLOW = 'FOLLOW',
  /** 通话记录 */
  CALL_LOG = 'CALL_LOG',
}

const options = [
  { label: '线索动态', value: ActivityTypeEnum.CLUE_ACTIVITY },
  { label: '信息变更', value: ActivityTypeEnum.CLUE_INFO_CHANGE },
  { label: '跟进动态', value: ActivityTypeEnum.FOLLOW },
  { label: '通话记录', value: ActivityTypeEnum.CALL_LOG },
];

const operationTypeLabelMap: Partial<Record<OperationTypeEnum, string>> = {
  [OperationTypeEnum.ALLOCATION]: '分配线索',
  [OperationTypeEnum.GET]: '领取线索',
  [OperationTypeEnum.DELETE]: '删除线索',
  [OperationTypeEnum.ADD_STAFF]: '添加员工',
  [OperationTypeEnum.DELETE_STAFF]: '删除员工',
  [OperationTypeEnum.STAFF_DELETE_CLUE]: '删除线索',
  [OperationTypeEnum.TRANSFER]: '转让线索',
  [OperationTypeEnum.ABANDON]: '放弃线索',
  [OperationTypeEnum.CREATE_CLUE]: '创建线索',
  [OperationTypeEnum.TAG_CHANGE]: '更新标签',
  [OperationTypeEnum.CLUE_INFO_CHANGE]: '变更信息',
  [OperationTypeEnum.ADD_FOLLOW]: '线索跟进',
  [OperationTypeEnum.BLOCK_CALL]: '拨打电话',
  [OperationTypeEnum.CALL_RECORDING_RETRIEVED]: '拨打电话',
  [OperationTypeEnum.CALL_RECORDING_LOST]: '拨打电话',
  [OperationTypeEnum.CALL_BACK_BLOCK_CALL]: '线索回拨电话',
  [OperationTypeEnum.CALL_BACK_CALL_RECORDING_LOST]: '线索回拨电话',
  [OperationTypeEnum.CALL_BACK_CALL_RECORDING_RETRIEVED]: '线索回拨电话',
};

const getTimeLineItem = (item: ActivityItem) => {
  const mapObj: Partial<Record<OperationTypeEnum, () => React.ReactNode>> = {
    [OperationTypeEnum.ALLOCATION]: () => (
      <p>
        {item.userName}分配了线索给{item.transferUserName}
      </p>
    ),
    [OperationTypeEnum.GET]: () => <p>{item.userName}领取了线索</p>,
    [OperationTypeEnum.DELETE]: () => <p>{item.userName}删除了线索</p>,
    [OperationTypeEnum.ADD_STAFF]: () => <p>线索添加了{item.userName}的企业微信</p>,
    [OperationTypeEnum.DELETE_STAFF]: () => <p>线索在微信上删除了{item.userName}</p>,
    [OperationTypeEnum.STAFF_DELETE_CLUE]: () => <p>{item.userName}在企业微信上删除了线索</p>,
    [OperationTypeEnum.TRANSFER]: () => (
      <p>
        {item.userName}转让了线索，后续由{item.transferUserName}负责
      </p>
    ),
    [OperationTypeEnum.ABANDON]: () => (
      <>
        <p>
          {item.userName}放弃了线索，线索进入{item.transferCluePoolName}
        </p>
        <p>内容：{item.info}</p>
        <FileList data={item.attachments} />
      </>
    ),
    [OperationTypeEnum.CREATE_CLUE]: () => (
      <>
        <p>{item.userName}创建了线索</p>
      </>
    ),
    [OperationTypeEnum.TAG_CHANGE]: () => (
      <>
        <p>{item.userName}更新了标签</p>
        <p>更新前：{item.updateBefore}</p>
        <p>更新后：{item.updateAfter}</p>
      </>
    ),
    [OperationTypeEnum.CLUE_INFO_CHANGE]: () => (
      <>
        <p>
          {item.userName}更新了{item.updateFieldName}
        </p>
        <div className="flex items-center">
          更新前：
          <div className="flex-1">
            {item.valueType === ValueType.ATTACHMENT ? (
              parserJSON(item.updateBefore)?.length ? (
                <EditableAttachment value={item.updateBefore} />
              ) : null
            ) : (
              item.updateBefore
            )}
          </div>
        </div>
        <div className="flex items-center">
          更新后：
          <div className="flex-1">
            {item.valueType === ValueType.ATTACHMENT ? (
              parserJSON(item.updateAfter)?.length ? (
                <EditableAttachment value={item.updateAfter} />
              ) : null
            ) : (
              item.updateAfter
            )}
          </div>
        </div>
      </>
    ),
    [OperationTypeEnum.ADD_FOLLOW]: () => (
      <>
        <p>{item.userName}添加了跟进记录</p>
        <p>内容：{item.info}</p>
        <FileList data={item.attachments} />
      </>
    ),
    [OperationTypeEnum.BLOCK_CALL]: () => (
      <p>
        {item.userName}拨打了电话 {item.cluePhone}，未接通
      </p>
    ),
    [OperationTypeEnum.CALL_RECORDING_RETRIEVED]: () => (
      <>
        <p>
          {item.userName}拨打了电话 {item.cluePhone}，通话时长{' '}
          {convertSecondsToHMS(item.callDuration)}
        </p>
        <FileList data={item.attachments} />
      </>
    ),
    [OperationTypeEnum.CALL_RECORDING_LOST]: () => (
      <>
        <p>
          {item.userName}拨打了电话 {item.cluePhone}，通话时长{' '}
          {convertSecondsToHMS(item.callDuration)}
        </p>
        <p>通话录音丢失</p>
      </>
    ),
    [OperationTypeEnum.CALL_BACK_BLOCK_CALL]: () => (
      <>
        <p>
          线索 {item.cluePhone} 回拨了{item.userName}电话，未接通
        </p>
        <FileList data={item.attachments} />
      </>
    ),
    [OperationTypeEnum.CALL_BACK_CALL_RECORDING_LOST]: () => (
      <>
        <p>
          线索 {item.cluePhone} 回拨了{item.userName}电话，通话时长{' '}
          {convertSecondsToHMS(item.callDuration)}
        </p>
        <p>通话录音丢失</p>
      </>
    ),
    [OperationTypeEnum.CALL_BACK_CALL_RECORDING_RETRIEVED]: () => (
      <>
        <p>
          线索 {item.cluePhone} 回拨了{item.userName}电话，通话时长{' '}
          {convertSecondsToHMS(item.callDuration)}
        </p>
        <FileList data={item.attachments} />
      </>
    ),
  };

  return mapObj[item.operationType]?.();
};

interface ClueTimelineTabProps {
  clueId: number;
  callPhone: string;
}

export interface ClueTimelineTabRef {
  reloadFollow: () => void;
}

const pageSize = 5;

const ClueTimelineTab = React.forwardRef<ClueTimelineTabRef, ClueTimelineTabProps>(
  ({ clueId, callPhone }, ref) => {
    const pageNum = useRef(1);
    const [data, setData] = useState<ActivityItem[]>([]);
    const [activityType, setActivityType] = useState(ActivityTypeEnum.CLUE_ACTIVITY);

    const {
      data: clueActivity,
      loading: getClueActivityLoading,
      runAsync: runClueActivity,
      mutate: mutateClueActivity,
    } = useRequest(getClueActivity, {
      manual: true,
    });

    const {
      data: operationLog,
      loading: getOperationLogLoading,
      runAsync: runOperationLog,
      mutate: mutateOperationLog,
    } = useRequest(getOperationLog, {
      manual: true,
    });

    const {
      data: clueFollowActivity,
      loading: getClueFollowActivityLoading,
      runAsync: runClueFollowActivity,
      mutate: mutateClueFollowActivity,
    } = useRequest(getClueFollowActivity, { manual: true });

    const {
      data: callPhoneLog,
      loading: getCallPhoneLogLoading,
      runAsync: runCallPhoneLog,
      mutate: mutateCallPhoneLog,
    } = useRequest(getCallPhoneLog, { manual: true });

    const [total, loading] = (() => {
      if (activityType === ActivityTypeEnum.CLUE_ACTIVITY) {
        return [clueActivity?.total || 0, getClueActivityLoading];
      } else if (activityType === ActivityTypeEnum.CLUE_INFO_CHANGE) {
        return [operationLog?.total || 0, getOperationLogLoading];
      } else if (activityType === ActivityTypeEnum.FOLLOW) {
        return [clueFollowActivity?.total || 0, getClueFollowActivityLoading];
      } else {
        return [callPhoneLog?.total || 0, getCallPhoneLogLoading];
      }
    })();

    const getData = async (type: 'reset' | 'concat') => {
      let res: ActivityItem[];

      if (activityType === ActivityTypeEnum.CLUE_ACTIVITY) {
        res = (await runClueActivity({ pageNum: pageNum.current, pageSize, clueId })).result;
      } else if (activityType === ActivityTypeEnum.CLUE_INFO_CHANGE) {
        res = (
          await runOperationLog({
            pageNum: pageNum.current,
            pageSize,
            businessId: clueId,
            businessType: 'CLUE',
          })
        ).result;
      } else if (activityType === ActivityTypeEnum.FOLLOW) {
        res = (await runClueFollowActivity({ pageNum: pageNum.current, pageSize, clueId })).result;
      } else {
        res = (await runCallPhoneLog({ pageNum: pageNum.current, pageSize, callPhone })).result;
      }

      if (type === 'reset') {
        setData(res);
      } else {
        setData((prev) => prev.concat(res));
      }
    };

    const reset = () => {
      pageNum.current = 1;
      mutateClueActivity(undefined);
      mutateOperationLog(undefined);
      mutateClueFollowActivity(undefined);
      mutateCallPhoneLog(undefined);
      setData([]);
    };

    React.useImperativeHandle(ref, () => ({
      reloadFollow: () => {
        // 如果在详情页修改了标签，且当前客户动态展示的是信息变更，要刷新一下
        // 如果在详情页添加了跟进，且当前客户动态展示的是跟进，要刷新一下
        if (
          activityType === ActivityTypeEnum.CLUE_INFO_CHANGE ||
          activityType === ActivityTypeEnum.FOLLOW
        ) {
          reset();
          getData('reset');
        }
      },
    }));

    // 类型变更从第一页开始请求
    useEffect(() => {
      getData('reset');
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [activityType]);

    // 下拉加载
    const loadMoreData = async () => {
      if (loading) {
        return;
      }

      pageNum.current += 1;

      getData('concat');
    };

    // 根据日期进行分组
    const groupData = useMemo(() => {
      const result: Record<string, ActivityItem[]> = {};

      data.forEach((item) => {
        const date = dayjs(item.activityTime).format('YYYY-MM-DD');

        if (result[date]) {
          result[date].push(item);
        } else {
          result[date] = [item];
        }
      });

      return result;
    }, [data]);

    return (
      <>
        <Segmented
          className="mb-5"
          value={activityType}
          options={options}
          onChange={(key) => {
            setActivityType(key);
            reset();
          }}
        />
        {!data.length ? (
          loading ? (
            <Skeleton paragraph={{ rows: 5 }} title={false} active />
          ) : (
            <Empty />
          )
        ) : (
          <div className="overflow-auto h-[500px]" id="scrollableDiv">
            <InfiniteScroll
              dataLength={data.length}
              next={loadMoreData}
              hasMore={data.length < total}
              loader={<Skeleton paragraph={{ rows: 3 }} title={false} active className="px-6" />}
              scrollableTarget="scrollableDiv"
              // 去掉 overflow，下面的 sticky 才有效
              style={{ overflow: undefined }}
            >
              {Object.keys(groupData).map((date) => (
                <div key={date}>
                  <p className="mb-2 sticky top-0 bg-white z-10">{date}</p>
                  <Timeline
                    items={groupData[date].map((item) => ({
                      children: (
                        <>
                          <p className="mb-1">{dayjs(item.activityTime).format('HH:mm:ss')}</p>
                          <div className="px-4 py-2 rounded bg-[#fafafa]">
                            <p className="text-gray-500 mb-1">
                              {operationTypeLabelMap[item.operationType]}
                            </p>
                            {getTimeLineItem(item)}
                          </div>
                        </>
                      ),
                    }))}
                  />
                </div>
              ))}
            </InfiniteScroll>
          </div>
        )}
      </>
    );
  },
);

export default ClueTimelineTab;
