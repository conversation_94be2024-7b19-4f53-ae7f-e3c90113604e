import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import {
  batchTransferClueToPool,
  getCluePoolPermissionList,
  transferClueToPool,
} from '@src/services/clues/clue-pool';
import { useRequest } from 'ahooks';
import { App, Form, Modal, ModalProps, Select } from 'antd';

interface TransferPoolModalProps extends ModalProps {
  cluePoolId: number;
  getId: () => number | null;
  selectedRowKeys?: number[];
  onSuccess: () => void;
}

const TransferPoolModal = React.forwardRef<ModalRef, TransferPoolModalProps>(
  ({ getId, cluePoolId, selectedRowKeys = [], onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);

    const clueId = getId();

    const { data } = useRequest(getCluePoolPermissionList, { ready: open });
    const { runAsync: transfer, loading: transferLoading } = useRequest(transferClueToPool, {
      manual: true,
    });
    const { runAsync: batchTransfer, loading: batchTransferLoading } = useRequest(
      batchTransferClueToPool,
      { manual: true },
    );

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const handleSave = async ({ id }: { id: number }) => {
      if (clueId) {
        await transfer({
          clueId,
          toCluePoolId: id,
          fromCluePoolId: cluePoolId,
        });
      } else {
        await batchTransfer({
          clueIds: selectedRowKeys,
          toCluePoolId: id,
          fromCluePoolId: cluePoolId,
        });
      }

      message.success('转移成功');
      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        title="转移线索池"
        destroyOnHidden
        confirmLoading={transferLoading || batchTransferLoading}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
        {...props}
        open={open}
      >
        <Form form={form} clearOnDestroy onFinish={handleSave}>
          <Form.Item
            label="线索池"
            name="id"
            rules={[{ required: true, message: '请选择目标线索池' }]}
          >
            <Select
              placeholder="请选择目标线索池"
              showSearch
              optionFilterProp="name"
              options={data?.map((i) => ({ ...i, disabled: i.id === cluePoolId }))}
              fieldNames={{ label: 'name', value: 'id' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default TransferPoolModal;
