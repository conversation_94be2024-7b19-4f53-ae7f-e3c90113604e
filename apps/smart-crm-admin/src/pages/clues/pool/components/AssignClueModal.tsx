import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { UserSelect } from '@src/components';
import { assignClue, batchAssignClue } from '@src/services/clues/clue-pool';
import { useRequest } from 'ahooks';
import { App, Form, Modal, ModalProps } from 'antd';

interface AssignClueModalProps extends ModalProps {
  getId: () => number | null;
  cluePoolId: number;
  selectedRowKeys?: number[];
  onSuccess: () => void;
}

const AssignClueModal = React.forwardRef<ModalRef, AssignClueModalProps>(
  ({ getId, cluePoolId, selectedRowKeys = [], onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);

    const clueId = getId();

    const { runAsync: assign, loading: assignLoading } = useRequest(assignClue, {
      manual: true,
    });
    const { runAsync: batchAssign, loading: batchAssignLoading } = useRequest(batchAssignClue, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const handleSave = async ({ id }: { id: number | number[] }) => {
      if (clueId) {
        await assign({
          clueId,
          toUserId: id as number,
          fromCluePoolId: cluePoolId,
        });
        message.success('分配成功');
      } else {
        const { successNum, failNum } = await batchAssign({
          clueIds: selectedRowKeys,
          toUserIds: id as number[],
          fromCluePoolId: cluePoolId,
        });

        if (failNum === 0) {
          message.success('分配成功');
        } else {
          message.warning(
            `已成功分配 ${successNum} 个线索，剩余 ${failNum} 个未分配，接收线索成员线索数已达上限`,
          );
        }
      }

      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        title="分配线索"
        destroyOnHidden
        confirmLoading={assignLoading || batchAssignLoading}
        onOk={form.submit}
        {...props}
        open={open}
        onCancel={() => setOpen(false)}
      >
        <Form form={form} clearOnDestroy onFinish={handleSave}>
          <Form.Item
            label="员工"
            name="id"
            extra="员工会收到相应的提醒"
            rules={[{ required: true, message: '请选择员工' }]}
          >
            <UserSelect
              mode={clueId ? undefined : 'multiple'}
              // 多选时，数量不能大于 selectedRowKeys 的数量，且最大 20
              maxCount={clueId ? undefined : Math.min(selectedRowKeys.length, 20)}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default AssignClueModal;
