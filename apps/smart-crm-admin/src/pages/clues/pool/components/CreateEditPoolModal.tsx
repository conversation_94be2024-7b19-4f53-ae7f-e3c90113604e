import { useEffect } from 'react';
import { UserSelect } from '@src/components';
import { createCluePool, editCluePool, getCluePoolDetail } from '@src/services/clues/clue-pool';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps } from 'antd';

interface CreateEditPoolModalProps extends ModalProps {
  currentId: number | null;
  onSuccess: () => void;
}

const CreateEditPoolModal: React.FC<CreateEditPoolModalProps> = ({
  open,
  currentId,
  onSuccess,
  ...props
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();

  const { runAsync: create, loading: createLoading } = useRequest(createCluePool, {
    manual: true,
  });
  const { runAsync: edit, loading: editLoading } = useRequest(editCluePool, { manual: true });
  const {
    data: detailData,
    runAsync: getDetail,
    loading: getDetailLoading,
  } = useRequest(getCluePoolDetail, {
    manual: true,
  });

  useEffect(() => {
    if (open) {
      if (currentId) {
        getDetail(currentId).then((res) => {
          form.setFieldsValue({
            name: res.name,
            userIds: res.userIds,
          });
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  const handleSave = async ({ userIds, name }: { userIds: number[]; name: string }) => {
    if (currentId) {
      const addUserIds = userIds.filter((item) => !detailData?.userIds.some((id) => id === item));
      const deleteUserIds = (detailData?.userIds || []).filter((id) => !userIds.includes(id));

      await edit({ name, addUserIds, deleteUserIds, id: currentId });
      message.success('编辑成功');
    } else {
      await create({ name, userIds });
      message.success('创建成功');
    }

    onSuccess();
  };

  return (
    <Modal
      {...props}
      title={currentId ? '编辑线索池' : '新建线索池'}
      open={open}
      destroyOnHidden
      loading={currentId ? getDetailLoading : false}
      modalRender={(node) => (
        <Form form={form} clearOnDestroy labelCol={{ span: 5 }} onFinish={handleSave}>
          {node}
        </Form>
      )}
      confirmLoading={createLoading || editLoading}
      onOk={form.submit}
    >
      <Form.Item
        label="线索池名称"
        name="name"
        rules={[
          { required: true, message: '请输入线索池名称' },
          { max: 20, message: '最多只能输入 20 个字符' },
        ]}
      >
        <Input placeholder="请输入线索池名称，最多 20 个字符" />
      </Form.Item>
      <Form.Item
        label="可见成员"
        name="userIds"
        rules={[{ required: true, message: '请选择可见成员' }]}
      >
        <UserSelect mode="multiple" />
      </Form.Item>
    </Modal>
  );
};

export default CreateEditPoolModal;
