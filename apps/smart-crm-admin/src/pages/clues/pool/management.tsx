import { useRef, useState } from 'react';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@src/components';
import { Permission, PermissionsMap } from '@src/permissions';
import { deleteCluePool, getCluePoolList } from '@src/services/clues/clue-pool';
import { CluePoolItem } from '@src/services/clues/clue-pool/type';
import { App, Button, Popover, Tag } from 'antd';
import Overflow from 'rc-overflow';
import CreateEditPoolModal from './components/CreateEditPoolModal';

interface ManagementProps {
  onUpdate: () => void;
}

const Management: React.FC<ManagementProps> = ({ onUpdate }) => {
  const { message, modal } = App.useApp();
  const [open, setOpen] = useState(false);
  const [currentId, setCurrentId] = useState<number | null>(null);
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<CluePoolItem>[] = [
    { title: '线索池名称', dataIndex: 'name' },
    { title: '当前线索', dataIndex: 'clueCount', search: false },
    {
      title: '可见范围',
      dataIndex: 'userNames',
      search: false,
      renderText: (value: string[]) => (
        <Overflow
          className="flex flex-nowrap"
          data={value}
          renderItem={(item) => {
            const isLast = value.findIndex((i) => i === item) === value.length - 1;

            return <Tag className={isLast ? 'mr-0' : ''}>{item}</Tag>;
          }}
          maxCount="responsive"
          renderRest={(items) => (
            <Popover
              content={
                <div className="flex gap-2 flex-wrap max-w-[240px]">
                  {items.map((i) => (
                    <Tag key={i} className="mr-0">
                      {i}
                    </Tag>
                  ))}
                </div>
              }
            >
              <span className="cursor-pointer text-gray-500">+{items.length}</span>
            </Popover>
          )}
        />
      ),
    },
    { title: '更新人', dataIndex: 'updateByName', search: false },
    { title: '更新时间', dataIndex: 'updateTime', search: false, valueType: 'dateTime' },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      search: false,
      width: 140,
      align: 'center',
      render: (_, { id, defaultFlag }) =>
        !defaultFlag && (
          <div className="flex gap-2 justify-center">
            <Permission value={PermissionsMap.CluePoolManagerUpdate}>
              <Button
                type="link"
                className="p-0"
                onClick={() => {
                  setOpen(true);
                  setCurrentId(id);
                }}
              >
                编辑
              </Button>
            </Permission>
            <Permission value={PermissionsMap.CluePoolManagerDelete}>
              <Button
                type="link"
                danger
                className="p-0"
                onClick={() =>
                  modal.confirm({
                    title: '删除线索池',
                    content: '确定要删除该线索池吗？删除后，线索池内线索将全部进入默认线索池',
                    onOk: async () => {
                      await deleteCluePool(id);
                      message.success('删除成功');
                      actionRef.current?.reload();
                      onUpdate();
                    },
                  })
                }
              >
                删除
              </Button>
            </Permission>
          </div>
        ),
    },
  ];

  return (
    <>
      <ProTable
        sticky
        actionRef={actionRef}
        size="small"
        toolbar={{
          actions: [
            <Permission value={PermissionsMap.CluePoolManagerCreate}>
              <Button
                type="primary"
                onClick={() => {
                  setOpen(true);
                  setCurrentId(null);
                }}
              >
                新建
              </Button>
            </Permission>,
          ],
        }}
        rowKey="id"
        columns={columns}
        options={false}
        request={async ({ current, pageSize, name }) => {
          const res = await getCluePoolList({ pageNum: current, pageSize, name });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <CreateEditPoolModal
        currentId={currentId}
        open={open}
        onCancel={() => setOpen(false)}
        onSuccess={() => {
          setOpen(false);
          actionRef.current?.reload();
          onUpdate();
        }}
      />
    </>
  );
};

export default Management;
