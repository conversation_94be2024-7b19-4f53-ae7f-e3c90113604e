import { useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { PermissionsMap, usePermission } from '@src/permissions';
import { getCluePoolPermissionList } from '@src/services/clues/clue-pool';
import { useRequest } from 'ahooks';
import { Dropdown, Skeleton, Tabs } from 'antd';
import List, { ListRef } from './list';
import Management from './management';

const CluePool = () => {
  const checkPermission = usePermission();
  const [cluePoolId, setCluePoolId] = useState<null | number>(null);
  const listRef = useRef<ListRef>(null);

  const { data = [], refresh } = useRequest(getCluePoolPermissionList, {
    onSuccess: (res) => {
      // 当前选择的线索池不在可见线索池内时，选到第一个线索池
      if (res.every((i) => i.id !== cluePoolId)) {
        setCluePoolId(res[0].id);
      }
    },
  });

  const seaName = data.find((i) => i.id === cluePoolId)?.name;

  return (
    <PageContainer>
      <Tabs
        items={[
          {
            label: (
              <Dropdown
                placement="bottom"
                menu={{
                  style: {
                    maxHeight: 310,
                    overflow: 'auto',
                  },
                  selectedKeys: [String(cluePoolId)],
                  items: data.map((i) => ({ label: i.name, key: String(i.id) })),
                  onClick: ({ key }) => {
                    const seaId = Number(key);

                    setCluePoolId(seaId);

                    if (cluePoolId !== seaId) {
                      listRef.current?.clearSelected();
                      listRef.current?.resetPage();
                    }
                  },
                }}
              >
                <span>
                  {seaName ? (
                    <>
                      {seaName}
                      <CaretDownOutlined className="ml-2" />
                    </>
                  ) : (
                    <Skeleton active title={{ width: 80 }} paragraph={false} />
                  )}
                </span>
              </Dropdown>
            ),
            key: 'list',
            auth: PermissionsMap.CluePoolList,
            children: cluePoolId ? (
              <List ref={listRef} cluePoolId={cluePoolId} />
            ) : (
              <Skeleton paragraph={{ rows: 15 }} />
            ),
          },
          {
            label: '线索池管理',
            key: 'management',
            auth: PermissionsMap.CluePoolManager,
            children: <Management onUpdate={refresh} />,
          },
        ].filter((i) => checkPermission(i.auth))}
      />
    </PageContainer>
  );
};

export default CluePool;
