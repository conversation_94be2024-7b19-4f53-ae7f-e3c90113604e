import React, { useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { ModalRef } from '@src/common/interface';
import { ETable, ExportButton, TableActions } from '@src/components';
import { ETableActionType, ETableColumn } from '@src/components/ETable';
import useTableViews from '@src/hooks/useTableViews';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import {
  batchReceiveClue,
  exportClueInPool,
  getClueListByCluePool,
  receiveClue,
} from '@src/services/clues/clue-pool';
import { batchDeleteClue, deleteClue } from '@src/services/clues/my';
import { ClueItemDTO } from '@src/services/clues/my/type';
import { getBusinessFieldList } from '@src/services/common';
import { BusinessTypeEnum } from '@src/services/common/type';
import {
  compatibleTableActionWidth,
  getFieldProps,
  getFilterProps,
  getParamsFromFilters,
} from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Dropdown, MenuProps, Skeleton, Typography } from 'antd';
import AssignClueModal from './components/AssignClueModal';
import TransferPoolModal from './components/TransferPoolModal';
import CallPhonePopover from '../components/CallPhonePopover';
import ClueDetailDrawer from '../components/ClueDetailDrawer';
import ImportClueButton from '../components/ImportClueButton';

interface ListProps {
  cluePoolId: number;
}

export interface ListRef {
  clearSelected: () => void;
  resetPage: () => void;
}

const List = React.forwardRef<ListRef, ListProps>(({ cluePoolId }, ref) => {
  const checkPermission = usePermission();
  const { modal, message } = App.useApp();
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const detailDrawerRef = useRef<ModalRef>(null);
  const assignClueModalRef = useRef<ModalRef>(null);
  const transferPoolModalRef = useRef<ModalRef>(null);
  const currentIdRef = useRef<number | null>(null);
  const actionRef = useRef<ETableActionType>(null);

  const { data, runAsync: getList } = useRequest(getClueListByCluePool, { manual: true });
  const { views, loading: getViewsLoading } = useTableViews(BusinessTypeEnum.CLUE_POOL);
  const { data: fields = [], loading: getFieldsLoading } = useRequest(() =>
    getBusinessFieldList({ businessType: BusinessTypeEnum.CLUE_POOL }),
  );

  React.useImperativeHandle(ref, () => ({
    clearSelected: () => setSelectedRowKeys([]),
    resetPage: () => actionRef.current?.setCurrent(1),
  }));

  // 进行操作后，对选中的进行处理
  const handleSuccess = (id?: number) => {
    const currentId = currentIdRef.current || id;

    // 单个操作
    if (currentId) {
      setSelectedRowKeys((prev) => prev.filter((i) => i !== currentId));
    } else {
      // 批量操作
      setSelectedRowKeys([]);
    }

    actionRef.current?.reload();
  };

  const handleReceive = (id?: number) => {
    modal.confirm({
      title: '领取线索',
      content: id ? '确定要领取该线索吗？' : '确定要领取选中的线索吗？',
      onOk: async () => {
        if (id) {
          await receiveClue({ clueId: id, fromCluePoolId: cluePoolId });
        } else {
          await batchReceiveClue({ clueIds: selectedRowKeys, fromCluePoolId: cluePoolId });
        }

        message.success('领取成功');
        detailDrawerRef.current?.close();

        if (id) {
          // selectedRowKeys 中没有当前 id 时，返回相同引用（性能优化）
          setSelectedRowKeys((prev) =>
            prev.some((i) => i === id) ? prev.filter((i) => i !== id) : prev,
          );
        } else {
          // 批量操作
          setSelectedRowKeys([]);
        }

        actionRef.current?.reload();
      },
    });
  };

  const columns: ETableColumn<ClueItemDTO>[] = [
    ...fields.map(
      (i) =>
        ({
          title: i.name,
          dataIndex: i.field,
          hidden: !i.showFlag,
          hideInFilters: !i.searchFlag,
          hideInSettings: !i.showFlag,
          fieldProps: (_, record) =>
            getFieldProps({ ...i, sensitiveValue: record[`${i.field}Sensitive`] }),
          filterProps: getFilterProps(i),
          valueType: i.valueType,
          ...(i.field === 'name'
            ? {
                fixed: 'left',
                ellipsis: false,
                render: (value, record) => (
                  <Typography.Text
                    ellipsis={{ tooltip: true }}
                    style={{ color: 'var(--ant-color-link)', cursor: 'pointer' }}
                    onClick={() => {
                      currentIdRef.current = record.id;
                      detailDrawerRef.current?.open();
                    }}
                  >
                    {value}
                  </Typography.Text>
                ),
              }
            : {}),
        } as ETableColumn<ClueItemDTO>),
    ),
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      hideInFilters: true,
      hideInSettings: true,
      width: compatibleTableActionWidth(140),
      render: (_, { id, phone, name }) => (
        <TableActions
          shortcuts={[
            {
              label: (isMobile) =>
                isMobile ? (
                  <CallPhonePopover phone={phone}>拨打电话</CallPhonePopover>
                ) : (
                  <CallPhonePopover phone={phone}>
                    <a>拨打电话</a>
                  </CallPhonePopover>
                ),
              show: checkPermission(PermissionsMap.CluePoolListCall),
            },
          ]}
          moreItems={[
            {
              label: '领取',
              show: checkPermission(PermissionsMap.CluePoolListReceive),
              onClick: () => {
                currentIdRef.current = id;
                handleReceive(id);
              },
            },
            {
              label: '分配',
              show: checkPermission(PermissionsMap.CluePoolListAssign),
              onClick: () => {
                currentIdRef.current = id;
                assignClueModalRef.current?.open();
              },
            },
            {
              label: '转移线索池',
              show: checkPermission(PermissionsMap.CluePoolListTransfer),
              onClick: () => {
                currentIdRef.current = id;
                transferPoolModalRef.current?.open();
              },
            },
            {
              label: '删除',
              show: checkPermission(PermissionsMap.CluePoolListDelete),
              danger: true,
              onClick: () =>
                modal.confirm({
                  title: '删除线索',
                  content: `确定要删除线索 “${name}” 吗？`,
                  onOk: async () => {
                    await deleteClue({ clueId: id, fromCluePoolId: cluePoolId });
                    message.success('删除成功');
                    handleSuccess();
                  },
                }),
            },
          ]}
        />
      ),
    },
  ];

  const batchMenuItems: MenuProps['items'] = [
    {
      key: '1',
      label: '领取',
      auth: PermissionsMap.CluePoolListReceive,
      onClick: () => handleReceive(),
    },
    {
      key: '2',
      label: '分配',
      auth: PermissionsMap.CluePoolListAssign,
      onClick: () => assignClueModalRef.current?.open(),
    },
    {
      key: '3',
      label: '转移线索池',
      auth: PermissionsMap.CluePoolListTransfer,
      onClick: () => transferPoolModalRef.current?.open(),
    },
    {
      key: '5',
      label: '删除',
      auth: PermissionsMap.CluePoolListDelete,
      danger: true,
      onClick: () =>
        modal.confirm({
          title: '删除线索',
          content: `确定要删除选中的 ${selectedRowKeys.length} 个线索吗？`,
          onOk: async () => {
            await batchDeleteClue({ clueIds: selectedRowKeys, fromCluePoolId: cluePoolId });
            message.success('删除成功');
            handleSuccess();
          },
        }),
    },
  ].filter((i) => checkPermission(i.auth));

  const headerNode = (
    <div className="sm:flex justify-between mb-3">
      <div className="self-end mb-2 sm:mb-0 pl-2">
        共 {data?.total || 0} 条线索
        {selectedRowKeys.length > 0 && (
          <>
            ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 条
            <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
              清空选择
            </a>
          </>
        )}
      </div>
      <div className="flex justify-end gap-2">
        {batchMenuItems.length > 0 && (
          <Dropdown
            disabled={!selectedRowKeys.length}
            menu={{
              items: batchMenuItems,
              onClick: () => {
                currentIdRef.current = null;
              },
            }}
          >
            <Button type="text" className="!flex items-center">
              批量操作 <CaretDownOutlined />
            </Button>
          </Dropdown>
        )}
        <Permission value={PermissionsMap.CluePoolListImport}>
          <ImportClueButton fields={fields} onSuccess={() => actionRef.current?.reload()} />
        </Permission>
        <Permission value={PermissionsMap.CluePoolListExport}>
          <ExportButton
            total={selectedRowKeys.length || data?.total}
            request={() =>
              exportClueInPool({
                columns: actionRef.current!.getShowFields(),
                cluePoolId,
                ...(selectedRowKeys.length
                  ? {
                      ids: selectedRowKeys,
                    }
                  : getParamsFromFilters(actionRef.current?.getFilters())),
              })
            }
          />
        </Permission>
      </div>
    </div>
  );

  const getClueId = () => currentIdRef.current;

  if (getViewsLoading || getFieldsLoading) {
    return <Skeleton paragraph={{ rows: 15 }} />;
  }

  return (
    <>
      <ETable
        actionRef={actionRef}
        sticky
        header={headerNode}
        columns={columns}
        rowSelection={{
          fixed: true,
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (rowKeys) => setSelectedRowKeys(rowKeys as number[]),
        }}
        views={views}
        params={{ cluePoolId }}
        rowKey="id"
        pagination={{ showSizeChanger: true, showQuickJumper: true }}
        request={async ({ current, pageSize }, filters) => {
          const res = await getList({
            pageNum: current,
            pageSize,
            cluePoolId,
            ...getParamsFromFilters(filters),
          });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <ClueDetailDrawer ref={detailDrawerRef} getId={getClueId} onDecrease={handleSuccess} />
      <AssignClueModal
        ref={assignClueModalRef}
        cluePoolId={cluePoolId}
        getId={getClueId}
        selectedRowKeys={selectedRowKeys}
        onSuccess={handleSuccess}
      />
      <TransferPoolModal
        ref={transferPoolModalRef}
        cluePoolId={cluePoolId}
        getId={getClueId}
        selectedRowKeys={selectedRowKeys}
        onSuccess={handleSuccess}
      />
    </>
  );
});

export default List;
