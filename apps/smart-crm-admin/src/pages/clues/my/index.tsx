import { useEffect, useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { ModalRef } from '@src/common/interface';
import { ETable, ExportButton, TableActions } from '@src/components';
import { ETableActionType, ETableColumn } from '@src/components/ETable';
import useTableViews from '@src/hooks/useTableViews';
import { useNoticeEventSubscription } from '@src/layout';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import {
  batchDeleteClue,
  deleteClue,
  exportClueExcel,
  getMyClueList,
} from '@src/services/clues/my';
import { ClueItemDTO } from '@src/services/clues/my/type';
import { getBusinessFieldList } from '@src/services/common';
import { BusinessTypeEnum } from '@src/services/common/type';
import {
  compatibleTableActionWidth,
  getFieldProps,
  getFilterProps,
  getParamsFromFilters,
} from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Badge, Button, Dropdown, MenuProps, Skeleton, Typography } from 'antd';
import { useSearchParams } from 'react-router-dom';
import AbandonModal from './components/AbandonModal';
import FollowUpModal from './components/FollowUpModal';
import TransferClueModal from './components/TransferClueModal';
import CallPhonePopover from '../components/CallPhonePopover';
import ClueDetailDrawer from '../components/ClueDetailDrawer';
import EditClueModal from '../components/EditClueModal';
import ImportClueButton from '../components/ImportClueButton';

const MyClues = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const checkPermission = usePermission();
  const { modal, message } = App.useApp();
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const currentIdRef = useRef<number | null>(null);
  const transferClueModalRef = useRef<ModalRef>(null);
  const abandonModalRef = useRef<ModalRef>(null);
  const followUpModalRef = useRef<ModalRef>(null);
  const actionRef = useRef<ETableActionType<ClueItemDTO>>(null);
  const detailDrawerRef = useRef<ModalRef>(null);
  const editClueModalRef = useRef<ModalRef>(null);

  const { data: fields = [], loading: getFieldsLoading } = useRequest(() =>
    getBusinessFieldList({ businessType: BusinessTypeEnum.MY_CLUE }),
  );
  const { data, runAsync: getList } = useRequest(getMyClueList, { manual: true });
  const { views, loading: getViewsLoading } = useTableViews(BusinessTypeEnum.MY_CLUE);

  // 在通知中修改了某个线索详情，列表如果有这个线索，则刷新列表
  useNoticeEventSubscription((event) => {
    if (event.type === 'clue' && data?.result.some((i) => i.id === event.payload.id)) {
      actionRef.current?.reload();
    }
  });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      currentIdRef.current = Number(id);
      detailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 进行操作后，对选中的进行处理
  const handleSuccess = (id?: number) => {
    const currentId = currentIdRef.current || id;

    // 单个操作
    if (currentId) {
      setSelectedRowKeys((prev) => prev.filter((i) => i !== currentId));
    } else {
      // 批量操作
      setSelectedRowKeys([]);
    }

    actionRef.current?.reload();
  };

  const columns: ETableColumn<ClueItemDTO>[] = [
    ...fields.map(
      (i) =>
        ({
          title: i.name,
          dataIndex: i.field,
          hidden: !i.showFlag,
          hideInFilters: !i.searchFlag,
          hideInSettings: !i.showFlag,
          fieldProps: (_, record) =>
            getFieldProps({ ...i, sensitiveValue: record[`${i.field}Sensitive`] }),
          filterProps: getFilterProps(i),
          valueType: i.valueType,
          ...(i.field === 'name'
            ? {
                ellipsis: false,
                fixed: 'left',
                onCell: () => ({
                  className: 'flex',
                }),
                render: (value, record) => (
                  <>
                    {!record.callFlag && <Badge status="error" classNames={{ root: 'mr-1' }} />}
                    <Typography.Text
                      ellipsis={{ tooltip: true }}
                      style={{ color: 'var(--ant-color-link)', cursor: 'pointer' }}
                      onClick={() => {
                        currentIdRef.current = record.id;
                        detailDrawerRef.current?.open();
                      }}
                    >
                      {value}
                    </Typography.Text>
                  </>
                ),
              }
            : {}),
        } as ETableColumn<ClueItemDTO>),
    ),
    {
      title: '操作',
      key: 'action',
      align: 'center',
      fixed: 'right',
      width: compatibleTableActionWidth(180),
      hideInFilters: true,
      hideInSettings: true,
      render: (_, { id, phone, name }) => (
        <TableActions
          shortcuts={[
            {
              label: '编辑',
              onClick: () => {
                currentIdRef.current = id;
                editClueModalRef.current?.open();
              },
            },
            {
              label: (isMobile) =>
                isMobile ? (
                  <CallPhonePopover phone={phone}>拨打电话</CallPhonePopover>
                ) : (
                  <CallPhonePopover phone={phone}>
                    <a>拨打电话</a>
                  </CallPhonePopover>
                ),
              show: checkPermission(PermissionsMap.MyCluesCall),
            },
          ]}
          moreItems={[
            {
              label: '添加跟进',
              show: checkPermission(PermissionsMap.MyCluesFollow),
              onClick: () => {
                currentIdRef.current = id;
                followUpModalRef.current?.open();
              },
            },
            {
              label: '放弃',
              show: checkPermission(PermissionsMap.MyCluesAbandon),
              onClick: () => {
                currentIdRef.current = id;
                abandonModalRef.current?.open();
              },
            },
            {
              label: '转让',
              show: checkPermission(PermissionsMap.MyCluesTransfer),
              onClick: () => {
                currentIdRef.current = id;
                transferClueModalRef.current?.open();
              },
            },
            {
              label: '删除',
              show: checkPermission(PermissionsMap.MyCluesDelete),
              danger: true,
              onClick: () =>
                modal.confirm({
                  title: '删除线索',
                  content: `确定要删除线索 “${name}” 吗？`,
                  onOk: async () => {
                    await deleteClue({ clueId: id });
                    message.success('删除成功');
                    handleSuccess();
                  },
                }),
            },
          ]}
        />
      ),
    },
  ];

  const batchMenuItems: MenuProps['items'] = [
    {
      key: 'abandon',
      label: '放弃',
      auth: PermissionsMap.MyCluesAbandon,
      onClick: () => abandonModalRef.current?.open(),
    },
    {
      key: 'transfer',
      label: '转让',
      auth: PermissionsMap.MyCluesTransfer,
      onClick: () => transferClueModalRef.current?.open(),
    },
    {
      key: 'delete',
      label: '删除',
      auth: PermissionsMap.MyCluesDelete,
      danger: true,
      onClick: () =>
        modal.confirm({
          title: '删除线索',
          content: `确定要删除选中的 ${selectedRowKeys.length} 个线索吗？`,
          onOk: async () => {
            await batchDeleteClue({ clueIds: selectedRowKeys });
            message.success('删除成功');
            handleSuccess();
          },
        }),
    },
  ].filter((i) => checkPermission(i.auth));

  const headerNode = (
    <div className="sm:flex justify-between mb-3">
      <div className="self-end mb-2 sm:mb-0 pl-2">
        共 {data?.total || 0} 条线索
        {selectedRowKeys.length > 0 && (
          <>
            ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 条
            <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
              清空选择
            </a>
          </>
        )}
      </div>
      <div className="flex justify-end gap-2">
        {batchMenuItems.length > 0 && (
          <Dropdown
            disabled={!selectedRowKeys.length}
            menu={{
              items: batchMenuItems,
              onClick: () => {
                currentIdRef.current = null;
              },
            }}
          >
            <Button type="text">
              批量操作 <CaretDownOutlined />
            </Button>
          </Dropdown>
        )}
        <Permission value={PermissionsMap.MyCluesImport}>
          <ImportClueButton fields={fields} onSuccess={() => actionRef.current?.reload()} />
        </Permission>
        <Permission value={PermissionsMap.MyCluesExport}>
          <ExportButton
            total={selectedRowKeys.length || data?.total}
            request={() =>
              exportClueExcel({
                columns: actionRef.current!.getShowFields(),
                ...(selectedRowKeys.length
                  ? { ids: selectedRowKeys }
                  : getParamsFromFilters(actionRef.current?.getFilters())),
              })
            }
          />
        </Permission>
      </div>
    </div>
  );

  const getClueId = () => currentIdRef.current;

  return (
    <>
      <PageContainer
        loading={
          (getViewsLoading || getFieldsLoading) && (
            <Skeleton paragraph={{ rows: 15 }} className="px-10" />
          )
        }
      >
        <ETable
          actionRef={actionRef}
          columns={columns}
          header={headerNode}
          sticky
          rowSelection={{
            fixed: true,
            preserveSelectedRowKeys: true,
            selectedRowKeys,
            onChange: (rowKeys) => setSelectedRowKeys(rowKeys as number[]),
          }}
          rowKey="id"
          views={views}
          pagination={{ showSizeChanger: true, showQuickJumper: true }}
          request={async ({ current, pageSize }, filters) => {
            const res = await getList({
              pageNum: current,
              pageSize,
              ...getParamsFromFilters(filters),
            });

            return {
              data: res.result,
              total: res.total,
            };
          }}
        />
      </PageContainer>

      {/* 放这外面，可能 PageContainer loading 时就要根据 url 上的 id 打开 */}
      <ClueDetailDrawer
        ref={detailDrawerRef}
        getId={getClueId}
        onUpdate={() => actionRef.current?.reload()}
        onDecrease={handleSuccess}
      />
      <TransferClueModal
        ref={transferClueModalRef}
        getId={getClueId}
        selectedRowKeys={selectedRowKeys}
        onSuccess={handleSuccess}
      />
      <AbandonModal
        ref={abandonModalRef}
        getId={getClueId}
        selectedRowKeys={selectedRowKeys}
        onSuccess={handleSuccess}
      />
      <FollowUpModal ref={followUpModalRef} getId={getClueId} />
      <EditClueModal
        ref={editClueModalRef}
        getId={getClueId}
        fields={fields}
        onSuccess={() => actionRef.current?.reload()}
      />
    </>
  );
};

export default MyClues;
