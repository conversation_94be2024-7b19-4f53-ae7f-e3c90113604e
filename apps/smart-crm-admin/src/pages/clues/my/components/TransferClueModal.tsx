import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { UserSelect } from '@src/components';
import { batchTransferClue, transferClue } from '@src/services/clues/my';
import { useRequest } from 'ahooks';
import { App, Form, Modal, ModalProps } from 'antd';

interface TransferClueModalProps extends ModalProps {
  getId: () => number | null;
  selectedRowKeys?: number[];
  onSuccess: () => void;
}

const TransferClueModal = React.forwardRef<ModalRef, TransferClueModalProps>(
  ({ getId, selectedRowKeys = [], onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);

    const clueId = getId();

    const { runAsync: transfer, loading: transferLoading } = useRequest(transferClue, {
      manual: true,
    });
    const { runAsync: batchTransfer, loading: batchTransferLoading } = useRequest(
      batchTransferClue,
      { manual: true },
    );

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const handleSave = async ({ id }: { id: number | number[] }) => {
      if (clueId) {
        await transfer({ clueId, transferUserId: id as number });
        message.success('转让成功');
      } else {
        const { successNum, failNum } = await batchTransfer({
          clueIds: selectedRowKeys,
          transferUserIds: id as number[],
        });

        if (failNum === 0) {
          message.success('转让成功');
        } else {
          message.warning(
            `已成功分配 ${successNum} 个线索，剩余 ${failNum} 个未分配，接收线索成员线索数已达上限`,
          );
        }
      }

      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        title="转让线索"
        destroyOnHidden
        confirmLoading={transferLoading || batchTransferLoading}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
        {...props}
        open={open}
      >
        <Form form={form} clearOnDestroy onFinish={handleSave}>
          <Form.Item
            label="员工"
            name="id"
            extra="员工会收到相应的提醒"
            rules={[{ required: true, message: '请选择员工' }]}
          >
            <UserSelect
              mode={clueId ? undefined : 'multiple'}
              // 多选时，数量不能大于 selectedRowKeys 的数量，且最大 20
              maxCount={clueId ? undefined : Math.min(selectedRowKeys.length, 20)}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default TransferClueModal;
