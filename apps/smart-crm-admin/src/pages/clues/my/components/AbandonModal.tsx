import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { FileUpload } from '@src/components';
import { getCluePoolPermissionList } from '@src/services/clues/clue-pool';
import { abandonClue, batchAbandonClue } from '@src/services/clues/my';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps, Select } from 'antd';

interface AbandonModalProps extends ModalProps {
  getId: () => number | null;
  selectedRowKeys?: number[];
  onSuccess: () => void;
}

const AbandonModal = React.forwardRef<ModalRef, AbandonModalProps>(
  ({ getId, selectedRowKeys = [], onSuccess, ...props }, ref) => {
    const [form] = Form.useForm();
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);

    const clueId = getId();

    const { data: cluePoolList } = useRequest(getCluePoolPermissionList, {
      ready: open,
    });
    const { runAsync: abandon, loading: abandonLoading } = useRequest(abandonClue, {
      manual: true,
    });
    const { runAsync: batchAbandon, loading: batchAbandonLoading } = useRequest(batchAbandonClue, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      if (clueId) {
        await abandon({ ...values, clueId });
      } else {
        await batchAbandon({ ...values, clueIds: selectedRowKeys });
      }

      message.success('放弃成功');
      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        title="放弃线索"
        destroyOnHidden
        confirmLoading={abandonLoading || batchAbandonLoading}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
        {...props}
        open={open}
      >
        <Form form={form} clearOnDestroy labelCol={{ span: 4 }} onFinish={handleSave}>
          <Form.Item
            label="放弃原因"
            name="info"
            rules={[
              { required: true, message: '请输入放弃原因' },
              { max: 500, message: '不能超过 500 个字符' },
            ]}
          >
            <Input.TextArea rows={3} placeholder="请输入放弃原因，最多不超过 500 个字符" />
          </Form.Item>
          <Form.Item
            label="线索池"
            name="transferCluePoolId"
            rules={[{ required: true, message: '请选择线索池' }]}
          >
            <Select
              style={{ width: 200 }}
              placeholder="请选择线索池"
              showSearch
              optionFilterProp="name"
              fieldNames={{ label: 'name', value: 'id' }}
              options={cluePoolList}
            />
          </Form.Item>
          <FileUpload />
        </Form>
      </Modal>
    );
  },
);

export default AbandonModal;
