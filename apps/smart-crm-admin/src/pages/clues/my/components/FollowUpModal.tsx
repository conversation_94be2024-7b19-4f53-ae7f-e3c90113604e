import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { FileUpload } from '@src/components';
import EditableDateTime from '@src/components/Editable/EditableDateTime';
import { addFollowClue } from '@src/services/clues/my';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps } from 'antd';
import dayjs from 'dayjs';

const getDisabledRange = (start: number, end: number) => {
  const result = [];

  for (let i = start; i < end + 1; i++) {
    result.push(i);
  }

  return result;
};

interface FollowUpModalProps extends ModalProps {
  getId: () => number | null;
  onSuccess?: () => void;
}

const FollowUpModal = React.forwardRef<ModalRef, FollowUpModalProps>(
  ({ getId, onSuccess, ...props }, ref) => {
    const [form] = Form.useForm();
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);

    const clueId = getId();

    const { runAsync, loading } = useRequest(addFollowClue, { manual: true });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      await runAsync({
        clueId,
        ...values,
      });
      message.success('添加跟进成功');
      setOpen(false);
      onSuccess?.();
    };

    return (
      <Modal
        title="添加跟进"
        destroyOnHidden
        confirmLoading={loading}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
        {...props}
        open={open}
      >
        <Form form={form} clearOnDestroy labelCol={{ span: 4 }} onFinish={handleSave}>
          <Form.Item
            label="内容"
            name="info"
            rules={[
              { required: true, message: '请输入内容' },
              { max: 500, message: '不能超过 500 个字符' },
            ]}
          >
            <Input.TextArea rows={3} placeholder="请输入内容，最多不超过 500 个字符" />
          </Form.Item>
          <EditableDateTime
            editable
            formItemProps={{
              label: '跟进时间',
              name: 'followTime',
              rules: [{ required: true, message: '请选择跟进时间' }],
            }}
            fieldProps={{
              disabledDate: (date) => date.isAfter(dayjs()),
              disabledTime: (date) => {
                const current = dayjs();
                const currentHour = current.hour();
                const currentMinute = current.minute();

                if (date?.isSame(current, 'd')) {
                  return {
                    disabledHours: () => getDisabledRange(currentHour + 1, 23),
                    disabledMinutes: (hour) =>
                      hour === currentHour ? getDisabledRange(currentMinute + 1, 59) : [],
                  };
                }

                return {};
              },
            }}
          />
          <FileUpload />
        </Form>
      </Modal>
    );
  },
);

export default FollowUpModal;
