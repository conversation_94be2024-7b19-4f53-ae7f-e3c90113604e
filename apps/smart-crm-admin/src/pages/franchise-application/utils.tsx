import { Editable } from '@src/components';
import { ETableColumn, ValueType } from '@src/components/ETable';
import { ExamAnswerDTO, ExamAnswerQuestionDTO } from '@src/services/franchise-application/type';
import { getFieldProps, getFilterProps } from '@src/utils';
import { ConfigProvider, Descriptions, Popover } from 'antd';
import { TemplateQuestionTypeEnum } from '../standard-settings/template/components/QuestionConfig/type';

export const getQuestionColumns = (questions: ExamAnswerQuestionDTO[]) =>
  questions.map((question): ETableColumn<ExamAnswerDTO> => {
    if (question.questionType === TemplateQuestionTypeEnum.COMPONENT) {
      return {
        title: question.name,
        dataIndex: question.questionKey,
        hidden: true,
        hideInFilters: true,
        render: (value) =>
          Array.isArray(value) && value.length > 0 ? (
            <Popover
              destroyOnHidden
              classNames={{
                body: 'max-w-[300px] max-h-[300px] overflow-auto !p-0',
              }}
              content={
                <ConfigProvider
                  theme={{
                    components: {
                      Descriptions: {
                        itemPaddingBottom: 6,
                      },
                    },
                  }}
                >
                  <div className="flex flex-col divide-y">
                    {value.map((valueItem, index) => (
                      <Descriptions
                        key={index}
                        layout="vertical"
                        colon={false}
                        column={1}
                        className="p-2"
                        items={question.childList?.map((child) => {
                          const questionValue = valueItem[child.questionKey];

                          return {
                            key: child.questionKey,
                            label: child.name,
                            children:
                              (!questionValue && questionValue !== 0) ||
                              (Array.isArray(questionValue) && !questionValue.length) ? (
                                '-'
                              ) : (
                                <Editable
                                  valueType={child.questionType as unknown as ValueType}
                                  initialValue={questionValue}
                                  fieldProps={getFieldProps({
                                    ...child,
                                    valueType: child.questionType,
                                    // 没传 sensitiveValue。 因为组件试题中暂时没有手机号和身份证，所以前后端都没做脱敏
                                  })}
                                />
                              ),
                          };
                        })}
                      />
                    ))}
                  </div>
                </ConfigProvider>
              }
            >
              <a className="text-center">{`共 ${value.length} 条，查看`}</a>
            </Popover>
          ) : null,
      };
    }

    return {
      title: question.name,
      dataIndex: question.questionKey,
      valueType: question.questionType as unknown as ValueType,
      hidden: true,
      // 筛选时后端需要
      transform: (value) =>
        question.questionType === TemplateQuestionTypeEnum.REGION
          ? {
              ...value,
              regionMode: question.regionMode,
              regionLevel: question.regionLevel,
            }
          : value,
      fieldProps: (_, record) =>
        getFieldProps({
          ...question,
          valueType: question.questionType,
          sensitiveValue: record[`${question.questionKey}Sensitive`],
        }),
      filterProps: getFilterProps({
        ...question,
        valueType: question.questionType,
      }),
    };
  });
