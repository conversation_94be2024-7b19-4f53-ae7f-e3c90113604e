import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { BusinessTypeEnum } from '@src/services/common/type';
import {
  getExamAnswerRecordDetail,
  getExamRecordItemQuestions,
  updateExamAnswerRecord,
} from '@src/services/franchise-application';
import { useRequest } from 'ahooks';
import { App, Button, Drawer, DrawerProps, Form, FormProps } from 'antd';
import EditableExam from './EditableExam';

interface EditFranchiseDrawerProps extends DrawerProps {
  getId: () => number | null;
  onSuccess: () => void;
}

const businessType = BusinessTypeEnum.ONLINE_APPLY;

const EditFranchiseDrawer = React.forwardRef<ModalRef, EditFranchiseDrawerProps>(
  ({ getId, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);
    const [activeKey, setActiveKey] = useState<number[]>([]);

    const id = getId()!;

    const { data, loading } = useRequest(
      () =>
        getExamAnswerRecordDetail({
          businessType,
          recordId: id,
        }),
      {
        ready: open,
        onSuccess: (res) => {
          form.setFieldsValue(res);
        },
      },
    );
    const { data: editQuestionData, loading: getEditQuestionDataLoading } = useRequest(
      () =>
        getExamRecordItemQuestions({
          businessType,
          recordId: id,
        }),
      {
        ready: open,
      },
    );
    const { runAsync: update, loading: updateLoading } = useRequest(updateExamAnswerRecord, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setActiveKey([]);
        setOpen(true);
      },
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      await update({
        businessType,
        recordId: data!.id,
        questionAnswers: Object.keys(values).map((questionKey) => ({
          questionKey,
          answer: Array.isArray(values[questionKey])
            ? JSON.stringify(values[questionKey])
            : values[questionKey],
        })),
      });
      message.success('修改成功');
      setOpen(false);
      onSuccess();
    };

    const onFinishFailed = (errorInfo: Parameters<Required<FormProps>['onFinishFailed']>[0]) => {
      const [{ name }] = errorInfo.errorFields;
      const pageId = editQuestionData?.find((item) =>
        item.questions.some((question) => question.questionKey === name[0]),
      )?.pageId as number;

      message.error('有内容校验未通过');

      // 如果校验失败的第一个问题没有展开，先展开再滚动到它的位置
      if (!activeKey.includes(pageId)) {
        setActiveKey((prev) => prev.concat(pageId));
        setTimeout(() => {
          form.scrollToField(name);
        }, 500);
      } else {
        form.scrollToField(name);
      }
    };

    const drawerLoading = loading || getEditQuestionDataLoading;

    return (
      <Drawer
        open={open}
        title="编辑加盟申请"
        destroyOnHidden
        loading={drawerLoading}
        width={500}
        drawerRender={(node) => (
          <Form
            className="h-full"
            form={form}
            layout="vertical"
            clearOnDestroy
            preserve={false}
            onFinish={handleSave}
            onFinishFailed={onFinishFailed}
          >
            {node}
          </Form>
        )}
        footer={
          !drawerLoading && (
            <div className="flex justify-end gap-2">
              <Button onClick={() => setOpen(false)}>取消</Button>
              <Button type="primary" loading={updateLoading} onClick={form.submit}>
                保存
              </Button>
            </div>
          )
        }
        onClose={() => setOpen(false)}
        {...props}
      >
        <div className="text-center mb-5 text-lg font-bold">{data?.examPaperName}</div>
        <EditableExam
          loading={getEditQuestionDataLoading}
          questionsData={editQuestionData}
          data={data}
          activeKey={activeKey}
          onActiveKeyChange={setActiveKey}
        />
      </Drawer>
    );
  },
);

export default EditFranchiseDrawer;
