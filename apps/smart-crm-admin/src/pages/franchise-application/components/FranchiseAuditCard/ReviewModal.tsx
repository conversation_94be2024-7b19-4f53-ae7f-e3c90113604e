import React from 'react';
import { UserSelect } from '@src/components';
import { reviewFranchiseTask } from '@src/services/franchise-application';
import { FranchiseRejectOrReviewReasonEnum } from '@src/services/franchise-application/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps, Select } from 'antd';

interface ReviewModalProps extends ModalProps {
  id: number;
  omitUserIds?: number[];
  onSuccess: () => void;
}

const ReviewModal: React.FC<ReviewModalProps> = ({
  id,
  open,
  omitUserIds,
  onSuccess,
  ...props
}) => {
  const [form] = Form.useForm();
  const { message } = App.useApp();

  const { runAsync: review, loading } = useRequest(reviewFranchiseTask, { manual: true });

  return (
    <Modal
      open={open}
      title="邀请复核"
      confirmLoading={loading}
      destroyOnHidden
      onOk={form.submit}
      {...props}
    >
      <Form
        form={form}
        clearOnDestroy
        labelCol={{ span: 4 }}
        onFinish={async (values) => {
          await review({
            ...values,
            recordId: id,
            reviewUserIds: [values.reviewUserIds],
          });
          onSuccess();
          message.success('邀请成功');
        }}
      >
        <Form.Item
          label="人员"
          name="reviewUserIds"
          rules={[{ required: true, message: '请选择人员' }]}
        >
          <UserSelect
            transformOptions={(options) => options.filter((i) => !omitUserIds?.includes(i.id))}
          />
        </Form.Item>
        <Form.Item
          name="reviewReasons"
          label="复核原因"
          rules={[{ required: true, message: '请选择复核原因' }]}
        >
          <Select
            mode="multiple"
            placeholder="请选择复核原因"
            optionFilterProp="label"
            options={enum2Options(FranchiseRejectOrReviewReasonEnum)}
          />
        </Form.Item>
        <Form.Item
          name="reviewDesc"
          label="复核说明"
          rules={[{ required: true, message: '请输入复核说明' }]}
        >
          <Input.TextArea autoSize maxLength={500} placeholder="请输入复核说明" showCount />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ReviewModal;
