import React from 'react';
import { FileUpload } from '@src/components';
import { approveFranchiseTask, rejectFranchiseTask } from '@src/services/franchise-application';
import {
  FranchiseRejectOrReviewReasonEnum,
  PassTypeEnum,
} from '@src/services/franchise-application/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps, Select } from 'antd';

interface AuditModalProps extends ModalProps {
  id: number;
  /** 审批类型 */
  approvalType: 'approve' | 'reject';
  showPassType: boolean;
  onSuccess: () => void;
}

const AuditModal: React.FC<AuditModalProps> = ({
  id,
  approvalType,
  showPassType,
  onSuccess,
  ...props
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();

  const { runAsync: approve, loading: approveLoading } = useRequest(approveFranchiseTask, {
    manual: true,
  });
  const { runAsync: reject, loading: rejectLoading } = useRequest(rejectFranchiseTask, {
    manual: true,
  });

  const handleSave = async (values: any) => {
    if (approvalType === 'approve') {
      await approve({
        recordId: id,
        ...values,
      });
    } else {
      await reject({
        recordId: id,
        ...values,
      });
    }

    message.success('审批成功');
    onSuccess();
  };

  return (
    <Modal
      title="审批意见"
      destroyOnHidden
      confirmLoading={approveLoading || rejectLoading}
      onOk={form.submit}
      {...props}
    >
      <Form
        form={form}
        clearOnDestroy
        labelCol={{ span: approvalType === 'approve' ? 4 : 5 }}
        onFinish={handleSave}
      >
        {showPassType && approvalType === 'approve' && (
          <Form.Item
            name="passType"
            label="通过类型"
            rules={[{ required: true, message: '请选择通过类型' }]}
          >
            <Select placeholder="请选择通过类型" options={enum2Options(PassTypeEnum)} />
          </Form.Item>
        )}
        {approvalType === 'reject' && (
          <Form.Item
            name="rejectReasons"
            label="不通过原因"
            rules={[{ required: true, message: '请选择不通过原因' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择不通过原因"
              optionFilterProp="label"
              options={enum2Options(FranchiseRejectOrReviewReasonEnum)}
            />
          </Form.Item>
        )}
        <Form.Item name="comments" label="说明">
          <Input.TextArea placeholder="请输入说明" maxLength={500} showCount />
        </Form.Item>
        <FileUpload />
      </Form>
    </Modal>
  );
};

export default AuditModal;
