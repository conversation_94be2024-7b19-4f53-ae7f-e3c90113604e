import React, { useState } from 'react';
import { AuditFlow } from '@src/components';
import { AuditStatusEnum } from '@src/services/common/type';
import { getFranchiseAuditHistory } from '@src/services/franchise-application';
import useUserStore from '@src/store/useUserStore';
import { useRequest } from 'ahooks';
import AuditModal from './AuditModal';
import ReviewModal from './ReviewModal';

interface FranchiseAuditCardProps {
  id: number;
  loading: boolean;
  auditStatus?: AuditStatusEnum;
  onSuccess: () => void;
  onTodoSuccess?: () => void;
}

export interface FranchiseAuditCardRef {
  refresh: () => void;
}

const FranchiseAuditCard = React.forwardRef<FranchiseAuditCardRef, FranchiseAuditCardProps>(
  ({ id, loading, auditStatus, onSuccess, onTodoSuccess }, ref) => {
    const [reviewModalOpen, setReviewModalOpen] = useState(false);
    const [auditModalOpen, setAuditModalOpen] = useState(false);
    const [auditModalType, setAuditModalType] = useState<'approve' | 'reject'>('approve');

    const {
      user: { shUserId },
    } = useUserStore();
    const {
      data: auditHistory = [],
      loading: getAuditHistoryLoading,
      refresh,
    } = useRequest(() => getFranchiseAuditHistory(id));

    React.useImperativeHandle(ref, () => ({
      refresh,
    }));

    const openApproveAuditModal = () => {
      setAuditModalOpen(true);
      setAuditModalType('approve');
    };

    // 是否是审批人
    const isAuditUser = !!auditHistory[auditHistory.length - 1]?.auditUserIds?.includes(shUserId);
    const isAuditStatus = auditStatus === AuditStatusEnum.AUDIT;

    return (
      <>
        <AuditFlow.OperateCard
          loading={loading || getAuditHistoryLoading}
          data={auditHistory}
          operateButtons={[
            {
              show: isAuditUser && auditStatus === AuditStatusEnum.TO_BE_REVIEWED,
              children: '确认复核',
              onClick: openApproveAuditModal,
            },
            {
              show: isAuditUser && isAuditStatus,
              children: '通过',
              onClick: openApproveAuditModal,
            },
            {
              show: isAuditUser && isAuditStatus,
              children: '拒绝',
              danger: true,
              onClick: () => {
                setAuditModalOpen(true);
                setAuditModalType('reject');
              },
            },
            {
              show: isAuditUser && isAuditStatus,
              children: '邀请复核',
              className: 'ml-auto',
              onClick: () => setReviewModalOpen(true),
            },
          ]}
        />

        <ReviewModal
          id={id}
          omitUserIds={auditHistory[auditHistory.length - 1]?.auditUserIds}
          open={reviewModalOpen}
          onSuccess={() => {
            setReviewModalOpen(false);
            refresh();
            onSuccess();
          }}
          onCancel={() => setReviewModalOpen(false)}
        />
        <AuditModal
          id={id}
          approvalType={auditModalType}
          showPassType={auditStatus === AuditStatusEnum.AUDIT}
          open={auditModalOpen}
          onSuccess={() => {
            setAuditModalOpen(false);
            refresh();
            onSuccess();
            onTodoSuccess?.();
          }}
          onCancel={() => setAuditModalOpen(false)}
        />
      </>
    );
  },
);

export default FranchiseAuditCard;
