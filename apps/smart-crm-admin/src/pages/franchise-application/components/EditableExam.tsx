import React from 'react';
import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { parserJSON } from '@pkg/utils';
import { Editable } from '@src/components';
import { ValueType } from '@src/components/ETable';
import { TemplateQuestionTypeEnum } from '@src/pages/standard-settings/template/components/QuestionConfig/type';
import { ExamAnswerQuestionDTO } from '@src/services/franchise-application/type';
import { QuestionRuleEnum } from '@src/services/visit/type';
import { getFieldProps } from '@src/utils';
import { Button, Collapse, Form, FormItemProps, SelectProps, Skeleton } from 'antd';
import { get, isEqual } from 'lodash-es';

// 单选、多选、特殊处理
function getQuestionFieldProps(
  item: ExamAnswerQuestionDTO & Partial<Parameters<typeof getFieldProps>[0]>,
) {
  if ([ValueType.SINGLE, ValueType.MULTIPLE].includes(item.questionType as any)) {
    return {
      // 已经删除的选项不显示
      options: item.options?.filter((i) => !i.deleted),
      // 已经删除的选项回显
      labelRender: (props) =>
        props.label || item.options?.find((i) => i.value === props.value)?.label,
    } as SelectProps;
  }

  return getFieldProps({ ...item, valueType: item.questionType });
}

interface EditableExamProps {
  activeKey: number[];
  onActiveKeyChange: (activeKey: number[]) => void;
  loading?: boolean;
  data?: Record<string, any>;
  questionsData?:
    | {
        pageId: number;
        pageName: string;
        questions: ExamAnswerQuestionDTO[];
      }[];
}

const EditableExam: React.FC<EditableExamProps> = ({
  loading,
  data,
  questionsData,
  activeKey,
  onActiveKeyChange,
}) => {
  if (loading) {
    return <Skeleton title={false} active />;
  }

  if (!questionsData?.length) {
    return null;
  }

  function getFormItemNode(
    question: ExamAnswerQuestionDTO,
    questions: ExamAnswerQuestionDTO[],
    meta?: {
      children?: React.ReactNode;
      formItemProps?: FormItemProps;
      formListMeta?: {
        fieldQuestionKey: string;
        fieldName: string | number;
        fieldIndex: number;
      };
    },
  ) {
    const { children, formItemProps, formListMeta } = meta || {};

    const formItemNode = children || (
      <Editable
        key={question.questionKey}
        editable
        valueType={question.questionType as unknown as ValueType}
        fieldProps={getQuestionFieldProps({
          ...question,
          ...(formListMeta
            ? {
                // 暂时无效，因为组件试题中暂时没有手机号和身份证，所以前后端都没做脱敏
                encryptSensitiveMap: (
                  (data?.[formListMeta.fieldQuestionKey] || []) as Record<string, any>[]
                ).reduce((total, cur) => {
                  total[cur[question.questionKey]] = cur[`${question.questionKey}Sensitive`];

                  return total;
                }, {}),
              }
            : {
                sensitiveValue: data?.[`${question.questionKey}Sensitive`],
              }),
        })}
        formItemProps={{
          labelCol: {
            span: 24,
          },
          tooltip: question.description,
          label: question.name,
          name: question.questionKey,
          rules: [
            ...(question.notNull ? [{ required: true, message: '必填' }] : []),
            ...(question.questionType === TemplateQuestionTypeEnum.TEXT
              ? [{ max: 1000, message: '不能超过 1000 个字符' }]
              : []),
          ],
          ...formItemProps,
        }}
      />
    );
    const ruleInfo: {
      rule: QuestionRuleEnum;
      options?: string[];
      questionKey: string;
    }[] = parserJSON(question.ruleInfo) || [];

    if (ruleInfo.length > 0) {
      return (
        <Form.Item
          key={question.questionKey}
          noStyle
          shouldUpdate={(prev, next) =>
            formListMeta
              ? ruleInfo.some(
                  (info) =>
                    !isEqual(
                      get(prev, [
                        formListMeta.fieldQuestionKey,
                        formListMeta.fieldIndex,
                        info.questionKey,
                      ]),
                      get(next, [
                        formListMeta.fieldQuestionKey,
                        formListMeta.fieldIndex,
                        info.questionKey,
                      ]),
                    ),
                )
              : ruleInfo.some((info) => prev[info.questionKey] !== next[info.questionKey])
          }
        >
          {({ getFieldValue }) => {
            const show = ruleInfo.some((info) => {
              // 依赖的题目的值
              const depQuestionValue = getFieldValue(
                formListMeta
                  ? [formListMeta.fieldQuestionKey, formListMeta.fieldName, info.questionKey]
                  : info.questionKey,
              );

              if (info.rule === QuestionRuleEnum.IN) {
                const isMultiple =
                  questions.find((i) => i.questionKey === info.questionKey)?.questionType ===
                  TemplateQuestionTypeEnum.MULTIPLE;

                if (isMultiple) {
                  return info.options?.some((option) => depQuestionValue?.includes(option));
                }

                return info.options?.includes(depQuestionValue);
              }

              // NOT_NULL，不为空
              return ['', null, undefined, []].every(
                (emptyValue) => !isEqual(depQuestionValue, emptyValue),
              );
            });

            return show && formItemNode;
          }}
        </Form.Item>
      );
    }

    return formItemNode;
  }

  function getComponentQuestionFormItemNode(
    question: ExamAnswerQuestionDTO,
    questions: ExamAnswerQuestionDTO[],
  ) {
    return getFormItemNode(question, questions, {
      children: (
        <Form.Item label={question.name} required={question.notNull}>
          <Form.List
            name={question.questionKey}
            rules={[
              {
                validator: (_, value) => {
                  if (question.notNull && (!value || (Array.isArray(value) && !value.length))) {
                    return Promise.reject(new Error(`${question.name}不能为空`));
                  }

                  return Promise.resolve();
                },
              },
            ]}
          >
            {(fields, { add, remove }, { errors }) => (
              // 添加 id 用来 Form.List 校验错误自动滚动
              <div id={question.questionKey}>
                {fields.length > 0 && (
                  <div className="flex flex-col border rounded-lg divide-y">
                    {fields.map(({ name, key }, fieldIndex) => (
                      <div key={key} className="relative px-5 pt-3">
                        <Button
                          shape="circle"
                          size="small"
                          icon={<MinusOutlined />}
                          className="absolute -top-3 -right-3"
                          onClick={() => remove(name)}
                        />
                        {question.childList?.map((child) => (
                          <React.Fragment key={child.questionKey}>
                            {getFormItemNode(child, question.childList!, {
                              formItemProps: {
                                name: [name, child.questionKey],
                              },
                              formListMeta: {
                                fieldQuestionKey: question.questionKey,
                                fieldName: name,
                                fieldIndex,
                              },
                            })}
                          </React.Fragment>
                        ))}
                      </div>
                    ))}
                  </div>
                )}
                <Form.ErrorList errors={errors} />
                <Button
                  type="dashed"
                  block
                  icon={<PlusOutlined />}
                  className="mt-4"
                  onClick={() => add()}
                >
                  添加
                </Button>
              </div>
            )}
          </Form.List>
        </Form.Item>
      ),
    });
  }

  return (
    <Collapse
      activeKey={activeKey}
      onChange={(keys) => onActiveKeyChange(keys as unknown as number[])}
      items={questionsData?.map((item) => ({
        key: item.pageId,
        label: item.pageName,
        forceRender: true,
        children: item.questions.map((question) => {
          if (question.questionType === TemplateQuestionTypeEnum.COMPONENT) {
            return getComponentQuestionFormItemNode(question, item.questions);
          }

          return getFormItemNode(question, item.questions);
        }),
      }))}
    />
  );
};

export default EditableExam;
