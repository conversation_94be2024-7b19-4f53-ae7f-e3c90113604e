import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { UserSelect } from '@src/components';
import { BusinessTypeEnum } from '@src/services/common/type';
import { batchTransferFranchise, transferFranchise } from '@src/services/franchise-application';
import { useRequest } from 'ahooks';
import { App, Form, Modal, ModalProps } from 'antd';

interface TransferFranchiseModalProps extends ModalProps {
  getId: () => number | null;
  selectedRowKeys?: number[];
  onSuccess: () => void;
}

const TransferFranchiseModal = React.forwardRef<ModalRef, TransferFranchiseModalProps>(
  ({ getId, selectedRowKeys = [], onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);

    const id = getId();

    const { runAsync: transfer, loading: transferLoading } = useRequest(transferFranchise, {
      manual: true,
    });
    const { runAsync: batchTransfer, loading: batchTransferLoading } = useRequest(
      batchTransferFranchise,
      { manual: true },
    );

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const handleSave = async (values: { userId: number }) => {
      if (id) {
        await transfer({ id, businessType: BusinessTypeEnum.ONLINE_APPLY, ...values });
        message.success('转让成功');
      } else {
        await batchTransfer({
          ids: selectedRowKeys,
          businessType: BusinessTypeEnum.ONLINE_APPLY,
          ...values,
        });
        message.success('转让成功');
      }

      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        title="转让加盟申请"
        destroyOnHidden
        confirmLoading={transferLoading || batchTransferLoading}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
        {...props}
        open={open}
      >
        <Form form={form} clearOnDestroy onFinish={handleSave}>
          <Form.Item
            label="员工"
            name="userId"
            extra="员工会收到相应的提醒"
            rules={[{ required: true, message: '请选择员工' }]}
          >
            <UserSelect />
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default TransferFranchiseModal;
