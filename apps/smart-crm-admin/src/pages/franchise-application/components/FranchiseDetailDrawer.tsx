import React, { useRef, useState } from 'react';
import { franchiseAuditStatusOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { AuditFlow, ButtonGroup } from '@src/components';
import EditableMultiple from '@src/components/Editable/EditableMultiple';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import BusinessDetailDrawer from '@src/pages/business-opportunity/components/BusinessDetailDrawer';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import { PermissionsMap, usePermission } from '@src/permissions';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { AuditStatusEnum, BusinessTypeEnum } from '@src/services/common/type';
import {
  getExamAnswerRecordDetail,
  getExamRecordItemQuestions,
  getFranchiseAuditAllHistory,
  reApproval,
} from '@src/services/franchise-application';
import {
  ExamAnswerDTO,
  FranchiseAutoAuditStatusEnum,
  FranchiseRejectOrReviewReasonEnum,
  PassTypeEnum,
} from '@src/services/franchise-application/type';
import useUserStore from '@src/store/useUserStore';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Card, Col, Drawer, DrawerProps, Row, Tabs } from 'antd';
import EditFranchiseDrawer from './EditFranchiseDrawer';
import ExamRecord from './ExamRecord';
import FranchiseAuditCard, { FranchiseAuditCardRef } from './FranchiseAuditCard';
import TransferFranchiseModal from './TransferFranchiseModal';

interface FranchiseDetailDrawerProps extends DrawerProps {
  getId: () => number | null;
  onUpdate?: () => void;
  onTodoSuccess?: () => void;
}

const businessType = BusinessTypeEnum.ONLINE_APPLY;

const FranchiseDetailDrawer = React.forwardRef<ModalRef, FranchiseDetailDrawerProps>(
  ({ getId, onUpdate, onTodoSuccess, ...drawerProps }, ref) => {
    const { modal, message } = App.useApp();
    const [open, setOpen] = useState(false);
    const customerDetailDrawerRef = useRef<ModalRef>(null);
    const businessOpportunityDrawerRef = useRef<ModalRef>(null);
    const transferFranchiseModalRef = useRef<ModalRef>(null);
    const editFranchiseDrawerRef = useRef<ModalRef>(null);
    const auditCardRef = useRef<FranchiseAuditCardRef>(null);

    const id = getId()!;

    const {
      user: { shUserId },
    } = useUserStore();
    const checkPermission = usePermission();
    const { data: users } = useAllUsers({ ready: open });
    const { data: questionData, loading: getQuestionDataLoading } = useRequest(
      () => getExamRecordItemQuestions({ businessType, recordId: id }),
      {
        ready: open,
      },
    );
    const { data, loading, refresh } = useRequest(
      () =>
        getExamAnswerRecordDetail({
          businessType,
          recordId: id,
        }),
      { ready: open },
    );

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const baseInfoItems: DescriptionFieldType<ExamAnswerDTO>[] = [
      {
        label: '申请表编号',
        field: 'code',
      },
      {
        label: '负责人',
        field: 'directUserId',
        valueType: ValueType.PERSON,
      },
      {
        label: '商机名称',
        field: 'businessOpportunityName',
        children: (
          <a onClick={() => businessOpportunityDrawerRef.current?.open()}>
            {data?.businessOpportunityName}
          </a>
        ),
      },
      {
        label: '商机负责人',
        field: 'businessOpportunityDirectUserId',
        valueType: ValueType.PERSON,
      },
      {
        label: '审核状态',
        field: 'auditStatus',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: franchiseAuditStatusOptions,
        },
      },
      {
        label: '通过类型',
        field: 'passType',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(PassTypeEnum),
        },
      },
      {
        label: '复核原因',
        field: 'reviewReasons',
        valueType: ValueType.MULTIPLE,
        fieldProps: {
          options: enum2Options(FranchiseRejectOrReviewReasonEnum),
        },
      },
      {
        label: '未通过原因',
        field: 'rejectReasons',
        valueType: ValueType.MULTIPLE,
        fieldProps: {
          options: enum2Options(FranchiseRejectOrReviewReasonEnum),
        },
      },
      {
        label: '所属大区',
        field: 'belongWarZone',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(BelongWarZoneEnum),
        },
      },
      {
        label: '所属省份',
        field: 'belongProvince',
      },
      {
        label: '自动审核状态',
        field: 'autoAuditStatus',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(FranchiseAutoAuditStatusEnum),
        },
      },
      {
        label: '自动审核未通过原因',
        field: 'autoAuditReason',
        children: (
          <EditableMultiple
            value={[...new Set(data?.autoAuditReason)]}
            fieldProps={{ options: enum2Options(FranchiseRejectOrReviewReasonEnum) }}
          />
        ),
      },
    ];

    const isDirectUser = shUserId === data?.directUserId;

    return (
      <Drawer
        open={open}
        title="加盟申请详情"
        width={1200}
        destroyOnHidden
        push={false}
        onClose={() => setOpen(false)}
        {...drawerProps}
      >
        <Card loading={loading}>
          <div className="flex justify-between">
            <div className="flex flex-col gap-2">
              <p>
                客户名称：
                <a onClick={() => customerDetailDrawerRef.current?.open()}>{data?.customerName}</a>
              </p>
              <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
              <p>创建时间：{data?.createTime}</p>
              <p>更新人：{users?.find((i) => i.id === data?.updateUserId)?.name}</p>
              <p>更新时间：{data?.updateTime}</p>
            </div>
            <ButtonGroup
              items={[
                {
                  label: '编辑',
                  show:
                    isDirectUser || checkPermission(PermissionsMap.FranchiseApplicationSuperEdit),
                  onClick: () => editFranchiseDrawerRef.current?.open(),
                },
                {
                  label: '转让',
                  show: isDirectUser,
                  onClick: () => transferFranchiseModalRef.current?.open(),
                },
                {
                  label: '发起审批',
                  show:
                    data?.auditStatus === AuditStatusEnum.NO_PASS && shUserId === data.directUserId,
                  onClick: () =>
                    modal.confirm({
                      title: '发起审批',
                      content: '确定要重新发起审批吗？',
                      onOk: async () => {
                        await reApproval(id);
                        message.success('操作成功');
                        refresh();
                        auditCardRef.current?.refresh();
                        onUpdate?.();
                        onTodoSuccess?.();
                      },
                    }),
                },
              ]}
            />
          </div>
        </Card>
        <Row gutter={[20, 20]} className="mt-5">
          <Col span={24} xl={16}>
            <Card loading={loading}>
              <Tabs
                className="-mt-5"
                items={[
                  {
                    label: '详细资料',
                    key: 'info',
                    children: (
                      <>
                        {renderDescriptions(baseInfoItems, data)}
                        <ExamRecord
                          data={data}
                          questionData={questionData}
                          loading={getQuestionDataLoading}
                        />
                      </>
                    ),
                  },
                  {
                    label: '审批记录',
                    key: 'audit-history',
                    children: <AuditFlow.Record request={() => getFranchiseAuditAllHistory(id)} />,
                  },
                ]}
              />
            </Card>
          </Col>
          <Col span={24} xl={8}>
            <FranchiseAuditCard
              ref={auditCardRef}
              loading={loading}
              id={id}
              auditStatus={data?.auditStatus}
              onSuccess={() => {
                refresh();
                onUpdate?.();
              }}
              onTodoSuccess={onTodoSuccess}
            />
          </Col>
        </Row>

        <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => data?.customerId} />
        <BusinessDetailDrawer
          ref={businessOpportunityDrawerRef}
          getId={() => data?.businessOpportunityId}
        />
        <TransferFranchiseModal
          ref={transferFranchiseModalRef}
          getId={() => id}
          onSuccess={() => {
            setOpen(false);
            onUpdate?.();
          }}
        />
        <EditFranchiseDrawer
          ref={editFranchiseDrawerRef}
          getId={() => id}
          onSuccess={() => {
            refresh();
            onUpdate?.();
          }}
        />
      </Drawer>
    );
  },
);

export default FranchiseDetailDrawer;
