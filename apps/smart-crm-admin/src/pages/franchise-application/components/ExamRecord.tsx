import React, { useMemo, useState } from 'react';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Editable } from '@src/components';
import { ValueType } from '@src/components/ETable';
import { TemplateQuestionTypeEnum } from '@src/pages/standard-settings/template/components/QuestionConfig/type';
import { ExamAnswerDTO, ExamAnswerQuestionDTO } from '@src/services/franchise-application/type';
import { getFieldProps } from '@src/utils';
import { Collapse, ConfigProvider, Descriptions, Empty, Segmented, Skeleton, Tooltip } from 'antd';

interface ExamRecordProps {
  loading: boolean;
  data?: ExamAnswerDTO;
  questionData?: {
    pageId: number;
    pageName: string;
    questions: ExamAnswerQuestionDTO[];
  }[];
}

const ExamRecord: React.FC<ExamRecordProps> = ({ data, loading, questionData }) => {
  const [showType, setShowType] = useState<'all' | 'done'>('all');

  const questionDataByType = useMemo(() => {
    if (showType === 'all') {
      return questionData;
    }

    return questionData?.reduce<(typeof questionData)[number][]>((total, item) => {
      const questions = item.questions.filter((question) => {
        const value = data?.[question.questionKey];

        if (Array.isArray(value)) {
          return value.length > 0;
        }

        return !!value && value !== 0;
      });

      if (questions.length > 0) {
        return total.concat({ ...item, questions });
      }

      return total;
    }, []);
  }, [data, questionData, showType]);

  const getChildren = (question: ExamAnswerQuestionDTO) => {
    const questionValue = data?.[question.questionKey];

    if (
      (!questionValue && questionValue !== 0) ||
      (Array.isArray(questionValue) && !questionValue.length)
    ) {
      return '-';
    } else {
      if (question.questionType === TemplateQuestionTypeEnum.COMPONENT) {
        if (Array.isArray(questionValue) && questionValue.length > 0) {
          return (
            <div className="flex flex-col gap-2">
              {questionValue.map((valItem, index) => (
                <Descriptions
                  key={index}
                  className="p-2 rounded-lg bg-gray-50"
                  column={1}
                  items={question.childList?.map((child) => {
                    const childValue = valItem[child.questionKey];

                    return {
                      label: (
                        <div className="flex items-center">
                          {child.name}
                          {child.description && (
                            <Tooltip title={child.description}>
                              <InfoCircleOutlined className="cursor-pointer ml-1" />
                            </Tooltip>
                          )}
                        </div>
                      ),
                      key: child.questionKey,
                      children:
                        (!childValue && childValue !== 0) ||
                        (Array.isArray(childValue) && !childValue.length) ? (
                          '-'
                        ) : (
                          <Editable
                            initialValue={childValue}
                            valueType={child.questionType as unknown as ValueType}
                            fieldProps={getFieldProps({
                              ...child,
                              valueType: child.questionType,
                              sensitiveValue: valItem[`${child.questionKey}Sensitive`],
                            })}
                          />
                        ),
                    };
                  })}
                />
              ))}
            </div>
          );
        }
      } else {
        return (
          <Editable
            initialValue={questionValue}
            valueType={question.questionType as unknown as ValueType}
            fieldProps={getFieldProps({
              ...question,
              valueType: question.questionType,
              sensitiveValue: data?.[`${question.questionKey}Sensitive`],
            })}
          />
        );
      }
    }
  };

  return (
    <>
      <div className="flex justify-between items-start bg-white sticky -top-6 py-2 z-10">
        <p className="text-base font-bold">试题记录（{data?.examPaperName}）</p>
        {(questionData || []).length > 0 && (
          <Segmented
            value={showType}
            options={[
              { label: '全部', value: 'all' },
              { label: '只看已填', value: 'done' },
            ]}
            onChange={setShowType}
          />
        )}
      </div>
      {loading ? (
        <Skeleton title={false} paragraph={{ rows: 5 }} className="mt-2" />
      ) : !questionDataByType?.length ? (
        <Empty />
      ) : (
        <ConfigProvider
          theme={{
            components: {
              Collapse: {
                contentPadding: '8px 16px',
              },
              Descriptions: {
                itemPaddingBottom: 6,
              },
            },
          }}
        >
          <Collapse
            defaultActiveKey={questionDataByType.map((i) => i.pageId)}
            items={questionDataByType.map((item) => ({
              label: item.pageName,
              key: item.pageId,
              children: (
                <Descriptions
                  layout="vertical"
                  colon={false}
                  column={1}
                  items={item.questions.map((question) => ({
                    label: (
                      <div className="flex items-center">
                        {question.name}
                        {question.description && (
                          <Tooltip title={question.description}>
                            <InfoCircleOutlined className="cursor-pointer ml-1" />
                          </Tooltip>
                        )}
                      </div>
                    ),
                    key: question.questionKey,
                    children: getChildren(question),
                  }))}
                />
              ),
            }))}
          />
        </ConfigProvider>
      )}
    </>
  );
};

export default ExamRecord;
