import { useEffect, useMemo, useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { franchiseAuditStatusOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ETable, ExportButton, TableActions } from '@src/components';
import { ETableActionType, ETableColumn, ValueType } from '@src/components/ETable';
import useDistricts from '@src/hooks/useDistricts';
import useTableViews from '@src/hooks/useTableViews';
import { useNoticeEventSubscription } from '@src/layout';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { BusinessTypeEnum } from '@src/services/common/type';
import { StoreCategoryEnum } from '@src/services/contract/formal/type';
import {
  exportExamAnswerRecord,
  getExamAnswerList,
  getExamAnswerQuestions,
} from '@src/services/franchise-application';
import {
  ExamAnswerDTO,
  FranchiseAutoAuditStatusEnum,
  FranchiseRejectOrReviewReasonEnum,
  PassTypeEnum,
} from '@src/services/franchise-application/type';
import useUserStore from '@src/store/useUserStore';
import { compatibleTableActionWidth, enum2Options, getParamsFromFilters } from '@src/utils';
import { useRequest } from 'ahooks';
import { Button, Dropdown, Skeleton } from 'antd';
import { isEqual } from 'lodash-es';
import { useSearchParams } from 'react-router-dom';
import EditFranchiseDrawer from './components/EditFranchiseDrawer';
import FranchiseDetailDrawer from './components/FranchiseDetailDrawer';
import TransferFranchiseModal from './components/TransferFranchiseModal';
import { getQuestionColumns } from './utils';
import BusinessDetailDrawer from '../business-opportunity/components/BusinessDetailDrawer';
import CustomerDetailDrawer from '../customers/components/CustomerDetailDrawer';

const businessType = BusinessTypeEnum.ONLINE_APPLY;

const FranchiseApplication = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const actionRef = useRef<ETableActionType>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const currentIdRef = useRef<number | null>(null);
  const customerIdRef = useRef<number | null>(null);
  const businessDetailDrawerRef = useRef<ModalRef>(null);
  const businessIdRef = useRef<number | null>(null);
  const transferFranchiseModalRef = useRef<ModalRef>(null);
  const franchiseDetailDrawerRef = useRef<ModalRef>(null);
  const editFranchiseDrawerRef = useRef<ModalRef>(null);

  const checkPermission = usePermission();
  const {
    user: { shUserId },
  } = useUserStore();
  const { data: questions = [], loading: getQuestionsLoading } = useRequest(() =>
    getExamAnswerQuestions(BusinessTypeEnum.ONLINE_APPLY),
  );
  const { data, runAsync: getList } = useRequest(getExamAnswerList, { manual: true });
  const { views, loading: getViewsLoading } = useTableViews(BusinessTypeEnum.ONLINE_APPLY);
  const { data: districts } = useDistricts();

  // 在通知中修改了某个加盟申请详情，列表如果有这个加盟申请，则刷新列表
  useNoticeEventSubscription((event) => {
    if (
      event.type === 'franchise-application' &&
      data?.result.some((i) => i.id === event.payload.id)
    ) {
      actionRef.current?.reload();
    }
  });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      currentIdRef.current = Number(id);
      franchiseDetailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 广东拆成两个
  const belongProvinceOptions = useMemo(() => {
    const options = (districts || [])
      .filter((i) => i.parentId === 0 && i.code !== '44')
      .map((i) => ({ label: i.name, value: i.name }))
      .concat([
        {
          label: '广东省（潮汕）',
          value: '广东省（潮汕）',
        },
        {
          label: '广东省（粤西）',
          value: '广东省（粤西）',
        },
      ]);

    return options;
  }, [districts]);

  const franchiseRejectOrReviewReasonOptions = enum2Options(FranchiseRejectOrReviewReasonEnum);

  const columns: ETableColumn<ExamAnswerDTO>[] = [
    {
      title: '申请表编号',
      dataIndex: 'code',
      fixed: 'left',
      valueType: ValueType.TEXT,
      render: (value, { id }) => (
        <a
          onClick={() => {
            currentIdRef.current = id;
            franchiseDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      valueType: ValueType.TEXT,
      render: (value, { customerId }) => (
        <a
          onClick={() => {
            customerIdRef.current = customerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '客户性质',
      dataIndex: 'customerNature',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(StoreCategoryEnum),
      },
      filterProps: {
        options: enum2Options(StoreCategoryEnum),
      },
    },
    {
      title: '商机名称',
      dataIndex: 'businessOpportunityName',
      valueType: ValueType.TEXT,
      render: (value, { businessOpportunityId }) => (
        <a
          onClick={() => {
            businessIdRef.current = businessOpportunityId;
            businessDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '商机编号',
      dataIndex: 'businessOpportunityCode',
      valueType: ValueType.TEXT,
    },
    {
      title: '商机负责人',
      dataIndex: 'businessOpportunityDirectUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: franchiseAuditStatusOptions,
      },
      filterProps: {
        options: franchiseAuditStatusOptions,
      },
    },
    {
      title: '通过类型',
      dataIndex: 'passType',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(PassTypeEnum),
      },
      filterProps: {
        options: enum2Options(PassTypeEnum),
      },
    },
    {
      title: '复核原因',
      dataIndex: 'reviewReasons',
      valueType: ValueType.MULTIPLE,
      fieldProps: {
        options: franchiseRejectOrReviewReasonOptions,
      },
      filterProps: {
        options: franchiseRejectOrReviewReasonOptions,
      },
    },
    {
      title: '未通过原因',
      dataIndex: 'rejectReasons',
      valueType: ValueType.MULTIPLE,
      fieldProps: {
        options: franchiseRejectOrReviewReasonOptions,
      },
      filterProps: {
        options: franchiseRejectOrReviewReasonOptions,
      },
    },
    {
      title: '负责人',
      dataIndex: 'directUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '所属大区',
      dataIndex: 'belongWarZone',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(BelongWarZoneEnum),
      },
      filterProps: {
        options: enum2Options(BelongWarZoneEnum),
      },
    },
    {
      title: '所属省份',
      dataIndex: 'belongProvince',
      valueType: (type) => (type === 'filter' ? ValueType.SINGLE : ValueType.TEXT),
      filterProps: {
        options: belongProvinceOptions,
      },
    },
    {
      title: '自动审核状态',
      dataIndex: 'autoAuditStatus',
      valueType: ValueType.SINGLE,
      hidden: true,
      fieldProps: {
        options: enum2Options(FranchiseAutoAuditStatusEnum),
      },
      filterProps: {
        options: enum2Options(FranchiseAutoAuditStatusEnum),
      },
    },
    {
      title: '自动审核未通过原因',
      dataIndex: 'autoAuditReason',
      valueType: ValueType.MULTIPLE,
      hidden: true,
      // 去重，可能有重复的
      valueFormatter: (value) => [...new Set(value)],
      fieldProps: {
        options: franchiseRejectOrReviewReasonOptions,
      },
      filterProps: {
        options: franchiseRejectOrReviewReasonOptions,
      },
    },
    {
      title: '更新人',
      dataIndex: 'updateUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: ValueType.DATE_TIME,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: ValueType.DATE_TIME,
    },
    ...getQuestionColumns(questions),
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      key: 'action',
      width: compatibleTableActionWidth(100),
      hideInFilters: true,
      hideInSettings: true,
      shouldCellUpdate: (prev, next) => !isEqual(prev, next),
      render: (_, { id, directUserId }) => {
        const isDirectUser = directUserId === shUserId;
        const showEdit =
          isDirectUser || checkPermission(PermissionsMap.FranchiseApplicationSuperEdit);

        return (
          <TableActions
            shortcuts={[
              {
                label: '编辑',
                show: showEdit,
                onClick: () => {
                  currentIdRef.current = id;
                  editFranchiseDrawerRef.current?.open();
                },
              },
              {
                label: '转让',
                show: isDirectUser,
                onClick: () => {
                  currentIdRef.current = id;
                  transferFranchiseModalRef.current?.open();
                },
              },
            ]}
          />
        );
      },
    },
  ];

  const headerNode = (
    <div className="sm:flex justify-between mb-3">
      <div className="self-end mb-2 sm:mb-0 pl-2">
        共 {data?.total || 0} 条记录{' '}
        {selectedRowKeys.length > 0 && (
          <>
            ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 条
            <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
              清空选择
            </a>
          </>
        )}
      </div>
      <div className="flex justify-end gap-2">
        <Dropdown
          disabled={!selectedRowKeys.length}
          menu={{
            items: [
              {
                label: '转让',
                key: 'transfer',
                onClick: () => {
                  currentIdRef.current = null;
                  transferFranchiseModalRef.current?.open();
                },
              },
            ],
          }}
        >
          <Button type="text">
            批量操作 <CaretDownOutlined />
          </Button>
        </Dropdown>
        <Permission value={PermissionsMap.FranchiseApplicationExport}>
          <ExportButton
            total={selectedRowKeys.length || data?.total}
            request={() =>
              exportExamAnswerRecord({
                businessType,
                columns: actionRef.current!.getShowFields(),
                ...(selectedRowKeys.length
                  ? { ids: selectedRowKeys }
                  : getParamsFromFilters(actionRef.current?.getFilters())),
              })
            }
          />
        </Permission>
      </div>
    </div>
  );

  return (
    <>
      <PageContainer
        loading={
          (getViewsLoading || getQuestionsLoading) && (
            <Skeleton paragraph={{ rows: 15 }} className="px-10" />
          )
        }
      >
        <ETable
          actionRef={actionRef}
          header={headerNode}
          columns={columns}
          rowKey="id"
          sticky
          pagination={{ showSizeChanger: true, showQuickJumper: true }}
          rowSelection={{
            fixed: true,
            preserveSelectedRowKeys: true,
            selectedRowKeys,
            onChange: (keys) => setSelectedRowKeys(keys as number[]),
          }}
          views={views}
          request={async ({ current, pageSize }, filters) => {
            const res = await getList({
              businessType,
              pageNum: current,
              pageSize,
              ...getParamsFromFilters(filters),
            });

            return {
              data: res.result,
              total: res.total,
            };
          }}
        />
      </PageContainer>

      <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />
      <BusinessDetailDrawer ref={businessDetailDrawerRef} getId={() => businessIdRef.current} />
      <TransferFranchiseModal
        ref={transferFranchiseModalRef}
        getId={() => currentIdRef.current}
        selectedRowKeys={selectedRowKeys}
        onSuccess={() => actionRef.current?.reload()}
      />
      <FranchiseDetailDrawer
        ref={franchiseDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
      />
      <EditFranchiseDrawer
        ref={editFranchiseDrawerRef}
        getId={() => currentIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
    </>
  );
};

export default FranchiseApplication;
