import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { batchUpdateCustomerScore } from '@src/services/customers';
import { CustomerScoreLevelEnum } from '@src/services/customers/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, InputNumber, Modal, ModalProps, Select } from 'antd';

interface UpdateCustomerScoreModalProps extends ModalProps {
  selectedRowKeys: number[];
  onSuccess: () => void;
}

const UpdateCustomerScoreModal = React.forwardRef<ModalRef, UpdateCustomerScoreModalProps>(
  ({ selectedRowKeys, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();

    const { runAsync: update, loading } = useRequest(batchUpdateCustomerScore, { manual: true });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      await update({ ...values, ids: selectedRowKeys });
      message.success('更新成功');
      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        title="更新评分评级"
        open={open}
        destroyOnHidden
        confirmLoading={loading}
        modalRender={(node) => (
          <Form form={form} clearOnDestroy onFinish={handleSave}>
            {node}
          </Form>
        )}
        onCancel={() => setOpen(false)}
        onOk={form.submit}
        {...props}
      >
        <Form.Item
          name="customerLevel"
          label="客户评级"
          rules={[{ required: true, message: '请选择客户评级' }]}
        >
          <Select placeholder="请选择客户评级" options={enum2Options(CustomerScoreLevelEnum)} />
        </Form.Item>
        <Form.Item
          name="customerScore"
          label="客户评分"
          rules={[{ required: true, message: '请输入客户评分' }]}
        >
          <InputNumber min={0} max={100} placeholder="请输入" precision={2} />
        </Form.Item>
      </Modal>
    );
  },
);

export default UpdateCustomerScoreModal;
