import React, { useState } from 'react';
import { CloseCircleFilled, PlusOutlined } from '@ant-design/icons';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { Encrypt, FileUpload, ImportFileFormItem } from '@src/components';
import EditableDate from '@src/components/Editable/EditableDate';
import EditableRegion from '@src/components/Editable/EditableRegion';
import { GenderEnum } from '@src/services/clues/my/type';
import { StoreCategoryEnum } from '@src/services/contract/formal/type';
import {
  checkCustomerIdentityCard,
  checkCustomerPhone,
  createCustomer,
  downloadCustomerExcelTemplate,
  getCustomerDetail,
  importCustomerExcel,
  updateCustomerField,
} from '@src/services/customers';
import {
  CustomerDTO,
  CustomerScoreLevelEnum,
  CustomerScoreSourceTypeEnum,
  HighestEducationLevelEnum,
  UpdateCustomerFieldReq,
} from '@src/services/customers/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Form, Input, InputNumber, Modal, ModalProps, Select, Tabs } from 'antd';

interface CreateEditCustomerModalProps extends ModalProps {
  getId: () => number | null;
  onSuccess: (info?: CustomerDTO) => void;
}

const CreateEditCustomerModal = React.forwardRef<ModalRef, CreateEditCustomerModalProps>(
  ({ getId, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);
    const [activeKey, setActiveKey] = useState<'manual' | 'excel'>('manual');
    const [availablePhones, setAvailablePhones] = useState<Record<string, boolean>>({});
    // 身份证号是否重复
    const [isIdentityCardRepeat, setIsIdentityCardRepeat] = useState(false);
    // 是否需要身份证号重复
    const [shouldValidateIdentityCard, setShouldValidateIdentityCard] = useState(true);

    const customerId = getId();
    const isEdit = !!customerId;

    const { showImportPrompt } = ImportFileFormItem.usePrompt();
    const { data, loading, mutate } = useRequest(() => getCustomerDetail(customerId!), {
      ready: open && isEdit,
      onSuccess: (res) => {
        const phones: Record<string, boolean> = {};

        res.phones?.forEach((i) => {
          phones[i] = true;
        });
        setAvailablePhones(phones);
        form.setFieldsValue(res);

        if (res.realAuthFlag) {
          setShouldValidateIdentityCard(false);
        }
      },
    });
    const { runAsync: validateIdentityCard, cancel } = useRequest(checkCustomerIdentityCard, {
      manual: true,
    });
    const { runAsync: create, loading: createLoading } = useRequest(createCustomer, {
      manual: true,
    });
    const { runAsync: update, loading: updateLoading } = useRequest(updateCustomerField, {
      manual: true,
    });
    const { runAsync: importExcel, loading: importExcelLoading } = useRequest(importCustomerExcel, {
      manual: true,
    });

    // 编辑且实名，某些字段不可编辑
    const disabled = isEdit && data?.realAuthFlag;
    // 评分评级是否来自大数据
    const isCustomerScoreFromBigData =
      data?.customerScoreSourceType === CustomerScoreSourceTypeEnum.BigData;

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        mutate(undefined);
        setAvailablePhones({});
        setIsIdentityCardRepeat(false);
        setShouldValidateIdentityCard(true);
      },
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      // 编辑
      if (customerId) {
        const params: UpdateCustomerFieldReq = { id: customerId, ...values };

        const customerInfo = await update(params);

        message.success('修改成功');
        onSuccess(customerInfo);
        // 新建
      } else {
        if (activeKey === 'manual') {
          await create(values);
          message.success('创建成功');
          onSuccess(values);
        } else {
          const res = await importExcel(values);

          showImportPrompt(res, onSuccess);
        }
      }

      setOpen(false);
    };

    const manualFormNode = (
      <>
        <Form.Item
          name="name"
          label="姓名"
          rules={[
            { required: true, message: '请输入姓名' },
            { max: 25, message: '不能超过 25 个字符' },
          ]}
        >
          <Input placeholder="请输入姓名" disabled={disabled} />
        </Form.Item>
        <Form.List name="phones">
          {(fields, { add, remove }) => (
            <Form.Item label="手机号" required className="mb-2">
              <div className="flex flex-col items-start gap-2">
                {fields.map((field) => (
                  <div key={field.key} className="flex w-full">
                    <Encrypt.PhoneFormItem
                      formItemProps={{
                        name: field.name,
                        className: 'mb-0 flex-1',
                        dependencies: fields
                          .filter((i) => i.name !== field.name)
                          .map((i) => ['phones', i.name]),
                        rules: [
                          { required: true, message: '请输入手机号' },
                          {
                            validator: (_, value) => {
                              // 如果跟其它手机号有重复
                              if (
                                fields
                                  .filter((i) => i.name !== field.name)
                                  .some((i) => form.getFieldValue(['phones', i.name]) === value)
                              ) {
                                return Promise.reject(new Error('不能与其它手机号重复'));
                              }

                              return Promise.resolve();
                            },
                          },
                          {
                            validateTrigger: 'onBlur',
                            validator: async (_, value) => {
                              // 这个手机号已经校验过可用
                              if (availablePhones[value]) {
                                return Promise.resolve();
                              }

                              // 这个手机号已经校验过不可用
                              if (availablePhones[value] === false) {
                                return Promise.reject(new Error('该手机号已被其他客户使用'));
                              }

                              const exists = await checkCustomerPhone({
                                phone: value,
                                ...(customerId ? { id: customerId } : {}),
                              });

                              if (exists) {
                                setAvailablePhones((prev) => ({
                                  ...prev,
                                  [value]: false,
                                }));

                                return Promise.reject(new Error('该手机号已被其他客户使用'));
                              } else {
                                setAvailablePhones((prev) => ({
                                  ...prev,
                                  [value]: true,
                                }));

                                return Promise.resolve();
                              }
                            },
                          },
                        ],
                      }}
                      fieldProps={{
                        placeholder: '请输入手机号',
                        encryptSensitiveMap: (data?.phones || []).reduce(
                          (total, item, idx) => ({
                            ...total,
                            [item]: data?.phonesSensitive?.[idx],
                          }),
                          {},
                        ),
                      }}
                    />
                    {fields.length > 1 && (
                      <Button
                        icon={<CloseCircleFilled className="text-gray-400" />}
                        type="text"
                        className="ml-2"
                        onClick={() => {
                          remove(field.name);
                          setTimeout(() => {
                            form.validateFields(['phones'], { recursive: true });
                          });
                        }}
                      />
                    )}
                  </div>
                ))}
                <Button
                  type="text"
                  className="!text-primary"
                  icon={<PlusOutlined />}
                  onClick={() => add('')}
                >
                  添加
                </Button>
              </div>
            </Form.Item>
          )}
        </Form.List>
        <Encrypt.IdCardFormItem
          formItemProps={{
            name: 'identityCard',
            label: '身份证号',
            rules: [
              {
                required: true,
                message: '请输入身份证号',
              },
              {
                validateTrigger: 'onBlur',
                validator: async (_, value) => {
                  cancel();

                  if (isIdentityCardRepeat) {
                    return Promise.reject(new Error('已存在相同身份证号客户'));
                  }

                  if (shouldValidateIdentityCard) {
                    const exists = await validateIdentityCard({
                      identityCard: value,
                      ...(customerId ? { id: customerId } : {}),
                    });

                    setShouldValidateIdentityCard(false);

                    if (exists) {
                      setIsIdentityCardRepeat(true);

                      return Promise.reject('已存在相同身份证号客户');
                    } else {
                      setIsIdentityCardRepeat(false);

                      return Promise.resolve();
                    }
                  }

                  return Promise.resolve();
                },
              },
            ],
          }}
          fieldProps={{
            disabled,
            placeholder: '请输入身份证号',
            sensitiveValue: data?.identityCardSensitive,
            onChange: () => {
              cancel();
              setIsIdentityCardRepeat(false);
              setShouldValidateIdentityCard(true);
            },
          }}
        />
        <Form.Item name="gender" label="性别">
          <Select
            allowClear
            disabled={disabled}
            placeholder="请选择性别"
            options={enum2Options(GenderEnum)}
          />
        </Form.Item>
        <EditableDate
          editable
          formItemProps={{
            name: 'birthday',
            label: '生日',
          }}
          fieldProps={{
            disabled,
          }}
        />
        <EditableRegion
          editable
          formItemProps={{ name: 'nativePlace', label: '籍贯' }}
          fieldProps={{ regionLevel: 3 }}
        />
        <Form.Item
          name="address"
          label="身份证住址"
          rules={[{ max: 100, message: '不能超过 100 个字符' }]}
        >
          <Input placeholder="请输入身份证住址" disabled={disabled} />
        </Form.Item>
        <Form.Item name="highestEducationLevel" label="最高学历">
          <Select
            allowClear
            options={enum2Options(HighestEducationLevelEnum)}
            placeholder="请选择最高学历"
          />
        </Form.Item>
        <Form.Item name="customerScoreLevel" label="客户评级">
          <Select
            allowClear
            disabled={
              // 数据来源是大数据不可编辑
              isEdit && isCustomerScoreFromBigData
            }
            placeholder="请选择客户评级"
            options={enum2Options(CustomerScoreLevelEnum)}
          />
        </Form.Item>
        <Form.Item name="customerScore" label="客户评分">
          <InputNumber
            disabled={
              // 数据来源是大数据不可编辑
              isEdit && isCustomerScoreFromBigData
            }
            min={0}
            max={100}
            placeholder="请输入"
            precision={2}
          />
        </Form.Item>
        <Form.Item
          name="customerNature"
          label="客户性质"
          rules={[
            {
              required: true,
              message: '请选择客户性质',
            },
          ]}
        >
          <Select
            allowClear
            placeholder="请选择客户性质"
            options={enum2Options(StoreCategoryEnum)}
          />
        </Form.Item>
        <FileUpload formItemProps={{ label: '学历证明', name: 'educationLevelProves' }} />
        <Form.Item label="面审爽约">
          <Select
            disabled
            placeholder="依据预约记录中的未到访原因自动生成"
            value={data?.interviewNoArrive}
            options={yesOrNoOptions}
          />
        </Form.Item>
        <Form.Item label="是否红线">
          <Select
            disabled
            placeholder="依据到访记录中的未通过原因自动生成"
            value={data?.redLine}
            options={yesOrNoOptions}
          />
        </Form.Item>
        <Form.Item label="是否黑名单">
          <Select
            disabled
            placeholder="依据面审爽约和是否红线自动生成"
            value={data?.blacklist}
            options={yesOrNoOptions}
          />
        </Form.Item>
      </>
    );

    return (
      <Modal
        title={isEdit ? '编辑客户' : '新建客户'}
        destroyOnHidden
        loading={loading}
        confirmLoading={createLoading || updateLoading || importExcelLoading}
        styles={{
          body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
        }}
        modalRender={(node) => (
          <Form
            form={form}
            clearOnDestroy
            preserve={false}
            scrollToFirstError
            initialValues={customerId ? undefined : { phones: [''] }}
            labelCol={{ span: 5 }}
            onFinish={handleSave}
          >
            {node}
          </Form>
        )}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
        {...props}
        open={open}
      >
        {isEdit ? (
          manualFormNode
        ) : (
          <Tabs
            activeKey={activeKey}
            destroyOnHidden
            onChange={(key) => {
              setActiveKey(key as typeof activeKey);
            }}
            items={[
              {
                label: '手动新建',
                key: 'manual',
                children: manualFormNode,
              },
              {
                label: '通过 EXCEL 导入',
                key: 'excel',
                children: <ImportFileFormItem downloadTemplate={downloadCustomerExcelTemplate} />,
              },
            ]}
          />
        )}
      </Modal>
    );
  },
);

export default CreateEditCustomerModal;
