import React, { useRef } from 'react';
import { ModalRef } from '@src/common/interface';
import EditablePhone from '@src/components/Editable/EditablePhone';
import ClueDetailDrawer from '@src/pages/clues/components/ClueDetailDrawer';
import { getClueListByRelCustomer } from '@src/services/customers';
import { useRequest } from 'ahooks';
import { Alert, Table } from 'antd';

interface RelClueListTabProps {
  customerId: number;
}

const RelClueListTab: React.FC<RelClueListTabProps> = ({ customerId }) => {
  const clueIdRef = useRef<number | null>(null);
  const clueDetailDrawerRef = useRef<ModalRef>(null);

  const { data, loading, refresh } = useRequest(() => getClueListByRelCustomer(customerId));

  return (
    <>
      <Alert showIcon message="当前账号不可见的线索不展示在该列表中" className="mb-3 px-2 py-1" />
      <Table
        size="small"
        rowKey="clueId"
        pagination={false}
        loading={loading}
        dataSource={data}
        columns={[
          {
            title: '线索名称',
            dataIndex: 'clueName',
            render: (value, { clueId }) => (
              <a
                onClick={() => {
                  clueIdRef.current = clueId;
                  clueDetailDrawerRef.current?.open();
                }}
              >
                {value}
              </a>
            ),
          },
          {
            title: '联系方式',
            dataIndex: 'cluePhone',
            render: (value, { cluePhoneSensitive }) => (
              <EditablePhone value={value} fieldProps={{ sensitiveValue: cluePhoneSensitive }} />
            ),
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
          },
        ]}
      />

      <ClueDetailDrawer
        ref={clueDetailDrawerRef}
        getId={() => clueIdRef.current}
        onUpdate={refresh}
      />
    </>
  );
};

export default RelClueListTab;
