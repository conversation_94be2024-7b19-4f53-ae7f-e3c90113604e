import { useRef } from 'react';
import { franchiseAuditStatusOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import ETable, {
  ConditionEnum,
  ETableActionType,
  ETableColumn,
  SearchRelationEnum,
  ValueType,
} from '@src/components/ETable';
import useTableViews from '@src/hooks/useTableViews';
import FranchiseDetailDrawer from '@src/pages/franchise-application/components/FranchiseDetailDrawer';
import { getQuestionColumns } from '@src/pages/franchise-application/utils';
import { BusinessTypeEnum } from '@src/services/common/type';
import { getExamAnswerList, getExamAnswerQuestions } from '@src/services/franchise-application';
import { ExamAnswerDTO } from '@src/services/franchise-application/type';
import { useRequest } from 'ahooks';
import { Card, Skeleton } from 'antd';

interface FranchiseeJoinTableProps {
  customerId?: number;
}

const businessType = BusinessTypeEnum.ONLINE_APPLY;

const FranchiseeJoinTable: React.FC<FranchiseeJoinTableProps> = ({ customerId }) => {
  const actionRef = useRef<ETableActionType>(null);
  const currentIdRef = useRef<number | null>(null);
  const franchiseDetailDrawerRef = useRef<ModalRef>(null);
  const { data: questions = [], loading: getQuestionsLoading } = useRequest(() =>
    getExamAnswerQuestions(BusinessTypeEnum.ONLINE_APPLY),
  );
  const { views, loading: getViewsLoading } = useTableViews(BusinessTypeEnum.FRANCHISEE_PROFILE);
  const { runAsync: getList } = useRequest(getExamAnswerList, { manual: true });

  const columns: ETableColumn<ExamAnswerDTO>[] = [
    {
      title: '申请表编号',
      dataIndex: 'code',
      fixed: 'left',
      valueType: ValueType.TEXT,
      render: (value, { id }) => (
        <a
          onClick={() => {
            currentIdRef.current = id;
            franchiseDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: franchiseAuditStatusOptions,
      },
    },
    ...getQuestionColumns(questions),
  ];

  if (getViewsLoading || getQuestionsLoading) {
    return <Skeleton paragraph={{ rows: 15 }} className="px-10" />;
  }

  return (
    <Card className="mt-5" title="加盟申请">
      <ETable
        actionRef={actionRef}
        columns={columns}
        options={{
          filters: false,
          reload: false,
        }}
        rowKey="id"
        sticky
        pagination={{ showSizeChanger: true, showQuickJumper: true }}
        views={views}
        request={async ({ current, pageSize }) => {
          if (!customerId)
            return {
              data: [],
              total: 0,
            };

          const res = await getList({
            businessType,
            pageNum: current,
            pageSize,
            searchRelation: SearchRelationEnum.AND,
            fields: [
              {
                field: 'customerId',
                condition: ConditionEnum.EQ,
                value: customerId,
                valueType: ValueType.TEXT,
              },
            ],
          });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />
      <FranchiseDetailDrawer
        ref={franchiseDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
      />
    </Card>
  );
};

export default FranchiseeJoinTable;
