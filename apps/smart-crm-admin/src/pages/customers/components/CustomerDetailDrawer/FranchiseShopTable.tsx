import { useRef } from 'react';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { ModalRef } from '@src/common/interface';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { getFranchiseDetail } from '@src/services/customers';
import { FranchiseShopDTO } from '@src/services/customers/type';
import { ShopStatusEnum } from '@src/services/shop/list/type';
import { calcTableWidth } from '@src/utils';
import { useRequest } from 'ahooks';
import { Card } from 'antd';

interface FranchiseShopTableProps {
  customerId?: number;
}

const FranchiseShopTable: React.FC<FranchiseShopTableProps> = ({ customerId }) => {
  const currentIdRef = useRef<string | null>(null);
  const shopDetailDrawerRef = useRef<ModalRef>(null);

  const { data, loading } = useRequest(() => getFranchiseDetail(customerId!), {
    ready: !!customerId,
    refreshDeps: [customerId],
  });

  const columns: ProColumns<FranchiseShopDTO>[] = [
    {
      title: '门店编号',
      dataIndex: 'shopId',
      fixed: 'left',
      renderText: (value) => (
        <a
          onClick={() => {
            currentIdRef.current = value;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '门店冠名',
      dataIndex: 'shopName',
      width: 200,
    },
    {
      title: '省市区',
      dataIndex: 'district',
      width: 200,
    },
    {
      title: '门店等级',
      dataIndex: 'shopLevel',
      width: 200,
    },
    {
      title: '门店状态',
      dataIndex: 'shopStatus',
      valueEnum: {
        [ShopStatusEnum.PREPARING]: {
          text: '筹备中',
          status: 'processing',
        },
        [ShopStatusEnum.OPEN]: {
          text: '营业中',
          status: 'success',
        },
        [ShopStatusEnum.OFFLINE]: {
          text: '歇业中',
          status: 'default',
        },
        [ShopStatusEnum.CLOSE]: {
          text: '已闭店',
          status: 'error',
        },
        [ShopStatusEnum.OPEN_FOR_BUSINESS]: {
          text: '待营业',
          status: 'warning',
        },
      },
    },
  ];

  return (
    <Card className="mt-5" title="相关门店" loading={loading}>
      <ProTable
        bordered
        size="small"
        rowKey="id"
        options={false}
        search={false}
        cardProps={false}
        scroll={{ x: calcTableWidth(columns) }}
        columns={columns}
        pagination={false}
        dataSource={data?.shopRelList}
      />
      <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => currentIdRef.current} />
    </Card>
  );
};

export default FranchiseShopTable;
