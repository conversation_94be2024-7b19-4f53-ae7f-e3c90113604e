import React, { useRef, useState } from 'react';
import { yesOrNoOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { ButtonGroup, OperationLog } from '@src/components';
import EditablePhone from '@src/components/Editable/EditablePhone';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import useBranchCompanyList from '@src/hooks/useBranchCompanyList';
import { PermissionsMap, usePermission } from '@src/permissions';
import { GenderEnum } from '@src/services/clues/my/type';
import { StoreCategoryEnum } from '@src/services/contract/formal/type';
import { deleteCustomer, getCustomerDetail } from '@src/services/customers';
import { CustomerDTO, HighestEducationLevelEnum } from '@src/services/customers/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Card, Drawer, DrawerProps, Tabs, Tag } from 'antd';
import FranchiseJoinTable from './FranchiseJoinTable';
import FranchiseShopTable from './FranchiseShopTable';
import RelClueListTab from './RelClueListTab';
import CreateEditCustomerModal from '../CreateEditCustomerModal';

interface CustomerDetailDrawerProps extends DrawerProps {
  isFranchise?: boolean; // 是否是加盟商
  getId: () => number | null | undefined;
  // 信息发生修改，外面列表要重新请求一下，为了刷新更新时间
  onUpdate?: (info?: CustomerDTO) => void;
  onDelete?: () => void;
}

enum ActiveKeyEnum {
  info = 'info',
  clue = 'clue',
  record = 'record',
}

const CustomerDetailDrawer = React.forwardRef<ModalRef, CustomerDetailDrawerProps>(
  ({ isFranchise, getId, onDelete, onUpdate, ...drawerProps }, ref) => {
    const { modal, message } = App.useApp();
    const [open, setOpen] = useState(false);
    const createEditCustomerModalRef = useRef<ModalRef>(null);
    // 编辑信息后保持在当前 Tab
    const [activeKey, setActiveKey] = useState(ActiveKeyEnum.info);

    const customerId = getId()!;

    const checkPermission = usePermission();
    const { data, loading, mutate, refresh } = useRequest(() => getCustomerDetail(customerId), {
      ready: open,
    });

    const { data: users } = useAllUsers({ ready: open });
    const { data: branchCompanyList } = useBranchCompanyList({ ready: open });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        mutate(undefined);
        setActiveKey(ActiveKeyEnum.info);
      },
      close: () => setOpen(false),
    }));

    const infoItems: DescriptionFieldType<CustomerDTO>[] = [
      {
        field: 'name',
        label: '姓名',
      },
      {
        field: 'code',
        label: '客户编号',
      },
      {
        field: 'gender',
        label: '性别',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(GenderEnum),
        },
      },
      {
        field: 'age',
        label: '年龄',
      },
      { field: 'birthday', label: '生日', valueType: ValueType.DATE },
      {
        field: 'phones',
        label: '手机号',
        children: (
          <div>
            {data?.phones?.map((phone, index) => (
              <EditablePhone
                key={index}
                value={phone}
                fieldProps={{ sensitiveValue: data?.phonesSensitive?.[index] }}
              />
            ))}
          </div>
        ),
      },
      {
        field: 'identityCard',
        label: '身份证号',
        valueType: ValueType.IDENTITY_CARD,
        fieldProps: {
          sensitiveValue: data?.identityCardSensitive,
        },
      },
      {
        field: 'nativePlace',
        label: '籍贯',
        valueType: ValueType.REGION,
        fieldProps: {
          regionLevel: 3,
        },
      },
      {
        field: 'address',
        label: '身份证住址',
      },
      {
        field: 'customerScoreLevel',
        label: '客户评级',
      },
      {
        field: 'customerScore',
        label: '客户评分',
      },
      {
        field: 'customerNature',
        label: '客户性质',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(StoreCategoryEnum),
        },
      },
      {
        field: 'belongArea',
        label: '所属区域',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: branchCompanyList,
          fieldNames: {
            value: 'branchCompanyCode',
            label: 'branchCompanyName',
          },
        },
      },
      {
        field: 'highestEducationLevel',
        label: '最高学历',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(HighestEducationLevelEnum),
        },
      },
      {
        field: 'educationLevelProves',
        label: '学历证明',
        valueType: ValueType.ATTACHMENT,
      },
      {
        field: 'interviewNoArrive',
        label: '面审爽约',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        field: 'redLine',
        label: '是否红线',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        field: 'blacklist',
        label: '是否黑名单',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        field: 'staff',
        label: '是否员工',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
    ];

    return (
      <Drawer
        width={800}
        title={isFranchise ? '加盟商详情' : '客户详情'}
        push={false}
        destroyOnHidden
        onClose={() => setOpen(false)}
        {...drawerProps}
        open={open}
      >
        <Card loading={loading}>
          <div className="flex justify-between">
            <div className="flex flex-col gap-1">
              <span>
                姓名：{data?.name}
                {data?.realAuthFlag ? (
                  <Tag color="success" className="ml-4">
                    已实名
                  </Tag>
                ) : (
                  <Tag color="error" className="ml-4">
                    未实名
                  </Tag>
                )}
              </span>
              <span>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</span>
              <span>更新人：{users?.find((i) => i.id === data?.updateUserId)?.name}</span>
              <span>创建时间：{data?.createTime}</span>
              <span>更新时间：{data?.updateTime}</span>
            </div>
            {!isFranchise && (
              <ButtonGroup
                items={[
                  {
                    label: '编辑',
                    onClick: () => createEditCustomerModalRef.current?.open(),
                  },
                  {
                    label: '删除',
                    danger: true,
                    show: checkPermission(PermissionsMap.CustomersDelete),
                    onClick: () =>
                      modal.confirm({
                        title: '删除客户',
                        content: `确定要删除客户 “${data?.name}” 吗？`,
                        onOk: async () => {
                          await deleteCustomer(customerId);
                          message.success('删除成功');
                          setOpen(false);
                          onDelete?.();
                        },
                      }),
                  },
                ]}
              />
            )}
          </div>
        </Card>
        <Card className="mt-5" loading={loading}>
          <Tabs
            className="-mt-5"
            activeKey={activeKey}
            items={[
              {
                label: '详细资料',
                key: ActiveKeyEnum.info,
                children: renderDescriptions(infoItems, data),
              },
              {
                label: '线索',
                key: ActiveKeyEnum.clue,
                hidden: isFranchise,
                children: <RelClueListTab customerId={customerId} />,
              },
              {
                label: '操作记录',
                key: ActiveKeyEnum.record,
                hidden: isFranchise,
                children: <OperationLog id={customerId} businessType="CUSTOMER" />,
              },
            ].filter((i) => !i.hidden)}
            onChange={(key) => setActiveKey(key as ActiveKeyEnum)}
          />
        </Card>

        {isFranchise && (
          <>
            <FranchiseShopTable customerId={customerId} />
            <FranchiseJoinTable customerId={customerId} />
          </>
        )}

        <CreateEditCustomerModal
          ref={createEditCustomerModalRef}
          getId={() => customerId}
          onSuccess={(info) => {
            refresh();
            onUpdate?.(info);
          }}
        />
      </Drawer>
    );
  },
);

export default CustomerDetailDrawer;
