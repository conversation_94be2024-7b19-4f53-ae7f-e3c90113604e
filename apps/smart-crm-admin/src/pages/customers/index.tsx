import { useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ModalRef } from '@src/common/interface';
import { ExportButton, ProTable, TableActions } from '@src/components';
import useCustomerColumns from '@src/hooks/useCustomerColumns';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import {
  batchDeleteCustomer,
  deleteCustomer,
  exportCustomer,
  getCustomerList,
} from '@src/services/customers';
import { CustomerDTO, ExportCustomerReq } from '@src/services/customers/type';
import { compatibleTableActionWidth } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Dropdown, MenuProps } from 'antd';
import CreateEditCustomerModal from './components/CreateEditCustomerModal';
import CustomerDetailDrawer from './components/CustomerDetailDrawer';
import UpdateCustomerScoreModal from './components/UpdateCustomerScoreModal';

const Customers = () => {
  const { modal, message } = App.useApp();
  const checkPermission = usePermission();
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const filterParamsRef = useRef<ExportCustomerReq>({});
  const actionRef = useRef<ActionType>(null);
  const createEditCustomerModalRef = useRef<ModalRef>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const customerIdRef = useRef<number | null>(null);
  const updateCustomerScoreModalRef = useRef<ModalRef>(null);

  const { data, runAsync: getList } = useRequest(getCustomerList, { manual: true });
  const customerColumns = useCustomerColumns({
    onCustomerNameClick: (record) => {
      customerDetailDrawerRef.current?.open();
      customerIdRef.current = record.id;
    },
  });

  const columns: ProColumns<CustomerDTO>[] = [
    ...customerColumns,
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      width: compatibleTableActionWidth(100),
      search: false,
      render: (_, { id, name }) => (
        <TableActions
          shortcuts={[
            {
              label: '编辑',
              onClick: () => {
                customerIdRef.current = id;
                createEditCustomerModalRef.current?.open();
              },
            },
            {
              label: '删除',
              danger: true,
              show: checkPermission(PermissionsMap.CustomersDelete),
              onClick: () => {
                modal.confirm({
                  title: '删除客户',
                  content: `确定要删除客户 “${name}” 吗？`,
                  onOk: async () => {
                    await deleteCustomer(id);
                    message.success('删除成功');
                    setSelectedRowKeys((prev) =>
                      prev.some((i) => i === id) ? prev.filter((i) => i !== id) : prev,
                    );
                    actionRef.current?.reload();
                  },
                });
              },
            },
          ]}
        />
      ),
    },
  ];

  const batchMenuItems: MenuProps['items'] = [
    {
      key: 'update-score',
      label: '更新评分评级',
      auth: PermissionsMap.CustomerBatchUpdateScore,
      onClick: () => updateCustomerScoreModalRef.current?.open(),
    },
    {
      key: 'delete',
      label: '删除',
      auth: PermissionsMap.CustomersDelete,
      danger: true,
      onClick: () =>
        modal.confirm({
          title: '删除客户',
          content: `确定要删除选中的 ${selectedRowKeys.length} 个客户吗？`,
          onOk: async () => {
            await batchDeleteCustomer({ ids: selectedRowKeys });
            message.success('删除成功');
            actionRef.current?.reload();
            setSelectedRowKeys([]);
          },
        }),
    },
  ].filter((i) => checkPermission(i.auth));

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        cardProps={false}
        bordered
        rowKey="id"
        sticky
        size="small"
        columnEmptyText=""
        columns={columns}
        scroll={{ x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0) }}
        options={false}
        tableAlertRender={false}
        toolbar={{
          subTitle: (
            <span className="text-black">
              共 {data?.total || 0} 个客户
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 个
                  <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            batchMenuItems.length > 0 && (
              <Dropdown
                disabled={!selectedRowKeys.length}
                menu={{
                  items: batchMenuItems,
                }}
              >
                <Button type="text" className="!flex items-center">
                  批量操作 <CaretDownOutlined />
                </Button>
              </Dropdown>
            ),
            <Button
              type="text"
              onClick={() => {
                customerIdRef.current = null;
                createEditCustomerModalRef.current?.open();
              }}
            >
              新建
            </Button>,
            <Permission value={PermissionsMap.CustomersExport}>
              <ExportButton
                total={selectedRowKeys.length || data?.total}
                request={() =>
                  exportCustomer(
                    selectedRowKeys.length ? { ids: selectedRowKeys } : filterParamsRef.current,
                  )
                }
              />
            </Permission>,
          ],
        }}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        rowSelection={{
          fixed: true,
          showSort: true,
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        request={async ({ current, pageSize, ...params }) => {
          filterParamsRef.current = params as ExportCustomerReq;

          const res = await getList({ pageNum: current, pageSize, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <CreateEditCustomerModal
        ref={createEditCustomerModalRef}
        getId={() => customerIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
      <CustomerDetailDrawer
        ref={customerDetailDrawerRef}
        getId={() => customerIdRef.current}
        onDelete={() => {
          setSelectedRowKeys((prev) => prev.filter((i) => i !== customerIdRef.current));
          actionRef.current?.reload();
        }}
        onUpdate={() => actionRef.current?.reload()}
      />
      <UpdateCustomerScoreModal
        ref={updateCustomerScoreModalRef}
        selectedRowKeys={selectedRowKeys}
        onSuccess={() => actionRef.current?.reload()}
      />
    </PageContainer>
  );
};

export default Customers;
