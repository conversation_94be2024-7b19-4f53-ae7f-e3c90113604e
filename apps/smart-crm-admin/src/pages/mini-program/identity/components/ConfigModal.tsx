import { FileUpload, RegionCascader } from '@src/components';
import useAllUsers from '@src/hooks/useAllUsers';
import { getIdentityUser, updateIdentifyConfig } from '@src/services/mini-program/identity';
import { IdentityTypeEnum } from '@src/services/mini-program/identity/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, Input, InputNumber, Modal, ModalProps, Radio } from 'antd';

interface ConfigModalProps extends ModalProps {
  currentEditId: number | null;
  onSuccess: () => void;
}

const ConfigModal: React.FC<ConfigModalProps> = ({ open, currentEditId, onSuccess, ...props }) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();

  const { runAsync: update, loading: updateLoading } = useRequest(updateIdentifyConfig, {
    manual: true,
  });
  const { data: detailData, loading: getDetailLoading } = useRequest(
    () => getIdentityUser(currentEditId!),
    {
      ready: open && !!currentEditId,
      onSuccess: ({ identityType, qrCodeOssKey, qrCodeUrl, proportion, territories }) => {
        form.setFieldsValue({
          identityType,
          attachments:
            qrCodeOssKey && qrCodeUrl
              ? [
                  {
                    fileUrl: qrCodeUrl,
                    fileType: 'image/jpg',
                    fileKey: qrCodeOssKey,
                  },
                ]
              : undefined,
          proportion,
          territories: territories?.map((id) => [id]),
        });
      },
    },
  );
  const { mutate: mutateAllUsers } = useAllUsers({ manual: true });

  const handleSave = async ({ territories, attachments, ...values }: any) => {
    await update({
      userId: currentEditId!,
      qrCodeOssKey: attachments?.[0].fileKey,
      territories: territories?.map(([provinceId]: number[]) => provinceId),
      ...values,
    });
    message.success('配置成功');
    mutateAllUsers((old) => {
      const result = [...old!];
      const index = result.findIndex((i) => i.id === currentEditId);

      if (index > -1) {
        result[index].identityType = values.identityType;
      }

      return result;
    });
    onSuccess();
  };

  return (
    <Modal
      open={open}
      title="配置"
      destroyOnHidden
      loading={getDetailLoading}
      confirmLoading={updateLoading}
      modalRender={(node) => (
        <Form form={form} clearOnDestroy labelCol={{ span: 7 }} onFinish={handleSave}>
          {node}
        </Form>
      )}
      onOk={form.submit}
      {...props}
    >
      <Form.Item label="姓名" required>
        <Input className="p-0" readOnly variant="borderless" value={detailData?.name} />
      </Form.Item>
      <Form.Item
        name="identityType"
        label="身份类型"
        rules={[{ required: true, message: '请选择身份类型' }]}
      >
        <Radio.Group
          className="flex flex-wrap gap-y-2 mt-1"
          options={enum2Options(IdentityTypeEnum)}
        />
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) =>
          [
            IdentityTypeEnum.招商顾问,
            IdentityTypeEnum.客户体验师,
            IdentityTypeEnum.大客户招商,
          ].includes(getFieldValue('identityType')) && (
            <FileUpload
              formItemProps={{
                label: '企业微信二维码',
                rules: [{ required: true, message: '请上传企业微信二维码' }],
              }}
              uploadProps={{
                maxCount: 1,
                accept: 'image/*',
              }}
            />
          )
        }
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) =>
          [
            IdentityTypeEnum.招商顾问,
            IdentityTypeEnum.客户体验师,
            IdentityTypeEnum.面审老师,
            IdentityTypeEnum.大客户招商,
          ].includes(getFieldValue('identityType')) && (
            <>
              <Form.Item
                label="权重"
                name="proportion"
                rules={[{ required: true, message: '请输入权重' }]}
              >
                <InputNumber
                  precision={0}
                  min={0}
                  max={100}
                  placeholder="请输入权重"
                  style={{ width: 120 }}
                />
              </Form.Item>
            </>
          )
        }
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) =>
          [
            IdentityTypeEnum.招商顾问,
            IdentityTypeEnum.客户体验师,
            IdentityTypeEnum.面审老师,
            IdentityTypeEnum.大客户招商,
            IdentityTypeEnum['训练大狮兄/大狮姐'],
            IdentityTypeEnum.训练经理,
          ].includes(getFieldValue('identityType')) && (
            <>
              <Form.Item
                label="负责区域"
                name="territories"
                rules={[{ required: true, message: '请选择负责区域' }]}
              >
                <RegionCascader
                  regionLevel={1}
                  autoClearSearchValue={false}
                  multiple
                  placeholder="请选负责区域"
                />
              </Form.Item>
            </>
          )
        }
      </Form.Item>
    </Modal>
  );
};

export default ConfigModal;
