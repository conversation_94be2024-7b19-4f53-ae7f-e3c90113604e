import { useRef, useState } from 'react';
import { EyeOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ProTable, UserSelect } from '@src/components';
import { getIdentityUserList } from '@src/services/mini-program/identity';
import { IdentityTypeEnum, IdentityUserDTO } from '@src/services/mini-program/identity/type';
import { enum2ValueEnum } from '@src/utils';
import { Image } from 'antd';
import ConfigModal from './components/ConfigModal';

const Identity = () => {
  const [currentEditId, setCurrentEditId] = useState<number | null>(null);
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<IdentityUserDTO>[] = [
    {
      title: '成员名称',
      dataIndex: 'name',
      search: {
        transform: (userId) => ({ userId }),
      },
      renderFormItem: () => <UserSelect />,
    },
    {
      title: '身份类型',
      dataIndex: 'identityType',
      valueEnum: enum2ValueEnum(IdentityTypeEnum),
    },
    {
      title: '企微二维码',
      dataIndex: 'qrCodeUrl',
      search: false,
      renderText: (url) =>
        url && (
          <Image
            src={url}
            width={25}
            style={{ height: 25, objectFit: 'contain' }}
            preview={{ mask: <EyeOutlined /> }}
          />
        ),
    },
    {
      title: '分配权重',
      dataIndex: 'proportion',
      search: false,
      renderText: (value, { identityType }) =>
        [
          IdentityTypeEnum.招商顾问,
          IdentityTypeEnum.客户体验师,
          IdentityTypeEnum.面审老师,
          IdentityTypeEnum.大客户招商,
        ].includes(identityType)
          ? value
          : null,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      width: 80,
      search: false,
      render: (_, { id }) => <a onClick={() => setCurrentEditId(id)}>配置</a>,
    },
  ];

  return (
    <PageContainer>
      <ProTable
        sticky
        actionRef={actionRef}
        options={false}
        rowKey="id"
        size="small"
        cardProps={false}
        bordered
        columns={columns}
        request={async ({ current, ...params }) => {
          const res = await getIdentityUserList({ pageNum: current, ...params });

          return { data: res.result, total: res.total };
        }}
      />

      <ConfigModal
        open={!!currentEditId}
        currentEditId={currentEditId}
        onCancel={() => setCurrentEditId(null)}
        onSuccess={() => {
          setCurrentEditId(null);
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default Identity;
