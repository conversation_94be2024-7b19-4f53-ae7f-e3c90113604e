import { FileUpload } from '@src/components';
import {
  createArticle,
  editArticle,
  getArticleDetail,
} from '@src/services/mini-program/content-management/article';
import { ArticleTypeEnum } from '@src/services/mini-program/content-management/article/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps, Select } from 'antd';

interface ArticleModalProps extends ModalProps {
  currentEditId: number | null;
  onSuccess: () => void;
}

const ArticleModal: React.FC<ArticleModalProps> = ({
  open,
  currentEditId,
  onSuccess,
  ...props
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();

  const { runAsync: create, loading: createLoading } = useRequest(createArticle, { manual: true });
  const { runAsync: edit, loading: editLoading } = useRequest(editArticle, { manual: true });
  const { loading: getDetailLoading } = useRequest(() => getArticleDetail(currentEditId!), {
    ready: open && !!currentEditId,
    onSuccess: ({ imageOssKey, imageOssUrl, ...params }) => {
      form.setFieldsValue({
        ...params,
        attachments: [
          {
            fileUrl: imageOssUrl,
            fileType: 'image/jpg',
            fileKey: imageOssKey,
          },
        ],
      });
    },
  });

  const handleSave = async ({ attachments, ...values }: any) => {
    if (currentEditId) {
      await edit({
        id: currentEditId,
        imageOssKey: attachments[0].fileKey,
        ...values,
      });
    } else {
      await create({
        imageOssKey: attachments[0].fileKey,
        ...values,
      });
    }

    message.success('保存成功');

    onSuccess();
  };

  return (
    <Modal
      title={currentEditId ? '编辑文章' : '新增文章'}
      open={open}
      loading={currentEditId ? getDetailLoading : false}
      destroyOnHidden
      confirmLoading={createLoading || editLoading}
      modalRender={(node) => (
        <Form form={form} clearOnDestroy onFinish={handleSave}>
          {node}
        </Form>
      )}
      onOk={form.submit}
      {...props}
    >
      <Form.Item
        label="文章标题"
        name="title"
        rules={[
          { required: true, message: '请输入文章标题' },
          { max: 40, message: '不能超过 40 个字符' },
        ]}
      >
        <Input placeholder="请输入文章标题" />
      </Form.Item>
      <Form.Item
        label="文章类型"
        name="articleType"
        rules={[{ required: true, message: '请选择文章类型' }]}
      >
        <Select
          placeholder="请选择文章类型"
          showSearch
          optionFilterProp="label"
          options={enum2Options(ArticleTypeEnum)}
        />
      </Form.Item>
      <FileUpload
        formItemProps={{
          label: '文章图片',
          rules: [{ required: true, message: '请上传文章图片' }],
        }}
        uploadProps={{
          maxCount: 1,
          accept: 'image/*',
        }}
      />
    </Modal>
  );
};

export default ArticleModal;
