import { useRef, useState } from 'react';
import { EyeOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@src/components';
import {
  deleteArticle,
  getArticleList,
} from '@src/services/mini-program/content-management/article';
import { ArticleTypeEnum } from '@src/services/mini-program/content-management/article/type';
import { enum2ValueEnum } from '@src/utils';
import { App, Button, Image, Typography } from 'antd';
import ArticleModal from './components/ArticleModal';

const Article = () => {
  const { modal, message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [articleModalOpen, setArticleModalOpen] = useState(false);
  const [currentEditId, setCurrentEditId] = useState<number | null>(null);

  const columns: ProColumns[] = [
    {
      title: '文章标题',
      dataIndex: 'title',
    },
    {
      title: '文章类型',
      search: false,
      dataIndex: 'articleType',
      valueEnum: enum2ValueEnum(ArticleTypeEnum),
    },
    {
      title: '文章图片',
      dataIndex: 'imageOssUrl',
      search: false,
      renderText: (url) => (
        <Image
          src={url}
          width={25}
          style={{ height: 25, objectFit: 'contain' }}
          preview={{ mask: <EyeOutlined /> }}
        />
      ),
    },
    {
      title: '阅读次数',
      dataIndex: 'readCount',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      search: false,
      width: 120,
      render: (_, { id }) => (
        <div className="flex justify-center gap-2">
          <a
            onClick={() => {
              setCurrentEditId(id);
              setArticleModalOpen(true);
            }}
          >
            编辑
          </a>
          <Typography.Link
            type="danger"
            onClick={() =>
              modal.confirm({
                title: '删除',
                content: '确定要删除该文章吗？',
                onOk: async () => {
                  await deleteArticle(id);
                  message.success('删除成功');
                  actionRef.current?.reload();
                },
              })
            }
          >
            删除
          </Typography.Link>
        </div>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        rowKey="id"
        size="small"
        sticky
        options={false}
        toolbar={{
          actions: [
            <Button
              type="primary"
              onClick={() => {
                setCurrentEditId(null);
                setArticleModalOpen(true);
              }}
            >
              新增
            </Button>,
          ],
        }}
        columns={columns}
        request={async ({ current, ...params }) => {
          const res = await getArticleList({ pageNum: current, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <ArticleModal
        open={articleModalOpen}
        currentEditId={currentEditId}
        onCancel={() => setArticleModalOpen(false)}
        onSuccess={() => {
          setArticleModalOpen(false);
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default Article;
