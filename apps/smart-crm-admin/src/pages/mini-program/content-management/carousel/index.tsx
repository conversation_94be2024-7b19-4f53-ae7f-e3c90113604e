import { useRef, useState } from 'react';
import { EyeOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@src/components';
import {
  deleteCarousel,
  getCarouselList,
} from '@src/services/mini-program/content-management/carousel';
import { CarouselItemType } from '@src/services/mini-program/content-management/carousel/type';
import { App, Button, Image, Typography } from 'antd';
import CarouselModal from './components/CarouselModal';

const Carousel = () => {
  const { modal, message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [carouselModalOpen, setCarouselModalOpen] = useState(false);
  const [currentEditId, setCurrentEditId] = useState<number | null>(null);

  const columns: ProColumns<CarouselItemType>[] = [
    {
      title: '标题',
      dataIndex: 'title',
    },
    {
      title: '排序',
      dataIndex: 'sort',
      search: false,
      tooltip: '相同排序的按更新时间进行排序，越晚更新的越前',
    },
    {
      title: '封面图片',
      dataIndex: 'imageOssUrl',
      search: false,
      renderText: (url) => (
        <Image
          src={url}
          width={25}
          style={{ height: 25, objectFit: 'contain' }}
          preview={{ mask: <EyeOutlined /> }}
        />
      ),
    },
    {
      title: '累计点击量',
      dataIndex: 'readCount',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      search: false,
      width: 120,
      render: (_, { id }) => (
        <div className="flex justify-center gap-2">
          <a
            onClick={() => {
              setCurrentEditId(id);
              setCarouselModalOpen(true);
            }}
          >
            编辑
          </a>
          <Typography.Link
            type="danger"
            onClick={() =>
              modal.confirm({
                title: '删除',
                content: '确定要删除该轮播图吗？',
                onOk: async () => {
                  await deleteCarousel(id);
                  message.success('删除成功');
                  actionRef.current?.reload();
                },
              })
            }
          >
            删除
          </Typography.Link>
        </div>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        rowKey="id"
        size="small"
        sticky
        options={false}
        toolbar={{
          actions: [
            <Button
              type="primary"
              onClick={() => {
                setCurrentEditId(null);
                setCarouselModalOpen(true);
              }}
            >
              新增
            </Button>,
          ],
        }}
        columns={columns}
        request={async ({ current, ...params }) => {
          const res = await getCarouselList({ pageNum: current, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <CarouselModal
        currentEditId={currentEditId}
        open={carouselModalOpen}
        onCancel={() => setCarouselModalOpen(false)}
        onSuccess={() => {
          setCarouselModalOpen(false);
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default Carousel;
