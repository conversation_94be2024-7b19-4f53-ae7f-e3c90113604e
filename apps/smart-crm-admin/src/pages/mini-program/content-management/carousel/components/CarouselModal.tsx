import { FileUpload } from '@src/components';
import {
  getAllNewsList,
  getCarouselDetail,
  saveCarousel,
} from '@src/services/mini-program/content-management/carousel';
import { ClassifierEnum } from '@src/services/mini-program/content-management/carousel/type';
import { getAllCategoryList } from '@src/services/mini-program/content-management/category';
import { useRequest } from 'ahooks';
import { App, Form, Input, InputNumber, Modal, ModalProps, Segmented, Select } from 'antd';

interface CarouselModalProps extends ModalProps {
  currentEditId: number | null;
  onSuccess: () => void;
}

const CarouselModal: React.FC<CarouselModalProps> = ({
  open,
  currentEditId,
  onSuccess,
  ...props
}) => {
  const [form] = Form.useForm();
  const { message } = App.useApp();

  const { runAsync: save, loading: saveLoading } = useRequest(saveCarousel, { manual: true });
  const { data: newsOptions } = useRequest(getAllNewsList, { ready: open });
  const { data: categoryOptions } = useRequest(() => getAllCategoryList({ parentId: 0 }), {
    ready: open,
  });
  const { loading } = useRequest(() => getCarouselDetail(currentEditId!), {
    ready: open && !!currentEditId,
    onSuccess: ({ imageOssKey, imageOssUrl, ...params }) => {
      form.setFieldsValue({
        ...params,
        attachments: [
          {
            fileUrl: imageOssUrl,
            fileType: 'image/jpg',
            fileKey: imageOssKey,
          },
        ],
      });
    },
  });

  const handleSave = async ({ attachments, ...values }: any) => {
    await save({
      ...(currentEditId ? { id: currentEditId } : {}),
      ...values,
      imageOssKey: attachments[0].fileKey,
    });
    message.success('保存成功');

    onSuccess();
  };

  return (
    <Modal
      open={open}
      destroyOnHidden
      title={currentEditId ? '编辑轮播图' : '新增轮播图'}
      loading={currentEditId ? loading : false}
      confirmLoading={saveLoading}
      modalRender={(node) => (
        <Form form={form} clearOnDestroy labelCol={{ span: 4 }} onFinish={handleSave}>
          {node}
        </Form>
      )}
      onOk={form.submit}
      {...props}
    >
      <Form.Item
        label="标题"
        name="title"
        rules={[
          { required: true, message: '请输入标题' },
          { max: 40, message: '不能超过 40 个字符' },
        ]}
      >
        <Input placeholder="请输入标题" />
      </Form.Item>
      <Form.Item label="排序" name="sort" rules={[{ required: true, message: '请输入排序' }]}>
        <InputNumber
          min={0}
          max={9999}
          precision={0}
          placeholder="请输入排序"
          style={{ width: 100 }}
        />
      </Form.Item>
      <FileUpload
        formItemProps={{
          label: '封面',
          rules: [{ required: true, message: '请上传封面' }],
        }}
        uploadProps={{
          maxCount: 1,
          accept: 'image/*',
        }}
      />
      <Form.Item
        label="落地页"
        name="classifier"
        initialValue={ClassifierEnum.NEWS}
        rules={[{ required: true, message: '请选择落地页' }]}
      >
        <Segmented
          options={[
            { label: '资讯', value: ClassifierEnum.NEWS },
            { label: '分类列表', value: ClassifierEnum.CATEGORY_LIST },
          ]}
          onChange={() => form.setFieldValue('classifierId', undefined)}
        />
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) => {
          const classifier = getFieldValue('classifier');
          const isNews = classifier === ClassifierEnum.NEWS;
          const label = isNews ? '资讯' : '分类';

          return (
            classifier && (
              <Form.Item
                label={label}
                name="classifierId"
                rules={[{ required: true, message: `请选择${label}` }]}
              >
                <Select
                  placeholder={`请选择${label}`}
                  fieldNames={{ label: 'title', value: 'id' }}
                  showSearch
                  optionFilterProp="title"
                  options={isNews ? newsOptions : categoryOptions}
                />
              </Form.Item>
            )
          );
        }}
      </Form.Item>
    </Modal>
  );
};

export default CarouselModal;
