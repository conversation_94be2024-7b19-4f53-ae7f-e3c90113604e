import { useRef, useState } from 'react';
import { EyeOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@src/components';
import { getAllCategoryTree } from '@src/services/mini-program/content-management/category';
import {
  deleteNews,
  getNewsList,
  updateNewsStatus,
} from '@src/services/mini-program/content-management/news';
import {
  NewsListItemType,
  NewsStatusEnum,
  NewsTypeEnum,
} from '@src/services/mini-program/content-management/news/type';
import { enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Image, Switch, Typography } from 'antd';
import NewsModal from './components/NewsModal';

const News = () => {
  const { modal, message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [currentEditId, setCurrentEditId] = useState<number | null>(null);
  const [newsModalOpen, setNewsModalOpen] = useState(false);

  const { data } = useRequest(async () => {
    const res = await getAllCategoryTree();

    return res.map((i) => ({
      label: i.title,
      value: i.id,
      ...(i.childList.length > 0
        ? { options: i.childList.map((child) => ({ label: child.title, value: child.id })) }
        : {}),
    }));
  });

  const columns: ProColumns<NewsListItemType>[] = [
    { title: '标题', dataIndex: 'title' },
    {
      title: '资讯分类',
      dataIndex: 'categoryTitle',
      valueType: 'select',
      search: {
        transform: (value) => ({
          categoryId: value,
        }),
      },
      fieldProps: {
        options: data,
      },
    },
    {
      title: '资讯形式',
      dataIndex: 'newsType',
      search: false,
      valueEnum: enum2ValueEnum(NewsTypeEnum),
    },
    {
      title: '排序',
      dataIndex: 'sort',
      search: false,
      tooltip: '相同排序的按更新时间进行排序，越晚更新的越前',
    },
    {
      title: '是否发布',
      dataIndex: 'newsStatus',
      search: false,
      renderText: (value, { id }) => (
        <Switch
          value={value === NewsStatusEnum.PUBLISH}
          onChange={(checked) => {
            modal.confirm({
              title: '提示',
              content: checked ? '确定要发布该资讯吗？' : '确定要取消发布该资讯吗？',
              onOk: async () => {
                await updateNewsStatus({
                  id,
                  newsStatus: checked ? NewsStatusEnum.PUBLISH : NewsStatusEnum.TO_BE_PUBLISH,
                });
                message.success('操作成功');
                actionRef.current?.reload();
              },
            });
          }}
        />
      ),
    },
    {
      title: '封面（横图）',
      dataIndex: 'horizontalImageOssUrl',
      search: false,
      renderText: (url) => (
        <Image
          src={url}
          width={25}
          style={{ height: 25, objectFit: 'contain' }}
          preview={{ mask: <EyeOutlined /> }}
        />
      ),
    },
    {
      title: '封面（竖图）',
      dataIndex: 'imageOssUrl',
      search: false,
      renderText: (url) => (
        <Image
          src={url}
          width={25}
          style={{ height: 25, objectFit: 'contain' }}
          preview={{ mask: <EyeOutlined /> }}
        />
      ),
    },
    {
      title: '累计点击量',
      dataIndex: 'readCount',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      search: false,
      width: 120,
      render: (_, { id }) => (
        <div className="flex justify-center gap-2">
          <a
            onClick={() => {
              setCurrentEditId(id);
              setNewsModalOpen(true);
            }}
          >
            编辑
          </a>
          <Typography.Link
            type="danger"
            onClick={() =>
              modal.confirm({
                title: '删除',
                content: '确定要删除该资讯吗？',
                onOk: async () => {
                  await deleteNews(id);
                  message.success('删除成功');
                  actionRef.current?.reload();
                },
              })
            }
          >
            删除
          </Typography.Link>
        </div>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        rowKey="id"
        size="small"
        columns={columns}
        sticky
        options={false}
        toolbar={{
          actions: [
            <Button
              type="primary"
              onClick={() => {
                setCurrentEditId(null);
                setNewsModalOpen(true);
              }}
            >
              新增
            </Button>,
          ],
        }}
        request={async ({ current, ...params }) => {
          const res = await getNewsList({ pageNum: current, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <NewsModal
        open={newsModalOpen}
        currentEditId={currentEditId}
        onCancel={() => setNewsModalOpen(false)}
        onSuccess={() => {
          setNewsModalOpen(false);
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default News;
