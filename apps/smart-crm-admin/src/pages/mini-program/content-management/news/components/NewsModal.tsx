import { useRef } from 'react';
import { Editor, FileUpload } from '@src/components';
import { EditorRef } from '@src/components/Editor';
import { getAllCategoryTree } from '@src/services/mini-program/content-management/category';
import {
  getNewsDetail,
  saveNews,
  savePublishNews,
} from '@src/services/mini-program/content-management/news';
import { NewsTypeEnum, SaveNewsReq } from '@src/services/mini-program/content-management/news/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Form, Input, InputNumber, Modal, ModalProps, Segmented, Select } from 'antd';

interface NewsModalProps extends ModalProps {
  currentEditId: number | null;
  onSuccess: () => void;
}

const NewsModal: React.FC<NewsModalProps> = ({ open, currentEditId, onSuccess, ...props }) => {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const editorRef = useRef<EditorRef>(null);
  const saveType = useRef<'save' | 'publish'>('save');

  const isEdit = !!currentEditId;

  const { data: categoryOptions } = useRequest(getAllCategoryTree, { ready: open });
  const { runAsync: save, loading: saveLoading } = useRequest(saveNews, { manual: true });
  const { runAsync: savePublish, loading: savePublishLoading } = useRequest(savePublishNews, {
    manual: true,
  });
  const { loading } = useRequest(() => getNewsDetail(currentEditId!), {
    ready: open && isEdit,
    onSuccess: ({
      imageOssKey,
      imageOssUrl,
      horizontalImageOssKey,
      horizontalImageOssUrl,
      editorText,
      ...params
    }) => {
      form.setFieldsValue({
        ...params,
        verticalImage: [
          {
            fileUrl: imageOssUrl,
            fileType: 'image/jpg',
            fileKey: imageOssKey,
          },
        ],
        horizontalImage: [
          {
            fileUrl: horizontalImageOssUrl,
            fileType: 'image/jpg',
            fileKey: horizontalImageOssKey,
          },
        ],
      });

      // 因为 loading 时没渲染出来，延迟才能获取到 ref
      setTimeout(() => {
        editorRef.current?.setContents(editorText);
      });
    },
  });

  const handleSave = async ({ horizontalImage, verticalImage, ...values }: any) => {
    const params: SaveNewsReq = {
      ...(isEdit ? { id: currentEditId } : {}),
      ...values,
      horizontalImageOssKey: horizontalImage[0].fileKey,
      imageOssKey: verticalImage[0].fileKey,
      content: editorRef.current?.getHTML(),
    };

    if (saveType.current === 'save') {
      await save(params);
      message.success('保存成功');
    } else {
      await savePublish(params);
      message.success('发布成功');
    }

    onSuccess();
  };

  return (
    <Modal
      open={open}
      destroyOnHidden
      width={700}
      title={isEdit ? '编辑资讯' : '新增资讯'}
      loading={isEdit ? loading : false}
      confirmLoading={saveLoading}
      styles={{
        body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
      }}
      footer={(_, { OkBtn, CancelBtn }) => (
        <>
          <CancelBtn />
          <OkBtn />
          {!isEdit && (
            <Button
              type="primary"
              loading={savePublishLoading}
              onClick={() => {
                saveType.current = 'publish';
                form.submit();
              }}
            >
              发布
            </Button>
          )}
        </>
      )}
      okText={isEdit ? '保存' : '保存，暂不发布'}
      modalRender={(node) => (
        <Form
          form={form}
          clearOnDestroy
          labelCol={{ span: 4 }}
          scrollToFirstError
          onFinish={handleSave}
        >
          {node}
        </Form>
      )}
      onOk={() => {
        saveType.current = 'save';
        form.submit();
      }}
      {...props}
    >
      <Form.Item
        label="标题"
        name="title"
        rules={[
          { required: true, message: '请输入标题' },
          { max: 40, message: '不能超过 40 个字符' },
        ]}
      >
        <Input placeholder="请输入标题" />
      </Form.Item>
      <Form.Item
        label="资讯形式"
        name="newsType"
        initialValue={NewsTypeEnum.图文}
        rules={[{ required: true, message: '请选择资讯形式' }]}
      >
        <Segmented options={enum2Options(NewsTypeEnum)} />
      </Form.Item>
      <Form.Item label="分类" name="categoryId" rules={[{ required: true, message: '请选择分类' }]}>
        <Select
          placeholder="请选择分类"
          options={categoryOptions?.map((i) => ({
            ...i,
            ...(i.childList.length > 0 ? { options: i.childList } : {}),
          }))}
          fieldNames={{ label: 'title', value: 'id' }}
          style={{ width: 200 }}
        />
      </Form.Item>
      <Form.Item label="排序" name="sort" rules={[{ required: true, message: '请输入排序' }]}>
        <InputNumber
          min={0}
          max={9999}
          precision={0}
          placeholder="请输入排序"
          style={{ width: 100 }}
        />
      </Form.Item>
      <FileUpload
        formItemProps={{
          label: '封面（横图）',
          name: 'horizontalImage',
          rules: [{ required: true, message: '请上传封面（横图）' }],
          extra: '建议比例 686:320',
        }}
        uploadProps={{
          maxCount: 1,
          accept: 'image/*',
        }}
      />
      <FileUpload
        formItemProps={{
          label: '封面（竖图）',
          name: 'verticalImage',
          rules: [{ required: true, message: '请上传封面（竖图）' }],
          extra: '建议比例 1:1（首页轮播 332:480）',
        }}
        uploadProps={{
          maxCount: 1,
          accept: 'image/*',
        }}
      />
      <Form.Item noStyle dependencies={['newsType']}>
        {({ getFieldValue }) => {
          const newsType = getFieldValue('newsType');

          if (newsType === NewsTypeEnum.图文) {
            return (
              <Form.Item
                label="内容"
                name="editorText"
                required
                rules={[
                  {
                    validator: (_, value) => {
                      if (value === '[{"insert":"\\n"}]') {
                        return Promise.reject(new Error('请输入内容'));
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Editor ref={editorRef} />
              </Form.Item>
            );
          } else if (newsType === NewsTypeEnum.视频号) {
            return (
              <>
                <Form.Item
                  label="视频号 ID"
                  name="channelsId"
                  rules={[{ required: true, message: '请输入视频号 ID' }]}
                >
                  <Input placeholder="请输入视频号 ID" />
                </Form.Item>
                <Form.Item
                  label="视频 ID"
                  name="videoId"
                  rules={[{ required: true, message: '请输入视频 ID' }]}
                >
                  <Input placeholder="请输入视频 ID" />
                </Form.Item>
              </>
            );
          }
        }}
      </Form.Item>
    </Modal>
  );
};

export default NewsModal;
