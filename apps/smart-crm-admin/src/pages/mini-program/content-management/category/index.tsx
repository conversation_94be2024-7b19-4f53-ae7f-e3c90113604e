import { useRef, useState } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@src/components';
import {
  changeCollegeStatus,
  changeHomePosterJumpFlag,
  deleteCategory,
  getCategoryList,
} from '@src/services/mini-program/content-management/category';
import { CategoryListItemType } from '@src/services/mini-program/content-management/category/type';
import { App, Button, Switch, Tooltip, Typography } from 'antd';
import CategoryModal from './components/CategoryModal';

const Category = () => {
  const { modal, message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [categoryModalOpen, setCategoryModalOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<{
    id: number;
    title: string;
    parentId: number;
  }>();

  const columns: ProColumns<CategoryListItemType>[] = [
    {
      title: '分类名称',
      dataIndex: 'title',
    },
    {
      title: '是否高校',
      dataIndex: 'collegeFlag',
      search: false,
      renderText: (value, { id, parentId }) => {
        if (parentId) {
          return null;
        }

        return (
          <Tooltip
            title={value ? '必须要有一个开启高校的分类，若要关闭，请开启其它分类的高校状态' : ''}
          >
            <Switch
              value={value}
              disabled={value}
              onChange={() =>
                modal.confirm({
                  title: '提示',
                  content: '确定要开启该分类的高校状态吗？开启后其它分类的状态将会被关闭。',
                  onOk: async () => {
                    await changeCollegeStatus({ id, collegeFlag: true });
                    message.success('修改成功');
                    actionRef.current?.reload();
                  },
                })
              }
            />
          </Tooltip>
        );
      },
    },
    {
      title: '是否首页海报跳转',
      dataIndex: 'homePosterJumpFlag',
      search: false,
      renderText: (value, { id, parentId }) => {
        if (parentId) {
          return null;
        }

        return (
          <Tooltip
            title={
              value
                ? '必须要有一个开启首页海报跳转的分类，若要关闭，请开启其它分类的首页海报跳转状态'
                : ''
            }
          >
            <Switch
              value={value}
              disabled={value}
              onChange={(homePosterJumpFlag) =>
                modal.confirm({
                  title: '提示',
                  content:
                    '确定要将小程序的首页海报跳转地址设定为该分类吗？开启后其他分类的状态将会关闭。',
                  onOk: async () => {
                    await changeHomePosterJumpFlag({ id, homePosterJumpFlag });
                    message.success('修改成功');
                    actionRef.current?.reload();
                  },
                })
              }
            />
          </Tooltip>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      search: false,
      width: 120,
      render: (_, { id, title, parentId }) => (
        <div className="flex justify-center gap-2">
          <a
            onClick={() => {
              setInitialValues({ id, title, parentId });
              setCategoryModalOpen(true);
            }}
          >
            编辑
          </a>
          <Typography.Link
            type="danger"
            onClick={() =>
              modal.confirm({
                title: '删除',
                content: '确定要删除该分类吗？',
                onOk: async () => {
                  await deleteCategory(id);
                  message.success('删除成功');
                  actionRef.current?.reload();
                },
              })
            }
          >
            删除
          </Typography.Link>
        </div>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        rowKey="id"
        size="small"
        columns={columns}
        sticky
        options={false}
        toolbar={{
          actions: [
            <Button
              type="primary"
              onClick={() => {
                setInitialValues(undefined);
                setCategoryModalOpen(true);
              }}
            >
              新增
            </Button>,
          ],
        }}
        request={async ({ current, ...params }) => {
          const res = await getCategoryList({ pageNum: current, ...params });

          return {
            data: res.result.map((i) => ({
              ...i,
              children: i.childList.length > 0 ? i.childList : null,
            })),
            total: res.total,
          };
        }}
      />

      <CategoryModal
        open={categoryModalOpen}
        initialValues={initialValues}
        onCancel={() => setCategoryModalOpen(false)}
        onSuccess={() => {
          setCategoryModalOpen(false);
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default Category;
