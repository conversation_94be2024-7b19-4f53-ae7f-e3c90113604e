import {
  getAllCategoryList,
  saveCategory,
} from '@src/services/mini-program/content-management/category';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps, Select } from 'antd';

interface CategoryModalProps extends ModalProps {
  initialValues?: { id: number; title: string; parentId: number };
  onSuccess: () => void;
}

const CategoryModal: React.FC<CategoryModalProps> = ({
  open,
  initialValues,
  onSuccess,
  ...props
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();

  const showParentSelect = initialValues?.parentId !== 0;

  const { runAsync: save, loading: saveLoading } = useRequest(saveCategory, { manual: true });
  const { data: categoryList } = useRequest(() => getAllCategoryList({ parentId: 0 }), {
    ready: open && showParentSelect,
  });

  const handleSave = async (values: any) => {
    await save({ ...values, id: initialValues?.id, parentId: values.parentId || 0 });
    message.success('保存成功');
    onSuccess();
  };

  return (
    <Modal
      title={initialValues ? '编辑分类' : '新增分类'}
      destroyOnHidden
      open={open}
      confirmLoading={saveLoading}
      onOk={form.submit}
      {...props}
    >
      <Form
        form={form}
        clearOnDestroy
        labelCol={{ span: 4 }}
        initialValues={initialValues}
        onFinish={handleSave}
      >
        <Form.Item
          label="分类名称"
          name="title"
          rules={[
            { required: true, message: '请输入分类名称' },
            { max: 40, message: '不能超过 40 个字符' },
          ]}
        >
          <Input placeholder="请输入分类名称" />
        </Form.Item>
        {showParentSelect && (
          <Form.Item label="上级分类" name="parentId">
            <Select
              allowClear
              placeholder="请选择上级分类"
              options={categoryList}
              fieldNames={{ label: 'title', value: 'id' }}
            />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default CategoryModal;
