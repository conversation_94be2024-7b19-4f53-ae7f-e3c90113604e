import { useRef, useState } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ExportButton, ProTable } from '@src/components';
import EditablePhone from '@src/components/Editable/EditablePhone';
import {
  deleteFeedback,
  exportFeedback,
  getFeedbackList,
} from '@src/services/mini-program/content-management/feedback';
import { useRequest } from 'ahooks';
import { App, Typography } from 'antd';

const Feedback = () => {
  const { modal, message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const filters = useRef({});

  const { data, runAsync: getList } = useRequest(getFeedbackList, { manual: true });

  const columns: ProColumns[] = [
    {
      title: '客户姓名',
      dataIndex: 'name',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      renderText: (value, { phoneSensitive }) => (
        <EditablePhone value={value} fieldProps={{ sensitiveValue: phoneSensitive }} />
      ),
    },
    {
      title: '反馈信息',
      dataIndex: 'message',
      search: false,
      renderText: (value) => (
        <Typography.Text ellipsis={{ tooltip: true }}>{value}</Typography.Text>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      search: false,
      width: 80,
      render: (_, { id }) => (
        <Typography.Link
          type="danger"
          onClick={() =>
            modal.confirm({
              title: '删除',
              content: '确定要删除该反馈吗？',
              onOk: async () => {
                await deleteFeedback(id);
                message.success('删除成功');
                actionRef.current?.reload();
              },
            })
          }
        >
          删除
        </Typography.Link>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        rowKey="id"
        size="small"
        sticky
        options={false}
        rowSelection={{
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        tableAlertRender={false}
        toolbar={{
          title: (
            <span className="font-normal text-sm">
              共 {data?.total || 0} 条信息
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 {selectedRowKeys.length} 条
                  <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            <ExportButton
              total={selectedRowKeys.length || data?.total}
              request={() =>
                exportFeedback(selectedRowKeys.length ? { ids: selectedRowKeys } : filters.current)
              }
            />,
          ],
        }}
        columns={columns}
        request={async ({ current, pageSize, ...params }) => {
          filters.current = params;

          const res = await getList({ pageNum: current, pageSize, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />
    </PageContainer>
  );
};

export default Feedback;
