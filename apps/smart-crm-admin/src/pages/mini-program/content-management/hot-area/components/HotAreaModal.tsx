import { useMemo, useRef } from 'react';
import { Editor, FileUpload } from '@src/components';
import { EditorRef } from '@src/components/Editor';
import useDistricts from '@src/hooks/useDistricts';
import {
  createHotArea,
  editHotArea,
  getHorAreaDetail,
} from '@src/services/mini-program/content-management/hot-area';
import { HotAreaTypeEnum } from '@src/services/mini-program/content-management/hot-area/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, Input, InputNumber, Modal, ModalProps, Segmented, Select } from 'antd';

interface HotAreaModalProps extends ModalProps {
  currentEditId: number | null;
  onSuccess: () => void;
}

const HotAreaModal: React.FC<HotAreaModalProps> = ({
  open,
  currentEditId,
  onSuccess,
  ...props
}) => {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const editorRef = useRef<EditorRef>(null);

  const { data: districts } = useDistricts();

  const { runAsync: create, loading: createLoading } = useRequest(createHotArea, { manual: true });
  const { runAsync: edit, loading: editLoading } = useRequest(editHotArea, { manual: true });
  const { loading } = useRequest(() => getHorAreaDetail(currentEditId!), {
    ready: open && !!currentEditId,
    onSuccess: ({ imageOssKey, imageOssUrl, editorText, ...params }) => {
      form.setFieldsValue({
        ...params,
        attachments: [
          {
            fileUrl: imageOssUrl,
            fileType: 'image/jpg',
            fileKey: imageOssKey,
          },
        ],
      });

      // 因为 loading 时没渲染出来，延迟才能获取到 ref
      setTimeout(() => {
        editorRef.current?.setContents(editorText);
      });
    },
  });

  const provinceList = useMemo(() => districts?.filter((i) => i.deep === 0), [districts]);

  const handleSave = async ({ attachments, ...values }: any) => {
    const params = {
      ...values,
      imageOssKey: attachments[0].fileKey,
      content: editorRef.current?.getHTML(),
    };

    if (currentEditId) {
      await edit({
        id: currentEditId,
        ...params,
      });
    } else {
      await create(params);
    }

    message.success('保存成功');

    onSuccess();
  };

  return (
    <Modal
      open={open}
      destroyOnHidden
      width={700}
      title={currentEditId ? '编辑热门区域' : '新增热门区域'}
      loading={currentEditId ? loading : false}
      confirmLoading={createLoading || editLoading}
      modalRender={(node) => (
        <Form
          form={form}
          clearOnDestroy
          scrollToFirstError
          labelCol={{ span: 3 }}
          onFinish={handleSave}
        >
          {node}
        </Form>
      )}
      onOk={form.submit}
      {...props}
    >
      <Form.Item
        label="标题"
        name="title"
        rules={[
          { required: true, message: '请输入标题' },
          { max: 40, message: '不能超过 40 个字符' },
        ]}
      >
        <Input placeholder="请输入标题" />
      </Form.Item>
      <Form.Item label="排序" name="sort" rules={[{ required: true, message: '请输入排序' }]}>
        <InputNumber
          min={0}
          max={9999}
          precision={0}
          placeholder="请输入排序"
          style={{ width: 100 }}
        />
      </Form.Item>
      <Form.Item
        label="所属省份"
        name="districtId"
        rules={[{ required: true, message: '请选择所属省份' }]}
      >
        <Select
          placeholder="请选择所属省份"
          options={provinceList}
          fieldNames={{ label: 'name', value: 'id' }}
          showSearch
          optionFilterProp="name"
          style={{ width: 200 }}
        />
      </Form.Item>
      <Form.Item
        label="类型"
        name="type"
        initialValue={HotAreaTypeEnum.重点区域}
        rules={[{ required: true, message: '请选择类型' }]}
      >
        <Segmented options={enum2Options(HotAreaTypeEnum)} />
      </Form.Item>
      <FileUpload
        formItemProps={{
          label: '封面',
          rules: [{ required: true, message: '请上传封面' }],
        }}
        uploadProps={{
          maxCount: 1,
          accept: 'image/*',
        }}
      />
      <Form.Item
        label="内容"
        name="editorText"
        required
        rules={[
          {
            validator: (_, value) => {
              if (value === '[{"insert":"\\n"}]') {
                return Promise.reject(new Error('请输入内容'));
              }

              return Promise.resolve();
            },
          },
        ]}
      >
        <Editor ref={editorRef} />
      </Form.Item>
    </Modal>
  );
};

export default HotAreaModal;
