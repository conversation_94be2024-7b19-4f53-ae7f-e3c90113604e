import { useMemo, useRef, useState } from 'react';
import { EyeOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@src/components';
import useDistricts from '@src/hooks/useDistricts';
import {
  deleteHotArea,
  getHotAreaList,
} from '@src/services/mini-program/content-management/hot-area';
import { HotAreaTypeEnum } from '@src/services/mini-program/content-management/hot-area/type';
import { enum2ValueEnum } from '@src/utils';
import { App, Button, Image, Typography } from 'antd';
import HotAreaModal from './components/HotAreaModal';

const HotArea = () => {
  const { modal, message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [hotAreaModalOpen, setHotAreaModalOpen] = useState(false);
  const [currentEditId, setCurrentEditId] = useState<number | null>(null);

  const { data: districts } = useDistricts();

  const provinceList = useMemo(() => districts?.filter((i) => i.deep === 0), [districts]);

  const columns: ProColumns[] = [
    {
      title: '标题',
      dataIndex: 'title',
    },
    {
      title: '排序',
      dataIndex: 'sort',
      search: false,
      tooltip: '相同排序的按更新时间进行排序，越晚更新的越前',
    },
    {
      title: '所属省份',
      dataIndex: 'districtId',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        options: provinceList,
        fieldNames: {
          label: 'name',
          value: 'id',
        },
      },
    },
    {
      title: '类型',
      dataIndex: 'type',
      valueEnum: enum2ValueEnum(HotAreaTypeEnum),
    },
    {
      title: '封面图片',
      dataIndex: 'imageOssUrl',
      search: false,
      renderText: (url) => (
        <Image
          src={url}
          width={25}
          style={{ height: 25, objectFit: 'contain' }}
          preview={{ mask: <EyeOutlined /> }}
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      width: 80,
      search: false,
      render: (_, { id }) => (
        <div className="flex justify-center gap-2">
          <a
            onClick={() => {
              setCurrentEditId(id);
              setHotAreaModalOpen(true);
            }}
          >
            编辑
          </a>
          <Typography.Link
            type="danger"
            onClick={() =>
              modal.confirm({
                title: '删除',
                content: '确定要删除该热门区域吗？',
                onOk: async () => {
                  await deleteHotArea(id);
                  message.success('删除成功');
                  actionRef.current?.reload();
                },
              })
            }
          >
            删除
          </Typography.Link>
        </div>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        options={false}
        size="small"
        sticky
        rowKey="id"
        toolbar={{
          actions: [
            <Button
              type="primary"
              onClick={() => {
                setCurrentEditId(null);
                setHotAreaModalOpen(true);
              }}
            >
              新增
            </Button>,
          ],
        }}
        columns={columns}
        request={async ({ current, ...params }) => {
          const res = await getHotAreaList({ pageNum: current, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <HotAreaModal
        open={hotAreaModalOpen}
        currentEditId={currentEditId}
        onCancel={() => setHotAreaModalOpen(false)}
        onSuccess={() => {
          setHotAreaModalOpen(false);
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default HotArea;
