import { useRef, useState } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { commonBOTypeOptions } from '@src/common/constants';
import { ProTable, UserSelect } from '@src/components';
import { BusinessOpportunityTypeEnum } from '@src/services/business-opportunity/type';
import {
  createOrUpdateAppointmentUser,
  deleteAppointmentUser,
  getAppointmentUserList,
} from '@src/services/mini-program/appointment-schedule';
import {
  AppointmentUserDTO,
  AppointmentUserStatusEnum,
} from '@src/services/mini-program/appointment-schedule/type';
import { App, Button, Tag, Typography } from 'antd';
import { useNavigate } from 'react-router-dom';
import CreateEditAppointmentUserModal from './components/CreateEditAppointmentUserModal';

const AppointmentTemplate = () => {
  const navigate = useNavigate();
  const { modal, message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [open, setOpen] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<AppointmentUserDTO | null>(null);

  const changeStatus = (id: number, status: AppointmentUserStatusEnum) => {
    const text = status === AppointmentUserStatusEnum.ENABLE ? '启用' : '停用';

    modal.confirm({
      title: '修改状态',
      content: `确定要${text}该成员吗？`,
      onOk: async () => {
        await createOrUpdateAppointmentUser({ id, status });
        message.success('操作成功');
        actionRef.current?.reload();
      },
    });
  };

  const columns: ProColumns<AppointmentUserDTO>[] = [
    {
      title: '面审负责人',
      dataIndex: 'name',
      search: {
        transform: (userId) => ({
          userId,
        }),
      },
      renderFormItem: () => <UserSelect />,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      search: false,
    },
    {
      title: '负责类型',
      dataIndex: 'responsibleType',
      search: false,
      renderText: (value) => {
        return value
          ?.split(',')
          ?.map((item: BusinessOpportunityTypeEnum) => {
            const option = commonBOTypeOptions.find((_option) => _option.value === item);

            return option?.label;
          })
          ?.join(',');
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      search: false,
      renderText: (value) =>
        value === AppointmentUserStatusEnum.ENABLE ? (
          <Tag color="success">启用</Tag>
        ) : (
          <Tag color="error">停用</Tag>
        ),
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      search: false,
      width: 200,
      render: (_, record) => {
        const { id, status, name } = record;

        return (
          <div className="flex gap-2 justify-center">
            {status === AppointmentUserStatusEnum.ENABLE ? (
              <Typography.Link
                type="danger"
                onClick={() => changeStatus(id, AppointmentUserStatusEnum.DISABLE)}
              >
                停用
              </Typography.Link>
            ) : (
              <a onClick={() => changeStatus(id, AppointmentUserStatusEnum.ENABLE)}>启用</a>
            )}
            <a
              onClick={() => {
                setCurrentRecord(record);
                setOpen(true);
              }}
            >
              编辑
            </a>
            <a onClick={() => navigate(`/mini-program/appointment-schedule/${id}`)}>设置排班</a>
            <Typography.Link
              type="danger"
              onClick={() =>
                modal.confirm({
                  title: '删除',
                  content: `确定要删除面审负责人 “${name}” 吗？`,
                  onOk: async () => {
                    await deleteAppointmentUser(id);
                    message.success('删除成功');
                    actionRef.current?.reload();
                  },
                })
              }
            >
              删除
            </Typography.Link>
          </div>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        rowKey="id"
        options={false}
        size="small"
        cardProps={false}
        bordered
        sticky
        toolbar={{
          actions: [
            <Button
              type="primary"
              onClick={() => {
                setOpen(true);
                setCurrentRecord(null);
              }}
            >
              新增
            </Button>,
          ],
        }}
        columns={columns}
        request={async ({ current, ...params }) => {
          const res = await getAppointmentUserList({ pageNum: current, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <CreateEditAppointmentUserModal
        open={open}
        currentRecord={currentRecord}
        onCancel={() => setOpen(false)}
        onSuccess={() => {
          setOpen(false);
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default AppointmentTemplate;
