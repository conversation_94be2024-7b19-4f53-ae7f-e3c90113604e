import { useEffect } from 'react';
import { commonBOTypeOptions } from '@src/common/constants';
import { UserSelect } from '@src/components';
import { createOrUpdateAppointmentUser } from '@src/services/mini-program/appointment-schedule';
import {
  AppointmentUserDTO,
  AppointmentUserStatusEnum,
} from '@src/services/mini-program/appointment-schedule/type';
import { IdentityTypeEnum } from '@src/services/mini-program/identity/type';
import { useRequest } from 'ahooks';
import { App, Form, Modal, ModalProps, Select } from 'antd';

interface CreateEditAppointmentUserModalProps extends ModalProps {
  currentRecord: AppointmentUserDTO | null;
  onSuccess: () => void;
}

const CreateEditAppointmentUserModal: React.FC<CreateEditAppointmentUserModalProps> = ({
  open,
  currentRecord,
  onSuccess,
  ...props
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();

  const { runAsync, loading } = useRequest(createOrUpdateAppointmentUser, { manual: true });

  useEffect(() => {
    if (open && currentRecord) {
      form.setFieldsValue({
        ...currentRecord,
        responsibleType: currentRecord.responsibleType?.split(','),
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  return (
    <Modal
      title={currentRecord ? '编辑面审负责人' : '新增面审负责人'}
      open={open}
      destroyOnHidden
      confirmLoading={loading}
      onOk={form.submit}
      {...props}
    >
      <Form
        form={form}
        clearOnDestroy
        labelCol={{ span: 5 }}
        onFinish={async (values) => {
          if (currentRecord) {
            await runAsync({
              id: currentRecord.id,
              ...values,
              responsibleType: values.responsibleType.join(','),
            });
            message.success('修改成功');
          } else {
            // 后端需要默认禁用参数
            await runAsync({
              ...values,
              status: AppointmentUserStatusEnum.DISABLE,
              responsibleType: values.responsibleType.join(','),
            });
            message.success('创建成功');
          }

          onSuccess();
        }}
      >
        {currentRecord ? (
          <Form.Item label="面审负责人" required>
            <UserSelect disabled value={currentRecord.name} />
          </Form.Item>
        ) : (
          <Form.Item
            label="面审负责人"
            name="userId"
            rules={[{ required: true, message: '请选择面审负责人' }]}
          >
            <UserSelect
              placeholder="请选择面审负责人"
              transformOptions={(options) =>
                options.filter((i) => i.identityType === IdentityTypeEnum.面审老师)
              }
            />
          </Form.Item>
        )}
        <Form.Item
          label="负责类型"
          name="responsibleType"
          rules={[{ required: true, message: '请选择负责类型' }]}
        >
          <Select mode="multiple" placeholder="请选择负责类型" options={commonBOTypeOptions} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateEditAppointmentUserModal;
