import { useEffect, useMemo } from 'react';
import { CloseCircleFilled, PlusOutlined } from '@ant-design/icons';
import EditableDate from '@src/components/Editable/EditableDate';
import {
  createAppointmentSchedule,
  updateAppointmentSchedule,
} from '@src/services/mini-program/appointment-schedule';
import { AppointmentScheduleDTO } from '@src/services/mini-program/appointment-schedule/type';
import { useRequest } from 'ahooks';
import { App, Button, DatePicker, Form, Modal, ModalProps, TimePicker } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { nanoid } from 'nanoid';
import { useParams } from 'react-router-dom';

interface ScheduleModalProps extends ModalProps {
  currentData?: AppointmentScheduleDTO;
  onSuccess: () => void;
}

const defaultTime = ['10:00', '11:00', '13:00', '14:00', '15:00', '16:00', '17:00'];

const ScheduleModal: React.FC<ScheduleModalProps> = ({
  open,
  currentData,
  onSuccess,
  ...props
}) => {
  const { id } = useParams();
  const { message } = App.useApp();
  const [form] = Form.useForm();

  const { runAsync: create, loading: createLoading } = useRequest(createAppointmentSchedule, {
    manual: true,
  });
  const { runAsync: update, loading: updateLoading } = useRequest(updateAppointmentSchedule, {
    manual: true,
  });

  const initialValue = useMemo(() => {
    if (!currentData) {
      return defaultTime.map((time) => ({
        time: dayjs(time, 'HH:mm'),
        id: nanoid(),
      }));
    }
  }, [currentData]);

  useEffect(() => {
    if (open && currentData) {
      form.setFieldsValue({
        date: currentData.day,
        timeFrameList: currentData.timeFrames.map((i) => ({
          time: dayjs(i.beginTime, 'HH:mm'),
          id: i.id,
          lockCount: i.lockCount,
        })),
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  const handleSave = async ({ date, timeFrameList }: any) => {
    const interviewManagerId = Number(id);

    if (currentData) {
      await update({
        interviewManagerId,
        date,
        timeFrameList: timeFrameList.map((i: { time: Dayjs; id: string | number }) => ({
          // id 是 string 类型说明是 nanoid 生成，是新增的，不传 id
          id: typeof i.id === 'number' ? i.id : undefined,
          beginTime: i.time.format('HH:mm'),
        })),
      });
    } else {
      await create({
        interviewManagerId,
        ...date,
        appointmentTimeList: timeFrameList.map((i: { time: Dayjs; id: string }) =>
          i.time.format('HH:mm'),
        ),
      });
    }

    message.success('设置成功');
    onSuccess();
  };

  return (
    <Modal
      open={open}
      title="设置排班"
      destroyOnHidden
      styles={{
        body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
      }}
      confirmLoading={createLoading || updateLoading}
      onOk={form.submit}
      {...props}
    >
      <Form form={form} clearOnDestroy labelCol={{ span: 4 }} onFinish={handleSave}>
        {currentData ? (
          <EditableDate
            editable
            formItemProps={{
              label: '排班日期',
              name: 'date',
              rules: [{ required: true, message: '请选择排班日期' }],
            }}
          />
        ) : (
          <Form.Item
            label="排班日期"
            name="date"
            getValueProps={(value) => ({
              value: value
                ? [dayjs(value.appointmentDayBegin), dayjs(value.appointmentDayEnd)]
                : undefined,
            })}
            normalize={(value) =>
              value
                ? {
                    appointmentDayBegin: value[0].format('YYYY-MM-DD'),
                    appointmentDayEnd: value[1].format('YYYY-MM-DD'),
                  }
                : undefined
            }
            rules={[{ required: true, message: '请选择排班日期' }]}
          >
            <DatePicker.RangePicker />
          </Form.Item>
        )}
        <Form.Item label="排班时段">
          <Form.List name="timeFrameList" initialValue={initialValue}>
            {(fields, { add, remove }) => (
              <div className="flex flex-col gap-2">
                {fields.map((field) => {
                  const disabled = form.getFieldValue(['timeFrameList', field.name, 'lockCount']);

                  return (
                    <div key={field.key} className="flex items-center">
                      <Form.Item
                        className="mb-0"
                        name={[field.name, 'time']}
                        validateFirst
                        // 其它选项
                        dependencies={fields
                          .filter((i) => i.name !== field.name)
                          .map((i) => ['timeFrameList', i.name, 'time'])}
                        rules={[
                          { required: true, message: '请选择' },
                          {
                            validator: (_, value) => {
                              // 如果跟其它选项有重复
                              if (
                                fields
                                  .filter((i) => i.name !== field.name)
                                  .some((i) =>
                                    (value as Dayjs).isSame(
                                      form.getFieldValue(['timeFrameList', i.name, 'time']),
                                    ),
                                  )
                              ) {
                                return Promise.reject(new Error('不能重复'));
                              }

                              return Promise.resolve();
                            },
                          },
                        ]}
                      >
                        <TimePicker format="HH:mm" disabled={disabled} />
                      </Form.Item>
                      {disabled ? (
                        <span className="ml-2 text-gray-400">已被预约</span>
                      ) : (
                        <Button
                          icon={<CloseCircleFilled className="text-gray-400" />}
                          type="text"
                          className="ml-2"
                          onClick={() => remove(field.name)}
                        />
                      )}
                    </div>
                  );
                })}
                <div>
                  <Button
                    type="text"
                    disabled={fields.length >= 15}
                    className={fields.length >= 15 ? '' : '!text-primary'}
                    icon={<PlusOutlined />}
                    onClick={() => add({ id: nanoid() })}
                  >
                    添加
                  </Button>
                </div>
              </div>
            )}
          </Form.List>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ScheduleModal;
