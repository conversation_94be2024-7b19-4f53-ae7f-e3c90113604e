import { useMemo, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { getAppointmentSchedule } from '@src/services/mini-program/appointment-schedule';
import { AppointmentScheduleDTO } from '@src/services/mini-program/appointment-schedule/type';
import { useRequest } from 'ahooks';
import { <PERSON>ton, Card, DatePicker, Spin } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useNavigate, useParams } from 'react-router-dom';
import ScheduleModal from './components/ScheduleModal';

function getWeekDays(startDay: Dayjs) {
  return Array(7)
    .fill(null)
    .map((_, index) => (index === 0 ? startDay : startDay.add(index, 'day')));
}

const indexToCNWeekMap: Record<string, string> = {
  0: '周一',
  1: '周二',
  2: '周三',
  3: '周四',
  4: '周五',
  5: '周六',
  6: '周日',
};

const AppointmentTemplate = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [startDay, setStartDay] = useState(() => dayjs().startOf('week'));
  const [open, setOpen] = useState(false);
  const [currentData, setCurrentData] = useState<AppointmentScheduleDTO>();

  const weekDays = useMemo(() => getWeekDays(startDay), [startDay]);

  const { data, loading, refresh } = useRequest(
    () =>
      getAppointmentSchedule({
        beginDate: weekDays[0].format('YYYY-MM-DD'),
        endDate: weekDays[weekDays.length - 1].format('YYYY-MM-DD'),
        interviewUserId: Number(id),
      }),
    { refreshDeps: [weekDays] },
  );

  return (
    <PageContainer onBack={() => navigate('/mini-program/appointment-schedule')}>
      <Card>
        <div className="flex items-center">
          <span className="flex-1 text-base">配置预约排班，供用户发起预约选择</span>
          <div className="flex-1 flex justify-center">
            <DatePicker
              size="large"
              variant="filled"
              picker="week"
              allowClear={false}
              value={startDay}
              format={(value) => {
                const _startDay = value.startOf('week');

                return `${_startDay.format('YYYY-MM-DD')} ~ ${_startDay
                  .add(6, 'day')
                  .format('YYYY-MM-DD')}`;
              }}
              onChange={(value) => setStartDay(value.startOf('week'))}
            />
          </div>
          <div className="flex-1 text-right">
            <Button
              type="primary"
              onClick={() => {
                setOpen(true);
                setCurrentData(undefined);
              }}
            >
              添加排班
            </Button>
          </div>
        </div>
        <Spin spinning={loading}>
          <div className="flex mt-5 divide-x divide-gray-50 border border-gray-100 rounded-lg overflow-auto">
            {weekDays.map((day, index) => {
              const item = data?.find((i) => i.day === day.format('YYYY-MM-DD'));
              const timeFrames = item?.timeFrames || [];

              return (
                <div key={index} className="flex flex-col" style={{ width: `${(1 / 7) * 100}%` }}>
                  <div className="text-center bg-gray-50 py-2">
                    {day.format('MM-DD')} <br /> {indexToCNWeekMap[index]}
                  </div>
                  <div className="flex flex-1 flex-col gap-3 items-center p-4 overflow-auto min-h-[530px]">
                    {timeFrames.length > 0 ? (
                      <>
                        {timeFrames.map((i) => (
                          <div
                            className="text-center border px-2 py-1 rounded-md w-full"
                            key={i.beginTime}
                          >
                            {dayjs(i.beginTime, 'HH:mm:ss').format('HH:mm')}
                          </div>
                        ))}

                        <Button
                          type="primary"
                          block
                          onClick={() => {
                            setOpen(true);
                            setCurrentData(item);
                          }}
                        >
                          编辑排班
                        </Button>
                      </>
                    ) : (
                      <div className="flex justify-center items-center h-full">暂无排班</div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </Spin>
      </Card>

      <ScheduleModal
        open={open}
        currentData={currentData}
        onCancel={() => setOpen(false)}
        onSuccess={() => {
          setOpen(false);
          refresh();
        }}
      />
    </PageContainer>
  );
};

export default AppointmentTemplate;
