import { useRef } from 'react';
import { ReloadOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@src/components';
import { getExportTaskList } from '@src/services/tool-center/export';
import { ExportTaskItem, ExportTaskStatusEnum } from '@src/services/tool-center/export/type';
import { Badge, BadgeProps, Button, Tooltip, Typography } from 'antd';

const badgeStatusMap: Record<ExportTaskStatusEnum, BadgeProps> = {
  [ExportTaskStatusEnum.PROCESSING]: { color: 'blue', text: '正在导出' },
  [ExportTaskStatusEnum.COMPLETED]: { status: 'success', text: '导出成功' },
  [ExportTaskStatusEnum.FAILED]: { status: 'error', text: '导出失败' },
};

const ExportCenter = () => {
  const actionRef = useRef<ActionType>(null);

  const columns: ProColumns<ExportTaskItem>[] = [
    { title: '功能模块', dataIndex: 'taskGroup' },
    {
      title: '操作时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      width: 200,
    },
    { title: '报表名称', dataIndex: 'fileName' },
    {
      title: '状态',
      dataIndex: 'status',
      width: 150,
      renderText: (value: ExportTaskStatusEnum) => <Badge {...badgeStatusMap[value]} />,
    },
    {
      title: '结果',
      key: 'operation',
      width: 150,
      render: (_, { status, url, rejectReasons }) => {
        if (status === ExportTaskStatusEnum.COMPLETED && url) {
          return <a href={url}>下载报表</a>;
        }

        if (status === ExportTaskStatusEnum.FAILED) {
          return (
            <Typography.Text type="danger">
              处理失败
              {rejectReasons && (
                <>
                  <Tooltip title={rejectReasons}>
                    <a className="ml-2">查看原因</a>
                  </Tooltip>
                </>
              )}
            </Typography.Text>
          );
        }

        return <Typography.Text type="secondary">处理中</Typography.Text>;
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable
        search={false}
        sticky
        actionRef={actionRef}
        options={false}
        toolbar={{
          actions: [
            <Button
              icon={<ReloadOutlined />}
              type="primary"
              onClick={() => actionRef.current?.reload()}
            >
              刷新
            </Button>,
          ],
        }}
        rowKey="randomId"
        size="small"
        columns={columns}
        request={async ({ current, pageSize }) => {
          const res = await getExportTaskList({ pageNum: current, pageSize });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />
    </PageContainer>
  );
};

export default ExportCenter;
