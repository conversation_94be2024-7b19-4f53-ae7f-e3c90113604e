import { useState } from 'react';
import { PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { UserSelect } from '@src/components';
import { getFlowRecordDetail, getFlowRecordList } from '@src/services/tool-center/flow-record';
import { FlowRecordItemDto, OperateTypeEnum } from '@src/services/tool-center/flow-record/type';
import { Button, Modal, Typography } from 'antd';

const operateTypeValueEnum = {
  [OperateTypeEnum.TRANSFER]: '转让线索',
  [OperateTypeEnum.ALLOCATION]: '分配线索',
  [OperateTypeEnum.GET]: '领取线索',
  [OperateTypeEnum.ABANDON]: '放弃线索',
  [OperateTypeEnum.DELETE]: '删除线索',
  [OperateTypeEnum.SHIFT]: '转移线索池',
};

const FlowRecord = () => {
  const [detailId, setDetailId] = useState<number | null>(null);

  const columns: ProColumns<FlowRecordItemDto>[] = [
    {
      title: '操作人',
      dataIndex: 'operateUserName',
      renderFormItem: () => <UserSelect />,
      search: {
        transform: (value) => ({
          operateUserId: value,
        }),
      },
    },
    { title: '操作时间', dataIndex: 'createTime', search: false, valueType: 'dateTime' },
    {
      title: '操作类型',
      dataIndex: 'operateType',
      valueType: 'select',
      valueEnum: operateTypeValueEnum,
    },
    { title: '流转原因', dataIndex: 'flowReason', search: false },
    {
      title: '操作结果',
      dataIndex: 'successNum',
      search: false,
      render: (_, { successNum, failNum }) => (
        <>
          <span>
            执行成功<Typography.Text type="success">（{successNum}个）</Typography.Text>
          </span>
          <span>
            执行失败<Typography.Text type="danger">（{failNum}个）</Typography.Text>
          </span>
        </>
      ),
    },
    {
      title: '操作',
      key: 'action',
      search: false,
      align: 'center',
      width: 100,
      render: (_, { id }) => (
        <Button type="link" onClick={() => setDetailId(id)}>
          详情
        </Button>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        sticky
        options={false}
        rowKey="id"
        size="small"
        cardProps={{ bodyStyle: { paddingTop: 16 } }}
        columns={columns}
        request={async ({ current, pageSize, ...params }) => {
          const res = await getFlowRecordList({ pageNum: current, pageSize, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <Modal
        open={!!detailId}
        width={800}
        destroyOnHidden
        title="流转详情"
        footer={null}
        onCancel={() => setDetailId(null)}
      >
        <ProTable
          options={false}
          size="small"
          search={false}
          rowKey="id"
          cardProps={false}
          scroll={{ y: 500 }}
          columns={[
            { title: '线索名称', dataIndex: 'flowClueName' },
            { title: '接收方', dataIndex: 'receiverName' },
            {
              title: '失败原因',
              dataIndex: 'rejectReasons',
            },
          ]}
          request={async ({ current, pageSize }) => {
            const res = await getFlowRecordDetail({ id: detailId!, pageNum: current, pageSize });

            return {
              data: res.result,
              total: res.total,
            };
          }}
        />
      </Modal>
    </PageContainer>
  );
};

export default FlowRecord;
