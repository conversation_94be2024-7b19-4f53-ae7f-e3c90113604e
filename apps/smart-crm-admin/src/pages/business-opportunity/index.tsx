import { useEffect, useMemo, useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import {
  commonBOTypeOptions,
  commonProgressNodeOptions,
  fissionBOTypeOptions,
  fissionProgressNodeOptions,
  progressNodesAndProgressStatesArray,
} from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ExportButton, ProTable, TableActions, UserSelect } from '@src/components';
import EditableMultiple from '@src/components/Editable/EditableMultiple';
import EditablePhone from '@src/components/Editable/EditablePhone';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useAllUsers from '@src/hooks/useAllUsers';
import { useNoticeEventSubscription } from '@src/layout';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import {
  batchDeleteBusinessOpportunity,
  batchStartAppointment,
  deleteBusinessOpportunity,
  exportBusinessOpportunity,
  getBusinessOpportunityList,
} from '@src/services/business-opportunity';
import {
  BelongWarZoneEnum,
  BusinessOpportunityAbandonReasonEnum,
  BusinessOpportunityDTO,
  BusinessOpportunityTypeEnum,
  ChannelTypeEnum,
  ExportBusinessOpportunityReq,
  OwnerTypeEnum,
  ProgressNodeEnum,
  ProgressStateEnum,
} from '@src/services/business-opportunity/type';
import { StoreCategoryEnum } from '@src/services/contract/formal/type';
import {
  calcTableWidth,
  compatibleTableActionWidth,
  enum2Options,
  enum2ValueEnum,
  optionsToValueEnum,
} from '@src/utils';
import { useRequest, useUpdateEffect } from 'ahooks';
import { App, Button, Cascader, Dropdown, Tabs, Typography } from 'antd';
import { useSearchParams } from 'react-router-dom';
import BusinessDetailDrawer from './components/BusinessDetailDrawer';
import ChooseApplyExamModal from './components/ChooseApplyExamModal';
import CreateEditBusinessModal from './components/CreateEditBusinessModal';
import TransferBusinessModal from './components/TransferBusinessModal';
import CallPhonePopover from '../clues/components/CallPhonePopover';
import CustomerDetailDrawer from '../customers/components/CustomerDetailDrawer';

const progressStateValueEnum = enum2ValueEnum(ProgressStateEnum);
const progressNodeValueEnum = enum2ValueEnum(ProgressNodeEnum);

enum CustomerTypeEnum {
  new = 'new',
  old = 'old',
}

const BusinessOpportunity = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { modal, message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [selectedRows, setSelectedRows] = useState<BusinessOpportunityDTO[]>([]);
  const createEditBusinessModalRef = useRef<ModalRef>(null);
  const transferBusinessModalRef = useRef<ModalRef>(null);
  const businessDetailDrawerRef = useRef<ModalRef>(null);
  const businessIdRef = useRef<number | null>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const customerIdRef = useRef<number | null>(null);
  const filterParamsRef = useRef<ExportBusinessOpportunityReq>({});
  const chooseApplyExamModalRef = useRef<ModalRef>(null);
  const [customerType, setCustomerType] = useState<CustomerTypeEnum>(CustomerTypeEnum.new);
  const [bizStage, setBizStage] = useState<ProgressNodeEnum | null>(null);
  const formRef = useRef<ProFormInstance>();

  const selectedRowKeys = selectedRows.map((i) => i.id);

  const checkPermission = usePermission();
  const { data: users } = useAllUsers();
  const { runAsync: getList, data } = useRequest(getBusinessOpportunityList, { manual: true });

  const bizType = useMemo(
    () =>
      customerType === CustomerTypeEnum.new
        ? commonBOTypeOptions.map((i) => i.value)
        : fissionBOTypeOptions.map((i) => i.value),
    [customerType],
  );

  useUpdateEffect(() => {
    formRef.current?.submit();
  }, [bizStage, bizType]);

  // 在通知中修改了某个商机详情，列表如果有这个商机，则刷新列表
  useNoticeEventSubscription((event) => {
    if (
      event.type === 'business-opportunity' &&
      data?.result.some((i) => i.id === event.payload.id)
    ) {
      actionRef.current?.reload();
    }
  });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      businessIdRef.current = Number(id);
      businessDetailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: ProColumns<BusinessOpportunityDTO>[] = [
    {
      title: '商机编号',
      dataIndex: 'code',
      fixed: 'left',
      render: (value, { id }) => (
        <a
          onClick={() => {
            businessIdRef.current = id;
            businessDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '商机名称',
      dataIndex: 'name',
      ellipsis: true,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      search: false,
      renderText: (value, { phoneSensitive }) => (
        <EditablePhone value={value} fieldProps={{ sensitiveValue: phoneSensitive }} />
      ),
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      renderText: (value, { customerId }) => (
        <Typography.Text
          style={{ color: 'var(--ant-color-link)', cursor: 'pointer' }}
          ellipsis={{ tooltip: true }}
          onClick={() => {
            customerIdRef.current = customerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </Typography.Text>
      ),
    },
    {
      title: '客户性质',
      dataIndex: 'customerNature',
      search: false,
      valueEnum: enum2ValueEnum(StoreCategoryEnum),
    },
    {
      title: '负责人',
      dataIndex: 'directUserId',
      renderFormItem: () => <UserSelect />,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '商机进展',
      dataIndex: 'businessOpportunityType',
      valueEnum: optionsToValueEnum([...commonBOTypeOptions, ...fissionBOTypeOptions]),
    },
    {
      title: '商机阶段',
      dataIndex: 'progressNode',
      valueEnum: enum2ValueEnum(ProgressNodeEnum),
      search: false,
    },
    {
      title: '渠道类型',
      dataIndex: 'channelTypes',
      search: false,
      renderText: (value) => (
        <EditableMultiple value={value} fieldProps={{ options: enum2Options(ChannelTypeEnum) }} />
      ),
    },
    {
      title: '状态',
      dataIndex: 'progressState',
      valueEnum: progressStateValueEnum,
      search: false,
      renderText: (value: ProgressStateEnum) => progressStateValueEnum[value]?.split('：')[1],
    },
    {
      title: '商机阶段与状态',
      key: 'progressNodesAndProgressStates',
      hidden: true,
      valueType: 'cascader',
      fieldProps: {
        showCheckedStrategy: Cascader.SHOW_CHILD,
        classNames: {
          popup: {
            root: 'cascader-popup-hidden-disabled-checkbox',
          },
        },
        multiple: true,
        options: progressNodesAndProgressStatesArray.map((item) => ({
          ...item,
          disableCheckbox: true,
          label: progressNodeValueEnum[item.value],
          children: item.children.map((val) => ({
            value: val,
            label: progressStateValueEnum[val],
          })),
        })),
      },
      search: {
        transform: (value) => ({
          progressStates: value.map((item: string[]) => item[1]),
        }),
      },
    },
    {
      title: '意向区域',
      dataIndex: 'intentionRegion',
      search: false,
      renderText: (value) => (
        <EditableRegion value={value} fieldProps={{ regionLevel: 4, multiple: true }} />
      ),
    },
    {
      title: '所属大区',
      dataIndex: 'belongWarZone',
      search: false,
      valueEnum: enum2ValueEnum(BelongWarZoneEnum),
    },
    {
      title: '意向点位',
      dataIndex: 'intentionPositions',
      search: false,
      renderText: (value: BusinessOpportunityDTO['intentionPositions']) => (
        <Typography.Text
          ellipsis={{
            tooltip: (
              <div>
                {value?.map((i, index) => (
                  <div key={index}>
                    {index + 1}. {i.name}（经度：{i.longitude}；纬度：{i.latitude}）
                  </div>
                ))}
              </div>
            ),
          }}
        >
          {value?.map(
            (i, index) => `${index + 1}. ${i.name}（经度：${i.longitude}；纬度：${i.latitude}）`,
          )}
        </Typography.Text>
      ),
    },
    {
      title: '业主类型',
      dataIndex: 'ownerType',
      search: false,
      valueEnum: enum2ValueEnum(OwnerTypeEnum),
    },
    {
      title: '客户情况描述',
      dataIndex: 'customerDesc',
      search: false,
      ellipsis: true,
    },
    {
      title: '放弃原因',
      dataIndex: 'abandonReason',
      search: false,
      valueEnum: enum2ValueEnum(BusinessOpportunityAbandonReasonEnum),
    },
    {
      title: '放弃说明',
      dataIndex: 'abandonDesc',
      search: false,
      ellipsis: true,
    },
    {
      title: '入库时间',
      dataIndex: 'directTime',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      search: false,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      fixed: 'right',
      search: false,
      width: compatibleTableActionWidth(200),
      render: (_, { name, id, phone }) => (
        <TableActions
          shortcuts={[
            {
              label: '编辑',
              onClick: () => {
                businessIdRef.current = id;
                createEditBusinessModalRef.current?.open();
              },
            },
            {
              label: '转让',
              onClick: () => {
                businessIdRef.current = id;
                transferBusinessModalRef.current?.open();
              },
            },
            {
              label: (isMobile) =>
                isMobile ? (
                  <CallPhonePopover phone={phone}>拨打电话</CallPhonePopover>
                ) : (
                  <CallPhonePopover phone={phone}>
                    <a>拨打电话</a>
                  </CallPhonePopover>
                ),
            },
            {
              label: '删除',
              danger: true,
              show: checkPermission(PermissionsMap.BusinessOpportunityDelete),
              onClick: () =>
                modal.confirm({
                  title: '删除',
                  content: `确定要删除商机 “${name}” 吗？`,
                  onOk: async () => {
                    await deleteBusinessOpportunity(id);
                    message.success('删除成功');
                    setSelectedRows((prev) => prev.filter((i) => i.id !== id));
                    actionRef.current?.reload();
                  },
                }),
            },
          ]}
        />
      ),
    },
  ];

  const handleOpenFranchiseApplication = () => {
    // 对所有勾选的商机类型去重
    const uniqBusinessOpportunityTypes = [
      ...new Set(selectedRows.map((i) => i.businessOpportunityType)),
    ];

    if (uniqBusinessOpportunityTypes.length > 1) {
      message.warning('请选择相同类型的商机');

      return;
    }

    if (
      [
        BusinessOpportunityTypeEnum.FISSION_SOCIETY,
        BusinessOpportunityTypeEnum.FISSION_CHANNEL,
      ].includes(uniqBusinessOpportunityTypes[0])
    ) {
      message.warning('请取消勾选裂变商机');

      return;
    }

    chooseApplyExamModalRef.current?.open();
  };

  return (
    <PageContainer>
      <Tabs
        activeKey={customerType}
        items={[
          {
            label: '新客户',
            key: CustomerTypeEnum.new,
          },
          {
            label: '老客户',
            key: CustomerTypeEnum.old,
          },
        ]}
        onChange={(key) => {
          setCustomerType(key as CustomerTypeEnum);
          setBizStage(null);
        }}
      />
      <Tabs
        type="card"
        activeKey={bizStage as string}
        items={[
          { label: '全部', key: null as any },
          ...(customerType === CustomerTypeEnum.new
            ? commonProgressNodeOptions
            : fissionProgressNodeOptions
          ).map((i) => ({ label: i.label, key: i.value })),
        ]}
        onChange={(key) => setBizStage(key as typeof bizStage)}
      />
      <ProTable
        bordered
        actionRef={actionRef}
        sticky
        rowKey="id"
        formRef={formRef}
        cardProps={false}
        scroll={{
          x: calcTableWidth(columns),
        }}
        toolbar={{
          subTitle: (
            <span className="text-black">
              共 {data?.total || 0} 个商机
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 个
                  <a className="ml-4" onClick={() => setSelectedRows([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            <Dropdown
              disabled={!selectedRowKeys.length}
              menu={{
                items: [
                  {
                    label: '转让',
                    key: 'transfer',
                    onClick: () => transferBusinessModalRef.current?.open(),
                  },
                  {
                    label: '开启加盟申请收集',
                    key: 'open-franchise-application',
                    onClick: handleOpenFranchiseApplication,
                  },
                  {
                    label: '开启预约面谈',
                    key: 'open-schedule-interview',
                    onClick: () =>
                      modal.confirm({
                        title: '开启预约面谈',
                        content: '确定要开启预约面谈阶段吗？',
                        onOk: async () => {
                          await batchStartAppointment({ ids: selectedRowKeys });
                          message.success('开启成功');
                          setSelectedRows([]);
                          actionRef.current?.reload();
                        },
                      }),
                  },
                  {
                    label: '删除',
                    key: 'delete',
                    auth: PermissionsMap.BusinessOpportunityDelete,
                    danger: true,
                    onClick: () =>
                      modal.confirm({
                        title: '删除',
                        content: `确定要删除当前选中的 ${selectedRowKeys.length} 个商机吗？`,
                        onOk: async () => {
                          await batchDeleteBusinessOpportunity({ ids: selectedRowKeys });
                          message.success('删除成功');
                          setSelectedRows([]);
                          actionRef.current?.reload();
                        },
                      }),
                  },
                ].filter((i) => checkPermission(i.auth)),
                onClick: () => {
                  businessIdRef.current = null;
                },
              }}
            >
              <Button type="text" className="!flex items-center">
                批量操作 <CaretDownOutlined />
              </Button>
            </Dropdown>,
            <Button
              type="text"
              onClick={() => {
                businessIdRef.current = null;
                createEditBusinessModalRef.current?.open();
              }}
            >
              新建
            </Button>,
            <Permission value={PermissionsMap.BusinessOpportunityExport}>
              <ExportButton
                total={selectedRowKeys.length || data?.total}
                request={() =>
                  exportBusinessOpportunity(
                    selectedRowKeys.length ? { ids: selectedRowKeys } : filterParamsRef.current,
                  )
                }
              />
            </Permission>,
          ],
        }}
        columns={columns}
        rowSelection={{
          fixed: true,
          showSort: true,
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (_, rows) => setSelectedRows(rows),
        }}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        request={async ({ current, pageSize, ...params }) => {
          Object.assign(params, { bizType, bizStage });
          filterParamsRef.current = params as ExportBusinessOpportunityReq;

          const res = await getList({ pageNum: current, pageSize, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <CreateEditBusinessModal
        ref={createEditBusinessModalRef}
        getId={() => businessIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />

      <TransferBusinessModal
        ref={transferBusinessModalRef}
        getId={() => businessIdRef.current}
        selectedRowKeys={selectedRowKeys}
        onSuccess={() => {
          setSelectedRows((prev) => prev.filter((i) => i.id !== businessIdRef.current));
          actionRef.current?.reload();
        }}
      />

      <BusinessDetailDrawer
        ref={businessDetailDrawerRef}
        getId={() => businessIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
        onDecrease={() => {
          setSelectedRows((prev) => prev.filter((i) => i.id !== businessIdRef.current));
          actionRef.current?.reload();
        }}
      />

      <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />

      <ChooseApplyExamModal
        ref={chooseApplyExamModalRef}
        getIdOrIds={() => selectedRowKeys}
        onSuccess={() => {
          setSelectedRows([]);
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default BusinessOpportunity;
