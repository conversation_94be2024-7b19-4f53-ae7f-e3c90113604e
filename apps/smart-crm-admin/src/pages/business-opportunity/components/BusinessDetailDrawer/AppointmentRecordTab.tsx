import { useRef, useState } from 'react';
import { ModalRef } from '@src/common/interface';
import useDistricts from '@src/hooks/useDistricts';
import AppointmentRecordDetailDrawer from '@src/pages/appointment-record/components/AppointmentRecordDetailDrawer';
import { getAppointmentRecordByCustomerId } from '@src/services/appointment-record';
import { VisitPurposeEnum, VisitResultsEnum } from '@src/services/appointment-record/type';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { Table } from 'antd';

interface AppointmentRecordTabProps {
  id: number;
  onUpdate: () => void;
}

const AppointmentRecordTab: React.FC<AppointmentRecordTabProps> = ({ id, onUpdate }) => {
  const appointmentRecordDetailDrawerRef = useRef<ModalRef>(null);
  const [currentId, setCurrentId] = useState<number | null>(null);

  const { data: districts } = useDistricts();
  const { data, loading } = useRequest(() => getAppointmentRecordByCustomerId(id));

  return (
    <>
      <Table
        rowKey="id"
        loading={loading}
        dataSource={data}
        pagination={false}
        size="small"
        bordered
        columns={[
          {
            title: '预约编号',
            dataIndex: 'code',
            render: (value, record) => (
              <a
                onClick={() => {
                  setCurrentId(record.id);
                  appointmentRecordDetailDrawerRef.current?.open();
                }}
              >
                {value}
              </a>
            ),
          },
          {
            title: '预约时间',
            dataIndex: 'scheduleTime',
          },
          {
            title: '到访目的',
            dataIndex: 'visitPurpose',
            render: (value) => enum2ValueEnum(VisitPurposeEnum)[value],
          },
          {
            title: '意向区域',
            dataIndex: 'intentionRegion',
          },
          {
            title: '意向省份',
            dataIndex: 'intentionProvinceCode',
            render: (value) => districts?.find((i) => i.code === value)?.name,
          },
          {
            title: '所属大区',
            dataIndex: 'belongWarZone',
            render: (value) => enum2ValueEnum(BelongWarZoneEnum)[value],
          },
          {
            title: '顾问描述客户简介',
            dataIndex: 'description',
          },
          {
            title: '到访结果',
            dataIndex: 'visitResults',
            render: (value) => enum2ValueEnum(VisitResultsEnum)[value],
          },
        ]}
      />
      <AppointmentRecordDetailDrawer
        ref={appointmentRecordDetailDrawerRef}
        getId={() => currentId}
        onUpdate={onUpdate}
      />
    </>
  );
};

export default AppointmentRecordTab;
