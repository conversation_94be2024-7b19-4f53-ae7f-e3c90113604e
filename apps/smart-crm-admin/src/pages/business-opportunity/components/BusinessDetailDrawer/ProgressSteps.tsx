import React, { useRef } from 'react';
import { ModalRef } from '@src/common/interface';
import useSimpleForm from '@src/hooks/useSimpleFormModal';
import CreateEditFissionModal from '@src/pages/fission/components/CreateEditFissionModal';
import {
  abandonBusinessOpportunity,
  getBusinessOpportunityProgressNodes,
  startAppointmentInProgress,
} from '@src/services/business-opportunity';
import {
  BusinessOpportunityAbandonReasonEnum,
  BusinessOpportunityDTO,
  ProgressNodeEnum,
  ProgressStateEnum,
} from '@src/services/business-opportunity/type';
import { enum2Options, enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Card, Input, Select, StepProps, Steps } from 'antd';
import ChooseApplyExamModal from '../ChooseApplyExamModal';

interface ProgressStepsProps {
  data?: BusinessOpportunityDTO;
  businessId?: number;
  loading?: boolean;
  onUpdate: () => void;
}

export interface ProgressStepsRef {
  refresh: () => void;
}

const ProgressSteps = React.forwardRef<ProgressStepsRef, ProgressStepsProps>(
  ({ data, businessId, loading, onUpdate }, ref) => {
    const { modal, message } = App.useApp();
    const chooseApplyExamModalRef = useRef<ModalRef>(null);
    const createEditFissionModalRef = useRef<ModalRef>(null);

    const {
      data: progressNodes = [],
      loading: getProgressNodesLoading,
      refresh,
    } = useRequest(() => getBusinessOpportunityProgressNodes(businessId!), { ready: !!businessId });
    const { runAsync: abandon, loading: abandonLoading } = useRequest(abandonBusinessOpportunity, {
      manual: true,
    });

    const currentIndex = progressNodes.findIndex((i) => i.node === data?.progressNode);

    const progressStateValueEnum = enum2ValueEnum(ProgressStateEnum);
    const progressNodeValueEnum = enum2ValueEnum(ProgressNodeEnum);

    React.useImperativeHandle(ref, () => ({
      refresh,
    }));

    const { modalNode: abandonModalNode, setOpen: setAbandonModalOpen } = useSimpleForm({
      modalProps: {
        title: '放弃商机',
        confirmLoading: abandonLoading,
      },
      formItems: [
        {
          label: '放弃原因',
          name: 'abandonReason',
          rules: [{ required: true, message: '请选择放弃原因' }],
          children: (
            <Select
              placeholder="请选择放弃原因"
              options={enum2Options(BusinessOpportunityAbandonReasonEnum)}
            />
          ),
        },
        {
          label: '放弃说明',
          name: 'abandonDesc',
          children: <Input.TextArea placeholder="请输入放弃说明" maxLength={500} />,
        },
      ],
      formProps: {
        labelCol: {
          span: 4,
        },
        onFinish: async (values) => {
          await abandon({ ...values, id: businessId });
          message.success('放弃成功');
          setAbandonModalOpen(false);
          refresh();
          onUpdate();
        },
      },
    });

    const stepItems: StepProps[] = progressNodes.map((i, index) => {
      let disabled = true;
      let status: StepProps['status'];

      // 节点紧挨在当前节点之后才可点击
      if (index === currentIndex + 1) {
        if (i.node === ProgressNodeEnum.加盟申请收集) {
          if (data?.progressNode === ProgressNodeEnum.意向收集) {
            disabled = false;
          }
        } else if (i.node === ProgressNodeEnum.预约面谈) {
          if (
            data?.progressNode === ProgressNodeEnum.加盟申请审核 &&
            data.progressState === ProgressStateEnum['加盟审核：通过']
          ) {
            disabled = false;
          }
        } else if (i.node === ProgressNodeEnum.裂变申请) {
          if (data?.progressNode === ProgressNodeEnum.意向收集) {
            disabled = false;
          }
        }
      }

      // 没有状态显示灰色
      if (!progressNodes[index].state) {
        status = 'wait';
      } else {
        const statusMap: Partial<Record<ProgressStateEnum, StepProps['status']>> = {
          // 错误
          [ProgressStateEnum['加盟审核：未通过']]: 'error',
          [ProgressStateEnum['面审审核：审批不通过']]: 'error',
          [ProgressStateEnum['裂变OA审批：未通过']]: 'error',
          [ProgressStateEnum['意向合同：取消意向签约']]: 'error',
          [ProgressStateEnum['流程结束：已放弃']]: 'error',

          // 结束
          [ProgressStateEnum['意向收集：已填写']]: 'finish',
          [ProgressStateEnum['加盟申请：已填写']]: 'finish',
          [ProgressStateEnum['加盟审核：通过']]: 'finish',
          [ProgressStateEnum['预约面审：已预约']]: 'finish',
          [ProgressStateEnum['面审审核：审批通过']]: 'finish',
          [ProgressStateEnum['意向合同：已归档']]: 'finish',
          [ProgressStateEnum['流程结束：已签约']]: 'finish',
          [ProgressStateEnum['裂变OA审批：已归档']]: 'finish',
        };

        status = statusMap[i.state] || 'process';
      }

      return {
        title: progressNodeValueEnum[i.node],
        status,
        subTitle: progressStateValueEnum[i.state]?.split('：')[1],
        disabled,
      };
    });

    // 是否可放弃
    const canAbandon = [
      ProgressStateEnum['加盟申请：待填写'],
      ProgressStateEnum['加盟审核：未通过'],
      ProgressStateEnum['预约面审：待预约'],
      ProgressStateEnum['预约面审：取消预约'],
      ProgressStateEnum['面审审核：审批不通过'],
      ProgressStateEnum['意向合同：台账意向未创建'],
      ProgressStateEnum['意向合同：取消意向签约'],
      ProgressStateEnum['裂变OA审批：未发起'],
      ProgressStateEnum['裂变OA审批：未通过'],
    ].includes(data?.progressState!);

    return (
      <Card
        className="mt-5"
        styles={{ body: { overflow: 'auto' } }}
        title="商机阶段"
        extra={
          canAbandon && (
            <Button danger type="primary" onClick={() => setAbandonModalOpen(true)}>
              放弃商机
            </Button>
          )
        }
        loading={loading || getProgressNodesLoading}
      >
        <Steps
          labelPlacement="vertical"
          current={currentIndex}
          items={stepItems}
          onChange={(index) => {
            const progressNode = progressNodes[index].node;

            if (progressNode === ProgressNodeEnum.加盟申请收集) {
              chooseApplyExamModalRef.current?.open();
            } else if (progressNode === ProgressNodeEnum.预约面谈) {
              modal.confirm({
                title: '开启预约面谈',
                content: '确定要开启预约面谈阶段吗？',
                onOk: async () => {
                  await startAppointmentInProgress(data!.id);
                  message.success('开启成功');
                  onUpdate();
                },
              });
            } else if (progressNode === ProgressNodeEnum.裂变申请) {
              createEditFissionModalRef.current?.open();
            }
          }}
        />

        <ChooseApplyExamModal
          ref={chooseApplyExamModalRef}
          getIdOrIds={() => data?.id!}
          onSuccess={onUpdate}
        />
        <CreateEditFissionModal
          ref={createEditFissionModalRef}
          createBusinessData={data}
          getId={() => data?.id}
          onSuccess={onUpdate}
        />

        {abandonModalNode}
      </Card>
    );
  },
);

export default ProgressSteps;
