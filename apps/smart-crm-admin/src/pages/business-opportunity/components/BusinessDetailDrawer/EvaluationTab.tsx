import { useRef, useState } from 'react';
import { evaluationAuditStatusOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import EditableMultiple from '@src/components/Editable/EditableMultiple';
import useAllUsers from '@src/hooks/useAllUsers';
import EvaluationDetailDrawer from '@src/pages/evaluation/components/EvaluationDetailDrawer';
import { BusinessTypeEnum } from '@src/services/common/type';
import { getExamAnswerRecordsByBusinessId } from '@src/services/franchise-application';
import { EvaluationRejectReasonEnum } from '@src/services/visit/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { Table } from 'antd';

interface EvaluationTabProps {
  id: number;
  onUpdate: () => void;
}

// 面谈评估
const EvaluationTab: React.FC<EvaluationTabProps> = ({ id, onUpdate }) => {
  const [currentId] = useState<number | null>(null);
  const evaluationDetailDrawerRef = useRef<ModalRef>(null);

  const { data: users } = useAllUsers();
  const { data, loading } = useRequest(() =>
    getExamAnswerRecordsByBusinessId({
      id,
      businessType: BusinessTypeEnum.INTERVIEW_EVALUATION,
    }),
  );

  return (
    <>
      <Table
        rowKey="id"
        loading={loading}
        dataSource={data}
        pagination={false}
        size="small"
        bordered
        columns={[
          {
            title: '评估表编号',
            dataIndex: 'code',
            // render: (value, record) => (
            //   <a
            //     onClick={() => {
            //       setCurrentId(record.id);
            //       evaluationDetailDrawerRef.current?.open();
            //     }}
            //   >
            //     {value}
            //   </a>
            // ),
          },
          {
            title: '评估表名称',
            dataIndex: 'examPaperName',
          },
          {
            title: '负责人',
            dataIndex: 'directUserId',
            render: (value) => users?.find((i) => i.id === value)?.name,
          },
          {
            title: '面审最终结果',
            dataIndex: 'auditStatus',
            render: (value) => evaluationAuditStatusOptions.find((i) => i.value === value)?.label,
          },
          {
            title: '未通过原因',
            dataIndex: 'rejectReasons',
            render: (value: EvaluationRejectReasonEnum[]) => (
              <EditableMultiple
                value={value}
                fieldProps={{ options: enum2Options(EvaluationRejectReasonEnum) }}
              />
            ),
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
          },
        ]}
      />

      <EvaluationDetailDrawer
        ref={evaluationDetailDrawerRef}
        getId={() => currentId}
        onUpdate={onUpdate}
      />
    </>
  );
};

export default EvaluationTab;
