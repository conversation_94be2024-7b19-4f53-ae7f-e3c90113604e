import { useRef } from 'react';
import { evaluationAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import EditableMultiple from '@src/components/Editable/EditableMultiple';
import useAllUsers from '@src/hooks/useAllUsers';
import VisitDetailDrawer from '@src/pages/visit/components/VisitDetailDrawer';
import { VisitPurposeEnum } from '@src/services/appointment-record/type';
import { getVisitListByBusinessId } from '@src/services/visit';
import { EvaluationRejectReasonEnum, SuggestionEnum } from '@src/services/visit/type';
import { enum2Options, enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { Table } from 'antd';

interface VisitTabProps {
  id: number;
  onUpdate: () => void;
}

const VisitTab: React.FC<VisitTabProps> = ({ id, onUpdate }) => {
  const visitDetailDrawerRef = useRef<ModalRef>(null);
  const currentIdRef = useRef<number | null>(null);

  const { data: users } = useAllUsers();
  const { data, loading } = useRequest(() => getVisitListByBusinessId(id));

  return (
    <>
      <Table
        bordered
        rowKey="id"
        size="small"
        dataSource={data}
        loading={loading}
        pagination={false}
        columns={[
          {
            title: '到访记录编号',
            dataIndex: 'code',
            render: (value, record) => (
              <a
                onClick={() => {
                  currentIdRef.current = record.id;
                  visitDetailDrawerRef.current?.open();
                }}
              >
                {value}
              </a>
            ),
          },
          {
            title: '到访日期时间',
            dataIndex: 'visitTime',
          },
          {
            title: '负责人',
            dataIndex: 'interviewer',
            render: (value) => users?.find((i) => i.id === value)?.name,
          },
          {
            title: '到访目的',
            dataIndex: 'visitPurpose',
            render: (value) => enum2ValueEnum(VisitPurposeEnum)[value],
          },
          {
            title: '到访信息是否一致',
            dataIndex: 'infoUnanimous',
            render: (value) => yesOrNoOptions.find((i) => i.value === value)?.label,
          },
          {
            title: '面审建议',
            dataIndex: 'suggestion',
            render: (value) => enum2ValueEnum(SuggestionEnum)[value],
          },
          {
            title: '面审最终结果',
            dataIndex: 'auditStatus',
            render: (value) => evaluationAuditStatusOptions.find((i) => i.value === value)?.label,
          },
          {
            title: '未通过原因',
            dataIndex: 'rejectReasons',
            render: (value: EvaluationRejectReasonEnum[]) => (
              <EditableMultiple
                value={value}
                fieldProps={{ options: enum2Options(EvaluationRejectReasonEnum) }}
              />
            ),
          },
        ]}
      />

      <VisitDetailDrawer
        ref={visitDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={onUpdate}
      />
    </>
  );
};

export default VisitTab;
