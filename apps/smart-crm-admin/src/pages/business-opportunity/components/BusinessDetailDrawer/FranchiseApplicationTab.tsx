import { useRef, useState } from 'react';
import { franchiseAuditStatusOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import useAllUsers from '@src/hooks/useAllUsers';
import FranchiseDetailDrawer from '@src/pages/franchise-application/components/FranchiseDetailDrawer';
import { BusinessTypeEnum } from '@src/services/common/type';
import { getExamAnswerRecordsByBusinessId } from '@src/services/franchise-application';
import { useRequest } from 'ahooks';
import { Table } from 'antd';

interface FranchiseApplicationTabProps {
  id: number;
  onUpdate: () => void;
}

// 加盟申请
const FranchiseApplicationTab: React.FC<FranchiseApplicationTabProps> = ({ id, onUpdate }) => {
  const [currentId, setCurrentId] = useState<number | null>(null);
  const franchiseDetailDrawerRef = useRef<ModalRef>(null);

  const { data: users } = useAllUsers();
  const { data, loading } = useRequest(() =>
    getExamAnswerRecordsByBusinessId({
      id,
      businessType: BusinessTypeEnum.ONLINE_APPLY,
    }),
  );

  return (
    <>
      <Table
        rowKey="id"
        loading={loading}
        size="small"
        bordered
        dataSource={data}
        pagination={false}
        columns={[
          {
            title: '申请表编号',
            dataIndex: 'code',
            render: (value, record) => (
              <a
                onClick={() => {
                  setCurrentId(record.id);
                  franchiseDetailDrawerRef.current?.open();
                }}
              >
                {value}
              </a>
            ),
          },
          {
            title: '申请表名称',
            dataIndex: 'examPaperName',
          },
          {
            title: '负责人',
            dataIndex: 'directUserId',
            render: (value) => users?.find((i) => i.id === value)?.name,
          },
          {
            title: '审核状态',
            dataIndex: 'auditStatus',
            render: (value) => franchiseAuditStatusOptions.find((i) => i.value === value)?.label,
          },
          {
            title: '提交时间',
            dataIndex: 'createTime',
          },
          {
            title: '审核时间',
            dataIndex: 'auditTime',
          },
        ]}
      />

      <FranchiseDetailDrawer
        ref={franchiseDetailDrawerRef}
        getId={() => currentId}
        onUpdate={onUpdate}
      />
    </>
  );
};

export default FranchiseApplicationTab;
