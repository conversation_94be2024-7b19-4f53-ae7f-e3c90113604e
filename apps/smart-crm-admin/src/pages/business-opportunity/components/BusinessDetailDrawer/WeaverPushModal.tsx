import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { getWeaverOAList, pushWeaverOA } from '@src/services/business-opportunity';
import { ContractTypeEnum } from '@src/services/payment/type';
import { useRequest } from 'ahooks';
import { App, Form, Modal, ModalProps, Select } from 'antd';

interface WeaverPushModalProps extends ModalProps {
  getIds: () => number[];
  contractType: ContractTypeEnum;
  onSuccess: () => void;
}

const WeaverPushModal = React.forwardRef<ModalRef, WeaverPushModalProps>(
  ({ getIds, contractType, open: propsOpen, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [form] = Form.useForm<{ workflowId: number }>();
    const [open, setOpen] = useState(false);

    const mergedOpen = propsOpen || open;

    const { data, loading } = useRequest(() => getWeaverOAList({ contractType }), {
      ready: mergedOpen,
    });
    const { runAsync: push, loading: pushLoading } = useRequest(pushWeaverOA, { manual: true });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    return (
      <Modal
        title="推送泛微"
        open={mergedOpen}
        destroyOnHidden
        confirmLoading={pushLoading}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
        {...props}
      >
        <Form
          form={form}
          clearOnDestroy
          onFinish={async ({ workflowId }) => {
            await push({
              ids: getIds(),
              contractType,
              workflowId,
              requestName: data?.find((i) => i.workflowId === workflowId)?.requestName!,
            });
            message.success('推送成功');
            setOpen(false);
            onSuccess();
          }}
        >
          <Form.Item
            label="泛微流程名称"
            name="workflowId"
            extra="提交后将推送至泛微 OA 中"
            rules={[{ required: true, message: '请选择泛微流程名称' }]}
          >
            <Select
              placeholder="请选择泛微流程名称"
              loading={loading}
              options={data}
              fieldNames={{ label: 'requestName', value: 'workflowId' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default WeaverPushModal;
