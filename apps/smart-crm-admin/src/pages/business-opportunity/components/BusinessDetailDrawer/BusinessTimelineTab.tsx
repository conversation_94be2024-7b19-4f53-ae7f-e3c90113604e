import React, { useEffect, useMemo, useRef, useState } from 'react';
import { parserJSON } from '@pkg/utils';
import { FileList } from '@src/components';
import { getCallPhoneLog, getOperationLog } from '@src/services/common';
import { ActivityItem, OperationTypeEnum } from '@src/services/common/type';
import { useRequest } from 'ahooks';
import { Empty, Segmented, Skeleton, Timeline } from 'antd';
import dayjs from 'dayjs';
import { padStart } from 'lodash-es';
import InfiniteScroll from 'react-infinite-scroll-component';

// 秒 -> 时:分:秒
function convertSecondsToHMS(seconds: number) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  // padStart 补齐 0:0:0 为 00:00:00 两位
  return [hours, minutes, remainingSeconds].map((i) => padStart(String(i), 2, '0')).join(':');
}

enum ActivityTypeEnum {
  /** 操作记录 */
  INFO_CHANGE = 'INFO_CHANGE',
  /** 通话记录 */
  CALL_LOG = 'CALL_LOG',
}

const options = [
  { label: '操作记录', value: ActivityTypeEnum.INFO_CHANGE },
  { label: '通话记录', value: ActivityTypeEnum.CALL_LOG },
];

const operationTypeLabelMap: Partial<Record<OperationTypeEnum, string>> = {
  [OperationTypeEnum.CREATE_BUSINESS_OPPORTUNITY]: '创建商机',
  [OperationTypeEnum.BUSINESS_OPPORTUNITY_INFO_CHANGE]: '更新商机信息',
};

const getTimeLineItem = (item: ActivityItem) => {
  const mapObj: Partial<Record<OperationTypeEnum, () => React.ReactNode>> = {
    [OperationTypeEnum.CREATE_BUSINESS_OPPORTUNITY]: () => <p>{item.userName}创建了商机</p>,
    [OperationTypeEnum.BUSINESS_OPPORTUNITY_INFO_CHANGE]: () => (
      <>
        <p>
          {item.userName}更新了{item.updateFieldName}
        </p>
        <div className="flex">
          更新前：
          {item.updateField === 'intentionPositions' ? (
            <div className="flex-1">
              {(parserJSON(item.updateBefore) || []).map((i: any, index: number) => (
                <div key={index}>
                  {index + 1}. 地址：{i.name}（经度：{i.longitude}；纬度：{i.latitude}）
                </div>
              ))}
            </div>
          ) : (
            item.updateBefore
          )}
        </div>
        <div className="flex">
          更新后：
          {item.updateField === 'intentionPositions' ? (
            <div className="flex-1">
              {(parserJSON(item.updateAfter) || []).map((i: any, index: number) => (
                <div key={index}>
                  {index + 1}. 地址：{i.name}（经度：{i.longitude}；纬度：{i.latitude}）
                </div>
              ))}
            </div>
          ) : (
            item.updateAfter
          )}
        </div>
      </>
    ),
    [OperationTypeEnum.BLOCK_CALL]: () => (
      <p>
        {item.userName}拨打了电话 {item.cluePhone}，未接通
      </p>
    ),
    [OperationTypeEnum.CALL_RECORDING_RETRIEVED]: () => (
      <>
        <p>
          {item.userName}拨打了电话 {item.cluePhone}，通话时长{' '}
          {convertSecondsToHMS(item.callDuration)}
        </p>
        <FileList data={item.attachments} />
      </>
    ),
    [OperationTypeEnum.CALL_RECORDING_LOST]: () => (
      <>
        <p>
          {item.userName}拨打了电话 {item.cluePhone}，通话时长{' '}
          {convertSecondsToHMS(item.callDuration)}
        </p>
        <p>通话录音丢失</p>
      </>
    ),
    [OperationTypeEnum.CALL_BACK_BLOCK_CALL]: () => (
      <>
        <p>
          线索 {item.cluePhone} 回拨了{item.userName}电话，未接通
        </p>
        <FileList data={item.attachments} />
      </>
    ),
    [OperationTypeEnum.CALL_BACK_CALL_RECORDING_LOST]: () => (
      <>
        <p>
          线索 {item.cluePhone} 回拨了{item.userName}电话，通话时长{' '}
          {convertSecondsToHMS(item.callDuration)}
        </p>
        <p>通话录音丢失</p>
      </>
    ),
    [OperationTypeEnum.CALL_BACK_CALL_RECORDING_RETRIEVED]: () => (
      <>
        <p>
          线索 {item.cluePhone} 回拨了{item.userName}电话，通话时长{' '}
          {convertSecondsToHMS(item.callDuration)}
        </p>
        <FileList data={item.attachments} />
      </>
    ),
  };

  return mapObj[item.operationType]?.();
};

interface BusinessTimelineTabProps {
  // 商机 id
  businessId: number;
  callPhone: string;
}

export interface BusinessTimelineTabRef {
  reload: () => void;
}

const pageSize = 5;

const BusinessTimelineTab = React.forwardRef<BusinessTimelineTabRef, BusinessTimelineTabProps>(
  ({ businessId, callPhone }, ref) => {
    const pageNum = useRef(1);
    const [data, setData] = useState<ActivityItem[]>([]);
    const [activityType, setActivityType] = useState(ActivityTypeEnum.INFO_CHANGE);

    const {
      data: operationLog,
      loading: getOperationLogLoading,
      runAsync: runOperationLog,
      mutate: mutateOperationLog,
    } = useRequest(getOperationLog, {
      manual: true,
    });

    const {
      data: callPhoneLog,
      loading: getCallPhoneLogLoading,
      runAsync: runCallPhoneLog,
      mutate: mutateCallPhoneLog,
    } = useRequest(getCallPhoneLog, { manual: true });

    const [total, loading] = (() => {
      if (activityType === ActivityTypeEnum.INFO_CHANGE) {
        return [operationLog?.total || 0, getOperationLogLoading];
      } else {
        return [callPhoneLog?.total || 0, getCallPhoneLogLoading];
      }
    })();

    const getData = async (type: 'reset' | 'concat') => {
      let res: ActivityItem[];

      if (activityType === ActivityTypeEnum.INFO_CHANGE) {
        res = (
          await runOperationLog({
            pageNum: pageNum.current,
            pageSize,
            businessId,
            businessType: 'BUSINESS_OPPORTUNITY',
          })
        ).result;
      } else {
        res = (await runCallPhoneLog({ pageNum: pageNum.current, pageSize, callPhone })).result;
      }

      if (type === 'reset') {
        setData(res);
      } else {
        setData((prev) => prev.concat(res));
      }
    };

    const reset = () => {
      pageNum.current = 1;
      mutateOperationLog(undefined);
      mutateCallPhoneLog(undefined);
      setData([]);
    };

    React.useImperativeHandle(ref, () => ({
      reload: () => {
        // 如果在详情页修改了内容，要刷新一下
        if (activityType === ActivityTypeEnum.INFO_CHANGE) {
          reset();
          getData('reset');
        }
      },
    }));

    // 类型变更从第一页开始请求
    useEffect(() => {
      getData('reset');
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [activityType]);

    // 下拉加载
    const loadMoreData = async () => {
      if (loading) {
        return;
      }

      pageNum.current += 1;

      getData('concat');
    };

    // 根据日期进行分组
    const groupData = useMemo(() => {
      const result: Record<string, ActivityItem[]> = {};

      data.forEach((item) => {
        const date = dayjs(item.activityTime).format('YYYY-MM-DD');

        if (result[date]) {
          result[date].push(item);
        } else {
          result[date] = [item];
        }
      });

      return result;
    }, [data]);

    return (
      <>
        <Segmented
          className="mb-5"
          value={activityType}
          options={options}
          onChange={(key) => {
            setActivityType(key);
            reset();
          }}
        />
        {!data.length ? (
          loading ? (
            <Skeleton paragraph={{ rows: 5 }} title={false} active />
          ) : (
            <Empty />
          )
        ) : (
          <div className="overflow-auto h-[500px]" id="scrollableDiv">
            <InfiniteScroll
              dataLength={data.length}
              next={loadMoreData}
              hasMore={data.length < total}
              loader={<Skeleton paragraph={{ rows: 3 }} title={false} active className="px-6" />}
              scrollableTarget="scrollableDiv"
              // 去掉 overflow，下面的 sticky 才有效
              style={{ overflow: undefined }}
            >
              {Object.keys(groupData).map((date) => (
                <div key={date}>
                  <p className="mb-2 sticky top-0 bg-white z-10">{date}</p>
                  <Timeline
                    items={groupData[date].map((item) => ({
                      children: (
                        <>
                          <p className="mb-1">{dayjs(item.activityTime).format('HH:mm:ss')}</p>
                          <div className="px-4 py-2 rounded bg-[#fafafa]">
                            <p className="text-gray-500 mb-1">
                              {operationTypeLabelMap[item.operationType]}
                            </p>
                            {getTimeLineItem(item)}
                          </div>
                        </>
                      ),
                    }))}
                  />
                </div>
              ))}
            </InfiniteScroll>
          </div>
        )}
      </>
    );
  },
);

export default BusinessTimelineTab;
