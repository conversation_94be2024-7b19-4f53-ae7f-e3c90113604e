import { useRef } from 'react';
import { fissionAuditStatusOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import FissionDetailDrawer from '@src/pages/fission/components/FissionDetailDrawer';
import { getFissionListByBusinessId } from '@src/services/business-opportunity';
import { AuditStatusEnum } from '@src/services/common/type';
import { FissionAttributeEnum } from '@src/services/fission/type';
import { enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { Table } from 'antd';

interface FissionTabProps {
  id: number;
  onUpdate: () => void;
}

const FissionTab: React.FC<FissionTabProps> = ({ id, onUpdate }) => {
  const fissionDetailDrawerRef = useRef<ModalRef>(null);
  const currentIdRef = useRef<number | null>(null);

  const { data, loading } = useRequest(() => getFissionListByBusinessId(id));

  return (
    <>
      <Table
        bordered
        rowKey="id"
        size="small"
        dataSource={data}
        loading={loading}
        pagination={false}
        columns={[
          {
            title: '裂变申请编号',
            dataIndex: 'code',
            render: (value, record) => (
              <a
                onClick={() => {
                  currentIdRef.current = record.id;
                  fissionDetailDrawerRef.current?.open();
                }}
              >
                {value}
              </a>
            ),
          },
          {
            title: '审核状态',
            dataIndex: 'auditStatus',
            render: (value: AuditStatusEnum) =>
              fissionAuditStatusOptions.find((i) => i.value === value)?.label,
          },
          {
            title: '裂变属性',
            dataIndex: 'fissionAttribute',
            render: (value) => enum2ValueEnum(FissionAttributeEnum)[value],
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
          },
          {
            title: '备注',
            dataIndex: 'remark',
            ellipsis: true,
          },
        ]}
      />

      <FissionDetailDrawer
        ref={fissionDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={onUpdate}
      />
    </>
  );
};

export default FissionTab;
