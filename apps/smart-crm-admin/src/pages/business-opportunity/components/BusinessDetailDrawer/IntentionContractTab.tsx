import { useRef, useState } from 'react';
import { ModalRef } from '@src/common/interface';
import CreateEditIntentionModal from '@src/pages/contract/intention/components/CreateEditIntentionModal';
import IntentionDetailDrawer from '@src/pages/contract/intention/components/IntentionDetailDrawer';
import { Permission, PermissionsMap } from '@src/permissions';
import { getIntentionContractByBusinessId } from '@src/services/business-opportunity';
import { BusinessOpportunityDTO, ProgressNodeEnum } from '@src/services/business-opportunity/type';
import { IntentionNodeStateEnum } from '@src/services/contract/intention/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import { enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { Button, Table } from 'antd';
import WeaverPushModal from './WeaverPushModal';

interface IntentionContractTabProps {
  data?: BusinessOpportunityDTO;
  onUpdate: () => void;
}

const IntentionContractTab: React.FC<IntentionContractTabProps> = ({ data, onUpdate }) => {
  const createEditIntentionModalRef = useRef<ModalRef>(null);
  const intentionDetailDrawerRef = useRef<ModalRef>(null);
  const [intentionId, setIntentionId] = useState<number | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [weaverPushModalOpen, setWeaverPushModalOpen] = useState(false);

  const {
    data: tableData,
    loading,
    refresh,
  } = useRequest(() => getIntentionContractByBusinessId(data?.id!), { ready: !!data?.id });

  return (
    <>
      <div className="flex gap-2 mb-3">
        <Permission value={PermissionsMap.ContractIntentionCreate}>
          <Button
            type="primary"
            disabled={data?.progressNode !== ProgressNodeEnum.意向合同}
            onClick={() => createEditIntentionModalRef.current?.open()}
          >
            新建
          </Button>
        </Permission>
        <Button disabled={!selectedRowKeys.length} onClick={() => setWeaverPushModalOpen(true)}>
          推送泛微
        </Button>
      </div>
      <Table
        rowKey="id"
        loading={loading}
        size="small"
        bordered
        dataSource={tableData}
        pagination={false}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
          getCheckboxProps: (record) => ({
            disabled:
              record.nodeState !== IntentionNodeStateEnum.OA未发起 ||
              // 是搬迁的话禁用
              record.relocation,
          }),
        }}
        columns={[
          {
            title: '合同编号',
            dataIndex: 'code',
            render: (value, record) => (
              <a
                onClick={() => {
                  setIntentionId(record.id);
                  intentionDetailDrawerRef.current?.open();
                }}
              >
                {value}
              </a>
            ),
          },
          {
            title: '合同状态',
            dataIndex: 'nodeState',
            render: (value) => enum2ValueEnum(IntentionNodeStateEnum)[value],
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
          },
        ]}
      />
      <CreateEditIntentionModal
        ref={createEditIntentionModalRef}
        onSuccess={onUpdate}
        createInitialValues={
          data
            ? {
                customerId: data.customerId,
                customerName: data.customerName,
                bizOpportunityId: data.id,
              }
            : undefined
        }
      />
      <IntentionDetailDrawer
        ref={intentionDetailDrawerRef}
        getId={() => intentionId}
        onDelete={refresh}
      />
      <WeaverPushModal
        open={weaverPushModalOpen}
        getIds={() => selectedRowKeys}
        contractType={ContractTypeEnum.意向合同}
        onCancel={() => setWeaverPushModalOpen(false)}
        onSuccess={() => {
          setWeaverPushModalOpen(false);
          setSelectedRowKeys([]);
          onUpdate();
        }}
      />
    </>
  );
};

export default IntentionContractTab;
