import React, { useRef, useState } from 'react';
import { commonBOTypeOptions, fissionBOTypeOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { ButtonGroup } from '@src/components';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import CallPhonePopover from '@src/pages/clues/components/CallPhonePopover';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import { PermissionsMap, usePermission } from '@src/permissions';
import {
  deleteBusinessOpportunity,
  getBusinessOpportunityDetail,
} from '@src/services/business-opportunity';
import {
  BelongWarZoneEnum,
  BusinessOpportunityAbandonReasonEnum,
  BusinessOpportunityDTO,
  BusinessOpportunityTypeEnum,
  ChannelTypeEnum,
  OwnerTypeEnum,
} from '@src/services/business-opportunity/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Card, Drawer, DrawerProps, Tabs } from 'antd';
import AppointmentRecordTab from './AppointmentRecordTab';
import BusinessTimelineTab, { BusinessTimelineTabRef } from './BusinessTimelineTab';
import EvaluationTab from './EvaluationTab';
import FissionTab from './FissionTab';
import FormalContractTab from './FormalContractTab';
import FranchiseApplicationTab from './FranchiseApplicationTab';
import IntentionContractTab from './IntentionContractTab';
import ProgressSteps, { ProgressStepsRef } from './ProgressSteps';
import VisitTab from './VisitTab';
import CreateEditBusinessModal from '../CreateEditBusinessModal';
import TransferBusinessModal from '../TransferBusinessModal';

interface BusinessDetailDrawerProps extends DrawerProps {
  getId: () => number | null | undefined;
  // 更新数据
  onUpdate?: () => void;
  // 数据从当前列表移除、可能是删除、转让
  onDecrease?: () => void;
}

const BusinessDetailDrawer = React.forwardRef<ModalRef, BusinessDetailDrawerProps>(
  ({ getId, onUpdate, onDecrease, ...drawerProps }, ref) => {
    const { modal, message } = App.useApp();
    const [open, setOpen] = useState(false);
    const transferBusinessModalRef = useRef<ModalRef>(null);
    const customerDetailDrawerRef = useRef<ModalRef>(null);
    const createEditBusinessModalRef = useRef<ModalRef>(null);
    const businessTimelineTabRef = useRef<BusinessTimelineTabRef>(null);
    const [activeKey, setActiveKey] = useState('info');
    const progressStepsRef = useRef<ProgressStepsRef>(null);

    const businessId = getId()!;

    const checkPermission = usePermission();
    const { data, loading, refresh, mutate } = useRequest(
      () => getBusinessOpportunityDetail(businessId),
      {
        ready: open,
      },
    );

    const { data: users, loading: getUsersLoading } = useAllUsers({ ready: open });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        setActiveKey('info');
        mutate(undefined);
      },
      close: () => setOpen(false),
    }));

    const baseInfoItems: DescriptionFieldType<BusinessOpportunityDTO>[] = [
      {
        label: '商机编号',
        field: 'code',
      },
      {
        label: '商机名称',
        field: 'name',
      },
      {
        label: '联系方式',
        field: 'phone',
        valueType: ValueType.PHONE,
        fieldProps: {
          sensitiveValue: data?.phoneSensitive,
        },
      },
      {
        label: '负责人',
        field: 'directUserId',
        valueType: ValueType.PERSON,
      },
      {
        label: '商机进展',
        field: 'businessOpportunityType',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: [...commonBOTypeOptions, ...fissionBOTypeOptions],
        },
      },
      {
        label: '渠道类型',
        field: 'channelTypes',
        valueType: ValueType.MULTIPLE,
        hidden: [
          BusinessOpportunityTypeEnum.SOCIETY,
          BusinessOpportunityTypeEnum.FISSION_SOCIETY,
        ].includes(data?.businessOpportunityType!),
        fieldProps: {
          mode: 'multiple',
          options: enum2Options(ChannelTypeEnum),
        },
      },
      {
        label: '意向区域',
        field: 'intentionRegion',
        valueType: ValueType.REGION,
        fieldProps: {
          multiple: true,
          regionLevel: 4,
        },
      },
      {
        label: '所属大区',
        field: 'belongWarZone',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(BelongWarZoneEnum),
        },
      },
      {
        label: '业主类型',
        field: 'ownerType',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(OwnerTypeEnum),
        },
      },
      {
        label: '客户情况描述',
        field: 'customerDesc',
      },
      {
        label: '意向点位',
        field: 'intentionPositions',
        children: (
          <div>
            {data?.intentionPositions?.map((i, index) => (
              <p key={index}>
                {index + 1}. {i.name}（经度：{i.longitude}；纬度：{i.latitude}）
              </p>
            ))}
          </div>
        ),
      },
      {
        label: '放弃原因',
        field: 'abandonReason',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(BusinessOpportunityAbandonReasonEnum),
        },
      },
      {
        label: '放弃说明',
        field: 'abandonDesc',
      },
    ];

    // 商机状态发生变更
    const handleRefreshInfo = () => {
      refresh();
      progressStepsRef.current?.refresh();
      onUpdate?.();
    };

    const isFissionApply = [
      BusinessOpportunityTypeEnum.FISSION_SOCIETY,
      BusinessOpportunityTypeEnum.FISSION_CHANNEL,
    ].includes(data?.businessOpportunityType!);

    return (
      <Drawer
        title="商机详情"
        width={1200}
        destroyOnHidden
        push={false}
        onClose={() => setOpen(false)}
        {...drawerProps}
        open={open}
      >
        <Card loading={loading || getUsersLoading}>
          <div className="flex justify-between w-full gap-2">
            <div className="flex flex-col gap-2">
              <p>商机名称：{data?.name}</p>
              <p>
                客户名称：
                <a onClick={() => customerDetailDrawerRef.current?.open()}>{data?.customerName}</a>
              </p>
              <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
              <p>创建时间：{data?.createTime}</p>
              <p>更新人：{users?.find((i) => i.id === data?.updateUserId)?.name}</p>
              <p>更新时间：{data?.updateTime}</p>
            </div>
            <ButtonGroup
              items={[
                {
                  label: '编辑',
                  onClick: () => createEditBusinessModalRef.current?.open(),
                },
                {
                  label: '转让',
                  onClick: () => transferBusinessModalRef.current?.open(),
                },
                {
                  label: (isMobile) => (
                    <CallPhonePopover phone={data?.phone!}>
                      {isMobile ? '拨打电话' : <Button>拨打电话</Button>}
                    </CallPhonePopover>
                  ),
                },
                {
                  label: '删除',
                  danger: true,
                  show: checkPermission(PermissionsMap.BusinessOpportunityDelete),
                  onClick: () =>
                    modal.confirm({
                      title: '删除',
                      content: `确定要删除商机 “${data?.name}” 吗？`,
                      onOk: async () => {
                        await deleteBusinessOpportunity(businessId);
                        message.success('删除成功');
                        setOpen(false);
                        onDecrease?.();
                      },
                    }),
                },
              ]}
            />
          </div>
        </Card>
        <ProgressSteps
          ref={progressStepsRef}
          loading={loading}
          data={data}
          businessId={businessId}
          onUpdate={handleRefreshInfo}
        />
        <Card className="mt-5" loading={loading}>
          <Tabs
            // refresh 时保持当前 Tab
            activeKey={activeKey}
            onChange={setActiveKey}
            className="-mt-5"
            items={[
              {
                label: '详细资料',
                key: 'info',
                children: renderDescriptions(baseInfoItems, data),
              },
              {
                label: '商机动态',
                key: 'timeline',
                destroyOnHidden: true,
                children: (
                  <BusinessTimelineTab
                    businessId={businessId}
                    callPhone={data?.phone!}
                    ref={businessTimelineTabRef}
                  />
                ),
              },
              {
                label: '预约记录',
                key: 'appointment-record',
                hidden: isFissionApply,
                children: <AppointmentRecordTab id={businessId} onUpdate={handleRefreshInfo} />,
              },
              {
                label: '加盟申请表',
                key: 'franchise-application',
                hidden: isFissionApply,
                children: <FranchiseApplicationTab id={businessId} onUpdate={handleRefreshInfo} />,
              },
              {
                label: '面审到访表',
                key: 'visit',
                hidden: isFissionApply,
                children: <VisitTab id={businessId} onUpdate={handleRefreshInfo} />,
              },
              {
                label: '面审评估表',
                key: 'evaluation',
                hidden: isFissionApply,
                children: <EvaluationTab id={businessId} onUpdate={handleRefreshInfo} />,
              },
              {
                label: '裂变申请',
                key: 'fission',
                hidden: !isFissionApply,
                children: <FissionTab id={businessId} onUpdate={handleRefreshInfo} />,
              },
              {
                label: '意向合同',
                key: 'intention-contract',
                children: <IntentionContractTab data={data} onUpdate={handleRefreshInfo} />,
              },
              {
                label: '正式合同',
                key: 'formal-contract',
                children: <FormalContractTab data={data} onUpdate={handleRefreshInfo} />,
              },
            ].filter((i) => !i.hidden)}
          />
        </Card>

        <TransferBusinessModal
          ref={transferBusinessModalRef}
          getId={() => businessId}
          onSuccess={() => {
            setOpen(false);
            onDecrease?.();
          }}
        />

        <CustomerDetailDrawer getId={() => data?.customerId} ref={customerDetailDrawerRef} />

        <CreateEditBusinessModal
          ref={createEditBusinessModalRef}
          getId={() => businessId}
          onSuccess={() => {
            businessTimelineTabRef.current?.reload();
            refresh();
            onUpdate?.();
          }}
        />
      </Drawer>
    );
  },
);

export default BusinessDetailDrawer;
