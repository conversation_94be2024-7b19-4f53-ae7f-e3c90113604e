import { useRef, useState } from 'react';
import { ModalRef } from '@src/common/interface';
import FormalDetailDrawer from '@src/pages/contract/formal/components/FormalDetailDrawer';
import { getFormalContractByBusinessId } from '@src/services/business-opportunity';
import { BusinessOpportunityDTO } from '@src/services/business-opportunity/type';
import { FormalNodeStateEnum } from '@src/services/contract/formal/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import { enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { Button, Table } from 'antd';
import WeaverPushModal from './WeaverPushModal';

interface FormalContractTabProps {
  data?: BusinessOpportunityDTO;
  onUpdate: () => void;
}

const FormalContractTab: React.FC<FormalContractTabProps> = ({ data, onUpdate }) => {
  const formalDetailDrawerRef = useRef<ModalRef>(null);
  const [formalId, setFormalId] = useState<number | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [weaverPushModalOpen, setWeaverPushModalOpen] = useState(false);

  const {
    data: tableData,
    loading,
    refresh,
  } = useRequest(() => getFormalContractByBusinessId(data?.id!), { ready: !!data?.id });

  return (
    <>
      <Button
        className="mb-3"
        disabled={!selectedRowKeys.length}
        onClick={() => setWeaverPushModalOpen(true)}
      >
        推送泛微
      </Button>
      <Table
        rowKey="id"
        loading={loading}
        size="small"
        bordered
        dataSource={tableData}
        pagination={false}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
          getCheckboxProps: (record) => ({
            disabled:
              record.nodeState !== FormalNodeStateEnum.OA未发起 ||
              // 是搬迁的话禁用
              record.relocation,
          }),
        }}
        columns={[
          {
            title: '合同编号',
            dataIndex: 'code',
            render: (value, record) => (
              <a
                onClick={() => {
                  setFormalId(record.id);
                  formalDetailDrawerRef.current?.open();
                }}
              >
                {value}
              </a>
            ),
          },
          {
            title: '合同状态',
            dataIndex: 'nodeState',
            render: (value) => enum2ValueEnum(FormalNodeStateEnum)[value],
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
          },
        ]}
      />
      <FormalDetailDrawer ref={formalDetailDrawerRef} getId={() => formalId} onDelete={refresh} />
      <WeaverPushModal
        open={weaverPushModalOpen}
        getIds={() => selectedRowKeys}
        contractType={ContractTypeEnum.正式合同}
        onCancel={() => setWeaverPushModalOpen(false)}
        onSuccess={() => {
          setWeaverPushModalOpen(false);
          setSelectedRowKeys([]);
          onUpdate();
        }}
      />
    </>
  );
};

export default FormalContractTab;
