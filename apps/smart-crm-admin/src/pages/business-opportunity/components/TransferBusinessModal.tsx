import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { UserSelect } from '@src/components';
import {
  batchTransferBusinessOpportunity,
  transferBusinessOpportunity,
} from '@src/services/business-opportunity';
import { useRequest } from 'ahooks';
import { App, Form, Modal, ModalProps } from 'antd';

interface TransferBusinessModalProps extends ModalProps {
  getId: () => number | null;
  selectedRowKeys?: number[];
  onSuccess: () => void;
}

const TransferBusinessModal = React.forwardRef<ModalRef, TransferBusinessModalProps>(
  ({ getId, selectedRowKeys = [], onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);

    const businessId = getId();

    const { runAsync: transfer, loading: transferLoading } = useRequest(
      transferBusinessOpportunity,
      {
        manual: true,
      },
    );
    const { runAsync: batchTransfer, loading: batchTransferLoading } = useRequest(
      batchTransferBusinessOpportunity,
      { manual: true },
    );

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const handleSave = async ({ id }: { id: number }) => {
      if (businessId) {
        await transfer({ id: businessId, transferUserId: id });
        message.success('转让成功');
      } else {
        await batchTransfer({
          ids: selectedRowKeys,
          transferUserId: id,
        });
        message.success('转让成功');
      }

      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        title="转让商机"
        destroyOnHidden
        confirmLoading={transferLoading || batchTransferLoading}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
        {...props}
        open={open}
      >
        <Form form={form} clearOnDestroy onFinish={handleSave}>
          <Form.Item
            label="员工"
            name="id"
            extra="员工会收到相应的提醒"
            rules={[{ required: true, message: '请选择员工' }]}
          >
            <UserSelect />
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default TransferBusinessModal;
