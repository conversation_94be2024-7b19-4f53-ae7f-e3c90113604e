import React, { useState } from 'react';
import { commonBOTypeOptions, fissionBOTypeOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ChooseMapPoints, Encrypt, ImportFileFormItem, UserSelect } from '@src/components';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useDistricts from '@src/hooks/useDistricts';
import useProvinceWarZoneMap from '@src/hooks/useProvinceWarZoneMap';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import {
  checkCustomerCreateRecently,
  createBusinessOpportunity,
  downloadBusinessExcelTemplate,
  editBusinessOpportunity,
  getBusinessOpportunityDetail,
  importBusinessExcel,
} from '@src/services/business-opportunity';
import {
  BelongWarZoneEnum,
  BusinessOpportunityTypeEnum,
  ChannelTypeEnum,
} from '@src/services/business-opportunity/type';
import { getCustomerDetail } from '@src/services/customers';
import useUserStore from '@src/store/useUserStore';
import { enum2Options, enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps, Select, Tabs } from 'antd';

interface CreateEditBusinessModalProps extends ModalProps {
  getId: () => number | null;
  onSuccess: () => void;
}

const CreateEditBusinessModal = React.forwardRef<ModalRef, CreateEditBusinessModalProps>(
  ({ getId, onSuccess, ...props }, ref) => {
    const {
      user: { shUserId },
    } = useUserStore();
    const { modal, message } = App.useApp();
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);
    const [isFission, setIsFission] = useState(false);
    const [activeKey, setActiveKey] = useState<'manual' | 'excel'>('manual');

    const { showImportPrompt } = ImportFileFormItem.usePrompt();
    const { data: districts } = useDistricts();

    const businessId = getId();

    const {
      data: customer,
      runAsync: getCustomer,
      loading: getCustomerLoading,
      mutate: mutateCustomer,
    } = useRequest(getCustomerDetail, {
      manual: true,
    });
    const { data, loading, mutate } = useRequest(() => getBusinessOpportunityDetail(businessId!), {
      ready: open && !!businessId,
      onSuccess: (res) => {
        getCustomer(res.customerId);
        form.setFieldsValue(res);
        setIsFission(fissionBOTypeOptions.some((i) => i.value === res.businessOpportunityType));
      },
    });

    const { runAsync: create, loading: createLoading } = useRequest(createBusinessOpportunity, {
      manual: true,
    });
    const { runAsync: edit, loading: editLoading } = useRequest(editBusinessOpportunity, {
      manual: true,
    });
    const { runAsync: importExcel, loading: importExcelLoading } = useRequest(importBusinessExcel, {
      manual: true,
    });
    const { runAsync: checkCreateRecently, loading: checkCreateRecentlyLoading } = useRequest(
      checkCustomerCreateRecently,
      { manual: true },
    );
    const provinceWarZoneMap = useProvinceWarZoneMap({ ready: open });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        mutate(undefined);
        mutateCustomer(undefined);
        setIsFission(false);
      },
      close: () => setOpen(false),
    }));

    const isCreate = !businessId;

    const getCustomerName = () => {
      const businessOpportunityType = [...commonBOTypeOptions, ...fissionBOTypeOptions].find(
        (i) => i.value === form.getFieldValue('businessOpportunityType'),
      )?.label;
      const channelTypeValueEnum = enum2ValueEnum(ChannelTypeEnum);
      const channelTypes = form
        .getFieldValue('channelTypes')
        ?.map((val: ChannelTypeEnum) => channelTypeValueEnum[val])
        .join('');
      const province = districts?.find(
        (i) => i.code === form.getFieldValue('intentionRegion')?.split(',')[0].split('/')[0],
      )?.name;

      return [customer?.name, province, businessOpportunityType, channelTypes]
        .filter(Boolean)
        .join('-');
    };

    const handleSave = async (values: any) => {
      if (isCreate) {
        if (activeKey === 'manual') {
          const isCreateRecently = await checkCreateRecently(values.customerId);

          if (isCreateRecently) {
            const shouldCreate = await modal.confirm({
              title: '提示',
              content: '当前客户近 90 天内存在新建的商机，是否继续新建？',
            });

            if (!shouldCreate) {
              return;
            }
          }

          await create({ name: getCustomerName(), ...values });

          message.success('创建成功');
          onSuccess();
        } else {
          const res = await importExcel(values);

          showImportPrompt(res, onSuccess);
        }
      } else {
        await edit({ id: businessId, name: getCustomerName(), ...values });
        message.success('修改成功');
        onSuccess();
      }

      setOpen(false);
    };

    const manualFormNode = (
      <>
        <Form.Item label="商机编号" required>
          <Input disabled placeholder="根据编号规则自动生成" value={data?.code} />
        </Form.Item>
        <Form.Item noStyle shouldUpdate>
          {() => (
            <Form.Item
              label="商机名称"
              required
              extra="根据客户+意向省份+商机进展+渠道类型自动生成"
            >
              <Input
                disabled
                value={getCustomerName()}
                placeholder="根据客户+意向省份+商机进展+渠道类型自动生成"
              />
            </Form.Item>
          )}
        </Form.Item>
        <Form.Item
          label="负责人"
          name="directUserId"
          initialValue={shUserId}
          rules={[{ required: true, message: '请选择负责人' }]}
        >
          <UserSelect />
        </Form.Item>
        <Form.Item
          label="客户名称"
          name="customerId"
          rules={[{ required: true, message: '请选择客户名称' }]}
        >
          <RelCustomer
            defaultCustomerIdToNameMap={{ [data?.customerId || '']: data?.customerName }}
            editable={isCreate}
            onChange={(_, info) => {
              if (!info?.hasClue) {
                message.warning('客户未绑定线索，请先绑定线索');

                return false;
              }

              mutateCustomer(info);
              form.setFieldValue('phone', undefined);

              // 客户关联了门店，为裂变
              const newIsFission = !!info?.hasShop;

              if (isFission !== newIsFission) {
                setIsFission(newIsFission);
                form.setFieldsValue({
                  businessOpportunityType: undefined,
                  intentionRegion: undefined,
                  belongWarZone: undefined,
                });
              }
            }}
            onUpdate={(info) => {
              mutateCustomer(info);

              const currentPhone = form.getFieldValue('phone');

              if (currentPhone && !info?.phones?.includes(currentPhone)) {
                form.setFieldValue('phone', undefined);
              }
            }}
          />
        </Form.Item>
        <Form.Item
          label="手机号"
          name="phone"
          rules={[{ required: true, message: '请选择手机号' }]}
        >
          <Encrypt.PhoneSelect
            placeholder="请选择手机号"
            options={customer?.phones?.map((phone, index) => ({
              label: customer.phonesSensitive?.[index],
              value: phone,
            }))}
            encryptSensitiveMap={{
              [data?.phone || '']: data?.phoneSensitive,
            }}
          />
        </Form.Item>
        <Form.Item
          label="商机进展"
          name="businessOpportunityType"
          initialValue={BusinessOpportunityTypeEnum.SOCIETY}
          rules={[{ required: true, message: '请选择商机进展' }]}
        >
          <Select
            disabled={!isCreate}
            placeholder="请选择商机进展"
            options={isFission ? fissionBOTypeOptions : commonBOTypeOptions}
          />
        </Form.Item>
        <Form.Item noStyle shouldUpdate>
          {({ getFieldValue }) => {
            const businessOpportunityType = getFieldValue('businessOpportunityType');
            const showChannelTypes = [
              BusinessOpportunityTypeEnum.CHANNEL,
              BusinessOpportunityTypeEnum.FISSION_CHANNEL,
            ].includes(businessOpportunityType);
            const isRegionMultiple = [
              BusinessOpportunityTypeEnum.SOCIETY,
              BusinessOpportunityTypeEnum.CHANNEL,
            ].includes(businessOpportunityType);

            return (
              <>
                {showChannelTypes && (
                  <Form.Item label="渠道类型" name="channelTypes">
                    <Select
                      allowClear
                      mode="multiple"
                      placeholder="请选择渠道类型"
                      options={enum2Options(ChannelTypeEnum)}
                    />
                  </Form.Item>
                )}
                <EditableRegion
                  editable
                  formItemProps={{
                    label: '意向区域',
                    name: 'intentionRegion',
                    rules: [{ required: true, message: '请选择意向区域' }],
                  }}
                  fieldProps={{
                    multiple: isRegionMultiple,
                    placeholder: '请选择意向区域',
                    // regionLevel: 4,
                    // transformDistricts,
                    onChange: (value: any) => {
                      form.setFieldValue(
                        'belongWarZone',
                        provinceWarZoneMap[isRegionMultiple ? value[0]?.[0] : value?.[0]],
                      );
                    },
                  }}
                />
              </>
            );
          }}
        </Form.Item>
        <Form.Item
          label="所属大区"
          name="belongWarZone"
          rules={[{ required: true, message: '请选择所属大区' }]}
        >
          <Select
            disabled
            placeholder="根据意向区域自动匹配"
            options={enum2Options(BelongWarZoneEnum)}
          />
        </Form.Item>
        <Form.Item label="意向点位" name="intentionPositions">
          <ChooseMapPoints />
        </Form.Item>
        <Form.Item label="客户情况描述" name="customerDesc">
          <Input placeholder="请输入客户情况描述" />
        </Form.Item>
      </>
    );

    return (
      <Modal
        title={isCreate ? '新建商机' : '编辑商机'}
        destroyOnHidden
        confirmLoading={
          createLoading || editLoading || importExcelLoading || checkCreateRecentlyLoading
        }
        loading={loading || getCustomerLoading}
        styles={{
          body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
        }}
        modalRender={(node) => (
          <Form
            form={form}
            labelCol={{ span: 7 }}
            clearOnDestroy
            scrollToFirstError
            onFinish={handleSave}
          >
            {node}
          </Form>
        )}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
        {...props}
        open={open}
      >
        {isCreate ? (
          <Tabs
            activeKey={activeKey}
            destroyOnHidden
            onChange={(key) => {
              setActiveKey(key as typeof activeKey);
            }}
            items={[
              {
                label: '手动新建',
                key: 'manual',
                children: manualFormNode,
              },
              {
                label: '通过 EXCEL 导入',
                key: 'excel',
                children: <ImportFileFormItem downloadTemplate={downloadBusinessExcelTemplate} />,
              },
            ]}
          />
        ) : (
          manualFormNode
        )}
      </Modal>
    );
  },
);

export default CreateEditBusinessModal;
