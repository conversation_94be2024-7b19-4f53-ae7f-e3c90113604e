import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import {
  assignExamInProgress,
  batchAssignExam,
  getFranchiseExamsInProgress,
} from '@src/services/business-opportunity';
import { useRequest } from 'ahooks';
import { Form, message, Modal, ModalProps, Select } from 'antd';

interface ChooseApplyExamModalProps extends ModalProps {
  getIdOrIds: () => number | number[];
  onSuccess: () => void;
}

const ChooseApplyExamModal = React.forwardRef<ModalRef, ChooseApplyExamModalProps>(
  ({ getIdOrIds, onSuccess, ...props }, ref) => {
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);

    const idOrIds = getIdOrIds();

    // 是否是批量操作
    const isBatch = Array.isArray(idOrIds);

    const {
      data: examList,
      loading: getExamListLoading,
      mutate: mutateExamList,
    } = useRequest(() => getFranchiseExamsInProgress(isBatch ? idOrIds[0] : idOrIds), {
      ready: open,
      onBefore: () => {
        mutateExamList(undefined);
      },
      onSuccess: (res) => {
        // 仅有一个加盟申请表时，默认选中
        if (res.length === 1) {
          form.setFieldValue('examPaperId', res[0].id);
        }
      },
    });
    const { runAsync: assign, loading: assignLoading } = useRequest(assignExamInProgress, {
      manual: true,
    });
    const { runAsync: batchAssign, loading: batchAssignLoading } = useRequest(batchAssignExam, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    return (
      <Modal
        title="加盟申请收集"
        open={open}
        destroyOnHidden
        confirmLoading={assignLoading || batchAssignLoading}
        onCancel={() => setOpen(false)}
        onOk={form.submit}
        {...props}
      >
        <Form
          form={form}
          clearOnDestroy
          onFinish={async (values) => {
            if (isBatch) {
              await batchAssign({ bizOpportunityIds: idOrIds, ...values });
            } else {
              await assign({ bizOpportunityId: idOrIds, ...values });
            }

            message.success('开启成功');
            setOpen(false);
            onSuccess();
          }}
        >
          <Form.Item
            name="examPaperId"
            label="加盟申请表"
            rules={[{ required: true, message: '请选择加盟申请表' }]}
          >
            <Select
              placeholder="请选择加盟申请表"
              showSearch
              optionFilterProp="name"
              options={examList}
              fieldNames={{ label: 'name', value: 'id' }}
              loading={getExamListLoading}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default ChooseApplyExamModal;
