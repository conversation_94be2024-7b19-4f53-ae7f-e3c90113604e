import React, { useMemo, useRef, useState } from 'react';
import { <PERSON>Container, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@src/components';
import useDistricts from '@src/hooks/useDistricts';
import { JoinRegionEnum } from '@src/services/regional/quota/type';
import {
  getRegionalQuotaCityText,
  getRegionalQuotaTextCityList,
  getRegionalQuotaTextProvinceList,
} from '@src/services/regional/quota-text';
import {
  GetRegionalQuotaTextProvinceListReq,
  RegionalQuotaTextDTO,
} from '@src/services/regional/quota-text/type';
import { copyClipboard } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Tabs } from 'antd';

const QuotaText = () => {
  const { message } = App.useApp();
  const [loadingCodeMap, setLoadingCodeMap] = useState<Record<string, boolean>>({});
  const [joinRegion, setJoinRegion] = useState(JoinRegionEnum['公开加盟区(M)']);
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const formRef = useRef<ProFormInstance>();

  const { data: districts } = useDistricts();

  const { data, loading, mutate, run, cancel } = useRequest(
    async (values?: Partial<GetRegionalQuotaTextProvinceListReq>) => {
      const res = await getRegionalQuotaTextProvinceList({ joinRegion, ...values });

      return res.map((i) => ({ ...i, children: [] as RegionalQuotaTextDTO[] }));
    },
  );

  const provinceValueEnum = useMemo(
    () =>
      districts?.reduce<Record<string, string>>((total, cur) => {
        if (cur.deep === 0) {
          total[cur.code] = cur.name;
        }

        return total;
      }, {}),
    [districts],
  );

  const getCopyText = (item: RegionalQuotaTextDTO, copyText: string) => {
    const { name, franchiseAreaQuotaNums } = item;

    if (franchiseAreaQuotaNums.length) {
      copyText += `${copyText ? '\n' : ''}${name}：${franchiseAreaQuotaNums
        .map((i) => i.districtName)
        .join('、')}`;
      franchiseAreaQuotaNums.forEach((i) => {
        if (i.remark) {
          copyText += `\n${i.districtName}备注：${i.remark}`;
        }
      });
    }

    return copyText;
  };

  const columns: ProColumns<RegionalQuotaTextDTO>[] = [
    {
      title: '省份',
      dataIndex: 'name',
      valueType: (_, type) => (type === 'table' ? 'text' : 'select'),
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
      },
      formItemProps: {
        name: 'provinceCodes',
      },
      valueEnum: provinceValueEnum,
      onCell: (record) => ({
        className: record.deep === 0 ? 'text-base' : '',
      }),
    },
    {
      title: '剩余可加盟区域',
      dataIndex: 'franchiseAreaQuotaNums',
      tooltip: '红色表示剩余加盟名额只剩 1 个',
      search: false,
      render: (_, { deep, franchiseAreaQuotaNums }) => {
        if (deep === 0) {
          return '-';
        }

        if (!franchiseAreaQuotaNums.length) {
          return <span className="text-gray-400">无</span>;
        }

        return (
          <>
            {franchiseAreaQuotaNums.map((i, index) => (
              <React.Fragment key={index}>
                {i.remainQuotaNum === 1 ? (
                  <span key={index} className="text-red-500">
                    {i.districtName}
                  </span>
                ) : (
                  i.districtName
                )}
                {index < franchiseAreaQuotaNums.length - 1 && '、'}
              </React.Fragment>
            ))}
            {franchiseAreaQuotaNums.map((i) => {
              return (
                i.remark && (
                  <div key={i.districtCode} className="mt-1">
                    {i.districtName}备注：{i.remark}
                  </div>
                )
              );
            })}
          </>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      width: 80,
      search: false,
      render: (_, { code, deep }) => (
        <Button
          type="link"
          size="small"
          className="!p-0"
          loading={loadingCodeMap[code]}
          onClick={async () => {
            setLoadingCodeMap((prev) => ({ ...prev, [code]: true }));

            let copyText = '';

            try {
              if (deep === 0) {
                const res = await getRegionalQuotaTextCityList({ joinRegion, provinceCode: code });

                res.forEach((item) => {
                  copyText = getCopyText(item, copyText);
                });
              } else {
                const res = await getRegionalQuotaCityText({ joinRegion, cityCode: code });

                copyText = getCopyText(res, copyText);
              }

              await copyClipboard(copyText);

              if (copyText) {
                message.success('复制成功');
              } else {
                message.warning('无内容可复制');
              }
            } catch (error) {}

            setLoadingCodeMap((prev) => ({ ...prev, [code]: false }));
          }}
        >
          复制
        </Button>
      ),
    },
  ];

  return (
    <PageContainer>
      <Tabs
        activeKey={joinRegion}
        items={[
          { label: '加盟 M', key: JoinRegionEnum['公开加盟区(M)'] },
          { label: '加盟 T', key: JoinRegionEnum['邀请加盟区(T)'] },
        ]}
        onChange={(key) => {
          setJoinRegion(key as JoinRegionEnum);
          mutate(undefined);
          cancel();
          formRef.current?.resetFields();
          setExpandedRowKeys([]);
          run({ joinRegion: key as JoinRegionEnum });
        }}
      />
      <ProTable
        dataSource={data}
        loading={loading}
        onSubmit={(values) => run(values)}
        onReset={run}
        rowKey="code"
        bordered
        cardProps={false}
        sticky
        pagination={false}
        columns={columns}
        formRef={formRef}
        // 这个样式可以识别 \n 换行
        className="whitespace-pre-line"
        expandable={{
          expandedRowKeys,
          onExpandedRowsChange: (keys) => setExpandedRowKeys(keys as string[]),
          onExpand: async (expanded, record) => {
            if (expanded) {
              const res = await getRegionalQuotaTextCityList({
                joinRegion,
                provinceCode: record.code,
              });

              mutate((prev) => {
                const result = [...prev!];
                const index = result.findIndex((i) => i.code === record.code);

                result[index].children = res;

                return result;
              });
            }
          },
        }}
      />
    </PageContainer>
  );
};

export default QuotaText;
