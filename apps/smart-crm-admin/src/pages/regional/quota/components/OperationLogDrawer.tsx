import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { OperationLog } from '@src/components';
import { Drawer } from 'antd';

interface OperationLogDrawerProps {
  getId: () => number | null;
}

const OperationLogDrawer = React.forwardRef<ModalRef, OperationLogDrawerProps>(
  ({ getId, ...props }, ref) => {
    const [open, setOpen] = useState(false);

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    return (
      <Drawer
        title="操作记录"
        open={open}
        destroyOnHidden
        onClose={() => setOpen(false)}
        {...props}
      >
        <OperationLog
          id={getId()!}
          businessType="FRANCHISE_AREA_QUOTA"
          pageSize={10}
          className="h-full"
        />
      </Drawer>
    );
  },
);

export default OperationLogDrawer;
