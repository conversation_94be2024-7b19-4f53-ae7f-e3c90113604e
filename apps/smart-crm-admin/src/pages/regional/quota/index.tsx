import { useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { ModalRef } from '@src/common/interface';
import { ETable, ExportButton } from '@src/components';
import { ETableActionType, ETableColumn, SaveInfoType, ValueType } from '@src/components/ETable';
import useTableViews from '@src/hooks/useTableViews';
import { Permission, PermissionsMap } from '@src/permissions';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { BusinessTypeEnum } from '@src/services/common/type';
import {
  exportRegionalQuota,
  getRegionalQuotaDetail,
  getRegionalQuotaList,
  updateRegionalQuota,
} from '@src/services/regional/quota';
import {
  JoinRegionEnum,
  RegionalQuotaDTO,
  RegionalQuotaOpenedEnum,
} from '@src/services/regional/quota/type';
import { compatibleTableActionWidth, enum2Options, getParamsFromFilters } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Skeleton } from 'antd';
import OperationLogDrawer from './components/OperationLogDrawer';
import CustomerDetailDrawer from '../../customers/components/CustomerDetailDrawer';

const RegionalQuota = () => {
  const { message } = App.useApp();
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const actionRef = useRef<ETableActionType>(null);
  const operationLogDrawerRef = useRef<ModalRef>(null);
  const currentIdRef = useRef<number | null>(null);
  const customerIdRef = useRef<number | null>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const { views, loading: getViewsLoading } = useTableViews(BusinessTypeEnum.FRANCHISE_AREA_QUOTA);

  const { data, runAsync: getList } = useRequest(getRegionalQuotaList, { manual: true });

  const columns: ETableColumn<RegionalQuotaDTO>[] = [
    {
      title: '行政区域编码',
      dataIndex: 'districtCode',
      fixed: 'left',
    },
    {
      title: '大区',
      dataIndex: 'belongWarZone',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(BelongWarZoneEnum),
      },
      filterProps: {
        options: enum2Options(BelongWarZoneEnum),
      },
    },
    {
      title: '省份',
      dataIndex: 'provinceCode',
      valueType: ValueType.REGION,
      hideInFilters: true,
    },
    {
      title: '城市',
      dataIndex: 'cityCode',
      valueType: ValueType.REGION,
      hideInFilters: true,
    },
    {
      title: '区县',
      filterTitle: '省市区',
      dataIndex: 'regionCode',
      valueType: ValueType.REGION,
    },
    {
      title: '乡镇',
      dataIndex: 'streetCode',
      valueType: ValueType.REGION,
      hideInFilters: true,
      fieldProps: {
        regionLevel: 4,
      },
      filterProps: {
        unlimited: {
          onChange: true,
        },
        regionLevel: 4,
      },
    },
    {
      title: 'T/M区域',
      dataIndex: 'joinRegion',
      valueType: ValueType.SINGLE,
      editable: true,
      fieldProps: {
        options: enum2Options(JoinRegionEnum),
        allowClear: false,
      },
      filterProps: {
        options: enum2Options(JoinRegionEnum),
      },
    },
    {
      title: '是否对外开放',
      dataIndex: 'opened',
      valueType: ValueType.SINGLE,
      editable: true,
      fieldProps: {
        options: enum2Options(RegionalQuotaOpenedEnum),
        allowClear: false,
      },
      filterProps: {
        options: enum2Options(RegionalQuotaOpenedEnum),
      },
    },
    {
      title: '加盟商特注',
      dataIndex: 'franchiseNote',
      editable: true,
      fieldProps: {
        maxLength: 500,
        showCount: true,
      },
    },
    {
      title: '推荐点位',
      dataIndex: 'recommendPosition',
      editable: true,
      fieldProps: {
        maxLength: 500,
        showCount: true,
      },
    },
    {
      title: '剩余加盟名额数',
      dataIndex: 'remainQuotaNum',
      valueType: ValueType.NUMBER,
      hideInFilters: true,
    },
    {
      title: '可用名额数',
      dataIndex: 'availableQuotaNum',
      valueType: ValueType.NUMBER,
      hideInFilters: true,
    },
    {
      title: '意向中扩容数',
      dataIndex: 'expUseNum',
      valueType: ValueType.NUMBER,
      hideInFilters: true,
    },
    {
      title: '未使用扩容数',
      dataIndex: 'expNotUseNum',
      valueType: ValueType.NUMBER,
      hideInFilters: true,
    },
    {
      title: '正式签约中数',
      dataIndex: 'formalSignNum',
      valueType: ValueType.NUMBER,
      hideInFilters: true,
    },
    {
      title: '意向中数（不含特渠）',
      dataIndex: 'intentionSignNum',
      valueType: ValueType.NUMBER,
      hideInFilters: true,
    },
    {
      title: '搬迁中数',
      dataIndex: 'relocationCount',
      valueType: ValueType.NUMBER,
      hideInFilters: true,
    },
    {
      title: '名额调整值',
      dataIndex: 'quotaAdjustment',
      valueType: ValueType.NUMBER,
      hideInFilters: true,
      editable: true,
      fieldProps: {
        max: 9999,
        min: -9999,
        precision: 0,
      },
    },
    {
      title: '服务组备注',
      dataIndex: 'serviceGroupRemark',
      editable: true,
      fieldProps: {
        maxLength: 500,
        showCount: true,
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      editable: true,
      fieldProps: {
        maxLength: 500,
        showCount: true,
      },
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: ValueType.DATE_TIME,
    },
    {
      title: '操作',
      key: 'action',
      width: compatibleTableActionWidth(120),
      align: 'center',
      fixed: 'right',
      hideInFilters: true,
      hideInSettings: true,
      render: (_, { id }) => (
        <a
          onClick={() => {
            currentIdRef.current = id;
            operationLogDrawerRef.current?.open();
          }}
        >
          查看操作记录
        </a>
      ),
    },
  ];

  const headerNode = (
    <div className="sm:flex justify-between mb-3">
      <div className="self-end mb-2 sm:mb-0 pl-2">
        共 {data?.total || 0} 个区域
        {selectedRowKeys.length > 0 && (
          <>
            ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 个
            <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
              清空选择
            </a>
          </>
        )}
      </div>
      <Permission value={PermissionsMap.RegionalQuotaExport}>
        <ExportButton
          total={selectedRowKeys.length || data?.total}
          request={() =>
            exportRegionalQuota({
              columns: actionRef.current!.getShowFields(),
              ...(selectedRowKeys.length
                ? { ids: selectedRowKeys }
                : getParamsFromFilters(actionRef.current?.getFilters())),
            })
          }
        />
      </Permission>
    </div>
  );

  const handleSave = async ({ record, index }: SaveInfoType<RegionalQuotaDTO>) => {
    actionRef.current?.setDataSource((prev) => {
      prev[index] = record;

      return [...prev];
    });

    try {
      await updateRegionalQuota(record);
      message.success('修改成功');
    } catch (error) {}

    const detailData = await getRegionalQuotaDetail(record.id);

    actionRef.current?.setDataSource((prev) => {
      const idx = prev.findIndex((i) => i.id === record.id);

      if (idx > -1) {
        prev[idx] = detailData;

        return [...prev];
      }

      return prev;
    });
  };

  return (
    <PageContainer
      loading={getViewsLoading && <Skeleton paragraph={{ rows: 15 }} className="px-10" />}
    >
      <ETable
        actionRef={actionRef}
        rowKey="id"
        bordered
        sticky
        size="small"
        views={views}
        header={headerNode}
        columns={columns}
        rowSelection={{
          fixed: true,
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        request={async ({ current, pageSize }, filters) => {
          const res = await getList({
            pageNum: current,
            pageSize,
            ...getParamsFromFilters(filters),
          });

          return {
            data: res.result,
            total: res.total,
          };
        }}
        onSave={handleSave}
      />

      <OperationLogDrawer ref={operationLogDrawerRef} getId={() => currentIdRef.current} />
      <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />
    </PageContainer>
  );
};

export default RegionalQuota;
