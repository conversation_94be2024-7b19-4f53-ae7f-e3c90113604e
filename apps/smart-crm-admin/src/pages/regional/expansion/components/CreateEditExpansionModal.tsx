import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { ChooseMapPoints, ImportFileFormItem } from '@src/components';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useDistrictsStreetOpened from '@src/hooks/useDistrictsStreetOpened';
import {
  createExpansionQuota,
  downloadExpansionQuotaExcelTemplate,
  getExpansionQuotaDetail,
  importExpansionQuotaExcel,
  updateExpansionQuota,
} from '@src/services/regional/expansion';
import { useRequest } from 'ahooks';
import { App, Form, Modal, ModalProps, Tabs } from 'antd';

interface CreateEditExpansionModalProps extends ModalProps {
  getId: () => number | null;
  onSuccess: () => void;
}

const CreateEditExpansionModal = React.forwardRef<ModalRef, CreateEditExpansionModalProps>(
  ({ getId, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    const [activeKey, setActiveKey] = useState<'manual' | 'excel'>('manual');

    const id = getId();
    const isEdit = !!id;

    const { showImportPrompt } = ImportFileFormItem.usePrompt();
    const { transformDistricts, getStreetOpenedRegionIdsLoading } = useDistrictsStreetOpened({
      ready: open,
    });
    const { runAsync: create, loading: createLoading } = useRequest(createExpansionQuota, {
      manual: true,
    });
    const { runAsync: update, loading: updateLoading } = useRequest(updateExpansionQuota, {
      manual: true,
    });
    const { runAsync: importExcel, loading: importExcelLoading } = useRequest(
      importExpansionQuotaExcel,
      {
        manual: true,
      },
    );
    const { loading } = useRequest(() => getExpansionQuotaDetail(id!), {
      ready: open && isEdit,
      onSuccess: (res) => {
        form.setFieldsValue(res);
      },
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        setActiveKey('manual');
      },
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      if (isEdit) {
        await update({ id, ...values });
        message.success('修改成功');
        onSuccess();
      } else {
        if (activeKey === 'manual') {
          await create(values);
          message.success('创建成功');
          onSuccess();
        } else {
          const res = await importExcel(values);

          showImportPrompt(res, onSuccess);
        }
      }

      setOpen(false);
    };

    const manualFormNode = (
      <>
        <EditableRegion
          editable
          formItemProps={{
            label: '扩容区域',
            name: 'regionCode',
            rules: [{ required: true, message: '请选择扩容区域' }],
          }}
          fieldProps={{
            regionLevel: 4,
            transformDistricts,
            onChange: () => {
              form.setFieldsValue({ positions: undefined });
            },
          }}
        />
        <Form.Item noStyle dependencies={['regionCode']}>
          {({ getFieldValue }) =>
            getFieldValue('regionCode') && (
              <Form.Item
                label="扩容点位"
                name="positions"
                rules={[{ required: true, message: '请选择扩容点位' }]}
              >
                <ChooseMapPoints
                  beforeSelect={async ({ adcode, towncode, longitude, latitude }, beforeValues) => {
                    if (
                      beforeValues.some((i) => i.longitude === longitude && i.latitude === latitude)
                    ) {
                      message.error('不能选择相同的点位');

                      return false;
                    }

                    const regionArray: string[] = form.getFieldValue('regionCode').split('/');

                    // 是否选到了4级街道
                    const hasStreet = regionArray.length === 4;
                    const lastCode = regionArray[regionArray.length - 1];

                    if (hasStreet) {
                      if (towncode) {
                        if (!towncode.startsWith(lastCode)) {
                          message.error('请选择与扩容区域相同的区域');

                          return false;
                        }
                      } else {
                        return await new Promise<boolean>((resolve) => {
                          window.AMap.plugin(['AMap.Geocoder'], function () {
                            const geocoder = new window.AMap.Geocoder({});

                            geocoder.getAddress(
                              [longitude, latitude],
                              function (status: string, result: any) {
                                if (
                                  status === 'complete' &&
                                  result.info === 'OK' &&
                                  !result.regeocode.addressComponent.towncode.startsWith(lastCode)
                                ) {
                                  message.error('请选择与扩容区域相同的区域');

                                  resolve(false);

                                  return;
                                }

                                resolve(true);
                              },
                            );
                          });
                        });
                      }
                    } else {
                      if (!lastCode.startsWith(adcode)) {
                        message.error('请选择与扩容区域相同的区域');

                        return false;
                      }
                    }

                    return true;
                  }}
                />
              </Form.Item>
            )
          }
        </Form.Item>
      </>
    );

    return (
      <Modal
        title={isEdit ? '编辑扩容名额' : '新增扩容名额'}
        open={open}
        destroyOnHidden
        loading={loading}
        confirmLoading={
          createLoading || updateLoading || importExcelLoading || getStreetOpenedRegionIdsLoading
        }
        modalRender={(node) => (
          <Form form={form} clearOnDestroy onFinish={handleSave}>
            {node}
          </Form>
        )}
        onCancel={() => setOpen(false)}
        onOk={form.submit}
        {...props}
      >
        {isEdit ? (
          manualFormNode
        ) : (
          <Tabs
            activeKey={activeKey}
            destroyOnHidden
            onChange={(key) => {
              setActiveKey(key as typeof activeKey);
            }}
            items={[
              {
                label: '手动新建',
                key: 'manual',
                children: manualFormNode,
              },
              {
                label: '通过 EXCEL 导入',
                key: 'excel',
                children: (
                  <ImportFileFormItem downloadTemplate={downloadExpansionQuotaExcelTemplate} />
                ),
              },
            ]}
          />
        )}
      </Modal>
    );
  },
);

export default CreateEditExpansionModal;
