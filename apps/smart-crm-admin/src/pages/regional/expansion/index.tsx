import { useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ModalRef } from '@src/common/interface';
import { ExportButton, ProTable, RegionCascader, TableActions } from '@src/components';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useAllUsers from '@src/hooks/useAllUsers';
import FormalDetailDrawer from '@src/pages/contract/formal/components/FormalDetailDrawer';
import IntentionDetailDrawer from '@src/pages/contract/intention/components/IntentionDetailDrawer';
import {
  batchDeleteExpansionQuota,
  deleteExpansionQuota,
  exportExpansionQuota,
  getExpansionQuotaList,
} from '@src/services/regional/expansion';
import {
  ExpansionQuotaDTO,
  ExpansionQuotaUseStatusEnum,
  ExportExpansionQuotaReq,
} from '@src/services/regional/expansion/type';
import { compatibleTableActionWidth } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Dropdown, MenuProps, Tag, Typography } from 'antd';
import CreateEditExpansionModal from './components/CreateEditExpansionModal';

const Expansion = () => {
  const { modal, message } = App.useApp();
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const actionRef = useRef<ActionType>(null);
  const currentIdRef = useRef<number | null>(null);
  const createEditModalRef = useRef<ModalRef>(null);
  const filterParamsRef = useRef<ExportExpansionQuotaReq>({});
  const contractIdRef = useRef<number | null>();
  const intentionDetailDrawerRef = useRef<ModalRef>(null);
  const formalDetailDrawerRef = useRef<ModalRef>(null);

  const { data: users } = useAllUsers();
  const { data, runAsync: getList } = useRequest(getExpansionQuotaList, { manual: true });

  const columns: ProColumns<ExpansionQuotaDTO>[] = [
    {
      title: '扩容区域',
      dataIndex: 'regionCode',
      fixed: 'left',
      ellipsis: true,
      renderText: (value) => <EditableRegion value={value} fieldProps={{ regionLevel: 4 }} />,
      renderFormItem: () => (
        <RegionCascader
          placeholder="请选择"
          multiple
          unlimited={{ options: true, onChange: true }}
        />
      ),
      search: {
        transform: (value) => ({
          regionCode: value.map((item: number[]) => item.join('/')).join(','),
        }),
      },
    },
    {
      title: '扩容点位',
      dataIndex: 'positions',
      width: 250,
      onCell: () => ({
        className: 'flex justify-between',
      }),
      search: {
        transform: (expansionPositionName) => ({
          expansionPositionName,
        }),
      },
      render: (_, { positions }) => (
        <>
          <Typography.Text
            ellipsis={{
              tooltip: {
                classNames: {
                  root: 'whitespace-pre-wrap',
                },
              },
            }}
          >
            {positions?.map(
              (i, index) =>
                `${index + 1}. ${i.name}（经度：${i.longitude}；纬度：${i.latitude}）\n`,
            )}
          </Typography.Text>
          <Tag>{positions?.length} 个</Tag>
        </>
      ),
    },
    {
      title: '扩容使用状态',
      dataIndex: 'useStatus',
      valueEnum: {
        [ExpansionQuotaUseStatusEnum.NOT_USE]: {
          text: '未使用',
          status: 'default',
        },
        [ExpansionQuotaUseStatusEnum.USE]: {
          text: '使用',
          status: 'processing',
        },
        [ExpansionQuotaUseStatusEnum.DISABLED]: {
          text: '作废',
          status: 'error',
        },
        [ExpansionQuotaUseStatusEnum.FORMALIZED]: {
          text: '转正',
          status: 'success',
        },
      },
    },
    {
      title: '意向合同编号',
      dataIndex: 'intentionContractCode',
      renderText: (value, { intentionContractId }) => (
        <a
          onClick={() => {
            contractIdRef.current = intentionContractId;
            intentionDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '正式合同编号',
      dataIndex: 'formalContractCode',
      renderText: (value, { formalContractId }) => (
        <a
          onClick={() => {
            contractIdRef.current = formalContractId;
            formalDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '更新人',
      dataIndex: 'updateUserId',
      search: false,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      search: false,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      search: false,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      search: false,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      search: false,
      width: compatibleTableActionWidth(100),
      render: (_, { id, useStatus }) => (
        <TableActions
          shortcuts={[
            {
              label: '编辑',
              show: useStatus === ExpansionQuotaUseStatusEnum.NOT_USE,
              onClick: () => {
                currentIdRef.current = id;
                createEditModalRef.current?.open();
              },
            },
            {
              label: '删除',
              danger: true,
              show: useStatus === ExpansionQuotaUseStatusEnum.NOT_USE,
              onClick: () =>
                modal.confirm({
                  title: '删除',
                  content: '确定要删除该扩容名额吗？',
                  onOk: async () => {
                    await deleteExpansionQuota(id);
                    message.success('删除成功');
                    actionRef.current?.reload();
                    setSelectedRowKeys((prev) => prev.filter((i) => i !== id));
                  },
                }),
            },
          ]}
        />
      ),
    },
  ];

  const batchMenuItems: MenuProps['items'] = [
    {
      label: '删除',
      key: 'delete',
      danger: true,
      onClick: () =>
        modal.confirm({
          title: '删除',
          content: `确定要删除选中的 ${selectedRowKeys.length} 个扩容名额吗？`,
          onOk: async () => {
            await batchDeleteExpansionQuota(selectedRowKeys);
            message.success('删除成功');
            setSelectedRowKeys([]);
            actionRef.current?.reload();
          },
        }),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        cardProps={false}
        bordered
        rowKey="id"
        search={{ labelWidth: 'auto' }}
        sticky
        size="small"
        columnEmptyText=""
        columns={columns}
        scroll={{ x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0) }}
        options={false}
        tableAlertRender={false}
        toolbar={{
          subTitle: (
            <span className="text-black">
              共 {data?.total || 0} 条记录
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 条
                  <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            batchMenuItems.length > 0 && (
              <Dropdown
                disabled={!selectedRowKeys.length}
                menu={{
                  items: batchMenuItems,
                }}
              >
                <Button type="text" className="!flex items-center">
                  批量操作 <CaretDownOutlined />
                </Button>
              </Dropdown>
            ),
            <Button
              type="text"
              onClick={() => {
                currentIdRef.current = null;
                createEditModalRef.current?.open();
              }}
            >
              新建
            </Button>,
            <ExportButton
              total={selectedRowKeys.length || data?.total}
              request={() =>
                exportExpansionQuota(
                  selectedRowKeys.length ? { ids: selectedRowKeys } : filterParamsRef.current,
                )
              }
            />,
          ],
        }}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        rowSelection={{
          fixed: true,
          showSort: true,
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        request={async ({ current, pageSize, ...params }) => {
          filterParamsRef.current = params as ExportExpansionQuotaReq;

          const res = await getList({ pageNum: current, pageSize, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <CreateEditExpansionModal
        ref={createEditModalRef}
        getId={() => currentIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
      <IntentionDetailDrawer ref={intentionDetailDrawerRef} getId={() => contractIdRef.current} />
      <FormalDetailDrawer ref={formalDetailDrawerRef} getId={() => contractIdRef.current} />
    </PageContainer>
  );
};

export default Expansion;
