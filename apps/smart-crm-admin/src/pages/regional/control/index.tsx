import { useMemo, useRef } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ProTable } from '@src/components';
import useDistricts from '@src/hooks/useDistricts';
import { getAreaList } from '@src/services/regional/control';
import { AreaListItemType } from '@src/services/regional/control/type';
import { useRequest } from 'ahooks';
import { Switch } from 'antd';
import OpenStreetModal from './components/OpenStreetModal';

const RegionControl = () => {
  const { data: districts } = useDistricts();
  const actionRef = useRef<ActionType>(null);
  const currentRecordRef = useRef<AreaListItemType | null>(null);
  const openStreetModalRef = useRef<ModalRef>(null);

  const { runAsync: getList } = useRequest(getAreaList, { manual: true });

  const provinceValueEnum = useMemo(() => {
    const provinceMap = new Map();

    districts?.forEach((i) => {
      if (i.deep === 0) {
        provinceMap.set(i.id, i.name);
      }
    });

    return provinceMap;
  }, [districts]);

  const columns: ProColumns<AreaListItemType>[] = [
    {
      title: (_, type) => (type === 'form' ? '省份' : '省市区'),
      dataIndex: 'districtName',
      valueEnum: provinceValueEnum,
      onCell: (record) => ({
        className: record.deep === 0 ? 'text-base' : '',
      }),
      fieldProps: {
        showSearch: true,
      },
      search: {
        transform: (districtId) => ({
          districtId,
        }),
      },
    },
    {
      title: '是否按乡镇显示名额',
      dataIndex: 'streetOpened',
      valueType: 'select',
      fieldProps: {
        options: yesOrNoOptions,
      },
      renderText: (value, record) =>
        record.deep === 2 ? (
          <Switch
            size="small"
            value={value}
            onChange={() => {
              currentRecordRef.current = record;
              openStreetModalRef.current?.open();
            }}
          />
        ) : (
          '-'
        ),
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: 'dateTime',
      search: false,
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        options={false}
        size="small"
        sticky
        rowKey="id"
        cardProps={false}
        bordered
        pagination={false}
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        expandable={{ childrenColumnName: 'childrenList' }}
        request={async (params) => {
          const res = await getList(params as Parameters<typeof getList>[0]);

          return {
            data: res,
          };
        }}
      />

      <OpenStreetModal
        ref={openStreetModalRef}
        getRecord={() => currentRecordRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
    </PageContainer>
  );
};

export default RegionControl;
