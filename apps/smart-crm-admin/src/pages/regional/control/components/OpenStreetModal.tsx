import React, { useEffect, useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { ETable } from '@src/components';
import { ETableColumn, ValueType } from '@src/components/ETable';
import useDistricts from '@src/hooks/useDistricts';
import { updateDistrictStreetOpened } from '@src/services/regional/control';
import { AreaListItemType } from '@src/services/regional/control/type';
import { JoinRegionEnum, RegionalQuotaOpenedEnum } from '@src/services/regional/quota/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, FormItemProps, Modal, ModalProps } from 'antd';

interface OpenStreetModalProps extends ModalProps {
  getRecord: () => AreaListItemType | null;
  onSuccess: () => void;
}

const formItemProps: FormItemProps = {
  rules: [{ required: true, message: '不能为空' }],
};

const defaultNumObj: {
  joinRegion: JoinRegionEnum;
  opened: RegionalQuotaOpenedEnum;
} = {
  joinRegion: JoinRegionEnum['邀请加盟区(T)'],
  opened: RegionalQuotaOpenedEnum.对外开放,
};

type DataSourceItem = {
  name: string;
  districtCode: string;
} & typeof defaultNumObj;

const OpenStreetModal = React.forwardRef<ModalRef, OpenStreetModalProps>(
  ({ getRecord, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [dataSource, setDataSource] = useState<DataSourceItem[]>([]);

    const { data: districts, loading: getDistrictsLoading } = useDistricts({
      ready: open,
      streets: true,
    });

    const record = getRecord();

    useEffect(() => {
      if (open) {
        // 开启
        if (record?.streetOpened) {
          setDataSource([
            { name: record.districtName, districtCode: record.districtCode, ...defaultNumObj },
          ]);
        } else {
          if (districts) {
            setDataSource(
              districts
                .filter((i) => i.parentId === record?.districtId)
                .map((i) => ({ name: i.name, districtCode: i.code, ...defaultNumObj })),
            );
          }
        }
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [open, districts]);

    const { runAsync: update, loading: updateLoading } = useRequest(updateDistrictStreetOpened, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const columns: ETableColumn<DataSourceItem>[] = [
      {
        title: '行政区名称',
        dataIndex: 'name',
      },
      {
        title: 'T/M区域',
        dataIndex: 'joinRegion',
        valueType: ValueType.SINGLE,
        editable: true,
        fieldProps: {
          options: enum2Options(JoinRegionEnum),
          allowClear: false,
        },
        formItemProps,
      },
      {
        title: '是否对外开放',
        dataIndex: 'opened',
        width: 150,
        valueType: ValueType.SINGLE,
        editable: true,
        fieldProps: {
          options: enum2Options(RegionalQuotaOpenedEnum),
          allowClear: false,
        },
        formItemProps,
      },
    ];

    const handleSave = async () => {
      await update({
        id: record!.id,
        streetOpened: !record!.streetOpened,
        franchiseAreaQuotas: dataSource,
      });
      message.success('操作成功');
      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        title={
          record?.streetOpened ? '关闭乡镇显示名额' : `开启乡镇显示名额（${record?.districtName}）`
        }
        open={open}
        width={800}
        destroyOnHidden
        loading={getDistrictsLoading}
        confirmLoading={updateLoading}
        styles={{
          body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
        }}
        onOk={handleSave}
        onCancel={() => setOpen(false)}
        {...props}
      >
        <ETable
          options={false}
          sticky
          size="small"
          rowKey="districtCode"
          pagination={false}
          dataSource={dataSource}
          scroll={{ x: 'max-content' }}
          onSave={(info) => {
            setDataSource((prev) => {
              prev[info.index] = info.record;

              return [...prev];
            });
          }}
          columns={columns}
        />
      </Modal>
    );
  },
);

export default OpenStreetModal;
