import { useEffect, useRef, useState } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { contractAuditStatusOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ExportButton, ProTable } from '@src/components';
import useAllUsers from '@src/hooks/useAllUsers';
import { exportPaymentList, getPaymentList } from '@src/services/payment';
import {
  ContractTypeEnum,
  ExportPaymentListReq,
  PaymentDTO,
  PaymentMannerEnum,
} from '@src/services/payment/type';
import { enum2ValueEnum, optionsToValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { Typography } from 'antd';
import { useSearchParams } from 'react-router-dom';
import PaymentDetailDrawer from './components/PaymentDetailDrawer';
import ChangeLegalPersonDetailDrawer from '../contract/change-legal-person/components/ChangeLegalPersonDetailDrawer';
import FormalDetailDrawer from '../contract/formal/components/FormalDetailDrawer';
import IntentionDetailDrawer from '../contract/intention/components/IntentionDetailDrawer';
import RelocationFormalDetailDrawer from '../contract/relocation-formal/components/RelocationFormalDetailDrawer';
import RelocationIntentionDetailDrawer from '../contract/relocation-intention/components/RelocationIntentionDetailDrawer';
import RenovationDetailDrawer from '../contract/renovation/components/RenovationDetailDrawer';
import CustomerDetailDrawer from '../customers/components/CustomerDetailDrawer';

const Payment = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const actionRef = useRef<ActionType>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const customerIdRef = useRef<number | null>(null);
  const intentionDetailDrawerRef = useRef<ModalRef>(null);
  const formalDetailDrawerRef = useRef<ModalRef>(null);
  const relocationIntentionDetailDrawerRef = useRef<ModalRef>(null);
  const relocationFormalDetailDrawerRef = useRef<ModalRef>(null);
  const renovationDetailDrawerRef = useRef<ModalRef>(null);
  const changeLegalPersonDetailDrawerRef = useRef<ModalRef>(null);
  const contractIdRef = useRef<number | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const filterParamsRef = useRef<ExportPaymentListReq>({});
  const paymentDetailDrawerRef = useRef<ModalRef>(null);
  const paymentIdRef = useRef<number | null>(null);

  const { data: users } = useAllUsers();
  const { data, runAsync: getList } = useRequest(getPaymentList, { manual: true });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      paymentIdRef.current = Number(id);
      paymentDetailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleOpenContract = (contractId: number, contractType: ContractTypeEnum) => {
    contractIdRef.current = contractId;

    const drawerRefMap = {
      [ContractTypeEnum.意向合同]: intentionDetailDrawerRef,
      [ContractTypeEnum.正式合同]: formalDetailDrawerRef,
      [ContractTypeEnum.搬迁意向合同]: relocationIntentionDetailDrawerRef,
      [ContractTypeEnum.搬迁正式合同]: relocationFormalDetailDrawerRef,
      [ContractTypeEnum.门店重装合同]: renovationDetailDrawerRef,
      [ContractTypeEnum.变更法人合同]: changeLegalPersonDetailDrawerRef,
    };

    drawerRefMap[contractType].current?.open();
  };

  const columns: ProColumns<PaymentDTO>[] = [
    {
      title: '打款编号',
      dataIndex: 'code',
      fixed: 'left',
      render: (value, { id }) => (
        <a
          onClick={() => {
            paymentIdRef.current = id;
            paymentDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      search: false,
      renderText: (value, { customerId }) => (
        <Typography.Text
          style={{ color: 'var(--ant-color-link)', cursor: 'pointer' }}
          ellipsis={{ tooltip: true }}
          onClick={() => {
            customerIdRef.current = customerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </Typography.Text>
      ),
    },
    {
      title: '合同类型',
      dataIndex: 'contractType',
      search: false,
      valueEnum: enum2ValueEnum(ContractTypeEnum),
    },
    {
      title: '合同编号',
      dataIndex: 'contracts',
      search: {
        transform: (contractCode) => ({
          contractCode,
        }),
      },
      render: (_, { contracts, contractType }) =>
        contracts?.map((i) => (
          <a key={i.id} onClick={() => handleOpenContract(i.id, contractType)}>
            {i.code}
          </a>
        )),
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      valueEnum: optionsToValueEnum(contractAuditStatusOptions),
      fieldProps: {
        mode: 'multiple',
      },
      search: {
        transform: (auditStatusList) => ({
          auditStatusList,
        }),
      },
    },
    {
      title: '打款时间',
      dataIndex: 'paymentTime',
      search: false,
      valueType: 'dateTime',
    },
    {
      title: '打款金额',
      dataIndex: 'paymentAmount',
      search: false,
    },
    {
      title: '打款方式',
      dataIndex: 'paymentManner',
      search: false,
      valueEnum: enum2ValueEnum(PaymentMannerEnum),
    },
    {
      title: '收款账户',
      dataIndex: 'receivingAccount',
      search: false,
      width: 220,
    },
    {
      title: '收款账号',
      dataIndex: 'receivingNumber',
      search: false,
      width: 200,
    },
    {
      title: '开户行',
      dataIndex: 'openBank',
      search: false,
      width: 250,
    },
    {
      title: '记账流水号',
      dataIndex: 'accountSerialNumber',
      search: false,
      ellipsis: true,
    },
    {
      title: '打款方名称',
      dataIndex: 'payerName',
      search: false,
      width: 220,
    },
    {
      title: '打款方账号',
      dataIndex: 'payerAccount',
      search: false,
      width: 200,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      search: false,
      ellipsis: true,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      search: false,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      search: {
        transform: (value) => ({
          createBeginTime: value[0],
          createEndTime: value[1],
        }),
      },
      valueType: (_, type) => (type === 'table' ? 'dateTime' : 'dateTimeRange'),
    },
    {
      title: '更新人',
      dataIndex: 'updateUserId',
      search: false,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      search: false,
      valueType: 'dateTime',
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        sticky
        cardProps={false}
        rowKey="id"
        bordered
        size="small"
        options={false}
        columns={columns}
        columnEmptyText=""
        tableAlertRender={false}
        rowSelection={{
          showSort: true,
          fixed: true,
          selectedRowKeys,
          preserveSelectedRowKeys: true,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        scroll={{ x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0) }}
        toolbar={{
          subTitle: (
            <span className="text-black">
              共 {data?.total || 0} 条打款
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 条
                  <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            <ExportButton
              total={selectedRowKeys.length || data?.total}
              request={() =>
                exportPaymentList(
                  selectedRowKeys.length > 0 ? { ids: selectedRowKeys } : filterParamsRef.current,
                )
              }
            />,
          ],
        }}
        request={async ({ current, pageSize, ...params }) => {
          filterParamsRef.current = params as ExportPaymentListReq;

          const res = await getList({ pageNum: current, pageSize, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <PaymentDetailDrawer
        ref={paymentDetailDrawerRef}
        getId={() => paymentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
        onDelete={() => actionRef.current?.reload()}
      />
      <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />
      <IntentionDetailDrawer ref={intentionDetailDrawerRef} getId={() => contractIdRef.current} />
      <FormalDetailDrawer ref={formalDetailDrawerRef} getId={() => contractIdRef.current} />
      <RelocationIntentionDetailDrawer
        ref={relocationFormalDetailDrawerRef}
        getId={() => contractIdRef.current}
      />
      <RelocationFormalDetailDrawer
        ref={relocationIntentionDetailDrawerRef}
        getId={() => contractIdRef.current}
      />
      <RenovationDetailDrawer ref={renovationDetailDrawerRef} getId={() => contractIdRef.current} />
      <ChangeLegalPersonDetailDrawer
        ref={changeLegalPersonDetailDrawerRef}
        getId={() => contractIdRef.current}
      />
    </PageContainer>
  );
};

export default Payment;
