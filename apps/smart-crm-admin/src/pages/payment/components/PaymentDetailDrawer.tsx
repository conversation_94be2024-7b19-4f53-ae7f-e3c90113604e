import React, { useRef, useState } from 'react';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import FormalDetailDrawer from '@src/pages/contract/formal/components/FormalDetailDrawer';
import IntentionDetailDrawer from '@src/pages/contract/intention/components/IntentionDetailDrawer';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import { getPaymentDetail } from '@src/services/payment';
import { ContractTypeEnum, PaymentDTO, PaymentMannerEnum } from '@src/services/payment/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { Card, Drawer, DrawerProps, Tabs } from 'antd';

interface PaymentDetailDrawerProps extends DrawerProps {
  getId: () => number | null;
  onUpdate?: () => void;
  onDelete?: () => void;
  onTodoSuccess?: () => void;
}

const PaymentDetailDrawer = React.forwardRef<ModalRef, PaymentDetailDrawerProps>(
  ({ getId, onUpdate, onDelete, onTodoSuccess, ...drawerProps }, ref) => {
    const [open, setOpen] = useState(false);
    const customerDetailDrawerRef = useRef<ModalRef>(null);
    const contractDetailDrawerRef = useRef<ModalRef>(null);
    const [contractId, setContractId] = useState<number | null>(null);
    const id = getId()!;

    const { data: users } = useAllUsers();
    const { data, loading } = useRequest(() => getPaymentDetail(id), { ready: open });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const baseInfoItems: DescriptionFieldType<PaymentDTO>[] = [
      {
        label: '打款编号',
        field: 'code',
      },
      {
        label: '合同类型',
        field: 'contractType',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(ContractTypeEnum),
        },
      },
      {
        label: '合同编号',
        field: 'contracts',
        children: (
          <div className="flex flex-col">
            {data?.contracts?.map((i) => (
              <a
                key={i.id}
                onClick={() => {
                  setContractId(i.id);
                  contractDetailDrawerRef.current?.open();
                }}
              >
                {i.code}
              </a>
            ))}
          </div>
        ),
      },
      {
        label: '打款时间',
        field: 'paymentTime',
        valueType: ValueType.DATE_TIME,
      },
      {
        label: '打款金额',
        field: 'paymentAmount',
      },
      {
        label: '打款方式',
        field: 'paymentManner',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(PaymentMannerEnum),
        },
      },
      {
        label: '收款账户',
        field: 'receivingAccount',
      },
      {
        label: '收款账号',
        field: 'receivingNumber',
      },
      {
        label: '开户行',
        field: 'openBank',
      },
      {
        label: '打款凭证',
        field: 'attachments',
        valueType: ValueType.ATTACHMENT,
      },
      {
        label: '记账流水号',
        field: 'accountSerialNumber',
      },
      {
        label: '打款方名称',
        field: 'payerName',
      },
      {
        label: '打款方账号',
        field: 'payerAccount',
      },
      {
        label: '回单截图',
        field: 'receiptScreenshotAttachment',
        valueType: ValueType.ATTACHMENT,
      },
      {
        label: '备注',
        field: 'remark',
      },
    ];

    return (
      <Drawer
        title="打款详情"
        open={open}
        width={800}
        destroyOnHidden
        push={false}
        onClose={() => setOpen(false)}
        {...drawerProps}
      >
        <Card loading={loading}>
          <div className="flex justify-between">
            <div className="flex flex-col gap-2">
              <p>
                客户名称：
                <a onClick={() => customerDetailDrawerRef.current?.open()}>{data?.customerName}</a>
              </p>
              <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
              <p>创建时间：{data?.createTime}</p>
              <p>更新人：{users?.find((i) => i.id === data?.updateUserId)?.name}</p>
              <p>更新时间：{data?.updateTime}</p>
            </div>
          </div>
        </Card>
        <Card className="mt-5" loading={loading}>
          <Tabs
            className="-mt-5"
            items={[
              {
                label: '详细资料',
                key: 'info',
                children: renderDescriptions(baseInfoItems, data),
              },
              // {
              //   label: '操作记录',
              //   key: 'record',
              //   children: <OperationLog id={id} businessType="PAYMENT" />,
              // },
            ]}
          />
        </Card>
        <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => data?.customerId} />
        {data?.contractType === ContractTypeEnum.意向合同 ? (
          <IntentionDetailDrawer ref={contractDetailDrawerRef} getId={() => contractId} />
        ) : (
          <FormalDetailDrawer ref={contractDetailDrawerRef} getId={() => contractId} />
        )}
      </Drawer>
    );
  },
);

export default PaymentDetailDrawer;
