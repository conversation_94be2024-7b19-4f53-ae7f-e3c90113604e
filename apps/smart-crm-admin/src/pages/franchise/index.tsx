import { useRef, useState } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ModalRef } from '@src/common/interface';
import { ExportButton, ProTable } from '@src/components';
import { Permission, PermissionsMap } from '@src/permissions';
import { exportFranchise, getFranchiseList } from '@src/services/customers';
import {
  ExportCustomerReq,
  FranchiseDTO,
  HighestEducationLevelEnum,
} from '@src/services/customers/type';
import { enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { Popover, Space } from 'antd';
import CustomerDetailDrawer from '../customers/components/CustomerDetailDrawer';
import ShopDetailDrawer from '../shop/list/components/ShopDetailDrawer';

const Customers = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const filterParamsRef = useRef<ExportCustomerReq>({});
  const actionRef = useRef<ActionType>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const customerIdRef = useRef<number | null>(null);
  const shopDetailDrawerRef = useRef<ModalRef>(null);

  const { data, runAsync: getList } = useRequest(getFranchiseList, { manual: true });

  const columns: ProColumns<FranchiseDTO>[] = [
    {
      title: '姓名',
      dataIndex: 'name',
      fixed: 'left',
      width: 120,
      render: (_v, { name, id }) => {
        return (
          <a
            onClick={() => {
              customerIdRef.current = id;
              customerDetailDrawerRef.current?.open();
            }}
          >
            {name}
          </a>
        );
      },
    },
    {
      title: '客户编号',
      dataIndex: 'code',
      width: 160,
      search: false,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      hideInTable: true,
    },
    {
      title: '身份证号',
      dataIndex: 'identityCard',
      hideInTable: true,
    },
    {
      title: '门店编号',
      dataIndex: 'shopId',
      hideInTable: true,
    },
    {
      title: '最高学历',
      dataIndex: 'educationLevel',
      search: false,
      width: 120,
      valueEnum: enum2ValueEnum(HighestEducationLevelEnum),
    },
    {
      title: '年龄',
      dataIndex: 'age',
      search: false,
      width: 120,
    },
    {
      title: '地址',
      dataIndex: 'address',
      width: 160,
      search: false,
    },
    {
      title: '名下门店',
      dataIndex: 'shops',
      search: false,
      width: 160,
      render: (_v, { shops }) => {
        return (
          <Space wrap>
            {shops
              ?.filter((_e, i) => i < 3)
              ?.map((v) => (
                <div key={`list-${v}`}>
                  <a
                    onClick={() => {
                      customerIdRef.current = v as unknown as number;
                      shopDetailDrawerRef.current?.open();
                    }}
                  >
                    {v}
                  </a>
                </div>
              ))}
            {shops.length > 3 && (
              <Popover
                placement="bottomLeft"
                content={
                  <div className="max-w-[200px]">
                    <Space wrap>
                      {shops
                        ?.filter((_e, i) => i >= 3)
                        ?.map((v) => (
                          <div key={`popover-${v}`}>
                            <a
                              onClick={() => {
                                customerIdRef.current = v as unknown as number;
                                shopDetailDrawerRef.current?.open();
                              }}
                            >
                              {v}
                            </a>
                          </div>
                        ))}
                    </Space>
                  </div>
                }
              >
                <div className="text-[#1677ff] cursor-pointer">+{shops.length - 3}</div>
              </Popover>
            )}
          </Space>
        );
      },
    },
    {
      title: '名下门店数',
      dataIndex: 'shopCount',
      search: false,
      width: 120,
    },
    {
      title: '加盟商等级',
      dataIndex: 'level',
      search: false,
      width: 120,
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        cardProps={false}
        bordered
        rowKey="id"
        sticky
        size="small"
        columnEmptyText=""
        columns={columns}
        scroll={{ x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0) }}
        options={false}
        tableAlertRender={false}
        toolbar={{
          subTitle: (
            <span className="text-black">
              共 {data?.total || 0} 个加盟商
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 个
                  <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            <Permission value={PermissionsMap.FranchiseExport}>
              <ExportButton
                total={selectedRowKeys.length || data?.total}
                request={() =>
                  exportFranchise(
                    selectedRowKeys.length ? { ids: selectedRowKeys } : filterParamsRef.current,
                  )
                }
              />
            </Permission>,
          ],
        }}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        rowSelection={{
          fixed: true,
          showSort: true,
          columnWidth: 60,
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        request={async ({ current, pageSize, ...params }) => {
          filterParamsRef.current = params as ExportCustomerReq;

          const res = await getList({ pageNum: current, pageSize, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <CustomerDetailDrawer
        ref={customerDetailDrawerRef}
        isFranchise={true}
        getId={() => customerIdRef.current}
        onDelete={() => {
          setSelectedRowKeys((prev) => prev.filter((i) => i !== customerIdRef.current));
          actionRef.current?.reload();
        }}
        onUpdate={() => actionRef.current?.reload()}
      />

      <ShopDetailDrawer
        ref={shopDetailDrawerRef}
        getId={() => customerIdRef.current as unknown as string}
      />
    </PageContainer>
  );
};

export default Customers;
