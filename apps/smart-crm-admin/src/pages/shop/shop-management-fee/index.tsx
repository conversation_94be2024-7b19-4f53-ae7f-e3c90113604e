import { useRef } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ModalRef } from '@src/common/interface';
import { ProTable, TableActions } from '@src/components';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { queryShopManagementFeePageList } from '@src/services/shop/shop-management-fee';
import { RateTypeEnum, ShopManagementFeeDTO } from '@src/services/shop/shop-management-fee/type';
import { compatibleTableActionWidth, enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { Button } from 'antd';
import CreateShopManagementFeeModal from './components/CreateShopManagementFeeModal';
import VersionRecordModal from './components/VersionRecordModal';
import ShopDetailDrawer from '../list/components/ShopDetailDrawer';

const ShopManagementFee = () => {
  const actionRef = useRef<ActionType>(null);
  const createShopManagementFeeModalRef = useRef<ModalRef>(null);
  const versionRecordModalRef = useRef<ModalRef>(null);
  const shopDetailDrawerRef = useRef<ModalRef>(null);
  const shopNoRef = useRef<string | null>(null);
  const checkPermission = usePermission();

  const { data, runAsync: queryList } = useRequest(queryShopManagementFeePageList, {
    manual: true,
  });

  const columns: ProColumns<ShopManagementFeeDTO>[] = [
    {
      title: '门店编号',
      dataIndex: 'shopNo',
      fixed: 'left',
      renderText: (value) => (
        <a
          onClick={() => {
            shopNoRef.current = value;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '管理费版本',
      dataIndex: 'version',
      search: false,
    },
    {
      title: '生效时间',
      dataIndex: 'effectiveDate',
      search: false,
    },
    {
      title: '生效周期',
      dataIndex: 'effectiveRange',
      search: false,
      width: 200,
      render: (_, { startDate, endDate }) => `${startDate} ~ ${endDate}`,
    },
    {
      title: '费率类型',
      dataIndex: 'rateType',
      search: false,
      valueEnum: enum2ValueEnum(RateTypeEnum),
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      fixed: 'right',
      search: false,
      width: compatibleTableActionWidth(100),
      render: (_, { shopNo }) => (
        <TableActions
          shortcuts={[
            {
              label: '版本记录',
              show: checkPermission(PermissionsMap.ShopManagementFeeDetail),
              onClick: () => {
                shopNoRef.current = shopNo;
                versionRecordModalRef.current?.open();
              },
            },
          ]}
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        rowKey="id"
        actionRef={actionRef}
        sticky
        cardProps={false}
        bordered
        columns={columns}
        toolbar={{
          subTitle: <span className="text-black">共 {data?.total || 0} 条信息</span>,
          actions: [
            <Permission value={PermissionsMap.ShopManagementFeeCreate}>
              <Button type="text" onClick={() => createShopManagementFeeModalRef.current?.open()}>
                新建
              </Button>
            </Permission>,
          ],
        }}
        scroll={{ x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0) }}
        request={async ({ current, ...params }) => {
          const { result, total } = await queryList({ pageNum: current, ...params });

          return {
            data: result,
            total,
          };
        }}
      />

      <CreateShopManagementFeeModal
        ref={createShopManagementFeeModalRef}
        onSuccess={() => actionRef.current?.reload()}
      />
      <VersionRecordModal
        ref={versionRecordModalRef}
        getShopNo={() => shopNoRef.current}
        onUpdate={() => actionRef.current?.reload()}
      />
      <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => shopNoRef.current} />
    </PageContainer>
  );
};

export default ShopManagementFee;
