import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { TableActions } from '@src/components';
import {
  deleteShopManagementFeeVersion,
  disableShopManagementFeeVersion,
  enableShopManagementFeeVersion,
  queryShopManagementFeeDetail,
} from '@src/services/shop/shop-management-fee';
import {
  RateTypeEnum,
  ShopManagementFeeChangeTypeEnum,
  ShopManagementFeeVersionRecordDTO,
  ShopManagementFeeVersionRecordStatusEnum,
} from '@src/services/shop/shop-management-fee/type';
import { compatibleTableActionWidth, enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Modal, ModalProps, Table } from 'antd';
import { ColumnsType } from 'antd/lib/table';

interface VersionRecordModalProps extends ModalProps {
  getShopNo: () => string | null;
  onUpdate: () => void;
}

const VersionRecordModal = React.forwardRef<ModalRef, VersionRecordModalProps>(
  ({ getShopNo, onUpdate, ...props }, ref) => {
    const { modal, message } = App.useApp();
    const [open, setOpen] = useState(false);

    const shopNo = getShopNo();

    const { data, mutate, loading, refresh } = useRequest(
      () => queryShopManagementFeeDetail(shopNo!),
      {
        ready: open,
        onBefore: () => mutate(undefined),
      },
    );

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const columns: ColumnsType<ShopManagementFeeVersionRecordDTO> = [
      {
        title: '记录编号',
        dataIndex: 'code',
      },
      {
        title: '变更类型',
        dataIndex: 'changeType',
        render: (value) => enum2ValueEnum(ShopManagementFeeChangeTypeEnum)[value],
      },
      {
        title: '管理费版本',
        dataIndex: 'version',
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
      },
      {
        title: '实际生效时间',
        dataIndex: 'effectiveDate',
      },
      {
        title: '实际失效时间',
        dataIndex: 'invalidDate',
      },
      {
        title: '生效周期',
        dataIndex: 'effectiveRange',
        render: (_, { startDate, endDate }) => `${startDate} ~ ${endDate}`,
      },
      {
        title: '费率',
        dataIndex: 'rateType',
        render: (value) => enum2ValueEnum(RateTypeEnum)[value],
      },
      {
        title: '状态',
        dataIndex: 'status',
        render: (value) => enum2ValueEnum(ShopManagementFeeVersionRecordStatusEnum)[value],
      },
      {
        title: '操作',
        align: 'center',
        width: compatibleTableActionWidth(150),
        fixed: 'right',
        render: (_, { id, status }) => (
          <TableActions
            shortcuts={[
              {
                label: '立即生效',
                show: status === ShopManagementFeeVersionRecordStatusEnum.待生效,
                onClick: () =>
                  modal.confirm({
                    title: '立即生效',
                    content: '确定要立即生效失效吗？',
                    onOk: async () => {
                      await enableShopManagementFeeVersion(id);
                      message.success('生效成功');
                      refresh();
                      onUpdate();
                    },
                  }),
              },
              {
                label: '立即失效',
                show: status === ShopManagementFeeVersionRecordStatusEnum.生效中,
                onClick: () =>
                  modal.confirm({
                    title: '立即失效',
                    content: '确定要执行立即失效吗？',
                    onOk: async () => {
                      await disableShopManagementFeeVersion(id);
                      message.success('失效成功');
                      refresh();
                      onUpdate();
                    },
                  }),
              },
              {
                label: '删除',
                danger: true,
                show: status !== ShopManagementFeeVersionRecordStatusEnum.生效中,
                onClick: () =>
                  modal.confirm({
                    title: '删除',
                    content: '确定要删除吗？',
                    onOk: async () => {
                      await deleteShopManagementFeeVersion(id);
                      message.success('删除成功');

                      // 只有一条，删除后关闭弹窗
                      if (data?.length === 1) {
                        setOpen(false);
                      } else {
                        refresh();
                      }

                      onUpdate();
                    },
                  }),
              },
            ]}
          />
        ),
      },
    ];

    return (
      <Modal
        title="版本记录"
        open={open}
        footer={null}
        width={1400}
        loading={loading}
        destroyOnHidden
        onCancel={() => setOpen(false)}
        {...props}
      >
        <Table
          rowKey="id"
          bordered
          size="small"
          pagination={false}
          dataSource={data}
          scroll={{ x: 'max-content' }}
          columns={columns}
        />
      </Modal>
    );
  },
);

export default VersionRecordModal;
