import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { ShopSelect } from '@src/components';
import { createShopManagementFee } from '@src/services/shop/shop-management-fee';
import {
  RateTypeEnum,
  ShopManagementFeeChangeTypeEnum,
} from '@src/services/shop/shop-management-fee/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, DatePicker, Form, Input, Modal, ModalProps, Select } from 'antd';
import dayjs from 'dayjs';

interface CreateShopManagementFeeModalProps extends ModalProps {
  onSuccess: () => void;
}

const CreateShopManagementFeeModal = React.forwardRef<ModalRef, CreateShopManagementFeeModalProps>(
  ({ onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();

    const { runAsync: create, loading: createLoading } = useRequest(createShopManagementFee, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const handleSave = async ({ effectiveRange, ...values }: any) => {
      await create({ ...effectiveRange, ...values });
      message.success('新建成功');
      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        title="新建门店管理费"
        open={open}
        destroyOnHidden
        confirmLoading={createLoading}
        modalRender={(node) => (
          <Form form={form} clearOnDestroy labelCol={{ span: 6 }} onFinish={handleSave}>
            {node}
          </Form>
        )}
        onCancel={() => setOpen(false)}
        onOk={form.submit}
        {...props}
      >
        <Form.Item
          label="门店编号"
          name="shopNo"
          rules={[{ required: true, message: '请选择门店编号' }]}
        >
          <ShopSelect />
        </Form.Item>
        <Form.Item
          label="变更类型"
          name="changeType"
          rules={[{ required: true, message: '请选择变更类型' }]}
        >
          <Select
            placeholder="请选择变更类型"
            options={enum2Options(ShopManagementFeeChangeTypeEnum)}
          />
        </Form.Item>
        <Form.Item
          label="生效日期"
          name="effectiveRange"
          getValueProps={(value) => ({
            value: value ? [dayjs(value.startDate), dayjs(value.endDate)] : undefined,
          })}
          normalize={(value) =>
            value
              ? {
                  startDate: value[0].format('YYYY-MM-DD'),
                  endDate: value[1].format('YYYY-MM-DD'),
                }
              : undefined
          }
          rules={[{ required: true, message: '请选择生效日期' }]}
        >
          <DatePicker.RangePicker />
        </Form.Item>
        <Form.Item
          label="费率类型"
          name="rateType"
          rules={[{ required: true, message: '请选择费率类型' }]}
        >
          <Select placeholder="请选择费率类型" options={enum2Options(RateTypeEnum)} />
        </Form.Item>
        <Form.Item noStyle shouldUpdate={(prev, next) => prev.changeType !== next.changeType}>
          {({ getFieldValue }) =>
            getFieldValue('changeType') === ShopManagementFeeChangeTypeEnum.搬迁 && (
              <Form.Item
                label="原门店编号"
                name="originalShopNo"
                rules={[{ required: true, message: '请选择原门店编号' }]}
              >
                <ShopSelect placeholder="请选择原门店编号" />
              </Form.Item>
            )
          }
        </Form.Item>
        <Form.Item label="变更说明" name="description">
          <Input.TextArea maxLength={255} placeholder="请输入变更说明" />
        </Form.Item>
      </Modal>
    );
  },
);

export default CreateShopManagementFeeModal;
