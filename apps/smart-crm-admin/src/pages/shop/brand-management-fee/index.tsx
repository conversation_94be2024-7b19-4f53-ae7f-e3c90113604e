import { useRef } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ModalRef } from '@src/common/interface';
import { ExportButton, ProTable, TableActions } from '@src/components';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import {
  exportBrandManagementFee,
  exportBrandManagementFeeDetail,
  queryBrandManagementFeePageList,
  refreshBrandManagementFee,
} from '@src/services/shop/brand-management-fee';
import {
  BrandManagementFeeDTO,
  ExportBrandManagementFeeReq,
} from '@src/services/shop/brand-management-fee/type';
import {
  RateTypeEnum,
  ShopManagementFeeChangeTypeEnum,
} from '@src/services/shop/shop-management-fee/type';
import { compatibleTableActionWidth, enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { App } from 'antd';
import dayjs from 'dayjs';
import ShopDetailDrawer from '../list/components/ShopDetailDrawer';

const BrandManagementFee = () => {
  const checkPermission = usePermission();
  const { modal, message } = App.useApp();
  const filterParamsRef = useRef<ExportBrandManagementFeeReq>({});
  const shopDetailDrawerRef = useRef<ModalRef>(null);
  const shopNoRef = useRef<string | null>(null);
  const actionRef = useRef<ActionType>(null);

  const { data, runAsync: queryList } = useRequest(queryBrandManagementFeePageList, {
    manual: true,
  });

  const columns: ProColumns<BrandManagementFeeDTO>[] = [
    {
      title: '门店编号',
      dataIndex: 'shopNo',
      renderText: (value) => (
        <a
          onClick={() => {
            shopNoRef.current = value;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '年月',
      dataIndex: 'period',
      valueType: 'dateMonth',
      initialValue: dayjs().subtract(1, 'month'),
      fieldProps: {
        allowClear: false,
      },
    },
    {
      title: '当月变更类型',
      dataIndex: 'changeType',
      valueEnum: enum2ValueEnum(ShopManagementFeeChangeTypeEnum),
    },
    {
      title: '费率',
      dataIndex: 'rateType',
      search: false,
      valueEnum: enum2ValueEnum(RateTypeEnum),
    },
    {
      title: '搬迁前门店编号',
      dataIndex: 'originalShopNo',
      search: false,
      renderText: (value) => (
        <a
          onClick={() => {
            shopNoRef.current = value;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      search: false,
      width: compatibleTableActionWidth(100),
      render: (_, { shopNo, period }) => (
        <TableActions
          shortcuts={[
            {
              label: '刷新报表',
              show: checkPermission(PermissionsMap.BrandManagementFeeRefresh),
              onClick: () =>
                modal.confirm({
                  title: '刷新报表',
                  content: '确定要刷新当月该门店管理费和变更记录吗？',
                  onOk: async () => {
                    await refreshBrandManagementFee({ shopNo, period });
                    message.success('刷新成功');
                    actionRef.current?.reload();
                  },
                }),
            },
          ]}
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        rowKey="id"
        sticky
        bordered
        cardProps={false}
        scroll={{ x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0) }}
        columns={columns}
        toolbar={{
          subTitle: <span className="text-black">共 {data?.total || 0} 条信息</span>,
          actions: [
            <Permission value={PermissionsMap.BrandManagementFeeExport}>
              <ExportButton
                total={data?.total}
                request={() => exportBrandManagementFeeDetail(filterParamsRef.current)}
              >
                导出明细
              </ExportButton>
            </Permission>,
            <Permission value={PermissionsMap.BrandManagementFeeExport}>
              <ExportButton
                total={data?.total}
                request={() => exportBrandManagementFee(filterParamsRef.current)}
              />
            </Permission>,
          ],
        }}
        request={async ({ current, pageSize, ...params }) => {
          filterParamsRef.current = params as ExportBrandManagementFeeReq;

          const { result, total } = await queryList({ pageNum: current, pageSize, ...params });

          return {
            data: result,
            total,
          };
        }}
      />
      <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => shopNoRef.current} />
    </PageContainer>
  );
};

export default BrandManagementFee;
