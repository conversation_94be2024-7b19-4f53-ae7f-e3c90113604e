import { useRef, useState } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ModalRef } from '@src/common/interface';
import { ExportButton, ProTable } from '@src/components';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { getMiddlePlatformProvinceCity } from '@src/services/common';
import { DevelopTagEnum } from '@src/services/contract/formal/type';
import { exportShopList, getShopList } from '@src/services/shop/list';
import {
  ExportShopListReq,
  ShopListItemDTO,
  ShopStatusEnum,
  ShopTypeEnum,
} from '@src/services/shop/list/type';
import { enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import EditShopModal from './components/EditShopModal';
import ShopDetailDrawer from './components/ShopDetailDrawer';

const Shop = () => {
  const actionRef = useRef<ActionType>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const filterParamsRef = useRef<ExportShopListReq>({});
  const shopDetailDrawerRef = useRef<ModalRef>(null);
  const currentIdRef = useRef<string | null>(null);
  const editShopModalRef = useRef<ModalRef>(null);

  const checkPermission = usePermission();
  const { data, runAsync: getList } = useRequest(getShopList, { manual: true });
  const { data: cityValueEnum } = useRequest(async () => {
    const res = await getMiddlePlatformProvinceCity();

    return res.reduce<Record<string, string>>((total, cur) => {
      total[cur.city] = `${cur.province} ${cur.city}`;

      return total;
    }, {});
  });

  const columns: ProColumns<ShopListItemDTO>[] = [
    {
      title: '门店编号',
      dataIndex: 'shopId',
      fixed: 'left',
      renderText: (value) => (
        <a
          onClick={() => {
            currentIdRef.current = value;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '门店冠名',
      dataIndex: 'shopName',
      width: 200,
      ellipsis: true,
    },
    {
      title: (_, type) => (type === 'table' ? '省市区' : '城市'),
      key: 'city',
      ellipsis: true,
      valueEnum: cityValueEnum,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
      },
      renderText: (_, { province, city, district }) => [province, city, district].join(' / '),
    },
    {
      title: '门店状态',
      dataIndex: 'shopStatus',
      valueEnum: {
        [ShopStatusEnum.PREPARING]: {
          text: '筹备中',
          status: 'processing',
        },
        [ShopStatusEnum.OPEN]: {
          text: '营业中',
          status: 'success',
        },
        [ShopStatusEnum.OFFLINE]: {
          text: '歇业中',
          status: 'default',
        },
        [ShopStatusEnum.CLOSE]: {
          text: '已闭店',
          status: 'error',
        },
        [ShopStatusEnum.OPEN_FOR_BUSINESS]: {
          text: '待营业',
          status: 'warning',
        },
      },
    },
    {
      title: '门店类型',
      dataIndex: 'shopType',
      valueEnum: {
        [ShopTypeEnum.REGULAR_CHAIN]: '加盟 T',
        [ShopTypeEnum.FRANCHISE_CHAIN]: '加盟 M',
      },
    },
    {
      title: '详细地址',
      dataIndex: 'address',
      ellipsis: true,
      search: false,
    },
    {
      title: '经纬度',
      key: 'latitude-longitude',
      width: 200,
      search: false,
      renderText: (_, { latitude, longitude }) =>
        latitude && longitude && `${latitude},${longitude}`,
    },
    {
      title: '门店落位开发标签',
      dataIndex: 'siteLocationLabel',
      valueEnum: enum2ValueEnum(DevelopTagEnum),
      fieldProps: {
        showSearch: true,
      },
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      search: false,
    },
    {
      title: '更新人',
      dataIndex: 'updateUserName',
      search: false,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      fixed: 'right',
      width: 70,
      search: false,
      hidden: !checkPermission(PermissionsMap.ShopListEdit),
      render: (_, { shopId }) => (
        <a
          onClick={() => {
            currentIdRef.current = shopId;
            editShopModalRef.current?.open();
          }}
        >
          编辑
        </a>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        rowKey="shopId"
        actionRef={actionRef}
        size="small"
        bordered
        sticky
        columnEmptyText=""
        search={{ labelWidth: 'auto' }}
        options={false}
        cardProps={false}
        columns={columns}
        tableAlertRender={false}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        scroll={{ x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0) }}
        rowSelection={{
          fixed: true,
          showSort: true,
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        toolbar={{
          subTitle: (
            <span className="text-black">
              共 {data?.total || 0} 个门店
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 个
                  <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            <Permission value={PermissionsMap.ShopListExport}>
              <ExportButton
                total={selectedRowKeys.length || data?.total}
                request={() =>
                  exportShopList(
                    selectedRowKeys.length > 0
                      ? { shopIds: selectedRowKeys }
                      : filterParamsRef.current,
                  )
                }
              />
            </Permission>,
          ],
        }}
        request={async ({ current, pageSize, ...params }) => {
          filterParamsRef.current = params as ExportShopListReq;

          const res = await getList({ pageNum: current, pageSize, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => currentIdRef.current} />
      <EditShopModal
        ref={editShopModalRef}
        getId={() => currentIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
    </PageContainer>
  );
};

export default Shop;
