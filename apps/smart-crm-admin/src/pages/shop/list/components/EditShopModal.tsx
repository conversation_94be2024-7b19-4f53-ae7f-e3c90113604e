import React, { useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { Encrypt } from '@src/components';
import EditableDate from '@src/components/Editable/EditableDate';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import { getCustomerDetail } from '@src/services/customers';
import { getShopDetail, updateShopExtra } from '@src/services/shop/list';
import { SignerMainTypeEnum } from '@src/services/shop/list/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Card, Form, Input, InputNumber, Modal, ModalProps, Select } from 'antd';

interface EditShopModalProps extends ModalProps {
  getId: () => string | null;
  onSuccess: () => void;
}

const EditShopModal = React.forwardRef<ModalRef, EditShopModalProps>(
  ({ getId, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();

    const shopId = getId()!;

    const { loading, data } = useRequest(() => getShopDetail(shopId), {
      ready: open,
      onSuccess: (res) => {
        form.setFieldsValue(res);

        if (res.customerId) {
          getCustomer(res.customerId);
        }
      },
    });

    const {
      data: customer,
      loading: getCustomerLoading,
      runAsync: getCustomer,
      mutate: mutateCustomer,
    } = useRequest(getCustomerDetail, {
      manual: true,
    });

    const { runAsync: update, loading: updateLoading } = useRequest(updateShopExtra, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      await update({ shopId, ...values });
      message.success('修改成功');
      setOpen(false);
      onSuccess();
    };

    return (
      <Modal
        title={`编辑门店（${shopId}）`}
        loading={loading || getCustomerLoading}
        open={open}
        destroyOnHidden
        confirmLoading={updateLoading}
        styles={{
          body: {
            display: 'flex',
            flexDirection: 'column',
            gap: 16,
            maxHeight: '60vh',
            margin: '0 -24px',
            padding: '0 24px',
            overflow: 'auto',
          },
        }}
        modalRender={(node) => (
          <Form form={form} labelCol={{ span: 8 }} clearOnDestroy onFinish={handleSave}>
            {node}
          </Form>
        )}
        onCancel={() => setOpen(false)}
        onOk={form.submit}
        {...props}
      >
        <Card
          title="签约人信息"
          classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
        >
          <Form.Item
            label="客户名称"
            name="customerId"
            rules={[{ required: true, message: '请选择客户名称' }]}
          >
            <RelCustomer
              editable
              defaultCustomerIdToNameMap={{
                [data?.customerId || '']: data?.signerName,
              }}
              onChange={(_, info) => {
                mutateCustomer(info);
                form.setFieldsValue({
                  signerName: info?.name,
                  signerPhone: info?.phones?.[0],
                  signerIdCard: info?.identityCard,
                });
              }}
            />
          </Form.Item>
          <Form.Item
            name="signerName"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input disabled placeholder="选择客户后自动填充" />
          </Form.Item>
          <Form.Item
            name="signerPhone"
            label="联系方式"
            validateFirst
            validateTrigger={['onChange', 'onBlur']}
            rules={[{ required: true, message: '请输入联系方式' }]}
          >
            <Encrypt.PhoneSelect
              placeholder="请选择手机号"
              options={customer?.phones?.map((phone, index) => ({
                label: customer.phonesSensitive?.[index],
                value: phone,
              }))}
              encryptSensitiveMap={{
                [data?.signerPhone || '']: data?.signerPhoneSensitive,
              }}
            />
          </Form.Item>
          <Encrypt.IdCardFormItem
            formItemProps={{
              label: '身份证号',
              name: 'signerIdCard',
              rules: [{ required: true, message: 'signerIdCard' }],
            }}
            fieldProps={{
              disabled: true,
              placeholder: '选择客户后自动填充',
              encryptSensitiveMap: {
                [customer?.identityCard || '']: customer?.identityCardSensitive,
                [data?.signerIdCard || '']: data?.signerIdCardSensitive,
              },
            }}
          />
        </Card>
        <Card
          title="品牌授权信息"
          classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
        >
          <EditableDate
            editable
            formItemProps={{
              name: 'brandAuthBeginDate',
              label: '品牌授权开始日期',
              rules: [{ required: true, message: '请选择品牌授权开始日期' }],
            }}
          />
          <EditableDate
            editable
            formItemProps={{
              name: 'brandAuthEndDate',
              label: '品牌授权截止日期',
              rules: [{ required: true, message: '请选择品牌授权截止日期' }],
            }}
          />
        </Card>
        <Card
          title="签约主体信息"
          classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
        >
          <Form.Item
            name="signerMainType"
            label="签约主体"
            rules={[{ required: true, message: '请选择签约主体' }]}
          >
            <Select
              placeholder="请选择签约主体"
              options={enum2Options(SignerMainTypeEnum, 'number')}
            />
          </Form.Item>
          <Form.Item noStyle dependencies={['signerMainType']}>
            {({ getFieldValue }) =>
              getFieldValue('signerMainType') === SignerMainTypeEnum.公司 && (
                <>
                  <Form.Item
                    name="signerMain"
                    label="公司名称"
                    rules={[{ required: true, message: '请输入公司名称' }]}
                  >
                    <Input placeholder="请输入公司名称" />
                  </Form.Item>
                  <Form.Item
                    name="signerUsci"
                    label="统一社会信用代码"
                    rules={[{ required: true, message: '请输入统一社会信用代码' }]}
                  >
                    <Input placeholder="请输入统一社会信用代码" />
                  </Form.Item>
                </>
              )
            }
          </Form.Item>
        </Card>
        <Card
          title="门店信息"
          classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
        >
          <Form.Item name="realUseArea" label="实际使用面积">
            <InputNumber placeholder="请输入" addonAfter="m²" min={0} precision={2} />
          </Form.Item>
          <Form.Item name="propertyAddress" label="不动产权证地址">
            <Input.TextArea autoSize placeholder="请输入不动产权证地址" />
          </Form.Item>
        </Card>
      </Modal>
    );
  },
);

export default EditShopModal;
