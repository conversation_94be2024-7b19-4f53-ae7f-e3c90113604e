import { DescriptionFieldGroupType } from '@src/common/interface';
import EditableAttachment from '@src/components/Editable/EditableAttachment';
import { ValueType } from '@src/components/ETable';
import { getShopCertDetail } from '@src/services/shop/list';
import { ShopCertDTO } from '@src/services/shop/list/type';
import { renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { Skeleton } from 'antd';

interface ShopCertProps {
  id: string;
}

const ShopCert: React.FC<ShopCertProps> = ({ id }) => {
  const { data, loading } = useRequest(() => getShopCertDetail(id));

  if (loading) {
    return <Skeleton title={false} active paragraph={{ rows: 3 }} />;
  }

  const items: DescriptionFieldGroupType<ShopCertDTO>[] = [
    {
      label: '证照状态',
      children: [
        {
          label: '状态',
          field: 'douyinCheckStatus',
          valueType: ValueType.SINGLE,
          fieldProps: {
            options: [
              { label: '校验中', value: 10 },
              { label: '校验失败', value: 20 },
              { label: '校验通过', value: 30 },
            ],
          },
        },
        { label: '失败原因', field: 'douyinCheckResult' },
      ],
    },
    {
      label: '营业执照',
      children: [
        { label: '营业执照名称', field: ['license', 'licenseName'] },
        {
          label: '营业执照类型',
          field: ['license', 'licenseType'],
        },
        { label: '统一社会信用代码', field: ['license', 'licenseId'] },
        { label: '经营者', field: ['license', 'licensePersonName'] },
        { label: '经营范围', field: ['license', 'salesRange'] },
        { label: '经营场所', field: ['license', 'address'] },
        { label: '有效期', field: ['license', 'licenseExpiration'] },
        {
          label: '营业执照照片',
          children: data?.license?.licenseUrl && (
            <EditableAttachment
              value={[
                {
                  fileUrl: data.license.licenseUrl,
                  fileName: '',
                  fileKey: '1',
                  fileType: 'image/png',
                },
              ]}
            />
          ),
        },
        {
          label: '发证日期',
          field: ['license', 'issueDate'],
        },
      ],
    },
    {
      label: '食品许可证',
      children: [
        { label: '经营者名称', field: ['qualification', 'qualificationPersonName'] },
        { label: '许可证编号', field: ['qualification', 'qualificationId'] },
        { label: '法定代表人（负责人）', field: ['qualification', 'legalPersonName'] },
        { label: '经营场所', field: ['qualification', 'qualificationAddress'] },
        { label: '主体业态', field: ['qualification', 'qualificationBusinessType'] },
        { label: '经营项目', field: ['qualification', 'qualificationScope'] },
        { label: '有效期', field: ['qualification', 'qualificationExpiration'] },
        {
          label: '食品许可证照片',
          children: data?.qualification?.qualificationUrl && (
            <EditableAttachment
              value={[
                {
                  fileUrl: data.qualification.qualificationUrl,
                  fileName: '',
                  fileKey: '1',
                  fileType: 'image/png',
                },
              ]}
            />
          ),
        },
        {
          label: '发证日期',
          field: ['qualification', 'issueDate'],
        },
      ],
    },
    {
      label: '法人身份证',
      children: [
        { label: '姓名', field: ['legalPerson', 'name'] },
        { label: '证件号', field: ['legalPerson', 'idCardNo'] },
        { label: '联系方式', field: ['legalPerson', 'phone'] },
        { label: '证件有效期', field: ['legalPerson', 'idCardExpiration'] },
        {
          label: '身份证照',
          children: (
            <EditableAttachment
              value={[
                {
                  fileUrl: data?.legalPerson?.idCardFrontUrl || '',
                  fileType: 'image/png',
                  fileKey: '1',
                  fileName: '',
                },
                {
                  fileUrl: data?.legalPerson?.idCardBackUrl || '',
                  fileType: 'image/png',
                  fileKey: '2',
                  fileName: '',
                },
              ].filter((i) => !!i.fileUrl)}
            />
          ),
        },
      ],
    },
  ];

  return renderDescriptions(items, data);
};

export default ShopCert;
