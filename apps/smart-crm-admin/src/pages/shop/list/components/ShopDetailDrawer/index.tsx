import React, { useRef, useState } from 'react';
import { DescriptionFieldGroupType, ModalRef } from '@src/common/interface';
import { ValueType } from '@src/components/ETable';
import { Permission, PermissionsMap } from '@src/permissions';
import { DevelopTagEnum } from '@src/services/contract/formal/type';
import { getShopDetail } from '@src/services/shop/list';
import {
  ShopDTO,
  ShopStatusEnum,
  ShopTypeEnum,
  SignerMainTypeEnum,
} from '@src/services/shop/list/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { Button, Card, Drawer, DrawerProps, Tabs } from 'antd';
import ShopCert from './ShopCert';
import ShopOnlineMerchant from './ShopOnlineMerchant';
import ShopTianYanCha from './ShopTianYanCha';
import EditShopModal from '../EditShopModal';

interface ShopDetailDrawerProps extends DrawerProps {
  getId: () => string | null | undefined;
}

const ShopDetailDrawer = React.forwardRef<ModalRef, ShopDetailDrawerProps>(
  ({ getId, ...props }, ref) => {
    const [open, setOpen] = useState(false);
    const editShopModalRef = useRef<ModalRef>(null);

    const shopId = getId()!;

    const { data, loading, refresh } = useRequest(() => getShopDetail(shopId), { ready: open });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const baseInfoItems: DescriptionFieldGroupType<ShopDTO>[] = [
      {
        label: '基本信息',
        children: [
          {
            field: 'shopId',
            label: '门店编号',
          },
          {
            field: 'shopName',
            label: '门店冠名',
          },
          {
            field: 'city',
            label: '省市区',
            children: [data?.province, data?.city, data?.district].join(' / '),
          },
          {
            field: 'shopStatus',
            label: '门店状态',
            valueType: ValueType.SINGLE,
            fieldProps: {
              options: [
                {
                  label: '筹备中',
                  value: ShopStatusEnum.PREPARING,
                },
                {
                  label: '营业中',
                  value: ShopStatusEnum.OPEN,
                },
                {
                  label: '歇业中',
                  value: ShopStatusEnum.OFFLINE,
                },
                {
                  label: '已闭店',
                  value: ShopStatusEnum.CLOSE,
                },
                {
                  label: '待营业',
                  value: ShopStatusEnum.OPEN_FOR_BUSINESS,
                },
              ],
            },
          },
          {
            field: 'shopType',
            label: '门店类型',
            valueType: ValueType.SINGLE,
            fieldProps: {
              options: [
                { label: '加盟 T', value: ShopTypeEnum.REGULAR_CHAIN },
                { label: '加盟 M', value: ShopTypeEnum.FRANCHISE_CHAIN },
              ],
            },
          },
          {
            field: 'address',
            label: '详细地址',
          },
          {
            field: 'latitude',
            label: '经度',
          },
          {
            field: 'longitude',
            label: '纬度',
          },
          {
            field: 'siteLocationLabel',
            label: '门店落位开发标签',
            valueType: ValueType.SINGLE,
            fieldProps: {
              options: enum2Options(DevelopTagEnum),
            },
          },
        ],
      },
      {
        label: '签约人信息',
        children: [
          {
            field: 'signerName',
            label: '姓名',
          },
          {
            field: 'signerPhone',
            label: '联系电话',
            valueType: ValueType.PHONE,
            fieldProps: {
              sensitiveValue: data?.signerPhoneSensitive,
            },
          },
          {
            field: 'signerIdCard',
            label: '身份证号',
            valueType: ValueType.IDENTITY_CARD,
            fieldProps: {
              sensitiveValue: data?.signerIdCardSensitive,
            },
          },
        ],
      },
      {
        label: '品牌授权信息',
        children: [
          {
            field: 'brandAuthBeginDate',
            label: '品牌授权开始日期',
          },
          {
            field: 'brandAuthEndDate',
            label: '品牌授权截止日期',
          },
        ],
      },
      {
        label: '签约主体信息',
        children: [
          {
            field: 'signerMainType',
            label: '签约主体',
            valueType: ValueType.SINGLE,
            fieldProps: {
              options: enum2Options(SignerMainTypeEnum, 'number'),
            },
          },
          {
            field: 'signerMain',
            label: '公司名称',
          },
          {
            field: 'signerUsci',
            label: '统一社会信用代码',
          },
        ],
      },
      {
        label: '门店信息',
        children: [
          {
            field: 'realUseArea',
            label: '实际使用面积',
            children:
              data?.realUseArea || data?.realUseArea === 0 ? `${data.realUseArea} m²` : null,
          },
          {
            field: 'propertyAddress',
            label: '不动产权证地址',
          },
        ],
      },
    ];

    return (
      <Drawer
        title="门店详情"
        open={open}
        width={800}
        destroyOnHidden
        onClose={() => setOpen(false)}
        {...props}
      >
        <Card loading={loading}>
          <div className="flex justify-between">
            <div className="flex flex-col gap-1">
              <span>门店编号：{data?.shopId}</span>
              <span>门店冠名：{data?.shopName}</span>
              <span>更新人：{data?.updateUserName}</span>
              <span>更新时间：{data?.updateTime}</span>
            </div>
            <Permission value={PermissionsMap.ShopListEdit}>
              <Button onClick={() => editShopModalRef.current?.open()}>编辑</Button>
            </Permission>
          </div>
        </Card>
        <Card className="mt-5" loading={loading}>
          <Tabs
            className="-mt-5"
            items={[
              {
                label: '门店信息',
                key: 'info',
                children: renderDescriptions(baseInfoItems, data),
              },
              {
                label: '证照信息',
                key: 'cert',
                children: <ShopCert id={shopId} />,
              },
              {
                label: '网商信息',
                key: 'online-merchant',
                children: <ShopOnlineMerchant id={shopId} />,
              },
              {
                label: '天眼查信息',
                key: 'tianyancha',
                children: <ShopTianYanCha id={shopId} />,
              },
            ]}
          />
        </Card>
        <EditShopModal ref={editShopModalRef} getId={() => shopId} onSuccess={refresh} />
      </Drawer>
    );
  },
);

export default ShopDetailDrawer;
