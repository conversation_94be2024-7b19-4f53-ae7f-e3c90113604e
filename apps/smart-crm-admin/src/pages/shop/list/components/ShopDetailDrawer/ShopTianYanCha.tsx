import { DescriptionFieldGroupType } from '@src/common/interface';
import { queryShopTycInfo } from '@src/services/shop/list';
import { ShopTycInfoDTO } from '@src/services/shop/list/type';
import { renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { Skeleton } from 'antd';

interface ShopTianYanChaProps {
  id: string;
}

const ShopTianYanCha: React.FC<ShopTianYanChaProps> = ({ id }) => {
  const { data, loading } = useRequest(() => queryShopTycInfo(id));

  if (loading) {
    return <Skeleton title={false} active paragraph={{ rows: 3 }} />;
  }

  const items: DescriptionFieldGroupType<ShopTycInfoDTO>[] = [
    {
      label: '营业执照',
      children: [
        {
          label: '营业执照名称',
          field: ['tycLicense', 'name'],
        },
        {
          label: '统一社会信用代码',
          field: ['tycLicense', 'creditCode'],
        },
        {
          label: '营业执照类型',
          field: ['tycLicense', 'companyOrgType'],
        },
        {
          label: '经营范围',
          field: ['tycLicense', 'businessScope'],
        },
        {
          label: '注册地址',
          field: ['tycLicense', 'regLocation'],
        },
        {
          label: '法定代表人',
          field: ['tycLicense', 'legalPersonName'],
        },
        {
          label: '企业状态（开业）',
          field: ['tycLicense', 'regStatus'],
        },
        {
          label: '企业标签（存续）',
          field: ['tycLicense', 'tags'],
        },
        {
          label: '更新时间',
          field: ['tycLicense', 'updateTimes'],
        },
      ],
    },
    {
      label: '食品许可证',
      children: [
        {
          label: '许可证编号',
          field: ['tycQualification', 'certNo'],
        },
        {
          label: '经营者',
          field: ['tycQualification', 'operator'],
        },
        {
          label: '法定代表人',
          field: ['tycQualification', 'legalPersonName'],
        },
        {
          label: '经营场所',
          field: ['tycQualification', 'address'],
        },
        {
          label: '主体业态',
          field: ['tycQualification', 'mainBusiness'],
        },
        {
          label: '有效期至',
          field: ['tycQualification', 'expireDate'],
        },
        {
          label: '发证日期',
          field: ['tycQualification', 'issueDate'],
        },
        {
          label: '经营项目',
          field: ['tycQualification', 'businessProject'],
        },
      ],
    },
  ];

  return renderDescriptions(items, data);
};

export default ShopTianYanCha;
