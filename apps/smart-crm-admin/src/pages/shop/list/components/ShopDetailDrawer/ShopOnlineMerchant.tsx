import { DescriptionFieldType } from '@src/common/interface';
import { ValueType } from '@src/components/ETable';
import { getShopCertDetail } from '@src/services/shop/list';
import {
  MerchantProcessStatusEnum,
  MerchantStatusEnum,
  MerchantTypeEnum,
  ShopCertDTO,
} from '@src/services/shop/list/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { Skeleton } from 'antd';

interface ShopOnlineMerchantProps {
  id: string;
}

const ShopOnlineMerchant: React.FC<ShopOnlineMerchantProps> = ({ id }) => {
  const { data, loading } = useRequest(() => getShopCertDetail(id));

  if (loading) {
    return <Skeleton title={false} active paragraph={{ rows: 3 }} />;
  }

  const items: DescriptionFieldType<ShopCertDTO>[] = [
    {
      label: '商户类型',
      field: ['franchiseeInfo', 'merchantType'],
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(MerchantTypeEnum),
      },
    },
    {
      label: '状态',
      field: ['franchiseeInfo', 'status'],
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(MerchantStatusEnum, 'number'),
      },
    },
    {
      label: '流程状态',
      field: ['franchiseeInfo', 'processStatus'],
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(MerchantProcessStatusEnum, 'number'),
      },
    },
    {
      label: '营业执照名称',
      field: ['franchiseeInfo', 'businessLicenseName'],
    },
    {
      label: '营业执照号码',
      field: ['franchiseeInfo', 'businessLicenseNo'],
    },
    {
      label: '法人姓名',
      field: ['franchiseeInfo', 'personName'],
    },
    {
      label: '法人身份证号',
      field: ['franchiseeInfo', 'idCard'],
    },
    {
      label: '法人手机号',
      field: ['franchiseeInfo', 'mobile'],
    },
  ];

  return renderDescriptions(items, data);
};

export default ShopOnlineMerchant;
