import React, { useRef, useState } from 'react';
import { yesOrNoOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { AuditFlow, ButtonGroup, OperationLog } from '@src/components';
import EditableIdentityCard from '@src/components/Editable/EditableIdentityCard';
import EditablePhone from '@src/components/Editable/EditablePhone';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import useCheckVisibleAuth from '@src/hooks/useCheckVisibleAuth';
import BusinessDetailDrawer from '@src/pages/business-opportunity/components/BusinessDetailDrawer';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import { BelongWarZoneEnum, ChannelTypeEnum } from '@src/services/business-opportunity/type';
import { AuditStatusEnum } from '@src/services/common/type';
import { CustomerScoreLevelEnum } from '@src/services/customers/type';
import {
  getFissionAuditAllHistory,
  getFissionDetail,
  passFissionAudit,
  submitFissionAudit,
} from '@src/services/fission';
import { FissionAttributeEnum, FissionDTO, ShopTypeEnum } from '@src/services/fission/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Card, Col, Drawer, DrawerProps, Row, Tabs } from 'antd';
import CreateEditFissionModal from './CreateEditFissionModal';
import FissionAuditCard, { FissionAuditCardRef } from './FissionAuditCard';

interface FissionDetailDrawerProps extends DrawerProps {
  getId: () => number | null;
  onUpdate?: () => void;
  onTodoSuccess?: () => void;
}

const FissionDetailDrawer = React.forwardRef<ModalRef, FissionDetailDrawerProps>(
  ({ getId, onUpdate, onTodoSuccess, ...drawerProps }, ref) => {
    const { modal, message } = App.useApp();
    const [open, setOpen] = useState(false);
    const customerDetailDrawerRef = useRef<ModalRef>(null);
    const businessDetailDrawerRef = useRef<ModalRef>(null);
    const createEditFissionModalRef = useRef<ModalRef>(null);
    const [activeKey, setActiveKey] = useState('info');
    const customerIdRef = useRef<number | null>(null);
    const auditCardRef = useRef<FissionAuditCardRef>(null);

    const id = getId()!;

    const checkVisibleAuth = useCheckVisibleAuth({ ready: open });
    const { data: users } = useAllUsers({ ready: open });
    const { data, loading, refresh, mutate } = useRequest(() => getFissionDetail(id), {
      ready: open,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        setActiveKey('info');
        mutate(undefined);
      },
      close: () => setOpen(false),
    }));

    const handleSubmitAudit = () => {
      modal.confirm({
        title: '提交审核',
        content: '确定要提交审核吗？',
        onOk: async () => {
          if (data?.auditStatus === AuditStatusEnum.RETURN) {
            await passFissionAudit({ id });
          } else {
            await submitFissionAudit(id);
          }

          message.success('提交审核成功');
          refresh();
          auditCardRef.current?.refresh();
          onUpdate?.();
          onTodoSuccess?.();
        },
      });
    };

    const baseInfoItems: DescriptionFieldType<FissionDTO>[] = [
      {
        label: '裂变申请编号',
        field: 'code',
      },
      {
        label: '商机名称',
        field: 'bizOpportunityName',
        children: (
          <a onClick={() => businessDetailDrawerRef.current?.open()}>{data?.bizOpportunityName}</a>
        ),
      },
      {
        label: '联系方式',
        field: 'phone',
        valueType: ValueType.PHONE,
        fieldProps: {
          sensitiveValue: data?.phoneSensitive,
        },
      },
      {
        label: '商机负责人',
        field: 'bizOpportunityDirectUserId',
        valueType: ValueType.PERSON,
      },
      {
        label: '门店类型',
        field: 'shopType',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(ShopTypeEnum),
        },
      },
      {
        label: '招商主管审核人',
        field: 'crmManagerUserId',
        valueType: ValueType.PERSON,
      },
      {
        label: '客户是否有评级',
        field: 'customerHasScoreLevel',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        label: '客户评级',
        field: 'customerScoreLevel',
        hidden: !data?.customerHasScoreLevel,
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(CustomerScoreLevelEnum),
        },
      },
      {
        label: '客户评分',
        field: 'customerScore',
        hidden: !data?.customerHasScoreLevel,
      },
      {
        label: '渠道类型',
        field: 'channelTypes',
        valueType: ValueType.MULTIPLE,
        fieldProps: {
          options: enum2Options(ChannelTypeEnum),
        },
      },
      {
        label: '裂变属性',
        field: 'fissionAttribute',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(FissionAttributeEnum),
        },
      },
      {
        label: '意向区域',
        field: 'intentionRegion',
        valueType: ValueType.REGION,
        fieldProps: {
          regionLevel: 3,
        },
      },
      {
        label: '所属大区',
        field: 'belongWarZone',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(BelongWarZoneEnum),
        },
      },
      {
        label: '计划开店数量',
        field: 'planStoreCount',
      },
      {
        label: '新门店储备店长',
        field: 'newStoreReserveManagers',
        children: (
          <div className="flex flex-col gap-1">
            {data?.newStoreReserveManagers?.map((i) => (
              <div key={i.id}>
                <div>
                  姓名：
                  <a
                    onClick={() => {
                      customerIdRef.current = i.id;
                      customerDetailDrawerRef.current?.open();
                    }}
                  >
                    {i.name}
                  </a>
                </div>
                <div className="flex flex-wrap">
                  身份证：
                  <EditableIdentityCard
                    value={i.identityCard}
                    fieldProps={{ sensitiveValue: i.identityCardSensitive }}
                  />
                </div>
                <div className="flex">
                  手机号：
                  <div>
                    {i.phones.map((phone, index) => (
                      <EditablePhone
                        key={index}
                        value={phone}
                        fieldProps={{ sensitiveValue: i.phonesSensitive[index] }}
                      />
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ),
      },
      {
        label: '新店长是原门店员工数',
        field: 'originalStoreManagerCount',
      },
      {
        label: '原门店储备人员',
        field: 'originalStoreReserveManagers',
        children: (
          <div className="flex flex-col gap-1">
            {data?.originalStoreReserveManagers?.map((i) => (
              <div key={i.id}>
                <div>
                  姓名：
                  <a
                    onClick={() => {
                      customerIdRef.current = i.id;
                      customerDetailDrawerRef.current?.open();
                    }}
                  >
                    {i.name}
                  </a>
                </div>
                <div className="flex flex-wrap">
                  身份证：
                  <EditableIdentityCard
                    value={i.identityCard}
                    fieldProps={{ sensitiveValue: i.identityCardSensitive }}
                  />
                </div>
                <div className="flex">
                  手机号：
                  <div>
                    {i.phones.map((phone, index) => (
                      <EditablePhone
                        key={index}
                        value={phone}
                        fieldProps={{ sensitiveValue: i.phonesSensitive[index] }}
                      />
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ),
      },
      {
        label: '名下关联全部门店',
        field: 'relStore',
      },
      {
        label: '备注',
        field: 'remark',
      },
    ];

    return (
      <Drawer
        width={1200}
        push={false}
        destroyOnHidden
        title="裂变申请详情"
        open={open}
        onClose={() => setOpen(false)}
        {...drawerProps}
      >
        <Card loading={loading}>
          <div className="flex justify-between">
            <div className="flex flex-col gap-2">
              <p>
                客户名称：
                <a
                  onClick={() => {
                    customerIdRef.current = data?.customerId!;
                    customerDetailDrawerRef.current?.open();
                  }}
                >
                  {data?.customerName}
                </a>
              </p>
              <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
              <p>创建时间：{data?.createTime}</p>
              <p>更新人：{users?.find((i) => i.id === data?.updateUserId)?.name}</p>
              <p>更新时间：{data?.updateTime}</p>
            </div>
            {checkVisibleAuth(data?.createUserId) &&
              [AuditStatusEnum.NOT_SUBMIT, AuditStatusEnum.NO_PASS].includes(
                data?.auditStatus!,
              ) && (
                <ButtonGroup
                  items={[
                    {
                      label: '编辑',
                      onClick: () => createEditFissionModalRef.current?.open(),
                    },
                    {
                      label: '提交审核',
                      onClick: handleSubmitAudit,
                    },
                  ]}
                />
              )}
          </div>
        </Card>
        <Row gutter={[20, 20]} className="mt-5">
          <Col span={24} xl={16}>
            <Card className="flex-1" loading={loading}>
              <Tabs
                activeKey={activeKey}
                className="-mt-5"
                items={[
                  {
                    label: '详细资料',
                    key: 'info',
                    children: renderDescriptions(baseInfoItems, data),
                  },
                  {
                    label: '操作记录',
                    key: 'record',
                    children: <OperationLog id={id} businessType="FISSION_APPLY" />,
                  },
                  {
                    label: '审批记录',
                    key: 'audit-history',
                    children: <AuditFlow.Record request={() => getFissionAuditAllHistory(id)} />,
                  },
                ]}
                onChange={setActiveKey}
              />
            </Card>
          </Col>
          <Col span={24} xl={8}>
            <FissionAuditCard
              ref={auditCardRef}
              id={id}
              loading={loading}
              auditStatus={data?.auditStatus}
              onSuccess={() => {
                refresh();
                onUpdate?.();
              }}
              onTodoSuccess={onTodoSuccess}
            />
          </Col>
        </Row>

        <CreateEditFissionModal
          ref={createEditFissionModalRef}
          getId={() => id}
          onSuccess={() => {
            refresh();
            onUpdate?.();
          }}
        />
        <CustomerDetailDrawer
          ref={customerDetailDrawerRef}
          getId={() => customerIdRef.current}
          onUpdate={() => {
            refresh();
            onUpdate?.();
          }}
        />
        <BusinessDetailDrawer
          ref={businessDetailDrawerRef}
          getId={() => data?.bizOpportunityId}
          onUpdate={() => {
            refresh();
            onUpdate?.();
          }}
        />
      </Drawer>
    );
  },
);

export default FissionDetailDrawer;
