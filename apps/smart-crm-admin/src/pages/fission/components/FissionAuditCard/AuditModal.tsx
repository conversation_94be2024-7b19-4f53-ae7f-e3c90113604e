import { passFissionAudit, rejectFissionAudit } from '@src/services/fission';
import { useRequest } from 'ahooks';
import { App, Form, Input, Modal, ModalProps } from 'antd';

export type FissionAuditType = 'pass' | 'reject';

interface AuditModalProps extends ModalProps {
  type: FissionAuditType;
  id: number;
  onSuccess: () => void;
}

const AuditModal: React.FC<AuditModalProps> = ({ type, open, id, onSuccess, ...props }) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();

  const { runAsync: passAudit, loading: passLoading } = useRequest(passFissionAudit, {
    manual: true,
  });
  const { runAsync: rejectAudit, loading: rejectLoading } = useRequest(rejectFissionAudit, {
    manual: true,
  });

  const handleSave = async (values: any) => {
    const requestMap = {
      pass: passAudit,
      reject: rejectAudit,
    };

    await requestMap[type]({ id, ...values });
    message.success('审批成功');
    onSuccess();
  };

  return (
    <Modal
      title={
        {
          pass: '通过',
          reject: '拒绝',
        }[type]
      }
      open={open}
      destroyOnHidden
      confirmLoading={passLoading || rejectLoading}
      onOk={form.submit}
      {...props}
    >
      <Form form={form} clearOnDestroy onFinish={handleSave}>
        <Form.Item
          name="description"
          label="说明"
          rules={[{ required: true, message: '请输入说明' }]}
        >
          <Input.TextArea maxLength={200} showCount placeholder="请输入说明" rows={3} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AuditModal;
