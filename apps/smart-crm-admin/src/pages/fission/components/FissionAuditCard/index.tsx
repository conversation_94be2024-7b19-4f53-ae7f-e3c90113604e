import React, { useState } from 'react';
import { AuditFlow } from '@src/components';
import { AuditStatusEnum } from '@src/services/common/type';
import { getFissionAuditHistory, revokeFissionAudit } from '@src/services/fission';
import useUserStore from '@src/store/useUserStore';
import { useRequest } from 'ahooks';
import { App } from 'antd';
import AuditModal, { FissionAuditType } from './AuditModal';

interface FissionAuditCardProps {
  id: number;
  loading: boolean;
  auditStatus?: AuditStatusEnum;
  onTodoSuccess?: () => void;
  onSuccess: () => void;
}

export interface FissionAuditCardRef {
  refresh: () => void;
}

const FissionAuditCard = React.forwardRef<FissionAuditCardRef, FissionAuditCardProps>(
  ({ id, loading, auditStatus, onSuccess, onTodoSuccess }, ref) => {
    const { modal, message } = App.useApp();
    const [auditModal, setAuditModalOpen] = useState(false);
    const [auditType, setAuditType] = useState<FissionAuditType>('pass');

    const {
      user: { shUserId },
    } = useUserStore();
    const {
      data: auditHistory = [],
      loading: getAuditHistoryLoading,
      refresh,
    } = useRequest(() => getFissionAuditHistory(id));

    React.useImperativeHandle(ref, () => ({
      refresh,
    }));

    const handleRevoke = () => {
      modal.confirm({
        title: '撤回审核',
        content: '确定要撤回该审核吗？',
        onOk: async () => {
          await revokeFissionAudit(id);
          message.success('撤回成功');
          refresh();
          onSuccess();
        },
      });
    };

    // 是否审核中
    const isAuditStatus = auditStatus === AuditStatusEnum.AUDIT;
    // 是否是驳回
    const isReturnStatus = auditStatus === AuditStatusEnum.RETURN;
    // 是否是审批人
    const isAuditUser = !!auditHistory[auditHistory.length - 1]?.auditUserIds?.includes(shUserId);
    // 是否是提交人
    const isSubmitter = shUserId === auditHistory[0]?.auditUserId;

    return (
      <>
        <AuditFlow.OperateCard
          loading={loading || getAuditHistoryLoading}
          data={auditHistory}
          operateButtons={[
            {
              show: isAuditStatus && isAuditUser,
              children: '通过',
              onClick: () => {
                setAuditType('pass');
                setAuditModalOpen(true);
              },
            },
            {
              show: isAuditStatus && isAuditUser,
              children: '拒绝',
              danger: true,
              onClick: () => {
                setAuditType('reject');
                setAuditModalOpen(true);
              },
            },
            {
              show: (isAuditStatus || isReturnStatus) && isSubmitter,
              children: '撤回',
              className: 'ml-auto',
              onClick: handleRevoke,
            },
          ]}
        />
        <AuditModal
          open={auditModal}
          type={auditType}
          id={id}
          onCancel={() => setAuditModalOpen(false)}
          onSuccess={() => {
            setAuditModalOpen(false);
            refresh();
            onSuccess();
            onTodoSuccess?.();
          }}
        />
      </>
    );
  },
);

export default FissionAuditCard;
