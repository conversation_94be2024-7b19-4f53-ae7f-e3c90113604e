import React from 'react';
import { CloseCircleFilled, PlusOutlined } from '@ant-design/icons';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import { CustomerDTO } from '@src/services/customers/type';
import { Button } from 'antd';
import { TableRowSelection } from 'antd/lib/table/interface';

interface RelCustomerListProps {
  placeholder?: string;
  tip?: React.ReactNode;
  getCheckboxProps?: TableRowSelection<CustomerDTO>['getCheckboxProps'];
  // 默认的客户 id 和客户名称的映射值
  defaultCustomerIdToNameMap?: Record<number, string | undefined>;
  value?: (number | undefined)[];
  onChange?: (value: (number | undefined)[]) => void;
}

const RelCustomerList: React.FC<RelCustomerListProps> = ({
  getCheckboxProps,
  placeholder,
  tip,
  defaultCustomerIdToNameMap,
  value,
  onChange,
}) => {
  return (
    <div className="flex flex-col gap-1 items-start">
      {value?.map((id, index) => (
        <div key={index} className="flex w-full">
          <div className="flex-1">
            <RelCustomer
              editable
              tip={tip}
              placeholder={placeholder}
              defaultCustomerIdToNameMap={defaultCustomerIdToNameMap}
              getCheckboxProps={getCheckboxProps}
              value={id}
              onChange={(val) => {
                const newValue = [...(value || [])];

                newValue[index] = val;
                onChange?.(newValue);
              }}
            />
          </div>
          <Button
            icon={<CloseCircleFilled className="text-gray-400" />}
            type="text"
            className="ml-2"
            onClick={() => onChange?.(value.slice(0, value.length - 1))}
          />
        </div>
      ))}
      <Button
        className="mb-1"
        type="dashed"
        size="small"
        icon={<PlusOutlined />}
        onClick={() => onChange?.((value || []).concat(undefined))}
      >
        添加
      </Button>
    </div>
  );
};

export default RelCustomerList;
