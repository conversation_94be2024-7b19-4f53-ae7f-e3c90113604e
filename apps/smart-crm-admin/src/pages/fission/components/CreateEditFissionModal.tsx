import React, { useEffect, useState } from 'react';
import { LoadingOutlined } from '@ant-design/icons';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ChooseMapPoints, Encrypt, UserSelect } from '@src/components';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useProvinceWarZoneMap from '@src/hooks/useProvinceWarZoneMap';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import {
  BelongWarZoneEnum,
  BusinessOpportunityDTO,
  BusinessOpportunityTypeEnum,
  ChannelTypeEnum,
} from '@src/services/business-opportunity/type';
import { getCustomerDetail } from '@src/services/customers';
import { CustomerScoreLevelEnum } from '@src/services/customers/type';
import {
  createFission,
  getCustomerScoreByDistrictCode,
  getFissionDetail,
  updateFission,
} from '@src/services/fission';
import { FissionAttributeEnum, ShopTypeEnum } from '@src/services/fission/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { Alert, App, Form, Input, InputNumber, Modal, ModalProps, Select } from 'antd';
import RelCustomerList from './RelCustomerList';

interface CreateEditFissionModalProps extends ModalProps {
  // 新建时的商机数据
  createBusinessData?: BusinessOpportunityDTO;
  getId: () => number | null | undefined;
  onSuccess: () => void;
}

// 商机类型转换成门店类型映射
const businessOpportunityTypeToShopTypeMap = {
  [BusinessOpportunityTypeEnum.SOCIETY]: ShopTypeEnum.社会店,
  [BusinessOpportunityTypeEnum.FISSION_SOCIETY]: ShopTypeEnum.社会店,
  [BusinessOpportunityTypeEnum.CHANNEL]: ShopTypeEnum.渠道店,
  [BusinessOpportunityTypeEnum.FISSION_CHANNEL]: ShopTypeEnum.渠道店,
};

const CreateEditFissionModal = React.forwardRef<ModalRef, CreateEditFissionModalProps>(
  ({ createBusinessData, getId, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();

    const id = getId()!;
    const isEdit = !createBusinessData;

    const provinceWarZoneMap = useProvinceWarZoneMap();
    const { runAsync: getCustomerScore, loading: getCustomerScoreLoading } = useRequest(
      getCustomerScoreByDistrictCode,
      { manual: true },
    );
    const {
      data: customer,
      loading: getCustomerLoading,
      mutate: mutateCustomer,
      runAsync: getCustomer,
    } = useRequest(getCustomerDetail, {
      manual: true,
    });

    useEffect(() => {
      if (open && !isEdit) {
        getCustomer(createBusinessData.customerId);
        form.setFieldsValue({
          ...createBusinessData,
          shopType:
            businessOpportunityTypeToShopTypeMap[createBusinessData.businessOpportunityType],
        });
        getCustomerScore({
          id: createBusinessData.customerId,
          districtCode: createBusinessData.intentionRegion,
        }).then(({ customerScore, customerScoreLevel }) => {
          form.setFieldsValue({
            customerScore,
            customerScoreLevel,
          });
        });
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [open]);

    const {
      data: fissionData,
      loading: getFissionLoading,
      mutate,
    } = useRequest(() => getFissionDetail(id), {
      ready: open && !!isEdit,
      onSuccess: (res) => {
        getCustomer(res.customerId);
        form.setFieldsValue({
          ...res,
          newStoreReserveManagers: res.newStoreReserveManagers?.map((i) => i.id),
          originalStoreReserveManagers: res.originalStoreReserveManagers?.map((i) => i.id),
        });
      },
    });
    const { runAsync: create, loading: createLoading } = useRequest(createFission, {
      manual: true,
    });
    const { runAsync: update, loading: updateLoading } = useRequest(updateFission, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        mutate(undefined);
        mutateCustomer(undefined);
      },
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      if (isEdit) {
        await update({ id, bizOpportunityId: fissionData?.bizOpportunityId, ...values });
        message.success('保存成功');
      } else {
        await create({
          bizOpportunityId: id,
          ...values,
        });
        message.success('提交成功');
      }

      setOpen(false);
      onSuccess();
    };

    const customerScoreLevelSuffix = getCustomerScoreLoading ? (
      <span className="text-primary text-xs">
        获取中
        <LoadingOutlined className="ml-2" />
      </span>
    ) : undefined;

    return (
      <Modal
        title={isEdit ? '编辑裂变申请' : '新建裂变申请'}
        open={open}
        loading={getFissionLoading || getCustomerLoading}
        styles={{
          body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
        }}
        destroyOnHidden
        okText={isEdit ? undefined : '提交审核'}
        confirmLoading={createLoading || updateLoading}
        onCancel={() => setOpen(false)}
        onOk={form.submit}
        modalRender={(node) => (
          <Form
            form={form}
            clearOnDestroy
            labelCol={{ span: 9 }}
            scrollToFirstError
            preserve={false}
            onFinish={handleSave}
          >
            {node}
          </Form>
        )}
        {...props}
      >
        <Form.Item label="客户名称" name="customerId" required>
          <RelCustomer
            defaultCustomerIdToNameMap={{
              [createBusinessData?.customerId || '']: createBusinessData?.customerName,
              [fissionData?.customerId || '']: fissionData?.customerName,
            }}
          />
        </Form.Item>
        <Form.Item
          label="联系方式"
          name="phone"
          rules={[{ required: true, message: '请输入联系方式' }]}
        >
          <Encrypt.PhoneSelect
            options={customer?.phones?.map((phone, index) => ({
              label: customer.phonesSensitive?.[index],
              value: phone,
            }))}
            encryptSensitiveMap={{
              [fissionData?.phone || '']: fissionData?.phoneSensitive,
            }}
          />
        </Form.Item>
        <Form.Item
          label="客户是否有评级"
          name="customerHasScoreLevel"
          rules={[{ required: true, message: '请选择客户是否有评级' }]}
        >
          <Select placeholder="请选择客户是否有评级" options={yesOrNoOptions} />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prev, next) => prev.customerHasScoreLevel !== next.customerHasScoreLevel}
        >
          {({ getFieldValue }) =>
            getFieldValue('customerHasScoreLevel') && (
              <>
                <Form.Item
                  label="客户评级"
                  name="customerScoreLevel"
                  rules={[{ required: true, message: '请选择客户评级' }]}
                >
                  <Select
                    allowClear
                    placeholder="请选择客户评级"
                    options={enum2Options(CustomerScoreLevelEnum)}
                    suffixIcon={customerScoreLevelSuffix}
                  />
                </Form.Item>
                <Form.Item
                  label="客户评分"
                  name="customerScore"
                  rules={[{ required: true, message: '请输入客户评分' }]}
                >
                  <InputNumber
                    placeholder="请输入客户评分"
                    precision={2}
                    min={0}
                    className="w-full"
                    suffix={customerScoreLevelSuffix}
                  />
                </Form.Item>
              </>
            )
          }
        </Form.Item>

        <Form.Item
          label="招商主管审核人"
          name="crmManagerUserId"
          rules={[{ required: true, message: '请选择招商主管审核人' }]}
        >
          <UserSelect placeholder="请选择招商主管审核人" />
        </Form.Item>
        <Form.Item label="门店类型" name="shopType" required>
          <Select disabled options={enum2Options(ShopTypeEnum)} />
        </Form.Item>
        <Form.Item noStyle shouldUpdate>
          {({ getFieldValue }) =>
            getFieldValue('shopType') === ShopTypeEnum.渠道店 && (
              <Form.Item
                label="渠道类型"
                name="channelTypes"
                rules={[{ required: true, message: '请选择渠道类型' }]}
              >
                <Select
                  mode="multiple"
                  placeholder="请选择渠道类型"
                  options={enum2Options(ChannelTypeEnum)}
                />
              </Form.Item>
            )
          }
        </Form.Item>
        <Form.Item
          label="裂变属性"
          name="fissionAttribute"
          rules={[{ required: true, message: '请选择裂变属性' }]}
        >
          <Select options={enum2Options(FissionAttributeEnum)} placeholder="请选择裂变属性" />
        </Form.Item>
        <EditableRegion
          editable
          formItemProps={{
            label: '意向区域',
            name: 'intentionRegion',
            rules: [{ required: true, message: '请选择意向区域' }],
          }}
          fieldProps={{
            regionLevel: 3,
            onChange: (value: any) => {
              form.setFieldValue('belongWarZone', provinceWarZoneMap[value?.[0]]);
              form.setFieldsValue({
                customerScore: undefined,
                customerScoreLevel: undefined,
              });

              if (value) {
                getCustomerScore({
                  id: form.getFieldValue('customerId'),
                  districtCode: value.join('/'),
                }).then(({ customerScore, customerScoreLevel }) => {
                  form.setFieldsValue({
                    customerScore,
                    customerScoreLevel,
                  });
                });
              }
            },
          }}
        />
        <Form.Item
          label="所属大区"
          name="belongWarZone"
          rules={[{ required: true, message: '请选择所属大区' }]}
        >
          <Select
            disabled
            placeholder="根据意向区域自动匹配"
            options={enum2Options(BelongWarZoneEnum)}
          />
        </Form.Item>
        <Form.Item label="意向点位" name="intentionPositions">
          <ChooseMapPoints />
        </Form.Item>
        <Form.Item
          label="计划开店数量"
          name="planStoreCount"
          initialValue={1}
          rules={[{ required: true, message: '请输入计划开店数量' }]}
        >
          <InputNumber placeholder="请输入" min={1} max={9} precision={0} />
        </Form.Item>
        <Form.Item
          label="新门店储备店长"
          name="newStoreReserveManagers"
          dependencies={['planStoreCount']}
          validateFirst
          rules={[
            {
              required: true,
              message: '请选择新门店储备店长',
            },
            {
              validator: (_, val) => {
                const value = val || [];

                if (value.length > form.getFieldValue('planStoreCount')) {
                  return Promise.reject(new Error('需要等于计划门店数量'));
                }

                if (value.some((i: any) => !i)) {
                  return Promise.reject(new Error('请将所有店长都选择完毕'));
                }

                const hasIdValue = value.filter(Boolean);

                if (hasIdValue.length !== [...new Set(hasIdValue)].length) {
                  return Promise.reject(new Error('不能重复'));
                }

                return Promise.resolve();
              },
            },
          ]}
        >
          <RelCustomerList
            placeholder="请选择店长"
            getCheckboxProps={(record) => ({ disabled: !record.identityCard })}
            tip={<Alert showIcon message="只允许选择有身份证的客户" className="mr-2" />}
            defaultCustomerIdToNameMap={
              isEdit
                ? fissionData?.newStoreReserveManagers?.reduce(
                    (total, cur) => ({
                      ...total,
                      [cur.id]: cur.name,
                    }),
                    {},
                  )
                : {}
            }
          />
        </Form.Item>
        <Form.Item
          label="新店长是原门店员工数"
          name="originalStoreManagerCount"
          validateFirst
          dependencies={['planStoreCount']}
          initialValue={0}
          rules={[
            { required: true, message: '请输入新店长是原门店员工数' },
            {
              validator: (_, value) => {
                if (value > form.getFieldValue('planStoreCount')) {
                  return Promise.reject(new Error('不能大于计划开店数量'));
                }

                return Promise.resolve();
              },
            },
          ]}
        >
          <InputNumber min={0} precision={0} placeholder="请输入" />
        </Form.Item>
        <Form.Item noStyle shouldUpdate>
          {({ getFieldValue }) =>
            getFieldValue('originalStoreManagerCount') > 0 && (
              <Form.Item
                label="原门店储备人员"
                name="originalStoreReserveManagers"
                dependencies={['originalStoreManagerCount']}
                rules={[
                  {
                    required: true,
                    message: '请选择原门店储备人员',
                  },
                  {
                    validator: (_, val) => {
                      const value = val || [];

                      if (value.length > form.getFieldValue('originalStoreManagerCount')) {
                        return Promise.reject(new Error('需要等于新店长是原门店员工数'));
                      }

                      if (value.some((i: any) => !i)) {
                        return Promise.reject(new Error('请将所有店长都选择完毕'));
                      }

                      const hasIdValue = value.filter(Boolean);

                      if (hasIdValue.length !== [...new Set(hasIdValue)].length) {
                        return Promise.reject(new Error('不能重复'));
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <RelCustomerList
                  placeholder="请选择店长"
                  tip={<Alert showIcon message="只允许选择有身份证的客户" className="mr-2" />}
                  defaultCustomerIdToNameMap={
                    isEdit
                      ? fissionData?.originalStoreReserveManagers?.reduce(
                          (total, cur) => ({
                            ...total,
                            [cur.id]: cur.name,
                          }),
                          {},
                        )
                      : {}
                  }
                  getCheckboxProps={(record) => ({ disabled: !record.identityCard })}
                />
              </Form.Item>
            )
          }
        </Form.Item>
        <Form.Item
          label="名下关联全部门店"
          name="relStore"
          rules={[{ required: true, message: '请输入名下关联全部门店' }]}
        >
          <Input.TextArea placeholder="请输入名下关联全部门店" autoSize />
        </Form.Item>
        <Form.Item label="备注" name="remark">
          <Input.TextArea maxLength={255} placeholder="请输入备注" autoSize />
        </Form.Item>
      </Modal>
    );
  },
);

export default CreateEditFissionModal;
