import { useEffect, useRef, useState } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { fissionAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ExportButton, ProTable, TableActions, UserSelect } from '@src/components';
import EditableMultiple from '@src/components/Editable/EditableMultiple';
import EditablePhone from '@src/components/Editable/EditablePhone';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useAllUsers from '@src/hooks/useAllUsers';
import useCheckVisibleAuth from '@src/hooks/useCheckVisibleAuth';
import { useNoticeEventSubscription } from '@src/layout';
import { Permission, PermissionsMap } from '@src/permissions';
import { BelongWarZoneEnum, ChannelTypeEnum } from '@src/services/business-opportunity/type';
import { AuditStatusEnum } from '@src/services/common/type';
import { StoreCategoryEnum } from '@src/services/contract/formal/type';
import { CustomerScoreLevelEnum } from '@src/services/customers/type';
import {
  exportFission,
  getFissionList,
  passFissionAudit,
  submitFissionAudit,
} from '@src/services/fission';
import {
  ExportFissionReq,
  FissionAttributeEnum,
  FissionDTO,
  ShopTypeEnum,
} from '@src/services/fission/type';
import {
  compatibleTableActionWidth,
  enum2Options,
  enum2ValueEnum,
  optionsToValueEnum,
} from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Popover, theme, Typography } from 'antd';
import Overflow from 'rc-overflow';
import { useSearchParams } from 'react-router-dom';
import CreateEditFissionModal from './components/CreateEditFissionModal';
import FissionDetailDrawer from './components/FissionDetailDrawer';
import BusinessDetailDrawer from '../business-opportunity/components/BusinessDetailDrawer';
import CustomerDetailDrawer from '../customers/components/CustomerDetailDrawer';

const Fission = () => {
  const { modal, message } = App.useApp();
  const [searchParams, setSearchParams] = useSearchParams();
  const { token } = theme.useToken();
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const actionRef = useRef<ActionType>(null);
  const fissionIdRef = useRef<number | null>(null);
  const fissionDetailDrawerRef = useRef<ModalRef>(null);
  const customerIdRef = useRef<number | null>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const businessIdRef = useRef<number | null>(null);
  const businessDetailDrawerRef = useRef<ModalRef>(null);
  const filterParamsRef = useRef<ExportFissionReq>({});
  const createEditFissionModalRef = useRef<ModalRef>(null);

  const checkVisibleAuth = useCheckVisibleAuth();
  const { data, runAsync: getList } = useRequest(getFissionList, { manual: true });
  const { data: users } = useAllUsers();

  useNoticeEventSubscription((event) => {
    if (event.type === 'fission' && data?.result.some((i) => i.id === event.payload.id)) {
      actionRef.current?.reload();
    }
  });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      fissionIdRef.current = Number(id);
      fissionDetailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSubmitAudit = (record: FissionDTO) => {
    modal.confirm({
      title: '提交审核',
      content: '确定要提交审核吗？',
      onOk: async () => {
        if (record.auditStatus === AuditStatusEnum.RETURN) {
          await passFissionAudit({ id: record.id });
        } else {
          await submitFissionAudit(record.id);
        }

        message.success('提交审核成功');
        actionRef.current?.reload();
      },
    });
  };

  const renderOverflow = (items?: { id: number; name: string }[]) => (
    <Overflow
      className="flex"
      maxCount="responsive"
      data={items || []}
      renderItem={(item) => (
        <a
          className={items?.findIndex((i) => i.id === item.id) === 0 ? '' : 'ml-2'}
          onClick={() => {
            customerIdRef.current = item.id;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {item.name}
        </a>
      )}
      renderRest={(restItems) => (
        <Popover
          // 比 Drawer 小 1
          zIndex={token.zIndexPopupBase - 1}
          destroyOnHidden
          content={
            <div className="flex gap-2">
              {restItems.map((item) => (
                <a
                  key={item.id}
                  onClick={() => {
                    customerIdRef.current = item.id;
                    customerDetailDrawerRef.current?.open();
                  }}
                >
                  {item.name}
                </a>
              ))}
            </div>
          }
        >
          <span className="cursor-pointer text-gray-500 ml-2">+{restItems.length}</span>
        </Popover>
      )}
    />
  );

  const columns: ProColumns<FissionDTO>[] = [
    {
      title: '裂变申请编号',
      dataIndex: 'code',
      fixed: 'left',
      search: false,
      renderText: (value, { id }) => (
        <a
          onClick={() => {
            fissionIdRef.current = id;
            fissionDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      renderText: (value, { customerId }) => (
        <Typography.Text
          style={{ color: 'var(--ant-color-link)', cursor: 'pointer' }}
          ellipsis={{ tooltip: true }}
          onClick={() => {
            customerIdRef.current = customerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </Typography.Text>
      ),
    },
    {
      title: '客户性质',
      dataIndex: 'customerNature',
      valueEnum: enum2ValueEnum(StoreCategoryEnum),
      search: false,
    },
    {
      title: '商机名称',
      dataIndex: 'bizOpportunityName',
      renderText: (value, { bizOpportunityId }) => (
        <Typography.Text
          style={{ color: 'var(--ant-color-link)', cursor: 'pointer' }}
          ellipsis={{ tooltip: true }}
          onClick={() => {
            businessIdRef.current = bizOpportunityId;
            businessDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </Typography.Text>
      ),
    },
    {
      title: '商机编号',
      dataIndex: 'bizOpportunityCode',
      search: false,
    },
    {
      title: '联系方式',
      dataIndex: 'phone',
      search: false,
      render: (_, { phone, phoneSensitive }) => (
        <EditablePhone value={phone} fieldProps={{ sensitiveValue: phoneSensitive }} />
      ),
    },
    {
      title: '商机负责人',
      dataIndex: 'bizOpportunityDirectUserId',
      renderFormItem: () => <UserSelect />,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      search: false,
      valueEnum: optionsToValueEnum(fissionAuditStatusOptions),
    },
    {
      title: '门店类型',
      dataIndex: 'shopType',
      valueEnum: enum2ValueEnum(ShopTypeEnum),
    },
    {
      title: '招商主管审核人',
      dataIndex: 'crmManagerUserId',
      search: false,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '客户是否有评级',
      dataIndex: 'customerHasScoreLevel',
      search: false,
      valueEnum: optionsToValueEnum(yesOrNoOptions, true),
    },
    {
      title: '客户评级',
      dataIndex: 'customerScoreLevel',
      search: false,
      valueEnum: enum2ValueEnum(CustomerScoreLevelEnum),
    },
    {
      title: '客户评分',
      dataIndex: 'customerScore',
      search: false,
    },
    {
      title: '渠道类型',
      dataIndex: 'channelTypes',
      search: false,
      renderText: (value) => (
        <EditableMultiple value={value} fieldProps={{ options: enum2Options(ChannelTypeEnum) }} />
      ),
    },
    {
      title: '裂变属性',
      dataIndex: 'fissionAttribute',
      valueEnum: enum2ValueEnum(FissionAttributeEnum),
    },
    {
      title: '意向区域',
      dataIndex: 'intentionRegion',
      width: 220,
      search: false,
      ellipsis: true,
      renderText: (value) => <EditableRegion value={value} fieldProps={{ regionLevel: 3 }} />,
    },
    {
      title: '所属大区',
      dataIndex: 'belongWarZone',
      search: false,
      valueEnum: enum2ValueEnum(BelongWarZoneEnum),
    },
    {
      title: '计划开店数量',
      dataIndex: 'planStoreCount',
      search: false,
    },
    {
      title: '新门店储备店长',
      dataIndex: 'newStoreReserveManagers',
      search: false,
      renderText: (value) => renderOverflow(value),
    },
    {
      title: '新店长是原门店员工数',
      dataIndex: 'originalStoreManagerCount',
      search: false,
    },
    {
      title: '原门店储备人员',
      dataIndex: 'originalStoreReserveManagers',
      search: false,
      renderText: (value) => renderOverflow(value),
    },
    {
      title: '名下关联全部门店',
      dataIndex: 'relStore',
      search: false,
      ellipsis: true,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      search: false,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      search: false,
    },
    {
      title: '更新人',
      dataIndex: 'updateUserId',
      search: false,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      search: false,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      search: false,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      fixed: 'right',
      search: false,
      width: compatibleTableActionWidth(120),
      render: (_, record) => {
        const { id, createUserId, auditStatus } = record;

        const show =
          checkVisibleAuth(createUserId) &&
          [AuditStatusEnum.NOT_SUBMIT, AuditStatusEnum.NO_PASS].includes(auditStatus);

        return (
          <TableActions
            shortcuts={[
              {
                label: '编辑',
                show,
                onClick: () => {
                  fissionIdRef.current = id;
                  createEditFissionModalRef.current?.open();
                },
              },
              {
                label: '提交审核',
                show,
                onClick: () => handleSubmitAudit(record),
              },
            ]}
          />
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        columns={columns}
        options={false}
        cardProps={false}
        bordered
        sticky
        size="small"
        columnEmptyText=""
        rowKey="id"
        tableAlertRender={false}
        scroll={{
          x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0),
        }}
        toolbar={{
          subTitle: (
            <span className="text-black">
              共 {data?.total || 0} 条裂变申请
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 条
                  <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            <Permission value={PermissionsMap.FissionExport}>
              <ExportButton
                total={selectedRowKeys.length || data?.total}
                request={() =>
                  exportFission(
                    selectedRowKeys.length > 0 ? { ids: selectedRowKeys } : filterParamsRef.current,
                  )
                }
              />
            </Permission>,
          ],
        }}
        rowSelection={{
          showSort: true,
          fixed: true,
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        request={async ({ current, pageSize, ...params }) => {
          filterParamsRef.current = params as ExportFissionReq;

          const res = await getList({ pageNum: current, pageSize, ...params });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <FissionDetailDrawer
        ref={fissionDetailDrawerRef}
        getId={() => fissionIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
      />
      <CustomerDetailDrawer
        ref={customerDetailDrawerRef}
        getId={() => customerIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
      />
      <BusinessDetailDrawer
        ref={businessDetailDrawerRef}
        getId={() => businessIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
      />
      <CreateEditFissionModal
        ref={createEditFissionModalRef}
        getId={() => fissionIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
    </PageContainer>
  );
};

export default Fission;
