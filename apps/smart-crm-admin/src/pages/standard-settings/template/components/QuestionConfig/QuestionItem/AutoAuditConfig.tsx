import React from 'react';
import { FranchiseRejectOrReviewReasonEnum } from '@src/services/franchise-application/type';
import { enum2Options } from '@src/utils';
import { Button, Divider, Input, InputNumber, Select, Space, Switch } from 'antd';
import ExclamationTooltip from '../ExclamationTooltip';
import { TemplateQuestionType, TemplateQuestionTypeEnum } from '../type';
import { questionTypeAutoAuditConditionOptionsMap } from '../utils';

interface AutoAuditConfigProps {
  item: TemplateQuestionType;
  show?: boolean;
  onChange: (value: Partial<TemplateQuestionType>) => void;
}

const AutoAuditConfig: React.FC<AutoAuditConfigProps> = ({ item, show, onChange }) => {
  if (!show || !Object.keys(questionTypeAutoAuditConditionOptionsMap).includes(item.questionType)) {
    return null;
  }

  const renderComponent = () => {
    if (
      [TemplateQuestionTypeEnum.SINGLE, TemplateQuestionTypeEnum.MULTIPLE].includes(
        item.questionType,
      )
    ) {
      const valueArray = item.autoAuditInfo?.value?.split(',');
      const options = item.options?.filter((i) => !i.deleted);

      return (
        <Select
          className="w-full"
          allowClear
          placeholder="请选择"
          mode="multiple"
          value={valueArray}
          options={options}
          labelRender={({ value }) =>
            options?.find((option) => option.value === value)?.label || (
              <ExclamationTooltip title="选项被删除，请重新选择" />
            )
          }
          onChange={(value) =>
            onChange({
              autoAuditInfo: {
                ...item.autoAuditInfo!,
                value: value.length ? value.join(',') : undefined,
              },
            })
          }
        />
      );
    }

    if (item.questionType === TemplateQuestionTypeEnum.NUMBER) {
      return (
        <InputNumber
          className="w-full"
          placeholder="请输入"
          stringMode
          controls={false}
          value={item.autoAuditInfo?.value}
          onChange={(value) =>
            onChange({ autoAuditInfo: { ...item.autoAuditInfo!, value: value as string } })
          }
        />
      );
    }

    if (item.questionType === TemplateQuestionTypeEnum.TEXT) {
      return (
        <Input
          placeholder="请输入内容"
          defaultValue={item.autoAuditInfo?.value}
          onChange={(e) =>
            onChange({ autoAuditInfo: { ...item.autoAuditInfo!, value: e.target.value } })
          }
        />
      );
    }

    if (item.questionType === TemplateQuestionTypeEnum.IDENTITY_CARD) {
      const valueArray = item.autoAuditInfo?.value?.split(',') || [];
      const [preValue, lastValue] = valueArray;

      return (
        <Space.Compact className="w-full">
          <InputNumber
            placeholder="请输入"
            precision={0}
            min="0"
            value={preValue}
            controls={false}
            suffix={<span />}
            onChange={(value) => {
              const result = [...valueArray];

              result[0] = value as string;
              onChange({ autoAuditInfo: { ...item.autoAuditInfo!, value: result.join(',') } });
            }}
          />
          <Button disabled className="px-2">
            ~
          </Button>
          <InputNumber
            placeholder="请输入"
            precision={0}
            min="0"
            value={lastValue}
            controls={false}
            suffix={
              preValue && (!lastValue || Number(preValue) > Number(lastValue)) ? (
                <ExclamationTooltip title="必须大于等于前面的值" className="pointer-events-auto" />
              ) : (
                <span />
              )
            }
            onChange={(value) => {
              const result = [...valueArray];

              result[1] = value as string;
              onChange({ autoAuditInfo: { ...item.autoAuditInfo!, value: result.join(',') } });
            }}
          />
        </Space.Compact>
      );
    }

    if (item.questionType === TemplateQuestionTypeEnum.DATE) {
      return (
        <Select
          className="w-full"
          placeholder="请选择"
          value={item.autoAuditInfo?.value}
          options={[
            { label: '一年内', value: 'ONE_YEAR' },
            { label: '半年内', value: 'HALF_YEAR' },
          ]}
          onChange={(value) =>
            onChange({
              autoAuditInfo: {
                ...item.autoAuditInfo!,
                value,
              },
            })
          }
        />
      );
    }
  };

  return (
    <>
      <Divider className="my-4" />
      <Switch
        size="small"
        className="mr-1"
        value={!!item.autoAuditInfo}
        onChange={(checked) => {
          if (checked) {
            onChange({
              autoAuditInfo: {
                condition: questionTypeAutoAuditConditionOptionsMap[item.questionType]?.[0].value,
              },
            });
          } else {
            onChange({ autoAuditInfo: undefined });
          }
        }}
      />
      自动审核
      {item.autoAuditInfo && (!item.autoAuditInfo.value || !item.autoAuditInfo?.rejectReason) && (
        <ExclamationTooltip title="请完善以下内容" className="ml-2" />
      )}
      {item.autoAuditInfo && (
        <div className="flex gap-2 mt-2">
          <Select
            style={{ width: 100, flexShrink: 0 }}
            value={item.autoAuditInfo.condition}
            options={questionTypeAutoAuditConditionOptionsMap[item.questionType]}
            onChange={(condition) =>
              onChange({ autoAuditInfo: { ...item.autoAuditInfo, condition } })
            }
          />
          <div style={{ width: 200, flexShrink: 0 }}>{renderComponent()}</div>
          <Select
            style={{ width: 160, flexShrink: 0 }}
            placeholder="请选择不通过原因"
            showSearch
            optionFilterProp="label"
            value={item.autoAuditInfo.rejectReason}
            options={enum2Options(FranchiseRejectOrReviewReasonEnum)}
            onChange={(rejectReason) =>
              onChange({ autoAuditInfo: { ...item.autoAuditInfo!, rejectReason } })
            }
          />
        </div>
      )}
    </>
  );
};

export default AutoAuditConfig;
