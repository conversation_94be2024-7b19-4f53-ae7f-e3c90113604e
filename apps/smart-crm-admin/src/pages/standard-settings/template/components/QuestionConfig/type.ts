import { FieldConfigType } from '@src/services/common/type';
import { FranchiseRejectOrReviewReasonEnum } from '@src/services/franchise-application/type';
import { QuestionConditionEnum } from '@src/services/standard-settings/template/type';
import { QuestionRuleEnum } from '@src/services/visit/type';

export enum TemplateQuestionTypeEnum {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  PHONE = 'PHONE',
  IDENTITY_CARD = 'IDENTITY_CARD',
  DATE = 'DATE',
  DATE_RANGE = 'DATE_RANGE',
  SINGLE = 'SINGLE',
  MULTIPLE = 'MULTIPLE',
  ATTACHMENT = 'ATTACHMENT',
  REGION = 'REGION',
  GROUP = 'GROUP',
  COMPONENT = 'COMPONENT',
}

export type TemplateQuestionType = {
  name: string;
  id?: number;
  /** 唯一标识，可能会改变（拖拽取消时，需要重新生成一个，否则之后无法拖拽） */
  uniqId: string;
  /** 唯一标识，不会改变 */
  questionKey: string;
  questionType: TemplateQuestionTypeEnum;
  notNull?: boolean;
  description?: string;
  ruleInfo?: {
    rule: QuestionRuleEnum;
    questionKey: string;
    options?: string[];
  }[];
  autoAuditInfo?: {
    condition: QuestionConditionEnum;
    value?: string;
    rejectReason?: FranchiseRejectOrReviewReasonEnum;
  };
  childList?: TemplateQuestionType[];
} & Omit<FieldConfigType, 'options'> & {
    options?: { id?: number; deleted?: boolean; label: string; value: string }[];
  };

export type NewQuestionBlockCommonType = {
  label: string;
  icon?: React.FunctionComponent<any>;
  id: string;
  value: TemplateQuestionTypeEnum;
  getDefaultConfig?: () => Record<string, any>;
};

export type NewQuestionBlockComponentType = {
  label: string;
  icon: React.FunctionComponent<any>;
  value: TemplateQuestionTypeEnum;
  children: {
    label: string;
    id: string;
    getDefaultConfig?: () => Record<string, any>;
  }[];
};

export type NewQuestionBlockType = NewQuestionBlockCommonType | NewQuestionBlockComponentType;
