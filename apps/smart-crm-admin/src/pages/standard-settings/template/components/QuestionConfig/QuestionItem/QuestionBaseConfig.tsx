import React, { useState } from 'react';
import { CloseCircleFilled, PlusOutlined } from '@ant-design/icons';
import { RegionModeEnum } from '@src/services/common/type';
import { App, Button, Form, Input, Radio } from 'antd';
import { nanoid } from 'nanoid';
import { useParams } from 'react-router-dom';
import ExclamationTooltip from '../ExclamationTooltip';
import { TemplateQuestionType, TemplateQuestionTypeEnum } from '../type';

interface QuestionBaseConfigProps {
  item: TemplateQuestionType;
  index: number;
  onChange: (value: Partial<TemplateQuestionType>, index: number) => void;
  getHrefWhenOptionDelete?: (id: number) => string;
  onCheckOptionDelete?: (params: {
    id?: number;
    value: string;
  }) => Promise<{ id: number; name: string }[]>;
}

const QuestionBaseConfig: React.FC<QuestionBaseConfigProps> = ({
  item,
  index,
  onChange,
  getHrefWhenOptionDelete,
  onCheckOptionDelete,
}) => {
  const { id } = useParams<{ id: string }>();
  const { modal } = App.useApp();

  const [deleteOptionLoadingMap, setDeleteOptionLoadingMap] = useState<Record<string, boolean>>({});

  const showOptions = item.options?.filter((i) => !i.deleted);

  const deleteOption = async (option: { label: string; value: string; id?: number }) => {
    if (onCheckOptionDelete) {
      // 选项没有 id 说明是新创建的，可以直接删除
      if (!option.id) {
        onChange(
          {
            options: item.options?.filter((i) => i.value !== option.value),
          },
          index,
        );
        // 如果选项是保存过的，需要发请求校验下是否关联了其它试卷
      } else {
        setDeleteOptionLoadingMap((prev) => ({
          ...prev,
          [option.value]: true,
        }));

        try {
          const res = await onCheckOptionDelete({
            id: id ? Number(id) : undefined,
            value: option.value,
          });

          if (!res.length) {
            onChange(
              {
                options: item.options?.map((i) => {
                  if (i.value === option.value) {
                    return { ...i, deleted: true };
                  }

                  return i;
                }),
              },
              index,
            );
          } else {
            modal.warning({
              title: '提示',
              width: 500,
              modalRender: (node) => (
                // 阻止冒泡，否则会将此试题变为非编辑状态
                <div onClick={(e) => e.stopPropagation()}>{node}</div>
              ),
              content: (
                <div onClick={(e) => e.stopPropagation()} className="max-h-[400px] overflow-auto">
                  此选项在以下表中存在条件关联，请先解除后再删除此选项。
                  <div className="flex flex-col items-start mt-2 gap-1">
                    {res.map((i) => (
                      <a
                        key={i.id}
                        type="link"
                        href={getHrefWhenOptionDelete?.(i.id)}
                        target="_blank"
                        rel="noreferrer"
                      >
                        {i.name}
                      </a>
                    ))}
                  </div>
                </div>
              ),
            });
          }
        } catch (error) {}

        setDeleteOptionLoadingMap((prev) => {
          const result = { ...prev };

          delete result[option.value];

          return result;
        });
      }
    } else {
      onChange(
        {
          options: item.options?.map((i) => {
            if (i.value === option.value) {
              return { ...i, deleted: true };
            }

            return i;
          }),
        },
        index,
      );
    }
  };

  return (
    <div className="flex flex-col gap-5">
      <Form.Item required label="表单问题" labelCol={{ span: 4 }} className="mb-0">
        <Input
          placeholder="请输入表单问题"
          defaultValue={item.name}
          maxLength={50}
          status={!item.name ? 'warning' : undefined}
          suffix={!item.name ? <ExclamationTooltip title="表单问题不能为空" /> : <span />}
          onChange={(e) => onChange({ name: e.target.value }, index)}
        />
      </Form.Item>
      <Form.Item label="表单描述" labelCol={{ span: 4 }} className="mb-0">
        <Input.TextArea
          autoSize
          placeholder="请输入表单描述"
          maxLength={150}
          defaultValue={item.description}
          onChange={(e) => onChange({ description: e.target.value }, index)}
        />
      </Form.Item>
      {item.questionType === TemplateQuestionTypeEnum.NUMBER && (
        <Form.Item label="数字类型" labelCol={{ span: 4 }} className="mb-0">
          <Radio.Group
            // 有 id 说明是保存过的题目，无法更改
            disabled={!!item.id}
            value={item.precisions}
            options={[
              { label: '整数', value: 0 },
              { label: '一位小数', value: 1 },
              { label: '两位小数', value: 2 },
            ]}
            onChange={(e) => onChange({ precisions: e.target.value }, index)}
          />
        </Form.Item>
      )}
      {item.questionType === TemplateQuestionTypeEnum.REGION && (
        <>
          <Form.Item required label="区域类型" className="mb-0">
            <Radio.Group
              // 有 id 说明是保存过的题目，无法更改
              disabled={!!item.id}
              value={item.regionLevel}
              options={[
                { label: '省份', value: 1 },
                { label: '城市', value: 2 },
                { label: '区县', value: 3 },
              ]}
              onChange={(e) => onChange({ regionLevel: e.target.value }, index)}
            />
          </Form.Item>
          <Form.Item required label="选择类型" className="mb-0">
            <Radio.Group
              // 有 id 说明是保存过的题目，无法更改
              disabled={!!item.id}
              value={item.regionMode}
              options={[
                { label: '单选', value: RegionModeEnum.SINGLE },
                { label: '多选', value: RegionModeEnum.MULTIPLE },
              ]}
              onChange={(e) => onChange({ regionMode: e.target.value }, index)}
            />
          </Form.Item>
        </>
      )}
      {[TemplateQuestionTypeEnum.SINGLE, TemplateQuestionTypeEnum.MULTIPLE].includes(
        item.questionType,
      ) && (
        <Form.Item label="选项内容" required className="mb-0">
          <div className="flex flex-col items-start gap-2">
            <div className="flex items-center gap-2">
              <Button
                type="text"
                className="!text-primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  onChange(
                    {
                      options: (item.options || []).concat({
                        label: '',
                        value: nanoid(),
                      }),
                    },
                    index,
                  );
                }}
              >
                添加选项
              </Button>
              {!showOptions?.length && <ExclamationTooltip title="至少需要添加一个选项" />}
            </div>
            {showOptions?.map((option) => {
              const emptyWarning = !option.label;
              const repeatWarning =
                (showOptions || []).filter((i) => i.label === option.label).length > 1;

              return (
                <div key={option.value} className="flex gap-2 w-full">
                  <Input
                    maxLength={40}
                    defaultValue={option.label}
                    placeholder="请输入选项"
                    status={emptyWarning || repeatWarning ? 'warning' : undefined}
                    suffix={
                      emptyWarning ? (
                        <ExclamationTooltip title="选项内容不能为空" />
                      ) : repeatWarning ? (
                        <ExclamationTooltip title="选项内容重复" />
                      ) : (
                        <span />
                      )
                    }
                    onChange={(e) => {
                      const newOptions = [...(item.options || [])];
                      const optionIndex = newOptions.findIndex((i) => i.value === option.value);

                      newOptions[optionIndex].label = e.target.value;
                      onChange({ options: newOptions }, index);
                    }}
                  />
                  <Button
                    icon={<CloseCircleFilled className="text-gray-400" />}
                    loading={deleteOptionLoadingMap[option.value]}
                    type="text"
                    onClick={() => deleteOption(option)}
                  />
                </div>
              );
            })}
          </div>
        </Form.Item>
      )}
    </div>
  );
};

export default QuestionBaseConfig;
