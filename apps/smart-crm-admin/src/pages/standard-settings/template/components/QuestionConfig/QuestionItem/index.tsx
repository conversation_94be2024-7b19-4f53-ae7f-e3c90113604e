import React from 'react';
import { HolderOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import { <PERSON><PERSON>, <PERSON><PERSON>, Switch, Tooltip } from 'antd';
import classNames from 'classnames';
import AutoAuditConfig from './AutoAuditConfig';
import ChildList from './ChildList';
import QuestionBaseConfig from './QuestionBaseConfig';
import RuleInfoConfig from './RuleInfoConfig';
import { TemplateQuestionType } from '../type';
import { getIsWarning, questionTypeOptions } from '../utils';

interface QuestionItemProps {
  isEditing: boolean;
  index: number;
  data: TemplateQuestionType[];
  isOverlay?: boolean;
  dragHandleProps?: {
    ref: (element: HTMLElement | null) => void;
  } & SyntheticListenerMap;
  getHrefWhenOptionDelete?: (id: number) => string;
  hasAutoAudit?: boolean;
  deletable?: boolean;
  sortable?: boolean;
  onChange?: (item: TemplateQuestionType) => void;
  onDelete?: () => void;
  onCheckOptionDelete?: (params: {
    id?: number;
    value: string;
  }) => Promise<{ id: number; name: string }[]>;
}

const QuestionItem: React.FC<QuestionItemProps> = ({
  isEditing,
  index,
  data,
  isOverlay,
  deletable,
  sortable,
  dragHandleProps,
  getHrefWhenOptionDelete,
  hasAutoAudit,
  onChange,
  onDelete,
  onCheckOptionDelete,
}) => {
  const item = data[index];

  const onInternalChange = (value: Partial<TemplateQuestionType>) => {
    onChange?.({ ...item, ...value });
  };

  const isWarning = getIsWarning(data, index);

  const questionTypeOption = questionTypeOptions.find((i) => i.value === item.questionType);
  const Icon = questionTypeOption?.icon;

  return (
    <div className="w-full">
      <div
        className={classNames('flex justify-between px-3 pt-3 pb-2', {
          'bg-gray-200/40': isEditing,
        })}
      >
        <div className="flex flex-1 items-start">
          {sortable && (
            <Button
              size="small"
              type="text"
              className={classNames('cursor-grab mr-3', { 'cursor-grabbing': isOverlay })}
              icon={<HolderOutlined />}
              {...dragHandleProps}
            />
          )}
          {item.name && (
            <div
              className={classNames({
                'ml-8': !sortable,
                "relative flex-1 before:content-['*'] before:absolute before:text-red-500 before:-left-2 before:font-[SimSun,sans-serif]":
                  item.notNull,
              })}
            >
              {item.name}
            </div>
          )}
        </div>
        <div className="flex items-start ml-2">
          <div className="flex items-center">
            <Switch
              size="small"
              value={item.notNull}
              onChange={(checked, e) => {
                e.stopPropagation();
                onInternalChange({ notNull: checked });
              }}
            />
            <span className="ml-2 mr-3">必填</span>
            {deletable && (
              <Tooltip title="移出表单">
                <Button
                  type="text"
                  size="small"
                  icon={<MinusCircleOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete?.();
                  }}
                />
              </Tooltip>
            )}
          </div>
        </div>
      </div>
      {isEditing ? (
        <div className="pt-5 px-8 pb-5">
          <QuestionBaseConfig
            item={item}
            index={index}
            onChange={onInternalChange}
            getHrefWhenOptionDelete={getHrefWhenOptionDelete}
            onCheckOptionDelete={onCheckOptionDelete}
          />
          <RuleInfoConfig index={index} data={data} onChange={onInternalChange} />
          <AutoAuditConfig item={item} show={hasAutoAudit} onChange={onInternalChange} />
          <ChildList
            data={item}
            hasAutoAudit={hasAutoAudit}
            onChange={onInternalChange}
            getHrefWhenOptionDelete={getHrefWhenOptionDelete}
            onCheckOptionDelete={onCheckOptionDelete}
          />
        </div>
      ) : (
        <div className="px-12 pb-2">
          {item.description && <div className="text-gray-400 break-all">{item.description}</div>}
          <div className="text-gray-500 mt-2">
            {Icon && <Icon className="mr-1" />}
            {questionTypeOption?.label}
          </div>
          {isWarning ? (
            <Alert
              showIcon
              type="warning"
              message="当前问题配置出错，请重新配置"
              className="py-1 my-3"
            />
          ) : (
            <>
              {(item.ruleInfo || []).length > 0 && (
                <Alert
                  showIcon
                  type="info"
                  className="py-1 my-3"
                  message="当前问题仅会在满足条件的时候展示"
                />
              )}
              {hasAutoAudit && item.autoAuditInfo && (
                <Alert showIcon type="info" className="py-1 my-3" message="当前问题存在自动审核" />
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default QuestionItem;
