import { useCallback, useEffect, useRef, useState } from 'react';
import {
  Active,
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import backgroundImage from '@src/assets/question-cover.svg';
import { PageListItemType, QuestionItemType } from '@src/services/standard-settings/template/type';
import { App, Card, Form, FormInstance, Input, Skeleton } from 'antd';
import { nanoid } from 'nanoid';
import { useEvent } from 'rc-util';
import { Virtuoso, VirtuosoHandle } from 'react-virtuoso';
import EmptyDroppable from './EmptyDroppable';
import LeftBar from './LeftBar';
import SearchSelect from './SearchSelect';
import SortableItem from './SortableItem';
import { NewQuestionBlockType, TemplateQuestionType, TemplateQuestionTypeEnum } from './type';
import {
  generateGroupItem,
  getGroupIndex,
  getIsWarning,
  getQuestionsByGroupId,
  questionTypeOptions,
  swapData,
} from './utils';

interface QuestionConfigProps {
  form: FormInstance<any>;
  data: TemplateQuestionType[];
  optionalQuestions: TemplateQuestionType[];
  // 左侧可选题目 loading
  optionalQuestionsLoading: boolean;
  // 主区域 loading
  mainLoading: boolean;
  // 删除单选、多选的选项时，有提示时快捷跳转的地址
  getHrefWhenOptionDelete?: (id: number) => string;
  // 是否有自动审核
  hasAutoAudit?: boolean;
  /** 需要隐藏的试题 */
  hiddenQuestionTypes?: TemplateQuestionTypeEnum[];
  // 删除单选、多选的选项时进行检测
  onCheckOptionDelete?: (params: {
    id?: number;
    value: string;
  }) => Promise<{ id: number; name: string }[]>;
  onDataChange: React.Dispatch<React.SetStateAction<TemplateQuestionType[]>>;
  onOptionalQuestionsChange: React.Dispatch<React.SetStateAction<TemplateQuestionType[]>>;
  onSave: (value: {
    name: string;
    pageList: (PageListItemType | { pageId: number; deleted: boolean })[];
  }) => void;
}

function getDefaultNewQuestionOptions(
  hiddenQuestionTypes?: TemplateQuestionTypeEnum[],
): NewQuestionBlockType[] {
  return questionTypeOptions
    .filter((item) => !item.hidden && !hiddenQuestionTypes?.includes(item.value))
    .map((item) => {
      if (Array.isArray(item.children) && item.children.length > 0) {
        return { ...item, children: item.children.map((child) => ({ ...child, id: nanoid() })) };
      }

      return { ...item, id: nanoid() };
    });
}

const QuestionConfig: React.FC<QuestionConfigProps> = ({
  form,
  data,
  optionalQuestions,
  optionalQuestionsLoading,
  mainLoading,
  getHrefWhenOptionDelete,
  hasAutoAudit,
  hiddenQuestionTypes,
  onCheckOptionDelete,
  onDataChange,
  onOptionalQuestionsChange,
  onSave,
}) => {
  const { message } = App.useApp();
  const [activeItem, setActiveItem] = useState<Active | null>(null);
  const [newQuestionOptions, setNewQuestionOptions] = useState(() =>
    getDefaultNewQuestionOptions(hiddenQuestionTypes),
  );
  // 存储正在编辑的试题，使得虚拟滚动后能保存编辑状态
  const [isEditingUniqId, setIsEditingUniqId] = useState<string | null>(null);
  const [collapseKeys, setCollapseKeys] = useState<string[]>([]);
  const virtuosoRef = useRef<VirtuosoHandle>(null);
  const [enableTransition, setEnableTransition] = useState(true);
  const [deletedGroupIds, setDeletedGroupIds] = useState<number[]>([]);

  const isLeftDragging = !!activeItem?.data.current?.type;

  useEffect(() => {
    if (!enableTransition) {
      setEnableTransition(true);
    }
  }, [enableTransition]);

  // 过滤掉折叠的题目
  const showData = data.filter((item, index) => {
    const groupIndex = getGroupIndex(data, index);

    if (
      // 这题没有分组
      groupIndex === -1 ||
      // item 是分组
      data[index].questionType === TemplateQuestionTypeEnum.GROUP ||
      // 正在拖拽这题
      activeItem?.id === item.uniqId
    ) {
      return true;
    }

    return !collapseKeys.includes(data[groupIndex].uniqId);
  });

  const handleChange = (value: TemplateQuestionType) => {
    onDataChange((prev) => {
      const result = [...prev];
      const index = prev.findIndex((i) => i.uniqId === value.uniqId);

      result[index] = value;

      return result;
    });
  };

  // 移除表单
  const handleDelete = (uniqId: string) => {
    onOptionalQuestionsChange((prev) => prev.concat(data.find((i) => i.uniqId === uniqId)!));
    onDataChange((prev) => prev.filter((i) => i.uniqId !== uniqId));
  };

  // 移除组及其中的所有题目
  const handleDeleteGroup = (index: number) => {
    const group = data[index];

    // 有 id 说明时保存过的，存起来，后端需要
    if (group.id) {
      setDeletedGroupIds((prev) => prev.concat(group.id as number));
    }

    const questions = data.filter(
      (item, idx) =>
        item.questionType !== TemplateQuestionTypeEnum.GROUP && getGroupIndex(data, idx) === index,
    );

    onOptionalQuestionsChange((prev) => prev.concat(questions));
    onDataChange((prev) =>
      prev
        // 先移除分类符
        .filter((_, idx) => idx !== index)
        // 再移除分类下的所有题目
        .filter((i) => !questions.some((question) => question.uniqId === i.uniqId)),
    );
  };

  const generateNewQuestionById = (id: string, useToUniqId?: boolean) => {
    const index = newQuestionOptions.findIndex((item) =>
      'children' in item ? item.children.some((child) => child.id === id) : item.id === id,
    )!;

    const uniqId = useToUniqId ? id : nanoid();
    const newQuestionOption = newQuestionOptions[index];
    const questionType = newQuestionOption.value;

    const questionBlock =
      'children' in newQuestionOption
        ? newQuestionOption.children.find((child) => child.id === id)!
        : newQuestionOption;

    const newQuestion = {
      uniqId,
      questionKey: uniqId,
      questionType,
      name: questionBlock.label,
      ...questionBlock.getDefaultConfig?.(),
    };

    return newQuestion;
  };

  // 点击新增试题
  const handleAddNewQuestion = useEvent((id: string) => {
    const newQuestion = generateNewQuestionById(id);

    onDataChange((prev) => {
      // 如果右侧有题目
      if (prev.length > 0) {
        return prev.concat(newQuestion);
      }

      const groupItem = generateGroupItem();

      if (newQuestion.questionType === TemplateQuestionTypeEnum.GROUP) {
        return prev.concat(groupItem);
      }

      return [...prev, groupItem, newQuestion];
    });
    setIsEditingUniqId(newQuestion.uniqId);
  });

  // 点击添加可选试题
  const handleAddOptionalQuestion = useEvent((uniqId: string) => {
    const question = optionalQuestions.find((i) => i.uniqId === uniqId);

    if (question) {
      onDataChange((prev) => {
        if (prev.length > 0) {
          return prev.concat(question);
        }

        return [...prev, generateGroupItem(), question];
      });
    }

    onOptionalQuestionsChange((prev) => prev.filter((i) => i.uniqId !== uniqId));
  });

  // 点击全部添加
  const handleAddAllOptionalQuestions = useEvent(() => {
    onDataChange((prev) => {
      // 判断右侧是否有分类符
      if (prev.some((i) => i.questionType === TemplateQuestionTypeEnum.GROUP)) {
        return prev.concat(optionalQuestions);
      }

      // 如果右侧没有分页符，添加到顶部
      return [generateGroupItem(), ...prev, ...optionalQuestions];
    });
    onOptionalQuestionsChange([]);
  });

  // 点击全部移除
  const handleRemoveAllQuestions = useEvent(() => {
    onDataChange([]);
    onOptionalQuestionsChange((prev) =>
      prev.concat(data.filter((i) => i.questionType !== TemplateQuestionTypeEnum.GROUP)),
    );
  });

  const handleDragStart = ({ active }: DragStartEvent) => {
    setActiveItem(active);

    // 如果拖拽的是分类，全部收起
    if (data.find((i) => i.uniqId === active.id)?.questionType === TemplateQuestionTypeEnum.GROUP) {
      setCollapseKeys(
        data.filter((i) => i.questionType === TemplateQuestionTypeEnum.GROUP).map((i) => i.uniqId),
      );
    }
  };

  const handleDragOver = ({ active, over }: DragOverEvent) => {
    // 不是从左边拖拽、或右边没有试题时，不走 dragOver
    if (!isLeftDragging || !data.length) {
      return;
    }

    const activeId = active.id;
    const overId = over?.id;

    onDataChange((prev) => {
      const activeIdInData = prev.some((i) => i.uniqId === activeId);

      // 如果 data 中已经添加
      if (activeIdInData) {
        // 如果有 overId
        if (overId) {
          return prev;
        } else {
          // 如果没有 overId，则要从 data 中删除这个 active（说明拖拽出了表单）
          return prev.filter((i) => i.uniqId !== activeId);
        }
      }

      const overIndex = prev.findIndex((i) => i.uniqId === overId);
      // 要用 dragStart 设置的 activeItem，不能用 active
      const activeData = activeItem.data.current;

      if (activeData?.type === 'new-question') {
        return [
          ...prev.slice(0, overIndex),
          generateNewQuestionById(activeItem.id as string, true),
          ...prev.slice(overIndex, prev.length),
        ];
      } else {
        const question = optionalQuestions.find((i) => i.uniqId === activeId)!;

        return [...prev.slice(0, overIndex), question, ...prev.slice(overIndex, prev.length)];
      }
    });
  };

  const handleDragEnd = ({ active, over }: DragEndEvent) => {
    setActiveItem(null);

    const type = activeItem?.data.current?.type;

    if (over) {
      // 右侧是空的时
      if (!data.length) {
        const activeData = active.data.current;

        if (activeData?.type === 'new-question') {
          handleAddNewQuestion(active.id as string);
        } else {
          handleAddOptionalQuestion(active.id as string);
        }
      } else if (active.id !== over.id) {
        onDataChange((prev) => {
          const activeIndex = data.findIndex((i) => i.uniqId === active.id);

          let overIndex = data.findIndex((i) => i.uniqId === over.id);

          // 如果放到折叠上，并且是往下拖，overIndex 应为折叠的最后一道题
          if (collapseKeys.includes(over.id as string) && overIndex > activeIndex) {
            const groupQuestions = getQuestionsByGroupId(data, over.id as string);

            if (groupQuestions.length > 0) {
              overIndex = prev.findIndex(
                (i) => i.uniqId === groupQuestions[groupQuestions.length - 1].uniqId,
              );
            }
          }

          // 如果拖拽的是折叠的
          if (collapseKeys.includes(active.id as string)) {
            const groupQuestions = getQuestionsByGroupId(data, active.id as string);
            const prevIndexes = [
              activeIndex,
              ...groupQuestions.map((question) =>
                data.findIndex((i) => i.uniqId === question.uniqId),
              ),
            ];

            // 这种情况结束时动画有问题，关闭
            setEnableTransition(false);

            return swapData(data, prevIndexes, overIndex);
          }

          const result = arrayMove(prev, activeIndex, overIndex);

          return result;
        });
      }
    }

    // 如果是新增试题
    if (type === 'new-question') {
      // 为左侧新增试题块生成一个新的 id，否则将无法拖拽，且去掉松手返回动画
      setNewQuestionOptions((prev) => {
        const result = [...prev];
        const index = prev.findIndex((item) =>
          'children' in item
            ? item.children.some((i) => i.id === active.id)
            : item.id === active.id,
        );

        if ('children' in result[index]) {
          const childIndex = result[index].children.findIndex((item) => item.id === active.id);

          result[index].children[childIndex].id = nanoid();
        } else {
          result[index].id = nanoid();
        }

        return result;
      });
      setIsEditingUniqId(active.id as string);
    }

    if (type === 'optional-question') {
      // 如果结束时右边有这个题目，删除左边的这个题目
      if (data.some((i) => i.uniqId === active.id)) {
        onOptionalQuestionsChange((prev) => prev.filter((i) => i.uniqId !== active.id));
      } else {
        // 如果结束时右边没有这个题目，为这题生成一个新的 id，否则将无法拖拽，且去掉松手返回动画
        onOptionalQuestionsChange((prev) => {
          const result = [...prev];
          const index = prev.findIndex((i) => i.uniqId === active.id);

          if (index > -1) {
            result[index].uniqId = nanoid();
          }

          return result;
        });
      }
    }
  };

  const handleSave = async (values: { name: string }) => {
    if (data.length > 0 && data[0].questionType !== TemplateQuestionTypeEnum.GROUP) {
      message.warning('最上方需要有一个分类');
      virtuosoRef.current?.scrollToIndex(0);

      return;
    }

    const warningIndex = data.findIndex((_, index) => getIsWarning(data, index));

    if (warningIndex > -1) {
      message.warning('问题配置出错，请重新配置');

      // 先全部展开，否则可能 index 不正确
      setCollapseKeys([]);

      virtuosoRef.current?.scrollToIndex(warningIndex);

      return;
    }

    function getItem(item: TemplateQuestionType, sort: number): QuestionItemType {
      return {
        name: item.name,
        description: item.description,
        id: item.id,
        questionKey: item.questionKey,
        questionType: item.questionType,
        notNull: item.notNull,
        precisions: item.precisions,
        options: item.options,
        regionLevel: item.regionLevel,
        regionMode: item.regionMode,
        sort,
        ruleInfo: JSON.stringify(item.ruleInfo),
        autoAuditInfo: item.autoAuditInfo,
        childList: item.childList?.map(
          (i, index) =>
            ({
              ...i,
              sort: index + 1,
              ruleInfo: JSON.stringify(i.ruleInfo),
            } as QuestionItemType),
        ),
      };
    }

    const pageList: PageListItemType[] = [];

    data.forEach((item) => {
      if (item.questionType === TemplateQuestionTypeEnum.GROUP) {
        pageList.push({
          pageName: item.name,
          pageId: item.id,
          questions: [],
          sort: pageList.length + 1,
        });
      } else {
        pageList[pageList.length - 1].questions.push(
          getItem(item, pageList[pageList.length - 1].questions.length + 1),
        );
      }
    });

    onSave({
      name: values.name,
      pageList: [
        ...pageList,
        ...deletedGroupIds.map((groupId) => ({ pageId: groupId, deleted: true })),
      ],
    });
  };

  // 缓存 Header，否则 Form 校验可能需要点两次
  const Header = useCallback(
    () => (
      <>
        <Form.Item
          name="name"
          className="mt-5 mx-2 text-center"
          rules={[{ required: true, message: '请输入标题' }]}
        >
          <Input
            placeholder="请输入标题"
            size="large"
            maxLength={20}
            variant="borderless"
            className="text-center text-2xl font-medium"
          />
        </Form.Item>
        {!data.length && <EmptyDroppable />}
      </>
    ),
    [data.length],
  );

  const handleCollapseChange = (value: boolean, uniqId: string) => {
    setCollapseKeys((prev) => (value ? prev.concat(uniqId) : prev.filter((i) => i !== uniqId)));
  };

  return (
    <DndContext
      sensors={useSensors(
        useSensor(PointerSensor, {
          activationConstraint: {
            distance: 1,
          },
        }),
      )}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <Form form={form} scrollToFirstError onFinish={handleSave}>
        <DragOverlay>{activeItem?.data.current?.renderOverlay()}</DragOverlay>
        <Card classNames={{ body: 'flex !p-0 h-full' }} style={{ height: 'calc(100vh - 160px)' }}>
          <div className="flex flex-1 overflow-auto">
            <LeftBar
              loading={optionalQuestionsLoading}
              optionalQuestions={optionalQuestions}
              removable={data.length > 0}
              newQuestionOptions={newQuestionOptions}
              onAddNewQuestion={handleAddNewQuestion}
              onAddOptionalQuestion={handleAddOptionalQuestion}
              onAddAllOptionalQuestions={handleAddAllOptionalQuestions}
              onRemoveAllQuestions={handleRemoveAllQuestions}
            />
            <div className="flex flex-col flex-1 bg-gray-50 rounded-r-lg">
              <div className="relative overflow-auto pt-10 pb-10">
                <div
                  className="absolute left-0 right-0 top-0 h-[200px] bg-no-repeat rounded-tr-lg bg-[50%] bg-[rgb(20,86,240)]"
                  style={{ backgroundImage: `url(${backgroundImage})` }}
                />
                <SortableContext
                  strategy={verticalListSortingStrategy}
                  items={data.map((i) => i.uniqId)}
                >
                  <div className="w-[550px] mx-auto">
                    <SearchSelect
                      options={data.reduce<{ label: string; value: string }[]>(
                        (total, cur) =>
                          cur.questionType !== TemplateQuestionTypeEnum.GROUP
                            ? total.concat({ label: cur.name, value: cur.uniqId })
                            : total,
                        [],
                      )}
                      onSelect={(value) => {
                        const index = data.findIndex((i) => i.uniqId === value);

                        // 先全部展开，否则可能 index 不正确
                        setCollapseKeys([]);
                        virtuosoRef.current?.scrollToIndex(index);
                      }}
                    />
                    {mainLoading ? (
                      <div className="relative p-6 rounded-xl bg-white h-full shadow-lg z-[1]">
                        <Skeleton paragraph={{ rows: 20 }} active title={false} />
                      </div>
                    ) : (
                      <Virtuoso
                        ref={virtuosoRef}
                        increaseViewportBy={200}
                        style={{ height: 'calc(100vh - 290px)' }}
                        data={showData}
                        className="rounded-xl bg-white shadow-lg"
                        computeItemKey={(_, item) => item.uniqId}
                        components={{
                          Header,
                          Footer: () => <div className="h-8" />,
                        }}
                        itemContent={(_, item) => {
                          // 不使用 itemContent 的 index 参数，使用在 data 中的 index
                          const index = data.findIndex((i) => i.uniqId === item.uniqId);

                          return (
                            <SortableItem
                              key={item.uniqId}
                              data={data}
                              index={index}
                              enableTransition={enableTransition}
                              isCollapse={collapseKeys.includes(item.uniqId)}
                              isEditing={isEditingUniqId === item.uniqId}
                              isLeftDragging={isLeftDragging}
                              getHrefWhenOptionDelete={getHrefWhenOptionDelete}
                              hasAutoAudit={hasAutoAudit}
                              onChange={handleChange}
                              onDelete={handleDelete}
                              onGroupDelete={handleDeleteGroup}
                              onEditingChange={setIsEditingUniqId}
                              onCollapseChange={handleCollapseChange}
                              onCheckOptionDelete={onCheckOptionDelete}
                            />
                          );
                        }}
                      />
                    )}
                  </div>
                </SortableContext>
              </div>
            </div>
          </div>
        </Card>
      </Form>
    </DndContext>
  );
};

export default QuestionConfig;
