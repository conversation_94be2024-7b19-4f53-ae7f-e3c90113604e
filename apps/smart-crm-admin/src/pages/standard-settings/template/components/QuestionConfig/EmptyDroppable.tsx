import { useDroppable } from '@dnd-kit/core';
import classNames from 'classnames';

const EmptyDroppable = () => {
  const { setNodeRef, isOver } = useDroppable({ id: 'empty' });

  return (
    <div
      ref={setNodeRef}
      className={classNames(
        'flex justify-center items-center border border-dashed bg-gray-50 text-gray-400',
        {
          'border-blue-400 bg-blue-50': isOver,
        },
      )}
      style={{ height: 'calc(100vh - 450px)' }}
    >
      点击或拖拽左侧题型到这里添加
    </div>
  );
};

export default EmptyDroppable;
