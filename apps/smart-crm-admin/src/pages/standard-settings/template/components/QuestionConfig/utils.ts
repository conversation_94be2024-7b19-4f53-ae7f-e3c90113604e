import {
  BarsOutlined,
  BuildOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  EnvironmentOutlined,
  FontSizeOutlined,
  GroupOutlined,
  IdcardOutlined,
  NumberOutlined,
  PaperClipOutlined,
  PhoneOutlined,
} from '@ant-design/icons';
import { RegionLevelType, RegionModeEnum } from '@src/services/common/type';
import { QuestionConditionEnum } from '@src/services/standard-settings/template/type';
import { QuestionRuleEnum } from '@src/services/visit/type';
import { uniqBy } from 'lodash-es';
import { nanoid } from 'nanoid';
import { TemplateQuestionType, TemplateQuestionTypeEnum } from './type';

export const generateUniqIdQuestionKey = () => {
  const randomKey = nanoid();

  return {
    uniqId: randomKey,
    questionKey: randomKey,
  };
};

export const questionTypeOptions = [
  {
    label: '文本',
    value: TemplateQuestionTypeEnum.TEXT,
    icon: FontSizeOutlined,
  },
  {
    label: '数值',
    value: TemplateQuestionTypeEnum.NUMBER,
    icon: NumberOutlined,
    getDefaultConfig: () => ({
      precisions: 0,
    }),
  },
  {
    label: '单选',
    value: TemplateQuestionTypeEnum.SINGLE,
    icon: CheckCircleOutlined,
  },
  {
    label: '多选',
    value: TemplateQuestionTypeEnum.MULTIPLE,
    icon: BarsOutlined,
  },
  {
    label: '附件',
    value: TemplateQuestionTypeEnum.ATTACHMENT,
    icon: PaperClipOutlined,
  },
  {
    label: '日期',
    value: TemplateQuestionTypeEnum.DATE,
    icon: CalendarOutlined,
  },
  {
    label: '日期范围',
    value: TemplateQuestionTypeEnum.DATE_RANGE,
    icon: CalendarOutlined,
    hidden: true,
  },
  {
    label: '手机号',
    value: TemplateQuestionTypeEnum.PHONE,
    icon: PhoneOutlined,
  },
  {
    label: '身份证号',
    value: TemplateQuestionTypeEnum.IDENTITY_CARD,
    icon: IdcardOutlined,
  },
  {
    label: '行政区划',
    value: TemplateQuestionTypeEnum.REGION,
    icon: EnvironmentOutlined,
    getDefaultConfig: () =>
      ({
        regionLevel: 2,
        regionMode: RegionModeEnum.SINGLE,
      } as {
        regionLevel: RegionLevelType;
        regionMode: RegionModeEnum;
      }),
  },
  {
    label: '分类',
    value: TemplateQuestionTypeEnum.GROUP,
    icon: GroupOutlined,
  },
  {
    label: '组件',
    icon: BuildOutlined,
    value: TemplateQuestionTypeEnum.COMPONENT,
    children: [
      {
        label: '工作经验',
        getDefaultConfig: () => ({
          childList: [
            {
              name: '起止时间',
              questionType: TemplateQuestionTypeEnum.DATE_RANGE,
              notNull: true,
              ...generateUniqIdQuestionKey(),
            },
            {
              name: '工作单位',
              questionType: TemplateQuestionTypeEnum.TEXT,
              notNull: true,
              ...generateUniqIdQuestionKey(),
            },
            {
              name: '工作证明',
              questionType: TemplateQuestionTypeEnum.ATTACHMENT,
              notNull: true,
              ...generateUniqIdQuestionKey(),
            },
            {
              name: '工作地区',
              questionType: TemplateQuestionTypeEnum.REGION,
              notNull: true,
              ...generateUniqIdQuestionKey(),
              regionLevel: 3 as RegionLevelType,
              regionMode: RegionModeEnum.SINGLE,
            },
            {
              name: '所属行业',
              questionType: TemplateQuestionTypeEnum.SINGLE,
              notNull: true,
              ...generateUniqIdQuestionKey(),
              options: [
                '零售业',
                '服务行业',
                '政府及事业单位',
                '通讯互联网',
                '金融保险',
                '制造业',
                '种植养殖业',
                '内容和新媒体行业',
                '其他',
              ].map((label) => ({
                label,
                value: nanoid(),
              })),
            },
            {
              name: '人员管理经验',
              questionType: TemplateQuestionTypeEnum.SINGLE,
              notNull: true,
              ...generateUniqIdQuestionKey(),
              options: [
                '未参与过人员管理',
                '1-3人',
                '4-10人',
                '11-30人',
                '31-100人',
                '101人及以上',
              ].map((label) => ({
                label,
                value: nanoid(),
              })),
            },
            {
              name: '主要工作内容',
              questionType: TemplateQuestionTypeEnum.TEXT,
              notNull: true,
              ...generateUniqIdQuestionKey(),
            },
          ],
        }),
      },
      {
        label: '经商经验',
        getDefaultConfig: () => ({
          childList: [
            {
              name: '所属行业',
              questionType: TemplateQuestionTypeEnum.SINGLE,
              notNull: true,
              ...generateUniqIdQuestionKey(),
              options: [
                '零售业',
                '服务行业',
                '政府及事业单位',
                '通讯互联网',
                '金融保险',
                '制造业',
                '种植养殖业',
                '内容和新媒体行业',
                '其他',
              ].map((label) => ({
                label,
                value: nanoid(),
              })),
            },
            {
              name: '品牌名称',
              questionType: TemplateQuestionTypeEnum.TEXT,
              notNull: true,
              ...generateUniqIdQuestionKey(),
            },
            {
              name: '开设地区',
              questionType: TemplateQuestionTypeEnum.REGION,
              notNull: true,
              ...generateUniqIdQuestionKey(),
              regionLevel: 3 as RegionLevelType,
              regionMode: RegionModeEnum.SINGLE,
            },
            {
              name: '门店数量',
              questionType: TemplateQuestionTypeEnum.SINGLE,
              notNull: true,
              ...generateUniqIdQuestionKey(),
              options: ['1家', '2-3家', '4-5家', '5家及以上'].map((label) => ({
                label,
                value: nanoid(),
              })),
            },
            {
              name: '营业执照/品牌授权书',
              questionType: TemplateQuestionTypeEnum.ATTACHMENT,
              notNull: true,
              ...generateUniqIdQuestionKey(),
            },
            {
              name: '人员管理经验',
              questionType: TemplateQuestionTypeEnum.SINGLE,
              notNull: true,
              ...generateUniqIdQuestionKey(),
              options: [
                '未参与过人员管理',
                '1-3人',
                '4-10人',
                '11-30人',
                '31-100人',
                '101人及以上',
              ].map((label) => ({
                label,
                value: nanoid(),
              })),
            },
            {
              name: '主要工作内容',
              questionType: TemplateQuestionTypeEnum.TEXT,
              notNull: true,
              ...generateUniqIdQuestionKey(),
            },
          ],
        }),
      },
    ],
  },
];

export const questionTypeRuleOptionsMap: Record<
  string,
  { label: string; value: QuestionRuleEnum }[]
> = {
  [TemplateQuestionTypeEnum.TEXT]: [{ label: '不为空', value: QuestionRuleEnum.NOT_NULL }],
  [TemplateQuestionTypeEnum.NUMBER]: [{ label: '不为空', value: QuestionRuleEnum.NOT_NULL }],
  [TemplateQuestionTypeEnum.SINGLE]: [
    { label: '包含', value: QuestionRuleEnum.IN },
    { label: '不为空', value: QuestionRuleEnum.NOT_NULL },
  ],
  [TemplateQuestionTypeEnum.MULTIPLE]: [
    { label: '包含', value: QuestionRuleEnum.IN },
    { label: '不为空', value: QuestionRuleEnum.NOT_NULL },
  ],
  [TemplateQuestionTypeEnum.ATTACHMENT]: [{ label: '不为空', value: QuestionRuleEnum.NOT_NULL }],
  [TemplateQuestionTypeEnum.DATE]: [{ label: '不为空', value: QuestionRuleEnum.NOT_NULL }],
  [TemplateQuestionTypeEnum.DATE_RANGE]: [{ label: '不为空', value: QuestionRuleEnum.NOT_NULL }],
  [TemplateQuestionTypeEnum.PHONE]: [{ label: '不为空', value: QuestionRuleEnum.NOT_NULL }],
  [TemplateQuestionTypeEnum.IDENTITY_CARD]: [{ label: '不为空', value: QuestionRuleEnum.NOT_NULL }],
  [TemplateQuestionTypeEnum.REGION]: [{ label: '不为空', value: QuestionRuleEnum.NOT_NULL }],
  [TemplateQuestionTypeEnum.COMPONENT]: [{ label: '不为空', value: QuestionRuleEnum.NOT_NULL }],
};

export const questionTypeAutoAuditConditionOptionsMap: Record<
  string,
  { label: string; value: QuestionConditionEnum }[]
> = {
  [TemplateQuestionTypeEnum.SINGLE]: [{ label: '包含', value: QuestionConditionEnum.IN }],
  [TemplateQuestionTypeEnum.MULTIPLE]: [{ label: '包含', value: QuestionConditionEnum.IN }],
  [TemplateQuestionTypeEnum.TEXT]: [{ label: '包含', value: QuestionConditionEnum.LIKE }],
  [TemplateQuestionTypeEnum.NUMBER]: [
    { label: '等于', value: QuestionConditionEnum.EQ },
    { label: '大于', value: QuestionConditionEnum.GT },
    { label: '小于', value: QuestionConditionEnum.LT },
    { label: '大于等于', value: QuestionConditionEnum.GE },
    { label: '小于等于', value: QuestionConditionEnum.LE },
  ],
  [TemplateQuestionTypeEnum.DATE]: [
    { label: '日期不在', value: QuestionConditionEnum.NOT_BETWEEN },
  ],
  [TemplateQuestionTypeEnum.IDENTITY_CARD]: [
    { label: '年龄不在', value: QuestionConditionEnum.NOT_BETWEEN },
  ],
};

// 可以自动审核的试题类型
export const canAutoAuditQuestionTypes = Object.keys(questionTypeAutoAuditConditionOptionsMap);

export const generateGroupItem = () => {
  const uniqId = nanoid();

  return {
    name: '分类',
    uniqId,
    questionKey: uniqId,
    questionType: TemplateQuestionTypeEnum.GROUP,
  };
};

// 计算题目所在分类的 index
export const getGroupIndex = (data: TemplateQuestionType[], index: number) => {
  let groupIndex = -1;

  // 在此分类中，上方的所有题目
  for (let i = index; i > -1; i--) {
    if (data[i].questionType === TemplateQuestionTypeEnum.GROUP) {
      groupIndex = i;

      break;
    }
  }

  return groupIndex;
};

// 将一组连续的 index 跟某一个调换
export const swapData = (array: any[], from: number[], to: number) => {
  const newArray = array.slice();

  newArray.splice(
    to < 0 ? newArray.length + to : to - (to > from[0] ? from.length - 1 : 0),
    0,
    ...newArray.splice(from[0], from.length),
  );

  return newArray;
};

export const getQuestionsByGroupId = (data: TemplateQuestionType[], uniqId: string) => {
  const groupIndex = data.findIndex((i) => i.uniqId === uniqId);
  const questions: TemplateQuestionType[] = [];

  for (let i = groupIndex + 1; i < data.length; i++) {
    const item = data[i];

    if (item.questionType !== TemplateQuestionTypeEnum.GROUP) {
      questions.push(item);
    } else {
      break;
    }
  }

  return questions;
};

// 计算展示条件可选的题目下拉列表
export const getRuleQuestionOptions = (data: TemplateQuestionType[], index: number) => {
  const groupIndex = getGroupIndex(data, index);

  // 上方有分类符
  if (groupIndex > -1) {
    return data.slice(groupIndex + 1, index).map((i) => ({ label: i.name, value: i.questionKey }));
    // 上方没分类符，说明是第一页
  } else {
    return data.slice(0, index).map((i) => ({ label: i.name, value: i.questionKey }));
  }
};

export const getIsWarning = (data: TemplateQuestionType[], index: number) => {
  const item = data[index];

  // 名称为空
  if (!item.name) {
    return true;
  }

  // 单选或多选
  if (
    [TemplateQuestionTypeEnum.SINGLE, TemplateQuestionTypeEnum.MULTIPLE].includes(item.questionType)
  ) {
    const options = item.options?.filter((option) => !option.deleted);

    // 没有选项
    if (!options?.length) {
      return true;
    }

    // 选项值为空
    if (options?.some((i) => !i.label)) {
      return true;
    }

    // 选项值重复
    if (uniqBy(options, 'label').length !== (options || []).length) {
      return true;
    }
  }

  // 检测分类下方是否有题目
  if (item.questionType === TemplateQuestionTypeEnum.GROUP) {
    const nextQuestion = data[index + 1];

    // 如果下一题有题目，并且不是分页，那就是正确的
    if (nextQuestion && nextQuestion.questionType !== TemplateQuestionTypeEnum.GROUP) {
      return false;
    }

    return true;
  }

  const questionOptions = getRuleQuestionOptions(data, index);

  // 检查 ruleInfo
  if (
    item.ruleInfo?.some((info) => {
      // 选项中找不到依赖的题目（被删除、在下方、或在其它分类中）
      if (questionOptions.every((i) => i.value !== info.questionKey)) {
        return true;
      }

      const ruleQuestion = data.find((i) => i.questionKey === info.questionKey);

      // 条件题目是单选、多选，rule 是包含，并且没有选择选项时
      if (
        info.rule === QuestionRuleEnum.IN &&
        ruleQuestion?.questionType &&
        [TemplateQuestionTypeEnum.SINGLE, TemplateQuestionTypeEnum.MULTIPLE].includes(
          ruleQuestion.questionType,
        ) &&
        !info.options?.length
      ) {
        return true;
      }

      // 选项被删除
      if (
        info.options?.some((optionValue) =>
          ruleQuestion?.options?.every((i) => i.value !== optionValue),
        )
      ) {
        return true;
      }

      return false;
    })
  ) {
    return true;
  }

  // 检查自动审核
  if (item.autoAuditInfo) {
    if (!item.autoAuditInfo.value || !item.autoAuditInfo.rejectReason) {
      return true;
    }

    if (
      [TemplateQuestionTypeEnum.SINGLE, TemplateQuestionTypeEnum.MULTIPLE].includes(
        item.questionType,
      )
    ) {
      const valueArray = item.autoAuditInfo.value?.split(',');
      const options = item.options?.filter((i) => !i.deleted);

      // 有选项被删除
      if (valueArray?.some((val) => options?.every((option) => option.value !== val))) {
        return true;
      }
    }

    if (item.questionType === TemplateQuestionTypeEnum.IDENTITY_CARD) {
      const [preValue, lastValue] = item.autoAuditInfo.value?.split(',');

      // 有一项没值
      if (!preValue || !lastValue) {
        return true;
      }

      // 前面大于后面
      if (Number(preValue) > Number(lastValue)) {
        return true;
      }
    }

    return false;
  }

  return false;
};
