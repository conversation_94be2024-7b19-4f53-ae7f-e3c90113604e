import { QuestionRuleEnum } from '@src/services/visit/type';
import { Divider, Select, Switch, Tooltip } from 'antd';
import ExclamationTooltip from '../ExclamationTooltip';
import { TemplateQuestionType, TemplateQuestionTypeEnum } from '../type';
import { getGroupIndex, getRuleQuestionOptions, questionTypeRuleOptionsMap } from '../utils';

interface RuleInfoConfigProps {
  index: number;
  data: TemplateQuestionType[];
  onChange: (value: Partial<TemplateQuestionType>) => void;
}

const RuleInfoConfig: React.FC<RuleInfoConfigProps> = ({ index, data, onChange }) => {
  const item = data[index];

  const questionOptions = getRuleQuestionOptions(data, index);

  // 前面一项为空，或为当前分类的第一个，并且没有 ruleInfo 时禁用
  const ruleInfoDisabled =
    (!data[index - 1] || data[index - 1].questionType === TemplateQuestionTypeEnum.GROUP) &&
    !item.ruleInfo?.length;

  return (
    <>
      <Divider className="my-4" />
      <Tooltip title={ruleInfoDisabled && '表单的首个问题不能添加展示条件'}>
        <span>
          <Switch
            size="small"
            className="mr-1"
            disabled={ruleInfoDisabled}
            value={(item.ruleInfo || []).length > 0}
            onChange={(checked) => {
              if (checked) {
                const questionKey = questionOptions[0].value;
                const { questionType } = data.find((i) => i.questionKey === questionKey)!;

                onChange({
                  ruleInfo: [
                    {
                      questionKey,
                      rule: questionTypeRuleOptionsMap[questionType][0].value,
                    },
                  ],
                });
              } else {
                onChange({ ruleInfo: [] });
              }
            }}
          />
          <span className={ruleInfoDisabled ? 'text-gray-300' : ''}>当满足条件时展示该问题</span>
        </span>
      </Tooltip>
      {(item.ruleInfo || []).length > 0 && (
        <div className="mt-2">
          {item.ruleInfo?.map((info, infoIndex) => {
            const ruleQuestionIndex = data.findIndex((i) => i.questionKey === info.questionKey);
            // 可能为 undefined
            const ruleQuestion = data[ruleQuestionIndex] as TemplateQuestionType | undefined;
            const isSelect =
              ruleQuestion?.questionType &&
              [TemplateQuestionTypeEnum.SINGLE, TemplateQuestionTypeEnum.MULTIPLE].includes(
                ruleQuestion.questionType,
              );
            const shouldShowOptions = info.rule === QuestionRuleEnum.IN && isSelect;
            // 题目在此题下方（依赖的题目 index 大于当前题目，并且在相同分类里）
            const questionUnderWarning =
              ruleQuestionIndex > index &&
              getGroupIndex(data, ruleQuestionIndex) === getGroupIndex(data, index);
            // 题目被移出此分类
            const questionRemovedWarning = questionOptions.every(
              (i) => i.value !== info.questionKey,
            );
            // 没有选择选项
            const noOptionsWarning = shouldShowOptions && !info.options?.length;

            return (
              <div key={infoIndex} className="flex gap-2 mt-2" onClick={(e) => e.stopPropagation()}>
                <Select
                  value={info.questionKey}
                  style={{ width: 160, flexShrink: 0 }}
                  options={questionOptions}
                  // 字段被移除后，不展示 option 的 value，展示为空
                  labelRender={({ value }) => data.find((i) => i.questionKey === value)?.name || ''}
                  showSearch
                  optionFilterProp="label"
                  status={questionRemovedWarning || questionUnderWarning ? 'warning' : undefined}
                  suffixIcon={
                    (questionRemovedWarning || questionUnderWarning) && (
                      <ExclamationTooltip
                        title={
                          questionUnderWarning
                            ? '设置展示条件的字段应在此字段之前，请重新选择'
                            : '字段已被移出此分类，请重新设置条件'
                        }
                      />
                    )
                  }
                  onChange={(value) => {
                    const result = [...(item.ruleInfo || [])];

                    result[infoIndex].questionKey = value;

                    const newType = data.find((i) => i.questionKey === value)!.questionType;

                    result[infoIndex].rule = questionTypeRuleOptionsMap[newType][0].value;
                    result[infoIndex].options = [];
                    onChange({ ruleInfo: result });
                  }}
                />
                {ruleQuestion && (
                  <>
                    <Select
                      value={info.rule}
                      style={{ width: 90, flexShrink: 0 }}
                      options={[
                        {
                          label: '包含',
                          value: QuestionRuleEnum.IN,
                          hidden: !isSelect,
                        },
                        { label: '不为空', value: QuestionRuleEnum.NOT_NULL },
                      ].filter((i) => !i.hidden)}
                      onChange={(value) => {
                        const result = [...(item.ruleInfo || [])];

                        result[infoIndex].rule = value;

                        if (value === QuestionRuleEnum.NOT_NULL) {
                          result[infoIndex].options = [];
                        }

                        onChange({ ruleInfo: result });
                      }}
                    />
                    {shouldShowOptions && (
                      <Select
                        value={info.options}
                        mode="multiple"
                        status={noOptionsWarning ? 'warning' : undefined}
                        suffixIcon={
                          noOptionsWarning && <ExclamationTooltip title="请选择条件内容" />
                        }
                        style={{ width: 210, flexShrink: 0 }}
                        placeholder="请选择选项"
                        showSearch
                        optionFilterProp="label"
                        options={ruleQuestion?.options}
                        // 依赖的选项不存在时，展示警告
                        labelRender={({ value }) =>
                          ruleQuestion?.options?.find((i) => i.value === value)?.label ?? (
                            <ExclamationTooltip title="选项不存在，请删除" />
                          )
                        }
                        onChange={(value) => {
                          const result = [...(item.ruleInfo || [])];

                          result[infoIndex].options = value;
                          onChange({ ruleInfo: result });
                        }}
                      />
                    )}
                  </>
                )}
              </div>
            );
          })}
        </div>
      )}
    </>
  );
};

export default RuleInfoConfig;
