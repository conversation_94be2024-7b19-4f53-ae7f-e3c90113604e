import React, { useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer } from 'antd';
import SortableItem from '../SortableItem';
import { TemplateQuestionType } from '../type';
import { getIsWarning } from '../utils';

interface ChildListProps {
  hasAutoAudit?: boolean;
  data: TemplateQuestionType;
  getHrefWhenOptionDelete?: (id: number) => string;
  onCheckOptionDelete?: (params: {
    id?: number;
    value: string;
  }) => Promise<{ id: number; name: string }[]>;
  onChange: (value: Partial<TemplateQuestionType>) => void;
}

const ChildList: React.FC<ChildListProps> = ({
  hasAutoAudit,
  data,
  getHrefWhenOptionDelete,
  onCheckOptionDelete,
  onChange,
}) => {
  const { message } = App.useApp();
  const [open, setOpen] = useState(false);
  const [internalValue, setInternalValue] = useState<TemplateQuestionType[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isEditingUniqId, setIsEditingUniqId] = useState<string | null>(null);

  if (!data.childList?.length) {
    return null;
  }

  const onInternalChange = (value: TemplateQuestionType) => {
    setInternalValue((prev) => {
      const result = [...prev];

      const index = result.findIndex((i) => i.questionKey === value.questionKey);

      result[index] = value;

      return result;
    });
  };

  const handleSave = () => {
    const warningIndex = internalValue.findIndex((_, index) => getIsWarning(internalValue, index));

    if (warningIndex > -1) {
      containerRef.current?.children[warningIndex].scrollIntoView();
      message.warning('问题配置出错，请重新配置');

      return;
    }

    onChange({ childList: internalValue });
    setOpen(false);
  };

  return (
    <div>
      <Button
        className="mt-4"
        onClick={() => {
          setOpen(true);
          setInternalValue(data.childList || []);
        }}
      >
        配置子题
      </Button>
      <Drawer
        open={open}
        width={550}
        title={data.name}
        destroyOnHidden
        classNames={{ body: '!p-0' }}
        footer={
          <div className="flex gap-2 justify-end">
            <Button onClick={() => setOpen(false)}>取消</Button>
            <Button
              type="primary"
              onClick={(e) => {
                e.stopPropagation();
                handleSave();
              }}
            >
              保存
            </Button>
          </div>
        }
        onClick={() => setIsEditingUniqId(null)}
        onClose={() => setOpen(false)}
      >
        <div ref={containerRef}>
          {internalValue.map((item, index) => (
            <SortableItem
              key={item.questionKey}
              data={internalValue}
              index={index}
              deletable={false}
              sortable={false}
              hasAutoAudit={hasAutoAudit}
              isEditing={item.uniqId === isEditingUniqId}
              onEditingChange={setIsEditingUniqId}
              getHrefWhenOptionDelete={getHrefWhenOptionDelete}
              onCheckOptionDelete={onCheckOptionDelete}
              onChange={onInternalChange}
            />
          ))}
        </div>
      </Drawer>
    </div>
  );
};

export default ChildList;
