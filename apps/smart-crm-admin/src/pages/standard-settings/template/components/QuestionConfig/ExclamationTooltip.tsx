import { ExclamationCircleFilled } from '@ant-design/icons';
import { theme, Tooltip, TooltipProps } from 'antd';

const ExclamationTooltip: React.FC<TooltipProps> = (props) => {
  const { token } = theme.useToken();

  return (
    <Tooltip
      classNames={{
        root: 'max-w-full',
      }}
      {...props}
    >
      <ExclamationCircleFilled
        style={{ color: token.colorWarning, fontSize: 14, cursor: 'pointer' }}
      />
    </Tooltip>
  );
};

export default ExclamationTooltip;
