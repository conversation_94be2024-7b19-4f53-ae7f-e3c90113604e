import React from 'react';
import { DownOutlined, HolderOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { useDndContext } from '@dnd-kit/core';
import { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import { Badge, Button, Input, Tooltip } from 'antd';
import classNames from 'classnames';
import ExclamationTooltip from './ExclamationTooltip';
import { TemplateQuestionType } from './type';
import { getIsWarning, getQuestionsByGroupId } from './utils';

interface GroupItemProps {
  isOverlay?: boolean;
  dragHandleProps?: {
    ref: (element: HTMLElement | null) => void;
  } & SyntheticListenerMap;
  data: TemplateQuestionType[];
  index: number;
  isCollapse?: boolean;
  isLeftDragging?: boolean;
  onChange?: (data: TemplateQuestionType) => void;
  onGroupDelete?: () => void;
  onCollapseChange?: (value: boolean) => void;
}

const GroupItem: React.FC<GroupItemProps> = ({
  isOverlay,
  dragHandleProps,
  data,
  index,
  isCollapse,
  isLeftDragging,
  onChange,
  onGroupDelete,
  onCollapseChange,
}) => {
  const { active } = useDndContext();
  const item = data[index];

  const isWarning = getIsWarning(data, index);

  // 如果左侧正在拖拽，过滤掉左侧正在拖拽的题目
  const groupQuestions = getQuestionsByGroupId(data, item.uniqId).filter((question) =>
    isLeftDragging ? question.uniqId !== active?.id : true,
  );

  const showCollapse = groupQuestions.length > 0;

  return (
    <div className="w-full cursor-default flex items-center px-20 py-2 bg-gray-50">
      <div className="flex gap-2 absolute left-3 z-[1]">
        <Button
          size="small"
          type="text"
          className={classNames('cursor-grab', {
            'cursor-grabbing': isOverlay,
          })}
          icon={<HolderOutlined />}
          {...dragHandleProps}
        />
        {showCollapse && (
          <Badge count={groupQuestions.length} size="small" overflowCount={9999}>
            <Tooltip title={isCollapse ? '展开' : '收起'}>
              <Button
                size="small"
                type="text"
                icon={
                  <DownOutlined
                    className={classNames('transition', {
                      '-rotate-90': isCollapse,
                    })}
                  />
                }
                onClick={() => onCollapseChange?.(!isCollapse)}
              />
            </Tooltip>
          </Badge>
        )}
      </div>
      {isWarning && (
        <div className={classNames('absolute left-10 z-10', { '!left-20': showCollapse })}>
          <ExclamationTooltip title="请确保此分类中存在题目且分类名称不为空" />
        </div>
      )}
      <Input
        defaultValue={item.name}
        maxLength={10}
        className="text-center flex-1"
        variant="borderless"
        placeholder="请输入分类名称"
        onChange={(e) => onChange?.({ ...item, name: e.target.value })}
      />
      <Tooltip title="移除此分类及其中的所有题目">
        <Button
          type="text"
          size="small"
          className="absolute right-3"
          icon={<MinusCircleOutlined />}
          onClick={onGroupDelete}
        />
      </Tooltip>
    </div>
  );
};

export default GroupItem;
