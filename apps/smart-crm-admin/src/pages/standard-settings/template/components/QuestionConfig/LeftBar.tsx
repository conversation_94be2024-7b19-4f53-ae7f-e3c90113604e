import React, { HTMLAttributes, useMemo, useState } from 'react';
import { BuildOutlined, PlusOutlined } from '@ant-design/icons';
import { useDraggable, UseDraggableArguments } from '@dnd-kit/core';
import { Button, Empty, Input, Popover, Skeleton, Tooltip } from 'antd';
import classNames from 'classnames';
import { Virtuoso } from 'react-virtuoso';
import {
  NewQuestionBlockCommonType,
  NewQuestionBlockType,
  TemplateQuestionType,
  TemplateQuestionTypeEnum,
} from './type';
import { questionTypeOptions } from './utils';

interface DraggableProps extends HTMLAttributes<HTMLDivElement> {
  draggableProps: UseDraggableArguments;
}

const Draggable: React.FC<DraggableProps> = ({ draggableProps, ...props }) => {
  const { setNodeRef, attributes, listeners } = useDraggable(draggableProps);

  return <div ref={setNodeRef} {...attributes} {...listeners} {...props} />;
};

interface LeftBarProps {
  newQuestionOptions: NewQuestionBlockType[];
  loading: boolean;
  optionalQuestions: TemplateQuestionType[];
  removable: boolean;
  onAddNewQuestion: (id: string) => void;
  onAddOptionalQuestion: (uniqId: string) => void;
  onAddAllOptionalQuestions: () => void;
  onRemoveAllQuestions: () => void;
}

const LeftBar: React.FC<LeftBarProps> = React.memo(
  ({
    optionalQuestions,
    loading,
    removable,
    newQuestionOptions,
    onAddNewQuestion,
    onAddOptionalQuestion,
    onAddAllOptionalQuestions,
    onRemoveAllQuestions,
  }) => {
    const [searchValue, setSearchValue] = useState('');

    const optionalNode = useMemo(() => {
      if (loading) {
        return <Skeleton paragraph={{ rows: 10 }} title={false} active className="px-5" />;
      }

      const showData = optionalQuestions.filter(
        (item) => item.name.toLowerCase().indexOf(searchValue.toLowerCase()) > -1,
      );

      if (!showData.length) {
        return <Empty className="mt-10" description="暂无可选试题" />;
      }

      return (
        <div className="flex-1">
          <Virtuoso
            data={showData}
            components={{ Footer: () => <div className="h-3" /> }}
            itemContent={(_, item) => {
              const className =
                'flex justify-between p-2 hover:bg-gray-100 rounded-md cursor-pointer mx-3';
              const Icon = questionTypeOptions.find((i) => i.value === item.questionType)?.icon;
              const children = (
                <div className="flex break-all">
                  {Icon ? <Icon className="mr-2" /> : null}
                  {item.name}
                </div>
              );

              return (
                <Draggable
                  key={item.uniqId}
                  draggableProps={{
                    id: item.uniqId,
                    data: {
                      type: 'optional-question',
                      name: item.name,
                      renderOverlay: () => (
                        <div className={classNames(className, 'border !cursor-grabbing')}>
                          {children}
                        </div>
                      ),
                    },
                  }}
                  className={className}
                  onClick={() => onAddOptionalQuestion(item.uniqId)}
                >
                  {children}
                  <PlusOutlined className="ml-2" />
                </Draggable>
              );
            }}
          />
        </div>
      );
    }, [loading, onAddOptionalQuestion, optionalQuestions, searchValue]);

    return (
      <div className="flex flex-col w-[250px] border-r flex-shrink-0">
        <p className="mt-3 px-3 font-medium">新增试题</p>
        <div className="flex flex-wrap gap-2 p-3 border-b">
          {newQuestionOptions.map((block, blockIndex) => {
            const blockClassName =
              'rounded-md bg-gray-100 px-3 py-1 cursor-pointer whitespace-nowrap hover:bg-gray-200';
            const renderNode = (item: NewQuestionBlockCommonType, blockWidth?: boolean) => {
              const Icon = item.icon;

              const children = (
                <>
                  {Icon ? <Icon className="mr-1" /> : null} {item.label}
                </>
              );

              return (
                <Draggable
                  key={item.id}
                  draggableProps={{
                    id: item.id,
                    data: {
                      type: 'new-question',
                      name: item.label,
                      questionType: item.value,
                      renderOverlay: () => (
                        <div className={classNames(blockClassName, 'border !cursor-grabbing')}>
                          {children}
                        </div>
                      ),
                    },
                  }}
                  className={blockClassName}
                  style={{
                    width: blockWidth ? undefined : 'calc(50% - 4px)',
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onAddNewQuestion(item.id);
                  }}
                >
                  {children}
                </Draggable>
              );
            };

            return 'children' in block ? (
              <Popover
                key={blockIndex}
                placement="right"
                content={
                  <div className="flex flex-col gap-2">
                    {block.children.map((item) =>
                      renderNode({ ...item, value: TemplateQuestionTypeEnum.COMPONENT }, true),
                    )}
                  </div>
                }
              >
                <div className={blockClassName} style={{ width: 'calc(50% - 4px)' }}>
                  <BuildOutlined className="mr-1" /> {block.label}
                </div>
              </Popover>
            ) : (
              renderNode(block)
            );
          })}
        </div>
        <div className="p-3">
          <div className="flex justify-between items-center mb-2">
            <p className="font-medium">可选题目</p>
            <div className="flex gap-2">
              <Tooltip title="添加所有字段到表单">
                <Button
                  type="link"
                  className="p-0 text-xs"
                  disabled={optionalQuestions.length === 0}
                  onClick={onAddAllOptionalQuestions}
                >
                  全部添加
                </Button>
              </Tooltip>
              <Tooltip title="移除表单中所有字段">
                <Button
                  type="link"
                  className="p-0 text-xs"
                  disabled={!removable}
                  onClick={onRemoveAllQuestions}
                >
                  全部移除
                </Button>
              </Tooltip>
            </div>
          </div>
          <Input
            placeholder="搜索试题名称"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
          />
        </div>
        {optionalNode}
      </div>
    );
  },
);

export default LeftBar;
