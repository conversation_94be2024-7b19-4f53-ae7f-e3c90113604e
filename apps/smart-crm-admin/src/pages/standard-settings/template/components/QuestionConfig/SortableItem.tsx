import React, { HTMLAttributes, useRef } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useClickAway } from 'ahooks';
import classNames from 'classnames';
import GroupItem from './GroupItem';
import QuestionItem from './QuestionItem';
import { TemplateQuestionType, TemplateQuestionTypeEnum } from './type';

interface SortableItemProps extends Omit<HTMLAttributes<HTMLDivElement>, 'onChange'> {
  data: TemplateQuestionType[];
  isLeftDragging?: boolean;
  isEditing: boolean;
  isCollapse?: boolean;
  enableTransition?: boolean;
  index: number;
  deletable?: boolean;
  sortable?: boolean;
  getHrefWhenOptionDelete?: (id: number) => string;
  hasAutoAudit?: boolean;
  onChange?: (item: TemplateQuestionType) => void;
  onDelete?: (uniqId: string) => void;
  onGroupDelete?: (index: number) => void;
  onEditingChange?: (key: string | null) => void;
  onCollapseChange?: (value: boolean, uniqId: string) => void;
  onCheckOptionDelete?: (params: {
    id?: number;
    value: string;
  }) => Promise<{ id: number; name: string }[]>;
}

const SortableItem: React.FC<SortableItemProps> = ({
  data,
  isLeftDragging,
  index,
  isEditing,
  isCollapse,
  sortable = true,
  deletable = true,
  enableTransition,
  getHrefWhenOptionDelete,
  hasAutoAudit,
  onChange,
  onDelete,
  onEditingChange,
  onGroupDelete,
  onCollapseChange,
  onCheckOptionDelete,
  ...props
}) => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const item = data[index];

  const {
    attributes,
    isDragging,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
  } = useSortable({
    id: item.uniqId,
    data: {
      renderOverlay: () => (
        <div className={className}>
          {item.questionType === TemplateQuestionTypeEnum.GROUP ? (
            <GroupItem
              data={data}
              isLeftDragging={isLeftDragging}
              index={index}
              onChange={onChange}
              isCollapse
              isOverlay
            />
          ) : (
            <QuestionItem isEditing={isEditing} data={data} index={index} isOverlay />
          )}
        </div>
      ),
    },
  });

  useClickAway(() => {
    onEditingChange?.(null);
  }, containerRef);

  const showPlaceholder = isLeftDragging && isDragging;

  const className = classNames(
    'relative flex bg-white hover:z-10 cursor-default',
    {
      '!bg-gray-100': isEditing,
      '!cursor-pointer hover:shadow-[0px_6px_18px_6px_rgba(31,35,41,0.03),0px_3px_6px_-6px_rgba(31,35,41,0.05),0px_4px_8px_0px_rgba(31,35,41,0.03)]':
        !isEditing,
    },
    props.className,
  );

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition: enableTransition ? transition : undefined,
    ...(isDragging && !showPlaceholder ? { opacity: 0.2 } : {}),
  };

  const isGroup = item.questionType === TemplateQuestionTypeEnum.GROUP;

  return (
    <div
      ref={(ref) => {
        containerRef.current = ref;
        setNodeRef(ref);
      }}
      {...attributes}
      onClick={(e) => {
        e.stopPropagation();

        if (!isGroup) {
          onEditingChange?.(item.uniqId);
        }
      }}
      {...props}
      style={style}
      className={className}
    >
      {showPlaceholder ? (
        <div className="border w-full border-dashed h-[62px] border-blue-400 bg-blue-50" />
      ) : isGroup ? (
        <GroupItem
          data={data}
          index={index}
          dragHandleProps={{ ref: setActivatorNodeRef, ...listeners }}
          isCollapse={isCollapse}
          isLeftDragging={isLeftDragging}
          onChange={onChange}
          onGroupDelete={() => onGroupDelete?.(index)}
          onCollapseChange={(val) => onCollapseChange?.(val, item.uniqId)}
        />
      ) : (
        <QuestionItem
          isEditing={isEditing}
          data={data}
          index={index}
          sortable={sortable}
          deletable={deletable}
          dragHandleProps={{ ref: setActivatorNodeRef, ...listeners }}
          getHrefWhenOptionDelete={getHrefWhenOptionDelete}
          hasAutoAudit={hasAutoAudit}
          onChange={onChange}
          onDelete={() => onDelete?.(item.uniqId)}
          onCheckOptionDelete={onCheckOptionDelete}
        />
      )}
    </div>
  );
};

export default SortableItem;
