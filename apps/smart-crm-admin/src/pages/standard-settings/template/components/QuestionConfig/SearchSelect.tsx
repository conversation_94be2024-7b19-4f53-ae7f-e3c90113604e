import { useRef, useState } from 'react';
import { EnterOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, RefSelectProps, Select, SelectProps } from 'antd';

const SearchSelect: React.FC<SelectProps> = (props) => {
  const [showSearch, setShowSearch] = useState(false);
  const selectRef = useRef<RefSelectProps>(null);
  const searchButtonRef = useRef(null);

  return (
    <div className="flex">
      <Button
        ref={searchButtonRef}
        icon={<SearchOutlined />}
        className="mb-3 mr-2 shrink-0"
        onClick={() => {
          setShowSearch(true);
          selectRef.current?.focus();
        }}
      />
      <div
        className="overflow-hidden"
        style={{
          width: showSearch ? '100%' : 0,
          transition: 'all 0.2s',
        }}
      >
        <Select
          ref={selectRef}
          value={[]}
          showSearch
          open
          styles={{
            popup: {
              root: { display: showSearch ? undefined : 'none' },
            },
          }}
          placeholder="输入名称搜索进行快速定位"
          className="w-full mb-3"
          optionFilterProp="label"
          optionRender={(option) => (
            <div className="flex justify-between">
              <div className="truncate mr-1">{option.label}</div>
              <div>
                <EnterOutlined className="text-primary mr-2" />
                <span className="text-xs">到这去</span>
              </div>
            </div>
          )}
          onBlur={(e) => {
            if (e.relatedTarget !== searchButtonRef.current) {
              setShowSearch(false);
            }
          }}
          {...props}
          onSelect={(...rest) => {
            setShowSearch(false);
            props.onSelect?.(...rest);
          }}
        />
      </div>
    </div>
  );
};

export default SearchSelect;
