import { useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { parserJSON } from '@pkg/utils';
import { BusinessTypeEnum } from '@src/services/common/type';
import {
  checkQuestionRule,
  getExamPaperAllQuestions,
  getExamPaperConfigDetail,
  saveExamPaperConfig,
} from '@src/services/standard-settings/template';
import { useRequest } from 'ahooks';
import { App, Button, Form } from 'antd';
import { nanoid } from 'nanoid';
import { useNavigate, useParams } from 'react-router-dom';
import QuestionConfig from '../QuestionConfig';
import { TemplateQuestionType, TemplateQuestionTypeEnum } from '../QuestionConfig/type';

interface QuestionConfigPageProps {
  businessType: BusinessTypeEnum;
  hasAutoAudit?: boolean;
}

const QuestionConfigPage: React.FC<QuestionConfigPageProps> = ({ businessType, hasAutoAudit }) => {
  const navigate = useNavigate();
  const { id: detailId } = useParams<{ id: string }>();
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [data, setData] = useState<TemplateQuestionType[]>([]);
  const [optionalQuestions, setOptionalQuestions] = useState<TemplateQuestionType[]>([]);

  const isCreate = !detailId;

  const { loading: getAllQuestionsLoading } = useRequest(
    () => getExamPaperAllQuestions({ businessType }),
    {
      ready: isCreate,
      onSuccess: (res) => {
        setOptionalQuestions(
          res.map((question) => ({
            ...question,
            uniqId: question.questionKey,
            questionType: question.questionType,
            ruleInfo: undefined,
            childList: question.childList?.map(
              (i) =>
                ({
                  ...i,
                  uniqId: i.questionKey,
                  questionType: question.questionType,
                  ruleInfo: undefined,
                } as TemplateQuestionType),
            ),
          })),
        );
      },
    },
  );

  const {
    loading,
    data: detailData,
    refresh,
  } = useRequest(() => getExamPaperConfigDetail({ examPaperId: Number(detailId), businessType }), {
    ready: !isCreate,
    onSuccess: (res) => {
      const result: TemplateQuestionType[] = [];

      res.pageList.forEach((item) => {
        const uniqId = nanoid();

        result.push({
          ...item,
          name: item.pageName,
          id: item.pageId,
          uniqId,
          questionKey: uniqId,
          questionType: TemplateQuestionTypeEnum.GROUP,
        });
        item.questions.forEach((question) => {
          result.push({
            ...question,
            uniqId: question.questionKey,
            ruleInfo: parserJSON(question.ruleInfo),
            childList: question.childList?.map(
              (i) =>
                ({
                  ...i,
                  uniqId: i.questionKey,
                  ruleInfo: parserJSON(i.ruleInfo),
                } as TemplateQuestionType),
            ),
          });
        });
      });
      form.setFieldValue('name', res.name);
      setData(result);
      setOptionalQuestions(
        res.questionBank.map((question) => ({
          ...question,
          uniqId: question.questionKey,
          ruleInfo: undefined,
          childList: question.childList?.map(
            (i) =>
              ({
                ...i,
                uniqId: i.questionKey,
              } as TemplateQuestionType),
          ),
        })),
      );
    },
  });

  const { runAsync: save, loading: saveLoading } = useRequest(saveExamPaperConfig, {
    manual: true,
  });

  const hrefLink =
    businessType === BusinessTypeEnum.ONLINE_APPLY ? 'franchise-application' : 'evaluation';

  return (
    <PageContainer
      title={detailData?.name}
      extra={
        <Button type="primary" disabled={loading} loading={saveLoading} onClick={form.submit}>
          保存
        </Button>
      }
      onBack={() =>
        navigate(
          `/standard-settings/template?tab=${
            businessType === BusinessTypeEnum.ONLINE_APPLY ? 'apply' : 'evaluation'
          }`,
        )
      }
    >
      <QuestionConfig
        form={form}
        data={data}
        optionalQuestions={optionalQuestions}
        optionalQuestionsLoading={loading || getAllQuestionsLoading}
        mainLoading={loading}
        hasAutoAudit={hasAutoAudit}
        getHrefWhenOptionDelete={(id) => `/standard-settings/template/${hrefLink}/${id}`}
        hiddenQuestionTypes={
          businessType === BusinessTypeEnum.INTERVIEW_EVALUATION
            ? [TemplateQuestionTypeEnum.COMPONENT]
            : undefined
        }
        onDataChange={setData}
        onOptionalQuestionsChange={setOptionalQuestions}
        onCheckOptionDelete={({ id, value }) => checkQuestionRule({ examPaperId: id, value })}
        onSave={async ({ name, pageList }) => {
          const newId = await save({
            id: detailData?.id, // 新建没有 id
            name,
            businessType,
            pageList,
          });

          message.success('保存成功');

          if (isCreate) {
            navigate(`/standard-settings/template/${hrefLink}/${newId}`);
          } else {
            refresh();
          }
        }}
      />
    </PageContainer>
  );
};

export default QuestionConfigPage;
