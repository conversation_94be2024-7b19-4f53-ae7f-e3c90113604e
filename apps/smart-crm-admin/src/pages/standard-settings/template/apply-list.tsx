import { useEffect, useRef, useState } from 'react';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { commonBOTypeOptions } from '@src/common/constants';
import { ProTable } from '@src/components';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { BusinessTypeEnum } from '@src/services/common/type';
import {
  configExamBizType,
  copyExamPaper,
  deleteExamPaper,
  getExamList,
  modifyExamPaperStatus,
} from '@src/services/standard-settings/template';
import { ExamItemDTO, ExamStatusEnum } from '@src/services/standard-settings/template/type';
import { optionsToValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Form, Modal, Select, Switch, Tag, Typography } from 'antd';
import { useNavigate } from 'react-router-dom';

const ApplyList = () => {
  const navigate = useNavigate();
  const { modal, message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [currentRecord, setCurrentRecord] = useState<ExamItemDTO>();
  const [loadingMap, setLoadingMap] = useState<Record<string, boolean>>({});

  const checkPermission = usePermission();
  const { runAsync: updateConfig, loading: updateLoading } = useRequest(configExamBizType, {
    manual: true,
  });

  useEffect(() => {
    if (open) {
      form.setFieldValue('bizOpportunityType', currentRecord?.businessOpportunityType);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  const handleChangeStatus = async (data: { id: number; status: ExamStatusEnum }) => {
    const text = data.status === ExamStatusEnum.ENABLE ? '启用' : '停用';

    setLoadingMap((prev) => ({ ...prev, [data.id]: true }));

    try {
      await modifyExamPaperStatus(data);
      message.success(`${text}成功`);
      actionRef.current?.reload();
    } catch (error) {}

    setLoadingMap((prev) => ({ ...prev, [data.id]: false }));
  };

  const handleCopy = (id: number) => {
    modal.confirm({
      title: '复制',
      content: '确定要复制此模版吗？',
      onOk: async () => {
        await copyExamPaper({ id });
        message.success('复制成功');
        actionRef.current?.reload();
      },
    });
  };

  const handleDelete = (id: number) => {
    modal.confirm({
      title: '删除',
      content: '确定要删除此模版吗？',
      onOk: async () => {
        await deleteExamPaper({ id });
        message.success('删除成功');
        actionRef.current?.reload();
      },
    });
  };

  const columns: ProColumns<ExamItemDTO>[] = [
    {
      title: '模版名称',
      dataIndex: 'paperName',
      search: { transform: (name) => ({ name }) },
    },
    {
      title: '模版状态',
      dataIndex: 'status',
      valueEnum: {
        [ExamStatusEnum.ENABLE]: '启用',
        [ExamStatusEnum.DISABLE]: '停用',
      },
      renderText: (value, { examPaperId }) => {
        const isEnable = value === ExamStatusEnum.ENABLE;

        if (checkPermission(PermissionsMap.StandardSettingsTemplateApplySwitch)) {
          return (
            <Switch
              loading={loadingMap[examPaperId]}
              value={isEnable}
              checkedChildren="启用"
              unCheckedChildren="停用"
              onChange={() =>
                handleChangeStatus({
                  id: examPaperId,
                  status: isEnable ? ExamStatusEnum.DISABLE : ExamStatusEnum.ENABLE,
                })
              }
            />
          );
        }

        return isEnable ? <Tag color="success">启用</Tag> : <Tag color="error">停用</Tag>;
      },
    },
    {
      title: '使用商机',
      dataIndex: 'businessOpportunityType',
      search: false,
      valueEnum: optionsToValueEnum(commonBOTypeOptions),
    },
    { title: '创建人', dataIndex: 'createName', search: false },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      search: false,
      valueType: 'dateTime',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      search: false,
      width: 220,
      render: (_, record) => (
        <div className="flex gap-2 justify-center">
          <Permission value={PermissionsMap.StandardSettingsTemplateApplyEdit}>
            <a onClick={() => navigate(`franchise-application/${record.examPaperId}`)}>编辑</a>
          </Permission>
          <a
            onClick={() => {
              setOpen(true);
              setCurrentRecord(record);
            }}
          >
            配置使用商机
          </a>
          <Permission value={PermissionsMap.StandardSettingsTemplateApplyCopy}>
            <a onClick={() => handleCopy(record.examPaperId)}>复制</a>
          </Permission>
          <Permission value={PermissionsMap.StandardSettingsTemplateApplyDelete}>
            <Typography.Link type="danger" onClick={() => handleDelete(record.examPaperId)}>
              删除
            </Typography.Link>
          </Permission>
        </div>
      ),
    },
  ];

  return (
    <>
      <ProTable
        actionRef={actionRef}
        rowKey="examPaperId"
        size="small"
        sticky
        bordered
        cardProps={false}
        options={false}
        columns={columns}
        toolbar={{
          actions: [
            <Permission value={PermissionsMap.StandardSettingsTemplateApplyCreate}>
              <Button type="primary" onClick={() => navigate('franchise-application/create')}>
                新建
              </Button>
            </Permission>,
          ],
        }}
        request={async ({ current, ...params }) => {
          const res = await getExamList({
            businessType: BusinessTypeEnum.ONLINE_APPLY,
            pageNum: current,
            ...params,
          });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <Modal
        title="配置使用商机"
        open={open}
        destroyOnHidden
        confirmLoading={updateLoading}
        onCancel={() => setOpen(false)}
        onOk={form.submit}
      >
        <Form
          form={form}
          onFinish={async (values) => {
            await updateConfig({ examPaperId: currentRecord!.examPaperId, ...values });
            message.success('修改成功');
            setOpen(false);
            actionRef.current?.reload();
          }}
        >
          <Form.Item
            label="商机类型"
            name="bizOpportunityType"
            rules={[{ required: true, message: '请选择商机' }]}
          >
            <Select placeholder="请选择商机类型" options={commonBOTypeOptions} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default ApplyList;
