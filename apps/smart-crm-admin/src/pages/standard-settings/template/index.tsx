import { useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { PermissionsMap, usePermission } from '@src/permissions';
import { getDefaultActiveKeyBySearchParam } from '@src/utils';
import { Tabs } from 'antd';
import { useSearchParams } from 'react-router-dom';
import ApplyList from './apply-list';
import EvaluationList from './evaluation-list';

const Template = () => {
  const checkPermission = usePermission();

  const [searchParams, setSearchParams] = useSearchParams();
  const [activeKey, setActiveKey] = useState(() =>
    getDefaultActiveKeyBySearchParam(
      ['apply', 'evaluation', 'information'],
      searchParams.get('tab'),
    ),
  );

  return (
    <PageContainer>
      <Tabs
        activeKey={activeKey}
        items={[
          {
            label: '加盟申请表',
            key: 'apply',
            children: <ApplyList />,
            auth: PermissionsMap.StandardSettingsTemplateApply,
          },
          {
            label: '面审评估表',
            key: 'evaluation',
            children: <EvaluationList />,
            auth: PermissionsMap.StandardSettingsTemplateEvaluation,
          },
        ].filter((i) => checkPermission(i.auth))}
        onChange={(key) => {
          setActiveKey(key);
          setSearchParams({ tab: key });
        }}
      />
    </PageContainer>
  );
};

export default Template;
