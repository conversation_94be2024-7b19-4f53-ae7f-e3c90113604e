import { useRef, useState } from 'react';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@src/components';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { BusinessTypeEnum } from '@src/services/common/type';
import {
  copyExamPaper,
  deleteExamPaper,
  getExamList,
  modifyExamPaperStatus,
} from '@src/services/standard-settings/template';
import { ExamItemDTO, ExamStatusEnum } from '@src/services/standard-settings/template/type';
import { App, Button, Switch, Tag, Typography } from 'antd';
import { useNavigate } from 'react-router-dom';

const EvaluationQuestion = () => {
  const navigate = useNavigate();
  const checkPermission = usePermission();
  const { modal, message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [loadingMap, setLoadingMap] = useState<Record<string, boolean>>({});

  const handleChangeStatus = async (data: { id: number; status: ExamStatusEnum }) => {
    const text = data.status === ExamStatusEnum.ENABLE ? '启用' : '停用';

    setLoadingMap((prev) => ({ ...prev, [data.id]: true }));

    try {
      await modifyExamPaperStatus(data);
      message.success(`${text}成功`);
      actionRef.current?.reload();
    } catch (error) {}

    setLoadingMap((prev) => ({ ...prev, [data.id]: false }));
  };

  const handleCopy = (id: number) => {
    modal.confirm({
      title: '复制',
      content: '确定要复制此模版吗？',
      onOk: async () => {
        await copyExamPaper({ id });
        message.success('复制成功');
        actionRef.current?.reload();
      },
    });
  };

  const handleDelete = (id: number) => {
    modal.confirm({
      title: '删除',
      content: '确定要删除此模版吗？',
      onOk: async () => {
        await deleteExamPaper({ id });
        message.success('删除成功');
        actionRef.current?.reload();
      },
    });
  };

  const columns: ProColumns<ExamItemDTO>[] = [
    {
      title: '模版名称',
      dataIndex: 'paperName',
      search: { transform: (name) => ({ name }) },
    },
    {
      title: '模版状态',
      dataIndex: 'status',
      valueEnum: {
        [ExamStatusEnum.ENABLE]: '启用',
        [ExamStatusEnum.DISABLE]: '停用',
      },
      renderText: (value, { examPaperId }) => {
        const isEnable = value === ExamStatusEnum.ENABLE;

        if (checkPermission(PermissionsMap.StandardSettingsTemplateEvaluationSwitch)) {
          return (
            <Switch
              loading={loadingMap[examPaperId]}
              value={isEnable}
              checkedChildren="启用"
              unCheckedChildren="停用"
              onChange={() =>
                handleChangeStatus({
                  id: examPaperId,
                  status: isEnable ? ExamStatusEnum.DISABLE : ExamStatusEnum.ENABLE,
                })
              }
            />
          );
        }

        return isEnable ? <Tag color="success">启用</Tag> : <Tag color="error">停用</Tag>;
      },
    },
    { title: '创建人', dataIndex: 'createName', search: false },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      search: false,
      valueType: 'dateTime',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      search: false,
      width: 150,
      render: (_, { examPaperId }) => (
        <div className="flex gap-2 justify-center">
          <Permission value={PermissionsMap.StandardSettingsTemplateEvaluationEdit}>
            <a onClick={() => navigate(`evaluation/${examPaperId}`)}>编辑</a>
          </Permission>
          <Permission value={PermissionsMap.StandardSettingsTemplateEvaluationCopy}>
            <a onClick={() => handleCopy(examPaperId)}>复制</a>
          </Permission>
          <Permission value={PermissionsMap.StandardSettingsTemplateEvaluationDelete}>
            <Typography.Link type="danger" onClick={() => handleDelete(examPaperId)}>
              删除
            </Typography.Link>
          </Permission>
        </div>
      ),
    },
  ];

  return (
    <ProTable
      actionRef={actionRef}
      rowKey="examPaperId"
      sticky
      size="small"
      cardProps={false}
      bordered
      options={false}
      columns={columns}
      toolbar={{
        actions: [
          <Permission value={PermissionsMap.StandardSettingsTemplateEvaluationCreate}>
            <Button type="primary" onClick={() => navigate('evaluation/create')}>
              新建
            </Button>
          </Permission>,
        ],
      }}
      request={async ({ current, ...params }) => {
        const res = await getExamList({
          businessType: BusinessTypeEnum.INTERVIEW_EVALUATION,
          pageNum: current,
          ...params,
        });

        return {
          data: res.result,
          total: res.total,
        };
      }}
    />
  );
};

export default EvaluationQuestion;
