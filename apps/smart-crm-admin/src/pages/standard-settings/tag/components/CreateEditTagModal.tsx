import { useState } from 'react';
import { CaretDownOutlined, PlusOutlined } from '@ant-design/icons';
import {
  createTagGroup,
  editTagGroup,
  getTagGroupDetail,
} from '@src/services/standard-settings/info';
import {
  TagRemoveRuleEnum,
  TagStatusEnum,
  TagTypeEnum,
} from '@src/services/standard-settings/info/type';
import { useRequest } from 'ahooks';
import { App, Dropdown, Form, Input, Modal, ModalProps, Radio, Tag } from 'antd';

type Values = {
  id: number;
  name: string;
  type: TagTypeEnum;
  removeRule: TagRemoveRuleEnum;
  tags: { name: string; id?: number }[];
};

interface CreateEditTagModalProps extends ModalProps {
  currentEditId: null | number;
  onSuccess: () => void;
}

const CreateEditTagModal: React.FC<CreateEditTagModalProps> = ({
  open,
  currentEditId,
  onSuccess,
  ...props
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();

  const { loading: createLoading, runAsync: create } = useRequest(createTagGroup, { manual: true });
  const { loading: editLoading, runAsync: edit } = useRequest(editTagGroup, { manual: true });
  const { loading, data } = useRequest(() => getTagGroupDetail(currentEditId!), {
    ready: open && !!currentEditId,
    onSuccess: (res) => {
      form.setFieldsValue({
        name: res.name,
        type: res.type,
        removeRule: res.removeRule,
        tags: res.tags.map((i) => ({ id: i.id, name: i.name })),
      });
    },
  });

  const handleSave = async (values: Values) => {
    if (!currentEditId) {
      await create(values);
      message.success('添加成功');
    } else {
      const addTags = values.tags.filter((i) => !i.id).map((i) => i.name);
      const deleteTags = data!.tags
        .filter((item) => !values.tags.some((i) => i.id === item.id))
        .map((i) => ({ id: i.id, status: TagStatusEnum.INVALID }));
      const editTags = values.tags.filter((item) => {
        if (item.id) {
          const origin = data!.tags.find((i) => i.id === item.id);

          return origin?.name !== item.name;
        }

        return false;
      }) as { id: number; name: string }[];

      await edit({
        id: currentEditId,
        name: values.name,
        type: values.type,
        removeRule: values.removeRule,
        addTags,
        updateTags: [...deleteTags, ...editTags],
      });
      message.success('修改成功');
    }

    onSuccess();
  };

  return (
    <Modal
      title={currentEditId ? '编辑分组' : '添加分组'}
      open={open}
      destroyOnHidden
      loading={currentEditId ? loading : false}
      confirmLoading={createLoading || editLoading}
      modalRender={(node) => (
        <Form form={form} clearOnDestroy labelCol={{ span: 5 }} onFinish={handleSave}>
          {node}
        </Form>
      )}
      onOk={form.submit}
      {...props}
    >
      <Form.Item
        label="标签组名称"
        name="name"
        normalize={(val) => val.trim()}
        rules={[
          { required: true, message: '请输入标签组名称' },
          { max: 40, message: '最多输入 40 个字符' },
        ]}
      >
        <Input placeholder="请输入标签组名称" />
      </Form.Item>
      {currentEditId && (
        <>
          <Form.Item
            label="标签组类型"
            name="type"
            rules={[{ required: true, message: '请选择标签组类型' }]}
          >
            <Radio.Group
              options={[
                { label: '单选', value: TagTypeEnum.RADIO },
                { label: '多选', value: TagTypeEnum.MULTI_SELECT },
              ]}
            />
          </Form.Item>
          <Form.Item
            label="标签移除"
            name="removeRule"
            extra="放弃线索进入线索池后，是否对当前标签组信息进行移除"
            rules={[{ required: true, message: '请选择标签移除类型' }]}
          >
            <Radio.Group
              options={[
                { label: '移除', value: TagRemoveRuleEnum.REMOVE },
                { label: '不移除', value: TagRemoveRuleEnum.NOT_REMOVE },
              ]}
            />
          </Form.Item>
          <Form.Item label="标签" name="tags" extra="删除后不会影响过去使用该标签线索的标签记录">
            <TagSelect />
          </Form.Item>
        </>
      )}
    </Modal>
  );
};

interface TagSelectProps {
  value?: { id?: number; name: string }[];
  onChange?: (value: { id?: number; name: string }[]) => void;
}

const TagSelect: React.FC<TagSelectProps> = ({ value, onChange }) => {
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [editIndex, setEditIndex] = useState(-1);

  return (
    <>
      <div className="flex gap-2 flex-wrap">
        {value?.map((i, index) => (
          <Dropdown
            key={i.name}
            placement="bottom"
            trigger={['click']}
            menu={{
              items: [
                {
                  label: '编辑',
                  key: '1',
                  onClick: () => {
                    setOpen(true);
                    setEditIndex(index);
                    form.setFieldValue('name', i.name);
                  },
                },
                {
                  label: '删除',
                  key: '2',
                  danger: true,
                  onClick: () => {
                    const newValue = [...value];

                    newValue.splice(index, 1);
                    onChange?.(newValue);
                    setEditIndex(-1);
                  },
                },
              ],
            }}
          >
            <Tag className="cursor-pointer mr-0">
              {i.name} <CaretDownOutlined />
            </Tag>
          </Dropdown>
        ))}
        <Tag
          className="border-dashed cursor-pointer mr-0"
          onClick={() => {
            setOpen(true);
            setEditIndex(-1);
          }}
        >
          <PlusOutlined /> 添加标签
        </Tag>
      </div>

      <Modal
        title={editIndex > -1 ? '编辑标签' : '添加标签'}
        open={open}
        destroyOnHidden
        onOk={form.submit}
        onCancel={() => setOpen(false)}
      >
        <Form
          form={form}
          clearOnDestroy
          onFinish={(values) => {
            const { name } = values;

            if (editIndex > -1) {
              const newValue = [...(value || [])];

              newValue[editIndex].name = name;
              onChange?.(newValue);
            } else {
              onChange?.((value || []).concat({ name }));
            }

            setOpen(false);
          }}
        >
          <Form.Item
            label="标签名称"
            name="name"
            // 禁止输入空格
            normalize={(val) => val.trim()}
            rules={[
              { required: true, message: '请输入标签名称', whitespace: true },
              { max: 40, message: '标签不允许超过 40 个字符' },
              {
                // 重复判断
                validator: (_, val) => {
                  if (editIndex > -1) {
                    const hasSame = value
                      ?.filter((__, index) => index !== editIndex)
                      ?.some((i) => i.name === val);

                    return hasSame
                      ? Promise.reject(new Error('不能与其它标签名称重复'))
                      : Promise.resolve();
                  } else if (value?.some((i) => i.name === val)) {
                    return Promise.reject(new Error('不能与其它标签名称重复'));
                  }

                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input placeholder="请输入标签名称，长度不超过 40 个字符" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default CreateEditTagModal;
