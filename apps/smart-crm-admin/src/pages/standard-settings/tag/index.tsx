import { useRef, useState } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ProTable, TableActions } from '@src/components';
import { deleteTagGroup, getTagList } from '@src/services/standard-settings/info';
import {
  TagCategoryEnum,
  TagGroupItem,
  TagRemoveRuleEnum,
  TagStatusEnum,
  TagTypeEnum,
} from '@src/services/standard-settings/info/type';
import { compatibleTableActionWidth } from '@src/utils';
import { App, Button, Tag } from 'antd';
import CreateEditTagModal from './components/CreateEditTagModal';

const TagManagement = () => {
  const { message, modal } = App.useApp();
  const [open, setOpen] = useState(false);
  const [currentEditId, setCurrentEditId] = useState<null | number>(null);
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<TagGroupItem>[] = [
    {
      title: '标签组名称',
      dataIndex: 'name',
      renderText: (value, { category }) =>
        category === TagCategoryEnum.系统 ? (
          <div className="flex justify-between">
            {value}
            <Tag className="ml-2" color="gold">
              系统标签
            </Tag>
          </div>
        ) : (
          value
        ),
    },
    {
      title: '标签',
      dataIndex: 'tags',
      renderText: (value: { id: number; name: string }[]) => (
        <div className="flex gap-2 flex-wrap">
          {value.map((i) => (
            <Tag key={i.id} className="mr-0">
              {i.name}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '标签组类型',
      dataIndex: 'type',
      width: 100,
      renderText: (value) => (value === TagTypeEnum.RADIO ? '单选' : '多选'),
    },
    {
      title: '标签移除',
      dataIndex: 'removeRule',
      width: 100,
      tooltip: '放弃线索进入线索池后，是否对当前标签组信息进行移除',
      renderText: (value) => (value === TagRemoveRuleEnum.REMOVE ? '移除' : '不移除'),
    },
    {
      title: '操作',
      align: 'center',
      width: compatibleTableActionWidth(100),
      render: (_, { id, category }) => (
        <TableActions
          shortcuts={[
            {
              label: '编辑',
              show: category === TagCategoryEnum.常规,
              onClick: () => {
                setOpen(true);
                setCurrentEditId(id);
              },
            },
            {
              label: '删除',
              danger: true,
              show: category === TagCategoryEnum.常规,
              onClick: () =>
                modal.confirm({
                  title: '删除标签分组',
                  content: '确定要删除该分组吗？删除后不会影响过去使用该标签线索的标签记录',
                  onOk: async () => {
                    await deleteTagGroup(id);
                    message.success('删除成功');
                    actionRef.current?.reload();
                  },
                }),
            },
          ]}
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        sticky
        size="small"
        actionRef={actionRef}
        cardProps={false}
        bordered
        search={false}
        options={false}
        columns={columns}
        toolbar={{
          subTitle: '自定义配置线索标签，使用标签对线索数据进行分类标识与管理',
          actions: [
            <Button
              type="primary"
              onClick={() => {
                setOpen(true);
                setCurrentEditId(null);
              }}
            >
              添加分组
            </Button>,
          ],
        }}
        pagination={false}
        rowKey="id"
        request={async () => {
          const res = await getTagList({ status: TagStatusEnum.AVAILABLE });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <CreateEditTagModal
        open={open}
        currentEditId={currentEditId}
        onCancel={() => setOpen(false)}
        onSuccess={() => {
          setOpen(false);
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default TagManagement;
