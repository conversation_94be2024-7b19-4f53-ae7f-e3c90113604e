import { updateBusinessParameter } from '@src/services/standard-settings/business-parameter';
import { BusinessParameterDTO } from '@src/services/standard-settings/business-parameter/type';
import { useRequest } from 'ahooks';
import { App, Form, Modal, ModalProps, Switch } from 'antd';

interface ClueAvgModalProps extends ModalProps {
  data?: BusinessParameterDTO;
  onSuccess: () => void;
}

const ClueAvgModal: React.FC<ClueAvgModalProps> = ({ data, onSuccess, ...props }) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();

  const { runAsync: update, loading: updateLoading } = useRequest(updateBusinessParameter, {
    manual: true,
  });

  return (
    <Modal destroyOnHidden confirmLoading={updateLoading} onOk={form.submit} {...props}>
      <Form
        form={form}
        clearOnDestroy
        initialValues={data?.configValue}
        onFinish={async (values) => {
          await update({ id: data!.id, configValue: values });
          message.success('修改成功');
          onSuccess();
        }}
      >
        <Form.Item label="平均分配" name="switch" required>
          <Switch />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ClueAvgModal;
