import { useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import useAllUsers from '@src/hooks/useAllUsers';
import { getBusinessParameterList } from '@src/services/standard-settings/business-parameter';
import {
  BusinessParameterDTO,
  ConfigKeyEnum,
} from '@src/services/standard-settings/business-parameter/type';
import { useRequest } from 'ahooks';
import { Table } from 'antd';
import ChooseDirectorModal from './components/ChooseDirectorModal';
import ClueAvgModal from './components/ClueAvgModal';
import TrainingPersonModal from './components/TrainingPersonModal';

const BusinessParameter = () => {
  const [clueAvgModalOpen, setClueAvgModalOpen] = useState(false);
  const [chooseDirectorModalOpen, setChooseDirectorModalOpen] = useState(false);
  const [trainingPersonModalOpen, setTrainingPersonModal] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<BusinessParameterDTO>();

  const { data, loading, refresh } = useRequest(getBusinessParameterList);
  const { data: users } = useAllUsers();

  return (
    <PageContainer>
      <Table
        rowKey="id"
        dataSource={data}
        loading={loading}
        bordered
        pagination={false}
        columns={[
          { title: '业务模块', dataIndex: 'configName' },
          {
            title: '结果',
            dataIndex: 'configValue',
            render: (value, record) => {
              switch (record.configKey) {
                case ConfigKeyEnum.FRANCHISE_DEFAULT_OWNER:
                  return users?.find((i) => i.id === value.userId)?.name;

                case ConfigKeyEnum.CLUE_AVG_ALLOCATION_SWITCH:
                  return value.switch ? '开启' : '关闭';

                case ConfigKeyEnum.TRAINING_PERSON_SWITCH:
                  return value.switch ? '开启' : '关闭';
              }
            },
          },
          {
            title: '更新时间',
            dataIndex: 'updateTime',
          },
          {
            title: '操作',
            key: 'action',
            align: 'center',
            render: (_, record) => (
              <a
                onClick={() => {
                  switch (record.configKey) {
                    case ConfigKeyEnum.FRANCHISE_DEFAULT_OWNER:
                      setChooseDirectorModalOpen(true);

                      break;

                    case ConfigKeyEnum.CLUE_AVG_ALLOCATION_SWITCH:
                      setClueAvgModalOpen(true);

                      break;
                    case ConfigKeyEnum.TRAINING_PERSON_SWITCH:
                      setTrainingPersonModal(true);

                      break;
                    default:
                      break;
                  }

                  setCurrentRecord(record);
                }}
              >
                编辑
              </a>
            ),
          },
        ]}
      />

      <ClueAvgModal
        title={currentRecord?.configName}
        data={currentRecord}
        open={clueAvgModalOpen}
        onCancel={() => setClueAvgModalOpen(false)}
        onSuccess={() => {
          setClueAvgModalOpen(false);
          refresh();
        }}
      />

      <ChooseDirectorModal
        title={currentRecord?.configName}
        data={currentRecord}
        open={chooseDirectorModalOpen}
        onCancel={() => setChooseDirectorModalOpen(false)}
        onSuccess={() => {
          setChooseDirectorModalOpen(false);
          refresh();
        }}
      />

      <TrainingPersonModal
        title={currentRecord?.configName}
        data={currentRecord}
        open={trainingPersonModalOpen}
        onCancel={() => setTrainingPersonModal(false)}
        onSuccess={() => {
          setTrainingPersonModal(false);
          refresh();
        }}
      />
    </PageContainer>
  );
};

export default BusinessParameter;
