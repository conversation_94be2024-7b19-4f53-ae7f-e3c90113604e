import { useRef, useState } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@src/components';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import {
  deleteClueLimitRule,
  getClueLimitRuleList,
  getClueLimitRuleStatus,
  switchClueLimitRule,
} from '@src/services/standard-settings/rule';
import {
  ClueLimitRuleListItem,
  ClueLimitRuleTypeEnum,
} from '@src/services/standard-settings/rule/type';
import { useRequest } from 'ahooks';
import { App, Button, Form, Skeleton, Switch, Tabs, Tag } from 'antd';
import CreateOrEditRuleModal from './components/CreateOrEditRuleModal';

const Rule = () => {
  const checkPermission = usePermission();
  const { message, modal } = App.useApp();
  const [enable, setEnable] = useState(false);
  const [createOrEditRuleModalOpen, setCreateOrEditRuleModal] = useState(false);
  const [currentEditId, setCurrentEditId] = useState<null | number>(null);
  const [isDefault, setIsDefault] = useState(false);
  const actionRef = useRef<ActionType>(null);

  const { loading: getStatusLoading } = useRequest(getClueLimitRuleStatus, {
    onSuccess: (res) => setEnable(res.status === 'ON'),
  });

  const { loading: switchLoading, runAsync: switchEnable } = useRequest(switchClueLimitRule, {
    manual: true,
  });

  const handleSwitch = async (checked: boolean) => {
    setEnable(checked);

    try {
      await switchEnable({ status: checked ? 'ON' : 'OFF' });
      message.success(checked ? '开启成功' : '关闭成功');
    } catch (error) {
      setEnable(!checked);
    }
  };

  const columns: ProColumns<ClueLimitRuleListItem>[] = [
    { title: '规则名', width: 200, dataIndex: 'name' },
    {
      title: '成员',
      dataIndex: 'bindUsers',
      render: (_, { ruleType, bindUsers }) =>
        ruleType === ClueLimitRuleTypeEnum.DEFAULT ? (
          '适用于未被划分规则的员工'
        ) : (
          <div className="flex gap-2 flex-wrap">
            {bindUsers.map((user) => (
              <Tag key={user.id} className="mr-0">
                {user.name}
              </Tag>
            ))}
          </div>
        ),
    },
    {
      title: '线索上限数',
      width: 140,
      dataIndex: 'limitNumber',
      renderText: (value) => (value === 2147483647 ? '无限制' : value),
    },
    {
      title: '操作',
      align: 'center',
      key: 'action',
      width: 140,
      render: (_, { id, ruleType }) => {
        const isRuleDefault = ruleType === ClueLimitRuleTypeEnum.DEFAULT;

        return (
          <div className="flex gap-2 justify-center">
            <Permission value={PermissionsMap.StandardSettingsRuleUpdate}>
              <Button
                type="link"
                size="small"
                onClick={() => {
                  setCreateOrEditRuleModal(true);
                  setCurrentEditId(id);
                  setIsDefault(isRuleDefault);
                }}
              >
                编辑
              </Button>
            </Permission>
            {!isRuleDefault && (
              <Permission value={PermissionsMap.StandardSettingsRuleDelete}>
                <Button
                  type="link"
                  size="small"
                  danger
                  onClick={() =>
                    modal.confirm({
                      title: '删除规则',
                      content: '确定要删除该规则吗？',
                      onOk: async () => {
                        await deleteClueLimitRule(id);
                        message.success('删除成功');
                        actionRef.current?.reload();
                      },
                    })
                  }
                >
                  删除
                </Button>
              </Permission>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <Tabs
        items={[
          {
            key: '1',
            label: '线索上限',
            children: (
              <>
                <ProTable
                  sticky
                  actionRef={actionRef}
                  cardProps={false}
                  bordered
                  toolbar={{
                    title: (
                      <Form.Item label="启用 “线索上限”" className="mb-0">
                        {getStatusLoading ? (
                          <Skeleton.Button active />
                        ) : (
                          <Switch
                            checked={enable}
                            disabled={!checkPermission(PermissionsMap.StandardSettingsRuleSwitch)}
                            loading={switchLoading}
                            onChange={handleSwitch}
                          />
                        )}
                      </Form.Item>
                    ),
                    actions: [
                      <Permission value={PermissionsMap.StandardSettingsRuleCreate}>
                        <Button
                          type="primary"
                          onClick={() => {
                            setCreateOrEditRuleModal(true);
                            setCurrentEditId(null);
                            setIsDefault(false);
                          }}
                        >
                          添加规则
                        </Button>
                      </Permission>,
                    ],
                  }}
                  rowKey="id"
                  search={false}
                  options={false}
                  size="small"
                  columns={columns}
                  request={async ({ current, pageSize }) => {
                    const res = await getClueLimitRuleList({
                      pageNum: current,
                      pageSize,
                    });

                    return {
                      data: res.result,
                      total: res.total,
                    };
                  }}
                />

                <p className="mt-5">
                  线索上限使用说明：
                  <br />
                  1、线索上限依赖限制员工名下线索的线索数。
                  <br />
                  2、如在设定线索上限数前，员工名下线索数已超出线索上限限时，名下的线索仍可继续跟进。
                  <br />
                  3、规则被删除或成员从规则中删除时，该员工将受默认规则限制。
                </p>

                <CreateOrEditRuleModal
                  open={createOrEditRuleModalOpen}
                  currentEditId={currentEditId}
                  isDefault={isDefault}
                  onCancel={() => setCreateOrEditRuleModal(false)}
                  onSuccess={() => {
                    setCreateOrEditRuleModal(false);
                    actionRef.current?.reload();
                  }}
                />
              </>
            ),
          },
        ]}
      />
    </PageContainer>
  );
};

export default Rule;
