import { getUsers } from '@src/services/common';
import {
  createClueLimitRule,
  editClueLimitRule,
  getClueLimitRule,
} from '@src/services/standard-settings/rule';
import { ClueLimitRuleTypeEnum } from '@src/services/standard-settings/rule/type';
import { useRequest } from 'ahooks';
import { App, Form, Input, InputNumber, Modal, ModalProps, Select } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';

interface CreateOrEditRuleModalProps extends ModalProps {
  currentEditId: null | number;
  isDefault: boolean;
  onSuccess: () => void;
}

const CreateOrEditRuleModal: React.FC<CreateOrEditRuleModalProps> = ({
  open,
  currentEditId,
  isDefault,
  onSuccess,
  ...props
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();

  const { data: users, loading: getUsersLoading } = useRequest(
    () => getUsers({ numberLimit: 'DEFAULT_RULE' }),
    { ready: open },
  );
  const { loading: createLoading, runAsync: create } = useRequest(createClueLimitRule, {
    manual: true,
  });
  const { loading: editLoading, runAsync: edit } = useRequest(editClueLimitRule, {
    manual: true,
  });
  const { data, loading: getDetailLoading } = useRequest(() => getClueLimitRule(currentEditId!), {
    ready: open && !!currentEditId,
    onSuccess: (res) => {
      if (res.ruleType === ClueLimitRuleTypeEnum.DEFAULT) {
        // 为 -1 时，是默认规则，要让他重新设置
        form.setFieldValue(
          'limitNumber',
          res.limitNumber === 2147483647 ? undefined : res.limitNumber,
        );
      } else {
        form.setFieldsValue({
          name: res.name,
          bindUsers: res.bindUsers.map((i) => i.id),
          limitNumber: res.limitNumber === -1 ? undefined : res.limitNumber,
        });
      }
    },
  });

  const handleSave = async (values: { name: string; limitNumber: number; bindUsers: number[] }) => {
    if (currentEditId) {
      if (data?.ruleType === ClueLimitRuleTypeEnum.DEFAULT) {
        await edit({
          id: currentEditId!,
          limitNumber: values.limitNumber,
        });
      } else {
        const addUserIds = values.bindUsers.filter(
          (id) => !data?.bindUsers.some((i) => i.id === id),
        );
        const removeUserIds = (data?.bindUsers || [])
          .filter((item) => !values.bindUsers.includes(item.id))
          .map((i) => i.id);

        await edit({
          id: currentEditId!,
          name: values.name,
          limitNumber: values.limitNumber,
          addUserIds,
          removeUserIds,
        });
      }

      message.success('编辑成功');
    } else {
      await create({
        name: values.name,
        userIds: values.bindUsers,
        limitNumber: values.limitNumber,
      });
      message.success('添加成功');
    }

    onSuccess();
  };

  return (
    <Modal
      title={currentEditId ? '修改规则' : '添加规则'}
      open={open}
      destroyOnHidden
      loading={currentEditId ? getDetailLoading : false}
      confirmLoading={createLoading || editLoading}
      modalRender={(node) => (
        <Form form={form} clearOnDestroy labelCol={{ span: 5 }} onFinish={handleSave}>
          {node}
        </Form>
      )}
      onOk={form.submit}
      {...props}
    >
      {!isDefault && (
        <>
          <Form.Item
            label="规则名称"
            name="name"
            rules={[
              { required: true, message: '请输入规则名称' },
              { max: 20, message: '不能超过超过 20 个字符' },
            ]}
          >
            <Input placeholder="请输入规则名称，最多不超过 20 个字符" />
          </Form.Item>
          <Form.Item
            label="使用员工"
            name="bindUsers"
            rules={[{ required: true, message: '请选择使用员工' }]}
          >
            <Select
              showSearch
              mode="multiple"
              loading={getUsersLoading}
              optionFilterProp="name"
              options={
                [
                  // 编辑规则的时候，带上原来的
                  ...(currentEditId ? data?.bindUsers || [] : []),
                  ...(users || []),
                ] as unknown as DefaultOptionType[]
              }
              fieldNames={{ label: 'name', value: 'id' }}
              placeholder="请选择人员"
            />
          </Form.Item>
        </>
      )}
      <Form.Item
        label="线索上限数"
        name="limitNumber"
        rules={[{ required: true, message: '请输入线索上限数' }]}
      >
        <InputNumber
          placeholder="请输入线索上限数"
          precision={0}
          min={1}
          max={9999}
          style={{ width: 200 }}
        />
      </Form.Item>
    </Modal>
  );
};

export default CreateOrEditRuleModal;
