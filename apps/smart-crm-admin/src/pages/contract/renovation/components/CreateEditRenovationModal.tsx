import React, { useRef, useState } from 'react';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { Encrypt, ShopSelect } from '@src/components';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import {
  getRenovationContractDetail,
  saveAndSubmitRenovationContract,
  saveRenovationContract,
  updateRenovationContract,
} from '@src/services/contract/renovation';
import { RenovationScopeEnum, RenovationTypeEnum } from '@src/services/contract/renovation/type';
import { getCustomerDetail } from '@src/services/customers';
import { getShopCertDetail, getShopDetail } from '@src/services/shop/list';
import { SignerMainTypeEnum } from '@src/services/shop/list/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, <PERSON><PERSON>, Card, Form, Input, Modal, ModalProps, Select } from 'antd';
import PaymentFormList from '../../components/PaymentFormList';

interface CreateEditRenovationModalProps extends ModalProps {
  getId: () => number | null;
  onSuccess: () => void;
}

const CreateEditRenovationModal = React.forwardRef<ModalRef, CreateEditRenovationModalProps>(
  ({ getId, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    const saveTypeRef = useRef<'save' | 'submit'>('save');

    const id = getId();
    const isEdit = !!id;

    const {
      data: customer,
      loading: getCustomerLoading,
      runAsync: getCustomer,
      mutate: mutateCustomer,
    } = useRequest(getCustomerDetail, {
      manual: true,
    });
    const { data, loading, mutate } = useRequest(() => getRenovationContractDetail(id!), {
      ready: open && isEdit,
      onSuccess: (res) => {
        form.setFieldsValue(res);
        getCustomer(res.customerId);
      },
    });
    const { runAsync: save, loading: saveLoading } = useRequest(saveRenovationContract, {
      manual: true,
    });
    const { runAsync: submit, loading: submitLoading } = useRequest(
      saveAndSubmitRenovationContract,
      { manual: true },
    );
    const { runAsync: update, loading: updateLoading } = useRequest(updateRenovationContract, {
      manual: true,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        mutate(undefined);
        mutateCustomer(undefined);
      },
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      const params = {
        ...values,
        renovationScope:
          values.renovationType === RenovationTypeEnum.整店全部翻新 ? [] : values.renovationScope,
      };

      if (isEdit) {
        await update({ id, ...params });
        message.success('修改成功');
      } else {
        if (saveTypeRef.current === 'save') {
          await save(params);
          message.success('保存成功');
        } else {
          await submit(params);
          message.success('提交审核成功');
        }
      }

      setOpen(false);
      onSuccess();
    };

    const handleShopIdChange = async (shopId: string | undefined) => {
      // 先清除掉这些字段，预防后面请求出问题
      form.resetFields([
        'shopName',
        'licenseSubjectName',
        'shopRealUseArea',
        'realEstateAddress',
        'toPublic',
        'signSubject',
        'socialCreditCode',
      ]);

      if (!shopId) {
        return;
      }

      const [shopDetail, shopCert] = await Promise.all([
        getShopDetail(shopId),
        getShopCertDetail(shopId),
      ]);

      form.setFieldsValue({
        shopName: shopDetail.shopName,
        licenseSubjectName: shopCert?.license?.licenseName,
        shopRealUseArea: shopDetail.realUseArea,
        realEstateAddress: shopDetail.propertyAddress,
        toPublic: shopDetail.signerMainType === SignerMainTypeEnum.公司,
        signSubject: shopDetail.signerMain,
        socialCreditCode: shopDetail.signerUsci,
      });
    };

    const shopInfoItems: {
      label: string;
      name: string;
      props?: Record<string, any>;
      component?: React.FunctionComponent<any>;
    }[] = [
      { label: '门店冠名', name: 'shopName' },
      { label: '营业执照主体名称', name: 'licenseSubjectName' },
      {
        label: '门店实际使用面积',
        name: 'shopRealUseArea',
        component: Input,
        props: {
          addonAfter: 'm²',
        },
      },
      { label: '不动产权证地址', name: 'realEstateAddress' },
    ];

    return (
      <Modal
        title={isEdit ? '编辑门店重装合同' : '新建门店重装合同'}
        open={open}
        width={1000}
        destroyOnHidden
        loading={loading}
        confirmLoading={saveLoading || submitLoading || updateLoading || getCustomerLoading}
        styles={{
          body: { maxHeight: '60vh', margin: '0 -24px', padding: '0 24px' },
        }}
        okText="保存"
        okButtonProps={{ ghost: !isEdit }}
        modalRender={(node) => (
          <Form
            form={form}
            labelCol={{ span: 9 }}
            scrollToFirstError
            clearOnDestroy
            onFinish={handleSave}
          >
            {node}
          </Form>
        )}
        footer={(_, { OkBtn, CancelBtn }) => (
          <>
            <CancelBtn />
            <OkBtn />
            {!isEdit && (
              <Button
                loading={saveLoading || submitLoading}
                type="primary"
                onClick={() => {
                  saveTypeRef.current = 'submit';
                  form.submit();
                }}
              >
                提交审核
              </Button>
            )}
          </>
        )}
        onCancel={() => setOpen(false)}
        onOk={() => {
          saveTypeRef.current = 'save';
          form.submit();
        }}
        {...props}
      >
        <div className="flex flex-col sm:flex-row gap-5 overflow-auto h-[60vh]">
          <div className="flex flex-col gap-4 flex-1 sm:overflow-auto sm:pr-5">
            <Card
              title="门店信息"
              classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
            >
              <Form.Item label="合同编号" required>
                <Input disabled placeholder="根据编号规则自动生成" value={data?.code} />
              </Form.Item>
              <Form.Item
                label="门店编号"
                name="shopId"
                rules={[{ required: true, message: '请选择原门店编号' }]}
              >
                <ShopSelect disabled={isEdit} onChange={(value) => handleShopIdChange(value)} />
              </Form.Item>
              {shopInfoItems.map((item) => {
                const Component = item.component || Input.TextArea;

                return (
                  <Form.Item
                    key={item.name}
                    label={item.label}
                    name={item.name}
                    rules={[{ required: true, message: '不能为空' }]}
                  >
                    <Component
                      disabled
                      placeholder="选择门店后自动填充"
                      {...(item.component
                        ? {}
                        : {
                            autoSize: true,
                          })}
                      {...item.props}
                    />
                  </Form.Item>
                );
              })}
              <Form.Item
                name="renovationType"
                label="翻新类型"
                rules={[{ required: true, message: '请选择翻新类型' }]}
              >
                <Select options={enum2Options(RenovationTypeEnum)} placeholder="请选择翻新类型" />
              </Form.Item>
              <Form.Item noStyle dependencies={['renovationType']}>
                {({ getFieldValue }) => {
                  if (getFieldValue('renovationType') === RenovationTypeEnum.局部翻新) {
                    return (
                      <Form.Item
                        name="renovationScope"
                        label="局改范围"
                        rules={[{ required: true, message: '请选择局改范围' }]}
                      >
                        <Select
                          mode="multiple"
                          allowClear
                          options={enum2Options(RenovationScopeEnum)}
                          placeholder="请选择局改范围"
                        />
                      </Form.Item>
                    );
                  }
                }}
              </Form.Item>
            </Card>
            <Card
              title="业主信息"
              classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
            >
              <Form.Item
                label="客户名称"
                name="customerId"
                rules={[{ required: true, message: '请选择客户' }]}
              >
                <RelCustomer
                  editable
                  defaultCustomerIdToNameMap={{
                    [data?.customerId || '']: data?.customerName,
                  }}
                  onChange={(_, info) => {
                    mutateCustomer(info);

                    form.setFieldsValue({
                      signer: info?.name,
                      phone: undefined,
                      identityCard: info?.identityCard,
                    });
                  }}
                />
              </Form.Item>
              <Form.Item
                label="签约人"
                name="signer"
                rules={[{ required: true, message: '不能为空' }]}
              >
                <Input disabled placeholder="选择客户后自动填充" />
              </Form.Item>
              <Form.Item
                label="联系电话"
                name="phone"
                rules={[{ required: true, message: '请选择联系电话' }]}
              >
                <Encrypt.PhoneSelect
                  placeholder="请选择手机号"
                  options={customer?.phones?.map((phone, index) => ({
                    label: customer.phonesSensitive?.[index],
                    value: phone,
                  }))}
                  encryptSensitiveMap={{
                    [data?.phone || '']: data?.phoneSensitive,
                  }}
                />
              </Form.Item>
              <Encrypt.IdCardFormItem
                formItemProps={{
                  label: '身份证号码',
                  name: 'identityCard',
                  rules: [{ required: true, message: '请输入身份证号码' }],
                }}
                fieldProps={{
                  disabled: true,
                  placeholder: '选择客户后自动填充',
                  encryptSensitiveMap: {
                    [customer?.identityCard || '']: customer?.identityCardSensitive,
                    [data?.identityCard || '']: data?.identityCardSensitive,
                  },
                }}
              />
              <Form.Item
                name="toPublic"
                label="是否对公"
                rules={[{ required: true, message: '不能为空' }]}
              >
                <Select disabled options={yesOrNoOptions} placeholder="选择门店后自动填充" />
              </Form.Item>
              <Form.Item noStyle shouldUpdate={(prev, next) => prev.toPublic !== next.toPublic}>
                {({ getFieldValue }) =>
                  getFieldValue('toPublic') && (
                    <>
                      <Form.Item
                        label="签约主体"
                        name="signSubject"
                        rules={[{ required: true, message: '不能为空' }]}
                      >
                        <Input disabled placeholder="选择门店后自动填充" />
                      </Form.Item>
                      <Form.Item
                        label="统一社会信用代码"
                        name="socialCreditCode"
                        rules={[{ required: true, message: '不能为空' }]}
                      >
                        <Input disabled placeholder="选择门店后自动填充" />
                      </Form.Item>
                    </>
                  )
                }
              </Form.Item>
            </Card>
          </div>
          <div className="flex-1 sm:overflow-auto sm:pr-5">
            <PaymentFormList />
          </div>
        </div>
      </Modal>
    );
  },
);

export default CreateEditRenovationModal;
