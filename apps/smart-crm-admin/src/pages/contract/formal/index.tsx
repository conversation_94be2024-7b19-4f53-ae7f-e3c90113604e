import { useEffect, useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { contractAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ETable, ExportButton, TableActions } from '@src/components';
import { ETableActionType, ETableColumn, ValueType } from '@src/components/ETable';
import useTableViews from '@src/hooks/useTableViews';
import { useNoticeEventSubscription } from '@src/layout';
import BusinessDetailDrawer from '@src/pages/business-opportunity/components/BusinessDetailDrawer';
import WeaverPushModal from '@src/pages/business-opportunity/components/BusinessDetailDrawer/WeaverPushModal';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { BelongWarZoneEnum, OwnerTypeEnum } from '@src/services/business-opportunity/type';
import { AuditStatusEnum, BusinessTypeEnum } from '@src/services/common/type';
import {
  batchDeleteFormalContract,
  exportFormalContract,
  getFormalContractDetail,
  getFormalContractList,
  submitFormalAudit,
} from '@src/services/contract/formal';
import {
  DevelopTagEnum,
  FormalContractDTO,
  FormalNodeStateEnum,
  StoreCategoryEnum,
  StoreStatusEnum,
} from '@src/services/contract/formal/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import { compatibleTableActionWidth, enum2Options, getParamsFromFilters } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Dropdown, Skeleton } from 'antd';
import { useSearchParams } from 'react-router-dom';
import CreateEditFormalModal from './components/CreateEditFormalModal';
import FormalDetailDrawer from './components/FormalDetailDrawer';
import IntentionDetailDrawer from '../intention/components/IntentionDetailDrawer';

const contractType = ContractTypeEnum.正式合同;

const Formal = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { modal, message } = App.useApp();
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const customerIdRef = useRef<number | null>(null);
  const businessDetailDrawerRef = useRef<ModalRef>(null);
  const businessIdRef = useRef<number | null>(null);
  const actionRef = useRef<ETableActionType>(null);
  const detailDrawerRef = useRef<ModalRef>(null);
  const currentIdRef = useRef<number | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const createEditFormalModalRef = useRef<ModalRef>(null);
  const weaverPushModalRef = useRef<ModalRef>(null);
  const intentionDetailDrawerRef = useRef<ModalRef>(null);
  const intentionIdRef = useRef<number | null>(null);

  const checkPermission = usePermission();
  const { data, runAsync: getList } = useRequest(getFormalContractList, { manual: true });
  const { views, loading: getViewsLoading } = useTableViews(BusinessTypeEnum.FORMAL_CONTRACT);

  useNoticeEventSubscription((event) => {
    if (event.type === 'formal' && data?.result.some((i) => i.id === event.payload.id)) {
      actionRef.current?.reload();
    }
  });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      currentIdRef.current = Number(id);
      detailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSubmitAudit = async (id: number) => {
    const detail = await getFormalContractDetail(id);
    const amount = detail.payments?.reduce((total, cur) => total + cur.paymentAmount, 0) || 0;

    modal.confirm({
      title: '提交审核',
      content: `累计打款金额：${amount.toFixed(2)} 元，是否提交合同审核？`,
      onOk: async () => {
        await submitFormalAudit(id);
        message.success('提交成功');
        actionRef.current?.reload();
      },
    });
  };

  const columns: ETableColumn<FormalContractDTO>[] = [
    {
      title: '合同编号',
      dataIndex: 'code',
      fixed: 'left',
      valueType: ValueType.TEXT,
      render: (value, { id }) => (
        <a
          onClick={() => {
            currentIdRef.current = id;
            detailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '门店性质',
      dataIndex: 'storeCategory',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(StoreCategoryEnum),
      },
      filterProps: {
        options: enum2Options(StoreCategoryEnum),
      },
    },
    {
      title: '合同节点',
      dataIndex: 'nodeState',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(FormalNodeStateEnum),
      },
      filterProps: {
        options: enum2Options(FormalNodeStateEnum),
      },
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: contractAuditStatusOptions,
      },
      filterProps: {
        options: contractAuditStatusOptions,
      },
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      valueType: ValueType.TEXT,
      render: (value, { customerId }) => (
        <a
          onClick={() => {
            customerIdRef.current = customerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '客户编号',
      dataIndex: 'customerCode',
      valueType: ValueType.TEXT,
    },
    {
      title: '负责人',
      dataIndex: 'directUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '业主类型',
      dataIndex: 'ownerType',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(OwnerTypeEnum),
      },
      filterProps: {
        options: enum2Options(OwnerTypeEnum),
      },
    },
    {
      title: '意向合同',
      dataIndex: 'intentionContractCode',
      valueType: ValueType.TEXT,
      render: (_, { intentionContractId, intentionContractCode }) => (
        <a
          onClick={() => {
            intentionIdRef.current = intentionContractId!;
            intentionDetailDrawerRef.current?.open();
          }}
        >
          {intentionContractCode}
        </a>
      ),
    },
    {
      title: '商机名称',
      dataIndex: 'bizOpportunityName',
      valueType: ValueType.TEXT,
      render: (value, { bizOpportunityId }) => (
        <a
          onClick={() => {
            businessIdRef.current = bizOpportunityId;
            businessDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '商机编号',
      dataIndex: 'bizOpportunityCode',
      valueType: ValueType.TEXT,
    },
    {
      title: '商机负责人',
      dataIndex: 'bizOpportunityDirectUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '是否对赌',
      dataIndex: 'vamFlag',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: yesOrNoOptions,
      },
      filterProps: {
        options: [
          // 后端这里不接受 Boolean
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
      },
    },
    {
      title: '所属招商顾问',
      dataIndex: 'consultantUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '签约人',
      dataIndex: 'signer',
      valueType: ValueType.TEXT,
    },
    {
      title: '联系电话',
      dataIndex: 'signerPhone',
      valueType: ValueType.PHONE,
      fieldProps: (_, { signerPhoneSensitive }) => ({
        sensitiveValue: signerPhoneSensitive,
      }),
    },
    {
      title: '身份证号码',
      dataIndex: 'signerIdentityCard',
      valueType: ValueType.IDENTITY_CARD,
      fieldProps: (_, { signerIdentityCardSensitive }) => ({
        sensitiveValue: signerIdentityCardSensitive,
      }),
    },
    {
      title: '是否对公',
      dataIndex: 'toPublic',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: yesOrNoOptions,
      },
      filterProps: {
        options: [
          // 后端这里不接受 Boolean
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
      },
    },
    {
      title: '签约主体',
      dataIndex: 'signSubject',
      valueType: ValueType.TEXT,
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'socialCreditCode',
      valueType: ValueType.TEXT,
    },
    {
      title: '正式打款日期',
      dataIndex: 'paymentDate',
      valueType: ValueType.DATE,
    },
    {
      title: '正式生效日期',
      dataIndex: 'effectiveDate',
      valueType: ValueType.DATE,
    },
    {
      title: '正式到期日期',
      dataIndex: 'formalExpirationDate',
      valueType: ValueType.DATE,
    },
    {
      title: '铺位编号',
      dataIndex: 'siteNo',
      valueType: ValueType.TEXT,
    },
    {
      title: '门店交付日期',
      dataIndex: 'storeDeliveryDate',
      valueType: ValueType.DATE,
    },
    {
      title: '门店编号',
      dataIndex: 'storeCode',
      valueType: ValueType.TEXT,
    },
    {
      title: '门店冠名',
      dataIndex: 'storeName',
      valueType: ValueType.TEXT,
    },
    {
      title: '门店定位',
      dataIndex: 'storeCoordinates',
      render: (_, { storeCoordinates }) =>
        storeCoordinates &&
        `${storeCoordinates.name}（经度：${storeCoordinates.longitude}；纬度：${storeCoordinates.latitude}）`,
    },
    {
      title: '门店地址',
      dataIndex: 'storeAddress',
      valueType: ValueType.TEXT,
    },
    {
      title: '所属省市区',
      dataIndex: 'region',
      width: 220,
      valueType: ValueType.REGION,
      fieldProps: {
        regionLevel: 4,
      },
      transform: (value) => ({
        ...value,
        field: 'regionCode',
      }),
    },
    {
      title: '乡镇',
      dataIndex: 'streetCode',
      valueType: ValueType.REGION,
      hidden: true,
      hideInFilters: true,
      hideInSettings: true,
      filterProps: {
        unlimited: {
          onChange: true,
        },
        regionLevel: 4,
      },
    },
    {
      title: '所属大区',
      dataIndex: 'belongWarZone',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(BelongWarZoneEnum),
      },
      filterProps: {
        options: enum2Options(BelongWarZoneEnum),
      },
    },
    {
      title: '开发标签',
      dataIndex: 'developTag',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(DevelopTagEnum),
      },
      filterProps: {
        options: enum2Options(DevelopTagEnum),
      },
    },
    {
      title: '选址报告流程 id',
      dataIndex: 'siteFlowId',
      valueType: ValueType.TEXT,
    },
    {
      title: '门店状态',
      dataIndex: 'storeStatus',
      valueType: ValueType.MULTIPLE,
      fieldProps: {
        options: enum2Options(StoreStatusEnum),
      },
      filterProps: {
        options: enum2Options(StoreStatusEnum),
      },
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: ValueType.DATE_TIME,
    },
    {
      title: '更新人',
      dataIndex: 'updateUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: ValueType.DATE_TIME,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      width: compatibleTableActionWidth(120),
      hideInFilters: true,
      hideInSettings: true,
      render: (_, { id, code, auditStatus, nodeState }) => {
        // 是否是未提交或驳回
        const isNotSubmitOrReturn = [AuditStatusEnum.NOT_SUBMIT, AuditStatusEnum.RETURN].includes(
          auditStatus,
        );

        return (
          <TableActions
            shortcuts={[
              {
                label: '编辑',
                show:
                  isNotSubmitOrReturn || checkPermission(PermissionsMap.ContractFormalSuperEdit),
                onClick: () => {
                  currentIdRef.current = id;
                  createEditFormalModalRef.current?.open();
                },
              },
            ]}
            moreItems={[
              {
                label: '提交审核',
                show: isNotSubmitOrReturn,
                onClick: () => handleSubmitAudit(id),
              },
              {
                label: '推送泛微',
                show: nodeState === FormalNodeStateEnum.OA未发起,
                onClick: () => {
                  currentIdRef.current = id;
                  weaverPushModalRef.current?.open();
                },
              },
              {
                label: '删除',
                show: isNotSubmitOrReturn,
                danger: true,
                onClick: () =>
                  modal.confirm({
                    title: '删除',
                    content: `确定要删除正式合同 “${code}” 吗？`,
                    onOk: async () => {
                      await batchDeleteFormalContract([id]);
                      message.success('删除成功');
                      setSelectedRowKeys((prev) => prev.filter((i) => i !== id));
                      actionRef.current?.reload();
                    },
                  }),
              },
            ]}
          />
        );
      },
    },
  ];

  const headerNode = (
    <div className="sm:flex justify-between mb-3">
      <div className="self-end mb-2 sm:mb-0 pl-2">
        共 {data?.total || 0} 个合同
        {selectedRowKeys.length > 0 && (
          <>
            ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 个
            <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
              清空选择
            </a>
          </>
        )}
      </div>
      <div className="flex justify-end gap-2">
        <Dropdown
          disabled={!selectedRowKeys.length}
          menu={{
            items: [
              {
                label: '删除',
                key: 'delete',
                danger: true,
                onClick: () =>
                  modal.confirm({
                    title: '删除',
                    content: `确定要删除当前选中的 ${selectedRowKeys.length} 个正式合同吗？`,
                    onOk: async () => {
                      await batchDeleteFormalContract(selectedRowKeys);
                      message.success('删除成功');
                      setSelectedRowKeys([]);
                      actionRef.current?.reload();
                    },
                  }),
              },
            ],
          }}
        >
          <Button type="text">
            批量操作 <CaretDownOutlined />
          </Button>
        </Dropdown>
        <Permission value={PermissionsMap.ContractFormalCreate}>
          <Button
            type="text"
            onClick={() => {
              currentIdRef.current = null;
              createEditFormalModalRef.current?.open();
            }}
          >
            新建
          </Button>
        </Permission>
        <Permission value={PermissionsMap.ContractFormalExport}>
          <ExportButton
            total={selectedRowKeys.length || data?.total}
            request={() =>
              exportFormalContract({
                columns: actionRef.current!.getShowFields(),
                ...(selectedRowKeys.length
                  ? { ids: selectedRowKeys }
                  : getParamsFromFilters(actionRef.current?.getFilters())),
              })
            }
          />
        </Permission>
      </div>
    </div>
  );

  return (
    <>
      <PageContainer
        loading={getViewsLoading && <Skeleton paragraph={{ rows: 15 }} className="px-10" />}
      >
        <ETable
          rowKey="id"
          actionRef={actionRef}
          sticky
          bordered
          size="small"
          header={headerNode}
          views={views}
          pagination={{ showQuickJumper: true, showSizeChanger: true }}
          columns={columns}
          rowSelection={{
            fixed: true,
            preserveSelectedRowKeys: true,
            selectedRowKeys,
            onChange: (keys) => setSelectedRowKeys(keys as number[]),
          }}
          request={async ({ current, pageSize }, filters) => {
            const res = await getList({
              pageNum: current,
              pageSize,
              ...getParamsFromFilters(filters),
            });

            return {
              data: res.result,
              total: res.total,
            };
          }}
        />

        <CreateEditFormalModal
          ref={createEditFormalModalRef}
          getId={() => currentIdRef.current}
          onSuccess={() => actionRef.current?.reload()}
        />
        <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />
        <BusinessDetailDrawer
          ref={businessDetailDrawerRef}
          getId={() => businessIdRef.current}
          onUpdate={() => actionRef.current?.reload()}
        />
        <WeaverPushModal
          ref={weaverPushModalRef}
          getIds={() => [currentIdRef.current!]}
          contractType={contractType}
          onSuccess={() => actionRef.current?.reload()}
        />
        <IntentionDetailDrawer
          ref={intentionDetailDrawerRef}
          getId={() => intentionIdRef.current}
        />
      </PageContainer>
      <FormalDetailDrawer
        ref={detailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
        onDelete={() => {
          setSelectedRowKeys((prev) => prev.filter((i) => i !== currentIdRef.current));
          actionRef.current?.reload();
        }}
      />
    </>
  );
};

export default Formal;
