import React, { useRef, useState } from 'react';
import { LoadingOutlined } from '@ant-design/icons';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ChooseMapPoints, Encrypt, TrainingPersonsSelect, UserSelect } from '@src/components';
import CheckCodeModal from '@src/components/CheckCodeModal';
import EditableDate from '@src/components/Editable/EditableDate';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useDistrictsStreetOpened from '@src/hooks/useDistrictsStreetOpened';
import useProvinceWarZoneMap from '@src/hooks/useProvinceWarZoneMap';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import { BelongWarZoneEnum, OwnerTypeEnum } from '@src/services/business-opportunity/type';
import { getAreaCodeByLatLng, getSiteInfoBySiteNo } from '@src/services/common';
import {
  checkContractStoreCategory,
  generateFormalCode,
  getBindableFormalList,
  getFormalContractDetail,
  saveAndSubmitFormalContract,
  saveFormalContract,
  updateFormalContract,
} from '@src/services/contract/formal';
import {
  BindableFormalDTO,
  DevelopTagEnum,
  StoreCategoryEnum,
  StoreStatusEnum,
} from '@src/services/contract/formal/type';
import { getCustomerDetail } from '@src/services/customers';
import { IdentityTypeEnum } from '@src/services/mini-program/identity/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import { JoinRegionEnum } from '@src/services/regional/quota/type';
import { findSystemConfig } from '@src/services/standard-settings/business-parameter';
import { ConfigKeyEnum } from '@src/services/standard-settings/business-parameter/type';
import useUserStore from '@src/store/useUserStore';
import { enum2Options, enum2ValueEnum } from '@src/utils';
import { useRequest, useUpdateEffect } from 'ahooks';
import { App, Button, Card, Form, Input, Modal, ModalProps, Select } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import dayjs from 'dayjs';
import PaymentFormList from '../../components/PaymentFormList';

const { Search } = Input;

interface CreateEditFormalModalProps extends ModalProps {
  getId?: () => number | null;
  onSuccess: () => void;
}

const CreateEditFormalModal = React.forwardRef<ModalRef, CreateEditFormalModalProps>(
  ({ getId, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);
    const [checkOpen, setCheckOpen] = useState(false);
    const saveTypeRef = useRef<'save' | 'submit'>('save');

    const storeCategoryValue = Form.useWatch('storeCategory', form);
    const regionValue = Form.useWatch('region', form);

    const formalId = getId?.();
    const isEdit = !!formalId;

    const {
      user: { shUserId },
    } = useUserStore();
    const { transformDistricts, getStreetOpenedRegionIdsLoading } = useDistrictsStreetOpened({
      ready: open,
    });
    const {
      data: customer,
      loading: getCustomerLoading,
      runAsync: getCustomer,
      mutate: mutateCustomer,
    } = useRequest(getCustomerDetail, {
      manual: true,
    });
    const {
      data: intentionList,
      loading: getIntentionListLoading,
      runAsync: getIntentionList,
      mutate: mutateIntentionList,
    } = useRequest(getBindableFormalList, { manual: true });

    const { data, loading, mutate } = useRequest(() => getFormalContractDetail(formalId!), {
      ready: open && isEdit,
      onSuccess: (res) => {
        form.setFieldsValue(res);

        // 编辑时，没有选择意向合同的话才需要请求，如果有选择，是禁用，直接取详情的数据作为 options
        if (!res.intentionContractId) {
          getIntentionList({ customerId: res.customerId });
        }

        getCustomer(res.customerId);
      },
    });

    const { runAsync: save, loading: saveLoading } = useRequest(saveFormalContract, {
      manual: true,
    });
    const { runAsync: submit, loading: submitLoading } = useRequest(saveAndSubmitFormalContract, {
      manual: true,
    });
    const { runAsync: update, loading: updateLoading } = useRequest(updateFormalContract, {
      manual: true,
    });
    const {
      runAsync: generateCode,
      loading: generateCodeLoading,
      cancel: cancelGenerateCode,
    } = useRequest(generateFormalCode, { manual: true });
    const provinceWarZoneMap = useProvinceWarZoneMap({ ready: open });

    const { run: searchSiteInfo } = useRequest(getSiteInfoBySiteNo, {
      manual: true,
      onSuccess: (res) => {
        if (res) {
          setAreaCode({ latitude: res.latitude, longitude: res.longitude });
          form.setFieldsValue({
            storeCoordinates: {
              latitude: res.latitude,
              longitude: res.longitude,
              name: res.address,
            },
            storeDeliveryDate: res.deliveryDate
              ? dayjs(res.deliveryDate).format('YYYY-MM-DD')
              : undefined,
            storeName: res.shopName,
            // storeAddress: res.leaseContractAddress,
          });
        }
      },
    });

    const { run: setAreaCode } = useRequest(getAreaCodeByLatLng, {
      manual: true,
      onSuccess: (res) => {
        if (res) {
          form.setFieldsValue({
            belongWarZone: provinceWarZoneMap[res.provinceCode],
            streetCode: res.streetCode,
            region: [res.provinceCode, res.cityCode, res.regionCode].join('/'),
          });
        }
      },
    });

    const { data: trainingPersonConfig, loading: trainingPersonConfigLoading } = useRequest(
      async () => {
        const res = await findSystemConfig({
          configKey: ConfigKeyEnum.TRAINING_PERSON_SWITCH,
        });

        return (res?.configValue as { switch: boolean })?.switch;
      },
    );

    const {
      data: checkStoreCategoryData,
      loading: checkStoreCategoryLoading,
      runAsync: runCheckStoreCategory,
      mutate: mutateCheckStoreCategoryData,
    } = useRequest(checkContractStoreCategory, { manual: true });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        mutate(undefined);
        mutateCustomer(undefined);
        mutateIntentionList(undefined);
        mutateCheckStoreCategoryData(undefined);
        cancelGenerateCode();
      },
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      if (isEdit) {
        await update({ id: formalId, ...values });
        message.success('修改成功');
      } else {
        if (saveTypeRef.current === 'save') {
          await save(values);
          message.success('保存成功');
        } else {
          await submit(values);
          message.success('提交审核成功');
        }
      }

      setOpen(false);
      onSuccess();
    };

    const handleFinish = async (values: any) => {
      const { customerId, intentionContractId, region, storeCategory } = values;

      const res = await runCheckStoreCategory({
        customerId,
        contractType: ContractTypeEnum.正式合同,
        relevanceCode:
          isEdit && data?.intentionContractId
            ? data.intentionContractCode || ''
            : intentionList?.find((v) => v.id === intentionContractId)?.code || '',
        regionCode: region,
        storeCategory,
      });

      if (res.success) {
        handleSave(values);
      } else {
        setCheckOpen(true);
      }
    };

    useUpdateEffect(() => {
      if (!isEdit && storeCategoryValue && regionValue && !isEdit) {
        const provinceCode = regionValue.split('/')[0] || '';

        generateCode({ storeCategory: storeCategoryValue, provinceCode }).then(({ code }) => {
          form.setFieldValue('code', code);
        });
      }
    }, [storeCategoryValue, regionValue]);

    return (
      <>
        <Modal
          open={open}
          width={1000}
          title={isEdit ? '编辑正式合同' : '新建正式合同'}
          loading={
            loading ||
            getCustomerLoading ||
            getStreetOpenedRegionIdsLoading ||
            trainingPersonConfigLoading
          }
          destroyOnHidden
          confirmLoading={
            saveLoading || updateLoading || submitLoading || checkStoreCategoryLoading
          }
          styles={{
            body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
          }}
          modalRender={(node) => (
            <Form
              form={form}
              clearOnDestroy
              scrollToFirstError
              labelCol={{ span: 9 }}
              onFinish={handleFinish}
            >
              {node}
            </Form>
          )}
          okText="保存"
          okButtonProps={{ ghost: !isEdit }}
          footer={(_, { OkBtn, CancelBtn }) => (
            <>
              <CancelBtn />
              <OkBtn />
              {!isEdit && (
                <Button
                  loading={saveLoading || submitLoading}
                  type="primary"
                  onClick={() => {
                    saveTypeRef.current = 'submit';
                    form.submit();
                  }}
                >
                  提交审核
                </Button>
              )}
            </>
          )}
          onOk={() => {
            saveTypeRef.current = 'save';
            form.submit();
          }}
          onCancel={() => setOpen(false)}
          {...props}
        >
          <div className="flex flex-col sm:flex-row gap-5 overflow-auto h-[60vh]">
            <div className="flex flex-col gap-4 flex-1 sm:overflow-auto sm:pr-5">
              <Card
                title="加盟信息"
                classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
              >
                <Form.Item
                  label="客户名称"
                  name="customerId"
                  rules={[{ required: true, message: '请选择客户' }]}
                >
                  <RelCustomer
                    editable={!isEdit}
                    defaultCustomerIdToNameMap={{
                      [data?.customerId || '']: data?.customerName,
                    }}
                    onChange={(id, info) => {
                      mutateCustomer(info);
                      mutateIntentionList(undefined);

                      form.setFieldsValue({
                        bizOpportunityId: undefined,
                        intentionContract: undefined,
                        vamFlag: undefined,
                        signer: info?.name,
                        signerPhone: undefined,
                        signerIdentityCard: info?.identityCard,
                        storeCategory: info?.customerNature,
                      });

                      if (id) {
                        getIntentionList({ customerId: id });
                      }
                    }}
                  />
                </Form.Item>
                <Form.Item
                  label="门店性质"
                  name="storeCategory"
                  rules={[{ required: true, message: '请选择门店性质' }]}
                >
                  <Select
                    disabled={!isEdit}
                    placeholder="请选择门店性质"
                    options={enum2Options(StoreCategoryEnum)}
                  />
                </Form.Item>
                <Form.Item
                  label="合同编号"
                  name="code"
                  rules={[{ required: true, message: '请输入合同编号' }]}
                  tooltip={{ title: '选择门店性质和所属省市区后自动生成' }}
                >
                  <Input
                    disabled
                    placeholder="自动生成"
                    suffix={
                      generateCodeLoading ? (
                        <span className="text-primary">
                          生成中
                          <LoadingOutlined className="ml-2" />
                        </span>
                      ) : (
                        <span />
                      )
                    }
                  />
                </Form.Item>
                <Form.Item
                  label="负责人"
                  name="directUserId"
                  initialValue={shUserId}
                  rules={[{ required: true, message: '请选择负责人' }]}
                >
                  <UserSelect placeholder="请选择负责人" />
                </Form.Item>

                <Form.Item
                  label="业主类型"
                  name="ownerType"
                  rules={[{ required: true, message: '请选择业主类型' }]}
                >
                  <Select placeholder="请选择业主类型" options={enum2Options(OwnerTypeEnum)} />
                </Form.Item>
                <Form.Item
                  name="intentionContractId"
                  label="意向合同"
                  rules={[{ required: true, message: '请选择意向合同' }]}
                >
                  <Select
                    allowClear
                    placeholder="请选择意向合同"
                    loading={getIntentionListLoading}
                    disabled={isEdit && !!data?.intentionContractId}
                    options={
                      // 编辑时有意向合同，直接用详情的数据作为 options
                      isEdit && data?.intentionContractId
                        ? [{ code: data.intentionContractCode, id: data.intentionContractId }]
                        : (intentionList as DefaultOptionType[])
                    }
                    fieldNames={{ label: 'code', value: 'id' }}
                    onChange={(_, _option) => {
                      const option = _option as BindableFormalDTO | undefined;

                      form.setFieldsValue({
                        bizOpportunityId: option?.bizOpportunityId,
                        vamFlag: option?.vamFlag,
                        consultantUserId: option?.consultantUserId,
                      });
                    }}
                  />
                </Form.Item>
                <Form.Item label="商机名称" name="bizOpportunityId">
                  <Select
                    disabled
                    placeholder="根据意向合同自动显示"
                    options={
                      isEdit && data?.bizOpportunityId
                        ? [{ label: data.bizOpportunityName, value: data.bizOpportunityId }]
                        : intentionList?.reduce<DefaultOptionType[]>(
                            (total, cur) =>
                              cur.bizOpportunityId
                                ? total.concat({
                                    label: cur.bizOpportunityName,
                                    value: cur.bizOpportunityId,
                                  })
                                : total,
                            [],
                          )
                    }
                    suffixIcon={null}
                  />
                </Form.Item>
                <Form.Item label="是否对赌" name="vamFlag">
                  <Select
                    disabled
                    placeholder="根据意向合同自动显示"
                    options={yesOrNoOptions}
                    suffixIcon={null}
                  />
                </Form.Item>
              </Card>
              {!!trainingPersonConfig && (
                <Card
                  title="加盟信息"
                  classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
                >
                  <TrainingPersonsSelect editable={!isEdit} />
                </Card>
              )}
              <Card
                title="合同签约信息"
                classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
              >
                <Form.Item label="所属招商顾问" name="consultantUserId">
                  <UserSelect
                    disabled
                    placeholder="请选择所属招商顾问"
                    transformOptions={(options) =>
                      options.filter((i) =>
                        [IdentityTypeEnum.招商顾问, IdentityTypeEnum.大客户招商].includes(
                          i.identityType,
                        ),
                      )
                    }
                  />
                </Form.Item>
                <Form.Item
                  label="签约人"
                  name="signer"
                  rules={[{ required: true, message: '不能为空' }]}
                >
                  <Input disabled placeholder="选择客户后自动填充" />
                </Form.Item>
                <Form.Item
                  label="联系电话"
                  name="signerPhone"
                  rules={[{ required: true, message: '请选择联系电话' }]}
                >
                  <Encrypt.PhoneSelect
                    placeholder="请选择手机号"
                    options={customer?.phones?.map((phone, index) => ({
                      label: customer.phonesSensitive?.[index],
                      value: phone,
                    }))}
                    encryptSensitiveMap={{
                      [data?.signerPhone || '']: data?.signerPhoneSensitive,
                    }}
                  />
                </Form.Item>
                <Encrypt.IdCardFormItem
                  formItemProps={{
                    label: '身份证号码',
                    name: 'signerIdentityCard',
                    rules: [{ required: true, message: '请输入身份证号码' }],
                  }}
                  fieldProps={{
                    disabled: true,
                    placeholder: '选择客户后自动填充',
                    encryptSensitiveMap: {
                      [customer?.identityCard || '']: customer?.identityCardSensitive,
                      [data?.signerIdentityCard || '']: data?.signerIdentityCardSensitive,
                    },
                  }}
                />
              </Card>
              <Card
                title="场景判断"
                classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
              >
                <Form.Item
                  name="toPublic"
                  label="是否对公"
                  initialValue={false}
                  rules={[{ required: true, message: '请选择是否对公' }]}
                >
                  <Select disabled={isEdit} options={yesOrNoOptions} placeholder="请选择是否对公" />
                </Form.Item>
                <Form.Item noStyle dependencies={['toPublic']}>
                  {({ getFieldValue }) =>
                    getFieldValue('toPublic') && (
                      <>
                        <Form.Item
                          label="签约主体"
                          name="signSubject"
                          rules={[{ required: true, message: '请输入签约主体' }]}
                        >
                          <Input disabled={isEdit} placeholder="请输入签约主体" />
                        </Form.Item>
                        <Form.Item
                          label="统一社会信用代码"
                          name="socialCreditCode"
                          rules={[{ required: true, message: '请输入统一社会信用代码' }]}
                        >
                          <Input disabled={isEdit} placeholder="请输入统一社会信用代码" />
                        </Form.Item>
                      </>
                    )
                  }
                </Form.Item>
              </Card>
              <Card
                title="门店信息"
                classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
              >
                <Form.Item
                  label="铺位编号"
                  name="siteNo"
                  rules={[{ required: true, message: '请输入铺位编号' }]}
                >
                  {isEdit ? (
                    <Input placeholder="请输入铺位编号" />
                  ) : (
                    <Search
                      placeholder="根据铺位编号，自动匹配门店信息"
                      onSearch={searchSiteInfo}
                    />
                  )}
                </Form.Item>
                <EditableDate
                  editable
                  formItemProps={{
                    label: '门店交付日期',
                    name: 'storeDeliveryDate',
                    rules: [{ required: true, message: '请选择门店交付日期' }],
                  }}
                />
                <Form.Item label="门店编号" required>
                  <Input disabled placeholder="根据编号规则自动生成" value={data?.storeCode} />
                </Form.Item>
                <Form.Item
                  label="门店冠名"
                  name="storeName"
                  rules={[{ required: true, message: '请输入门店冠名' }]}
                >
                  <Input disabled={isEdit} placeholder="请输入门店冠名" />
                </Form.Item>
                <Form.Item
                  label="门店定位"
                  name="storeCoordinates"
                  getValueProps={(value) => ({
                    value: value ? [value] : undefined,
                  })}
                  normalize={(value) => (Array.isArray(value) ? value[0] : undefined)}
                  rules={[{ required: !isEdit, message: '请选择门店定位' }]}
                >
                  <ChooseMapPoints
                    multiple={false}
                    onChange={(e) => {
                      if (e?.length) {
                        setAreaCode({ latitude: e[0].latitude, longitude: e[0].longitude });
                      }
                    }}
                  />
                </Form.Item>
                <Form.Item
                  label="门店地址"
                  name="storeAddress"
                  rules={[{ required: true, message: '请输入门店地址' }]}
                >
                  <Input.TextArea disabled={isEdit} autoSize placeholder="请输入门店地址" />
                </Form.Item>
                <EditableRegion
                  editable
                  formItemProps={{
                    label: '所属省市区',
                    name: 'region',
                    rules: [
                      { required: true, message: '请选择所属省市区' },
                      {
                        validator: (_, value) => {
                          if (value && !isEdit) {
                            const intentionContractData = intentionList
                              ? intentionList?.find(
                                  (v) => v.id === form.getFieldValue('intentionContractId'),
                                )
                              : ({} as BindableFormalDTO);

                            const intentionContractRegionCode =
                              intentionContractData?.intentionRegion?.split('/')?.[2];
                            const valueRegionCode = value?.split('/')?.[2];

                            if (intentionContractRegionCode !== valueRegionCode) {
                              return Promise.reject(new Error('所属省市区与意向合同不符，请确认'));
                            } else {
                              return Promise.resolve();
                            }
                          }

                          return Promise.resolve();
                        },
                      },
                    ],
                  }}
                  fieldProps={{
                    disabled: isEdit,
                    regionLevel: 4,
                    transformDistricts,
                    placeholder: '请选择所属省市区',
                    onChange: (value: any) => {
                      const streetCode = value?.[3];

                      if (streetCode) {
                        form.setFieldValue('streetCode', streetCode);
                      }

                      form.setFieldValue('belongWarZone', provinceWarZoneMap[value?.[0]]);
                    },
                  }}
                />
                <Form.Item hidden={true} label="所属乡镇" name="streetCode">
                  <Input />
                </Form.Item>
                <Form.Item
                  label="所属大区"
                  name="belongWarZone"
                  rules={[{ required: true, message: '请选择所属大区' }]}
                >
                  <Select
                    disabled
                    placeholder="根据省份自动匹配"
                    options={enum2Options(BelongWarZoneEnum)}
                  />
                </Form.Item>
                <Form.Item
                  label="开发标签"
                  name="developTag"
                  rules={[{ required: true, message: '请选择开发标签' }]}
                >
                  <Select
                    placeholder="请选择开发标签"
                    options={enum2Options(DevelopTagEnum)}
                    showSearch
                    optionFilterProp="label"
                  />
                </Form.Item>

                {isEdit && (
                  <Form.Item label="门店状态" name="storeStatus">
                    <Select
                      placeholder="请选择门店状态"
                      options={enum2Options(StoreStatusEnum)}
                      mode="multiple"
                      showSearch
                      optionFilterProp="label"
                    />
                  </Form.Item>
                )}
              </Card>
            </div>
            <div className="flex-1 sm:overflow-auto sm:pr-5">
              <PaymentFormList />
            </div>
          </div>
        </Modal>
        <CheckCodeModal
          title="加盟属性不匹配"
          open={checkOpen}
          onSuccess={() => {
            setCheckOpen(false);
            handleSave(form.getFieldsValue());
          }}
          onCancel={() => setCheckOpen(false)}
          messageInfo={() => {
            const storeCategoryEnum = enum2ValueEnum(StoreCategoryEnum);

            return (
              <div className="text-[#e10600] text-sm mb-2">
                <div>
                  正式合同门店性质：
                  {storeCategoryEnum[checkStoreCategoryData?.contractStoreCategory!]}
                </div>
                <div>
                  意向合同门店性质：
                  {storeCategoryEnum[checkStoreCategoryData?.relevanceStoreCategory!]}
                </div>
                <div>客户性质：{storeCategoryEnum[checkStoreCategoryData?.customerNature!]}</div>
                <div>
                  区域性质：
                  {enum2ValueEnum(JoinRegionEnum)[checkStoreCategoryData?.joinRegion!]}
                </div>
              </div>
            );
          }}
        />
      </>
    );
  },
);

export default CreateEditFormalModal;
