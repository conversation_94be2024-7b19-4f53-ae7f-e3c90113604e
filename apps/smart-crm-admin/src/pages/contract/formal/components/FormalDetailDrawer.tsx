import React, { useRef, useState } from 'react';
import { contractAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { ButtonGroup } from '@src/components';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import BusinessDetailDrawer from '@src/pages/business-opportunity/components/BusinessDetailDrawer';
import WeaverPushModal from '@src/pages/business-opportunity/components/BusinessDetailDrawer/WeaverPushModal';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import { PermissionsMap, usePermission } from '@src/permissions';
import { BelongWarZoneEnum, OwnerTypeEnum } from '@src/services/business-opportunity/type';
import { AuditStatusEnum } from '@src/services/common/type';
import { ContractEnum } from '@src/services/contract';
import {
  batchDeleteFormalContract,
  getFormalContractDetail,
  submitFormalAudit,
} from '@src/services/contract/formal';
import {
  DevelopTagEnum,
  FormalContractDTO,
  FormalNodeStateEnum,
  StoreCategoryEnum,
  StoreStatusEnum,
} from '@src/services/contract/formal/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Card, Col, Drawer, DrawerProps, Row, Tabs } from 'antd';
import CreateEditFormalModal from './CreateEditFormalModal';
import ContractAuditCard, { ContractAuditCardRef } from '../../components/ContractAuditCard';
import ContractFileList from '../../components/ContractFileList';
import ContractFileUpModal from '../../components/ContractFileUpModal';
import PaymentRecords from '../../components/PaymentRecords';
import IntentionDetailDrawer from '../../intention/components/IntentionDetailDrawer';

interface FormalDetailDrawerProps extends DrawerProps {
  getId: () => number | null | undefined;
  onUpdate?: () => void;
  onDelete?: () => void;
  onTodoSuccess?: () => void;
}

const contractType = ContractTypeEnum.正式合同;

const FormalDetailDrawer = React.forwardRef<ModalRef, FormalDetailDrawerProps>(
  ({ getId, onUpdate, onDelete, onTodoSuccess, ...props }, ref) => {
    const { modal, message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [activeKey, setActiveKey] = useState('info');
    const customerDetailDrawerRef = useRef<ModalRef>(null);
    const businessOpportunityDrawerRef = useRef<ModalRef>(null);
    const createEditFormalModalRef = useRef<ModalRef>(null);
    const [weaverPushModalOpen, setWeaverPushModalOpen] = useState(false);
    const intentionDetailDrawerRef = useRef<ModalRef>(null);
    const intentionIdRef = useRef<number | null>(null);
    const contractAuditCardRef = useRef<ContractAuditCardRef>(null);

    const formalId = getId()!;

    const checkPermission = usePermission();
    const { data: users } = useAllUsers();
    const { data, loading, refresh } = useRequest(() => getFormalContractDetail(formalId), {
      ready: open,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        setActiveKey('info');
      },
      close: () => setOpen(false),
    }));

    const handleSubmitAudit = async () => {
      const amount = data?.payments?.reduce((total, cur) => total + cur.paymentAmount, 0) || 0;

      modal.confirm({
        title: '提交审核',
        content: `累计打款金额：${amount.toFixed(2)} 元，是否提交合同审核？`,
        onOk: async () => {
          await submitFormalAudit(formalId);
          message.success('提交成功');
          refresh();
          contractAuditCardRef.current?.refresh();
          onUpdate?.();

          // 驳回才会在待办里
          if (data?.auditStatus === AuditStatusEnum.RETURN) {
            onTodoSuccess?.();
          }
        },
      });
    };

    const baseInfoItems: DescriptionFieldType<FormalContractDTO>[] = [
      {
        field: 'code',
        label: '合同编号',
      },
      {
        field: 'storeCategory',
        label: '门店性质',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(StoreCategoryEnum),
        },
      },
      {
        field: 'nodeState',
        label: '合同节点',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(FormalNodeStateEnum),
        },
      },
      {
        field: 'auditStatus',
        label: '审核状态',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: contractAuditStatusOptions,
        },
      },
      {
        field: 'directUserId',
        label: '负责人',
        valueType: ValueType.PERSON,
      },
      {
        field: 'ownerType',
        label: '业主类型',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(OwnerTypeEnum),
        },
      },
      {
        field: 'intentionContractId',
        label: '意向合同',
        children: (
          <a
            onClick={() => {
              intentionIdRef.current = data?.intentionContractId!;
              intentionDetailDrawerRef.current?.open();
            }}
          >
            {data?.intentionContractCode}
          </a>
        ),
      },
      {
        field: 'bizOpportunityName',
        label: '商机名称',
        children: (
          <a onClick={() => businessOpportunityDrawerRef.current?.open()}>
            {data?.bizOpportunityName}
          </a>
        ),
      },
      {
        field: 'bizOpportunityDirectUserName',
        label: '商机负责人',
      },
      {
        field: 'vamFlag',
        label: '是否对赌',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        field: 'consultantUserId',
        label: '所属招商顾问',
        valueType: ValueType.PERSON,
      },
      {
        field: 'signer',
        label: '签约人',
      },
      {
        field: 'signerPhone',
        label: '联系电话',
        valueType: ValueType.PHONE,
        fieldProps: {
          sensitiveValue: data?.signerPhoneSensitive,
        },
      },
      {
        field: 'signerIdentityCard',
        label: '身份证号码',
        valueType: ValueType.IDENTITY_CARD,
        fieldProps: {
          sensitiveValue: data?.signerIdentityCardSensitive,
        },
      },
      {
        field: 'toPublic',
        label: '是否对公',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        field: 'signSubject',
        label: '签约主体',
        hidden: !data?.toPublic,
      },
      {
        field: 'socialCreditCode',
        label: '统一社会信用代码',
        hidden: !data?.toPublic,
      },
      {
        field: 'paymentDate',
        label: '正式打款日期',
      },
      {
        field: 'effectiveDate',
        label: '正式生效日期',
      },
      {
        field: 'formalExpirationDate',
        label: '正式到期日期',
      },
      {
        field: 'storeDeliveryDate',
        label: '门店交付日期',
      },
      {
        field: 'siteNo',
        label: '铺位编号',
      },
      {
        field: 'storeCode',
        label: '门店编号',
      },
      {
        field: 'storeName',
        label: '门店冠名',
      },
      {
        field: 'storeCoordinates',
        label: '门店定位',
        children:
          data?.storeCoordinates &&
          `${data.storeCoordinates.name}（经度：${data.storeCoordinates.longitude}；纬度：${data.storeCoordinates.latitude}）`,
      },
      {
        field: 'storeAddress',
        label: '门店地址',
      },
      {
        field: 'region',
        label: '所属省市区',
        valueType: ValueType.REGION,
        fieldProps: {
          regionLevel: 4,
        },
      },
      {
        field: 'belongWarZone',
        label: '所属大区',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(BelongWarZoneEnum),
        },
      },
      {
        field: 'developTag',
        label: '开发标签',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(DevelopTagEnum),
        },
      },
      {
        field: 'siteFlowId',
        label: '选址报告流程 id',
      },
      {
        field: 'storeStatus',
        label: '门店状态',
        valueType: ValueType.MULTIPLE,
        fieldProps: {
          options: enum2Options(StoreStatusEnum),
        },
      },
    ];

    // 是否是未提交或驳回
    const isNotSubmitOrReturn = [AuditStatusEnum.NOT_SUBMIT, AuditStatusEnum.RETURN].includes(
      data?.auditStatus!,
    );

    return (
      <Drawer
        width={1200}
        title="正式合同详情"
        open={open}
        push={false}
        destroyOnHidden
        onClose={() => setOpen(false)}
        {...props}
      >
        <Card loading={loading}>
          <div className="flex justify-between">
            <div className="flex flex-col gap-2">
              <p>
                客户名称：
                <a onClick={() => customerDetailDrawerRef.current?.open()}>{data?.customerName}</a>
              </p>
              <p>客户编号：{data?.customerCode}</p>
              <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
              <p>创建时间：{data?.createTime}</p>
              <p>更新人：{users?.find((i) => i.id === data?.updateUserId)?.name}</p>
              <p>更新时间：{data?.updateTime}</p>
            </div>
            <ButtonGroup
              items={[
                {
                  label: '编辑',
                  show:
                    isNotSubmitOrReturn || checkPermission(PermissionsMap.ContractFormalSuperEdit),
                  onClick: () => createEditFormalModalRef.current?.open(),
                },
                {
                  label: '提交审核',
                  show: isNotSubmitOrReturn,
                  onClick: handleSubmitAudit,
                },
                {
                  label: '推送泛微',
                  show: data?.nodeState === FormalNodeStateEnum.OA未发起,
                  onClick: () => setWeaverPushModalOpen(true),
                },
                {
                  label: (_r) => (
                    <ContractFileUpModal
                      getId={() => formalId}
                      onSuccess={refresh}
                      contractType={ContractEnum.正式合同}
                      maxCount={data?.attachments ? 10 - data?.attachments?.length : 10}
                    />
                  ),
                  show: !data?.attachments || data?.attachments?.length < 10,
                },
                {
                  label: '删除',
                  danger: true,
                  show: isNotSubmitOrReturn,
                  onClick: () =>
                    modal.confirm({
                      title: '删除',
                      content: '确定要删除该正式合同吗？',
                      onOk: async () => {
                        await batchDeleteFormalContract([formalId]);
                        message.success('删除成功');
                        setOpen(false);
                        onDelete?.();
                      },
                    }),
                },
              ]}
            />
          </div>
        </Card>

        <Row gutter={[20, 20]} className="mt-5">
          <Col span={24} xl={16}>
            <Card loading={loading}>
              <Tabs
                className="-mt-5"
                activeKey={activeKey}
                onTabClick={setActiveKey}
                items={[
                  {
                    label: '合同详情',
                    key: 'info',
                    children: (
                      <>
                        {renderDescriptions(baseInfoItems, data)}
                        <PaymentRecords data={data?.payments} />
                      </>
                    ),
                  },
                  {
                    label: '合同文件',
                    key: 'file',
                    children: (
                      <>
                        <ContractFileList data={data?.attachments || []} onRefresh={refresh} />
                      </>
                    ),
                  },
                ]}
              />
            </Card>
          </Col>
          <Col span={24} xl={8}>
            <ContractAuditCard
              ref={contractAuditCardRef}
              id={formalId}
              data={data}
              loading={loading}
              contractType={contractType}
              onTodoSuccess={onTodoSuccess}
              onSuccess={() => {
                refresh();
                onUpdate?.();
              }}
            />
          </Col>
        </Row>

        <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => data?.customerId} />
        <BusinessDetailDrawer
          ref={businessOpportunityDrawerRef}
          getId={() => data?.bizOpportunityId}
          onUpdate={() => {
            refresh();
            onUpdate?.();
          }}
        />
        <CreateEditFormalModal
          ref={createEditFormalModalRef}
          getId={() => formalId}
          onSuccess={() => {
            refresh();
            onUpdate?.();
          }}
        />
        <WeaverPushModal
          open={weaverPushModalOpen}
          getIds={() => [formalId]}
          contractType={contractType}
          onCancel={() => setWeaverPushModalOpen(false)}
          onSuccess={() => {
            setWeaverPushModalOpen(false);
            refresh();
            onUpdate?.();
          }}
        />
        <IntentionDetailDrawer
          ref={intentionDetailDrawerRef}
          getId={() => intentionIdRef.current}
        />
      </Drawer>
    );
  },
);

export default FormalDetailDrawer;
