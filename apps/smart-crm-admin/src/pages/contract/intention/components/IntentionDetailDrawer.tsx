import React, { useRef, useState } from 'react';
import { contractAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { ButtonGroup } from '@src/components';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import BusinessDetailDrawer from '@src/pages/business-opportunity/components/BusinessDetailDrawer';
import WeaverPushModal from '@src/pages/business-opportunity/components/BusinessDetailDrawer/WeaverPushModal';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import IntendedAreaChangeModal from '@src/pages/process/intended-area-change/components/IntendedAreaChangeModal';
import IntendedContractExtensionModal from '@src/pages/process/intended-contract-extension/components/IntendedContractExtensionModal';
import { PermissionsMap, usePermission } from '@src/permissions';
import {
  BelongWarZoneEnum,
  BusinessOpportunityTypeEnum,
  OwnerTypeEnum,
} from '@src/services/business-opportunity/type';
import { AuditStatusEnum } from '@src/services/common/type';
import { ContractEnum } from '@src/services/contract';
import { StoreCategoryEnum } from '@src/services/contract/formal/type';
import {
  batchDeleteIntentionContract,
  getIntentionContractDetail,
  submitIntentionAudit,
  updateIntentionStatus,
} from '@src/services/contract/intention';
import {
  ContractChannelTypeEnum,
  IntentionContractDTO,
  IntentionContractStatusEnum,
  IntentionNodeStateEnum,
} from '@src/services/contract/intention/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Card, Col, Drawer, DrawerProps, Row, Tabs } from 'antd';
import CreateEditIntentionModal from './CreateEditIntentionModal';
import ContractAuditCard, { ContractAuditCardRef } from '../../components/ContractAuditCard';
import ContractFileList from '../../components/ContractFileList';
import ContractFileUpModal from '../../components/ContractFileUpModal';
import PaymentRecords from '../../components/PaymentRecords';

interface IntentionDetailDrawerProps extends DrawerProps {
  getId: () => number | null | undefined;
  onUpdate?: () => void;
  onDelete?: () => void;
  onTodoSuccess?: () => void;
}

const contractType = ContractTypeEnum.意向合同;

const IntentionDetailDrawer = React.forwardRef<ModalRef, IntentionDetailDrawerProps>(
  ({ getId, onUpdate, onDelete, onTodoSuccess, ...props }, ref) => {
    const { modal, message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [activeKey, setActiveKey] = useState('info');
    const customerDetailDrawerRef = useRef<ModalRef>(null);
    const businessOpportunityDrawerRef = useRef<ModalRef>(null);
    const createEditIntentionModalRef = useRef<ModalRef>(null);
    const [weaverPushModalOpen, setWeaverPushModalOpen] = useState(false);
    const contractAuditCardRef = useRef<ContractAuditCardRef>(null);
    const intendedAreaChangeModalRef = useRef<ModalRef>(null);
    const intendedContractExtensionModalRef = useRef<ModalRef>(null);

    const intentionId = getId()!;

    const checkPermission = usePermission();
    const { data: users } = useAllUsers();
    const { data, loading, refresh } = useRequest(() => getIntentionContractDetail(intentionId), {
      ready: open,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        setActiveKey('info');
      },
      close: () => setOpen(false),
    }));

    const handleSubmitAudit = async () => {
      const amount = data?.payments?.reduce((total, cur) => total + cur.paymentAmount, 0) || 0;

      modal.confirm({
        title: '提交审核',
        content: `累计打款金额：${amount.toFixed(2)} 元，是否提交合同审核？`,
        onOk: async () => {
          await submitIntentionAudit(intentionId);
          message.success('提交成功');
          refresh();
          contractAuditCardRef.current?.refresh();
          onUpdate?.();

          // 驳回才会在待办里
          if (data?.auditStatus === AuditStatusEnum.RETURN) {
            onTodoSuccess?.();
          }
        },
      });
    };

    const baseInfoItems: DescriptionFieldType<IntentionContractDTO>[] = [
      {
        field: 'code',
        label: '合同编号',
      },
      {
        field: 'storeCategory',
        label: '门店性质',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(StoreCategoryEnum),
        },
      },
      {
        field: 'nodeState',
        label: '合同节点',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(IntentionNodeStateEnum),
        },
      },
      {
        field: 'status',
        label: '合同状态',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(IntentionContractStatusEnum),
        },
      },
      {
        field: 'auditStatus',
        label: '审核状态',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: contractAuditStatusOptions,
        },
      },
      {
        field: 'directUserId',
        label: '负责人',
        valueType: ValueType.PERSON,
      },
      {
        field: 'ownerType',
        label: '业主类型',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(OwnerTypeEnum),
        },
      },
      {
        field: 'bizOpportunityName',
        label: '商机名称',
        children: (
          <a onClick={() => businessOpportunityDrawerRef.current?.open()}>
            {data?.bizOpportunityName}
          </a>
        ),
      },
      {
        field: 'bizOpportunityDirectUserId',
        label: '商机负责人',
        valueType: ValueType.PERSON,
      },
      {
        field: 'channelType',
        label: '合作的特殊渠道',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(ContractChannelTypeEnum),
        },
        hidden: ![
          BusinessOpportunityTypeEnum.CHANNEL,
          BusinessOpportunityTypeEnum.FISSION_CHANNEL,
        ].includes(data?.bizOpportunityType!),
      },
      {
        field: 'consultantUserId',
        label: '所属招商顾问',
        valueType: ValueType.PERSON,
      },
      {
        field: 'signer',
        label: '签约人',
      },
      {
        field: 'signerPhone',
        label: '联系电话',
        valueType: ValueType.PHONE,
        fieldProps: {
          sensitiveValue: data?.signerPhoneSensitive,
        },
      },
      {
        field: 'signerIdentityCard',
        label: '身份证号码',
        valueType: ValueType.PHONE,
        fieldProps: {
          sensitiveValue: data?.signerIdentityCardSensitive,
        },
      },
      {
        field: 'toPublic',
        label: '是否对公',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        field: 'signSubject',
        label: '签约主体',
        hidden: !data?.toPublic,
      },
      {
        field: 'socialCreditCode',
        label: '统一社会信用代码',
        hidden: !data?.toPublic,
      },
      {
        field: 'intentionSignatureDate',
        label: '意向对账日期',
      },
      {
        field: 'effectiveDate',
        label: '意向生效日期',
      },
      {
        field: 'intentionExpirationDate',
        label: '意向到期日期',
      },
      {
        field: 'vamFlag',
        label: '是否对赌',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        field: 'effectiveDays',
        label: '有效天数',
      },
      {
        field: 'vamExpirationDate',
        label: '对赌到期时间',
      },
      {
        field: 'region',
        label: '意向区域',
        valueType: ValueType.REGION,
        fieldProps: {
          regionLevel: 4,
        },
      },
      {
        field: 'belongWarZone',
        label: '所属大区',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(BelongWarZoneEnum),
        },
      },
      {
        label: '意向点位',
        field: 'intentionPositions',
        children: (
          <div>
            {data?.intentionPositions?.map((i, index) => (
              <p key={index}>
                {i.name}（经度：{i.longitude}；纬度：{i.latitude}）
              </p>
            ))}
          </div>
        ),
      },
      {
        label: '扩容点位',
        field: 'expansionPosition',
        children:
          data?.expansionPosition &&
          `${data.expansionPosition.name}（经度：${data.expansionPosition.longitude}；纬度：${data.expansionPosition.latitude}）`,
      },
    ];

    // 是否是未提交或驳回
    const isNotSubmitOrReturn = [AuditStatusEnum.NOT_SUBMIT, AuditStatusEnum.RETURN].includes(
      data?.auditStatus!,
    );

    return (
      <Drawer
        width={1200}
        title="意向合同详情"
        open={open}
        push={false}
        destroyOnHidden
        onClose={() => setOpen(false)}
        {...props}
      >
        <Card loading={loading}>
          <div className="flex justify-between">
            <div className="flex flex-col gap-2">
              <p>
                客户名称：
                <a onClick={() => customerDetailDrawerRef.current?.open()}>{data?.customerName}</a>
              </p>
              <p>客户编号：{data?.customerCode}</p>
              <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
              <p>创建时间：{data?.createTime}</p>
              <p>更新人：{users?.find((i) => i.id === data?.updateUserId)?.name}</p>
              <p>更新时间：{data?.updateTime}</p>
            </div>
            <ButtonGroup
              items={[
                {
                  label: '编辑',
                  show:
                    isNotSubmitOrReturn && checkPermission(PermissionsMap.ContractIntentionEdit),
                  onClick: () => createEditIntentionModalRef.current?.open(),
                },
                {
                  label: '提交审核',
                  show: isNotSubmitOrReturn,
                  onClick: handleSubmitAudit,
                },
                {
                  label: '推送泛微',
                  show: data?.nodeState === IntentionNodeStateEnum.OA未发起,
                  onClick: () => setWeaverPushModalOpen(true),
                },
                {
                  label: '标记为已退款',
                  show: [
                    IntentionContractStatusEnum.意向中,
                    IntentionContractStatusEnum.已失效,
                    IntentionContractStatusEnum.已作废,
                  ].includes(data?.status!),
                  onClick: () =>
                    modal.confirm({
                      title: '标记为已退款',
                      content: '确定要将该合同标记为已退款状态吗？',
                      onOk: async () => {
                        await updateIntentionStatus({
                          ids: [intentionId],
                          status: IntentionContractStatusEnum.已退款,
                        });
                        message.success('标记成功');
                        refresh();
                        onUpdate?.();
                      },
                    }),
                },
                {
                  label: '标记为已作废',
                  show: [
                    IntentionContractStatusEnum.意向中,
                    IntentionContractStatusEnum.已失效,
                  ].includes(data?.status!),
                  onClick: () =>
                    modal.confirm({
                      title: '标记为已作废',
                      content: '确定要将该合同标记为已作废状态吗？',
                      onOk: async () => {
                        await updateIntentionStatus({
                          ids: [intentionId],
                          status: IntentionContractStatusEnum.已作废,
                        });
                        message.success('标记成功');
                        refresh();
                        onUpdate?.();
                      },
                    }),
                },
                {
                  label: '意向区域变更',
                  show:
                    checkPermission(PermissionsMap.ContractIntentionIntendedAreaChange) &&
                    [
                      IntentionContractStatusEnum.意向中,
                      IntentionContractStatusEnum.已失效,
                    ].includes(data?.status!),
                  onClick: () => {
                    intendedAreaChangeModalRef.current?.open();
                  },
                },
                {
                  label: '延期申请',
                  show:
                    checkPermission(PermissionsMap.ContractIntentionIntendedContractExtension) &&
                    [
                      IntentionContractStatusEnum.意向中,
                      IntentionContractStatusEnum.已失效,
                    ].includes(data?.status!),
                  onClick: () => {
                    intendedContractExtensionModalRef.current?.open();
                  },
                },
                {
                  label: (_r) => (
                    <ContractFileUpModal
                      getId={() => intentionId}
                      onSuccess={refresh}
                      contractType={ContractEnum.意向合同}
                      maxCount={data?.attachments ? 10 - data?.attachments?.length : 10}
                    />
                  ),
                  show: !data?.attachments || data?.attachments?.length < 10,
                },
                {
                  label: '删除',
                  danger: true,
                  show: isNotSubmitOrReturn,
                  onClick: () =>
                    modal.confirm({
                      title: '删除',
                      content: '确定要删除该意向合同吗？',
                      onOk: async () => {
                        await batchDeleteIntentionContract([intentionId]);
                        message.success('删除成功');
                        setOpen(false);
                        onDelete?.();
                      },
                    }),
                },
              ]}
            />
          </div>
        </Card>

        <Row gutter={[20, 20]} className="mt-5">
          <Col span={24} xl={16}>
            <Card loading={loading}>
              <Tabs
                className="-mt-5"
                activeKey={activeKey}
                onTabClick={setActiveKey}
                items={[
                  {
                    label: '合同详情',
                    key: 'info',
                    children: (
                      <>
                        {renderDescriptions(baseInfoItems, data)}
                        <PaymentRecords data={data?.payments} />
                      </>
                    ),
                  },
                  {
                    label: '合同文件',
                    key: 'file',
                    children: (
                      <ContractFileList data={data?.attachments || []} onRefresh={refresh} />
                    ),
                  },
                ]}
              />
            </Card>
          </Col>
          <Col span={24} xl={8}>
            <ContractAuditCard
              ref={contractAuditCardRef}
              id={intentionId}
              data={data}
              loading={loading}
              contractType={contractType}
              onTodoSuccess={onTodoSuccess}
              onSuccess={() => {
                refresh();
                onUpdate?.();
              }}
            />
          </Col>
        </Row>

        <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => data?.customerId} />
        <BusinessDetailDrawer
          ref={businessOpportunityDrawerRef}
          getId={() => data?.bizOpportunityId}
        />
        <CreateEditIntentionModal
          ref={createEditIntentionModalRef}
          getId={() => intentionId}
          onSuccess={() => {
            refresh();
            onUpdate?.();
          }}
        />
        <WeaverPushModal
          open={weaverPushModalOpen}
          getIds={() => [intentionId]}
          contractType={contractType}
          onCancel={() => setWeaverPushModalOpen(false)}
          onSuccess={() => {
            setWeaverPushModalOpen(false);
            refresh();
            onUpdate?.();
          }}
        />
        <IntendedAreaChangeModal ref={intendedAreaChangeModalRef} getCreateId={() => intentionId} />
        <IntendedContractExtensionModal
          ref={intendedContractExtensionModalRef}
          getCreateId={() => intentionId}
        />
      </Drawer>
    );
  },
);

export default IntentionDetailDrawer;
