import React, { useEffect, useRef, useState } from 'react';
import { LoadingOutlined } from '@ant-design/icons';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ChooseMapPoints, Encrypt, UserSelect } from '@src/components';
import CheckCodeModal from '@src/components/CheckCodeModal';
import EditableDate from '@src/components/Editable/EditableDate';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useDistrictsStreetOpened from '@src/hooks/useDistrictsStreetOpened';
import useProvinceWarZoneMap from '@src/hooks/useProvinceWarZoneMap';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import { getBusinessOpportunityByCustomerId } from '@src/services/appointment-record';
import {
  BelongWarZoneEnum,
  BusinessOpportunityTypeEnum,
  OwnerTypeEnum,
  ProgressNodeEnum,
} from '@src/services/business-opportunity/type';
import { checkContractStoreCategory } from '@src/services/contract/formal';
import { StoreCategoryEnum } from '@src/services/contract/formal/type';
import {
  generateIntentionCode,
  getIntentionContractDetail,
  saveAndSubmitIntentionContract,
  saveIntentionContract,
  updateIntentionContract,
} from '@src/services/contract/intention';
import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';
import { getCustomerDetail } from '@src/services/customers';
import { IdentityTypeEnum } from '@src/services/mini-program/identity/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import { getExpansionPositionsByDistrictCode } from '@src/services/regional/expansion';
import { getAreaQuotaByDistrictCode } from '@src/services/regional/quota';
import { JoinRegionEnum } from '@src/services/regional/quota/type';
import useUserStore from '@src/store/useUserStore';
import { enum2Options, enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Card, Form, Input, InputNumber, Modal, ModalProps, Select } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import PaymentFormList from '../../components/PaymentFormList';

interface CreateEditIntentionModalProps extends ModalProps {
  getId?: () => number | null;
  createInitialValues?: {
    customerId: number;
    customerName: string;
    bizOpportunityId: number;
  };
  onSuccess: () => void;
}

const CreateEditIntentionModal = React.forwardRef<ModalRef, CreateEditIntentionModalProps>(
  ({ getId, createInitialValues, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);
    const [checkModalOpen, setCheckModalOpen] = useState(false);
    const saveTypeRef = useRef<'save' | 'submit'>('save');

    const intentionId = getId?.();
    const isEdit = !!intentionId;

    const {
      user: { shUserId },
    } = useUserStore();
    const {
      data: customer,
      loading: getCustomerLoading,
      runAsync: getCustomer,
      mutate: mutateCustomer,
    } = useRequest(getCustomerDetail, {
      manual: true,
    });
    const {
      runAsync: getBusinessOpportunityList,
      data: businessOpportunityList,
      loading: getBusinessOpportunityListLoading,
      mutate: mutateBusinessOpportunityList,
    } = useRequest(
      (id: number) => getBusinessOpportunityByCustomerId({ id, node: ProgressNodeEnum.意向合同 }),
      {
        manual: true,
      },
    );
    const {
      data: expansionPositions,
      runAsync: getExpansionPositions,
      loading: getExpansionPositionsLoading,
      mutate: mutateExpansionPositions,
    } = useRequest(getExpansionPositionsByDistrictCode, {
      manual: true,
    });
    const { data, loading, mutate } = useRequest(() => getIntentionContractDetail(intentionId!), {
      ready: open && isEdit,
      onSuccess: (res) => {
        form.setFieldsValue({ ...res, expansionPositionId: res.expansionPosition?.id });

        // 如果没有选择商机，才需要请求。如果有选，是禁用状态，直接取详情的数据
        if (!res.bizOpportunityId) {
          getBusinessOpportunityList(res.customerId);
        }

        getCustomer(res.customerId);
        getExpansionPositions(res.region);
      },
    });
    const { runAsync: save, loading: saveLoading } = useRequest(saveIntentionContract, {
      manual: true,
    });
    const { runAsync: submit, loading: submitLoading } = useRequest(
      saveAndSubmitIntentionContract,
      {
        manual: true,
      },
    );
    const { runAsync: update, loading: updateLoading } = useRequest(updateIntentionContract, {
      manual: true,
    });
    const {
      runAsync: generateCode,
      loading: generateCodeLoading,
      cancel: cancelGenerateCode,
    } = useRequest(generateIntentionCode, { manual: true });
    const provinceWarZoneMap = useProvinceWarZoneMap({ ready: open });
    const { transformDistricts, getStreetOpenedRegionIdsLoading } = useDistrictsStreetOpened({
      ready: open,
    });
    const {
      data: checkStoreCategoryData,
      loading: checkStoreCategoryLoading,
      runAsync: runCheckStoreCategory,
      mutate: mutateCheckStoreCategoryData,
    } = useRequest(checkContractStoreCategory, { manual: true });
    const {
      data: areaQuota,
      runAsync: getAreaQuota,
      mutate: mutateAreaQuota,
    } = useRequest(getAreaQuotaByDistrictCode, { manual: true });

    useEffect(() => {
      if (open && createInitialValues) {
        form.setFieldsValue({ ...createInitialValues, signer: createInitialValues.customerName });
        getBusinessOpportunityList(createInitialValues.customerId);
        getCustomer(createInitialValues.customerId).then(({ identityCard }) => {
          form.setFieldsValue({
            signerIdentityCard: identityCard,
          });
        });
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [open]);

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        mutate(undefined);
        mutateCustomer(undefined);
        mutateBusinessOpportunityList(undefined);
        mutateExpansionPositions(undefined);
        cancelGenerateCode();
        mutateCheckStoreCategoryData(undefined);
        mutateAreaQuota(undefined);
      },
      close: () => setOpen(false),
    }));

    // 获取商机是否是渠道类型
    const getIsChannelType = () => {
      // 如果是编辑且之前选择了商机，直接用详情返回的商机类型，否则用返回的商机
      const businessOpportunityType =
        isEdit && !!data?.bizOpportunityId
          ? data?.bizOpportunityType
          : businessOpportunityList?.find((i) => i.id === form.getFieldValue('bizOpportunityId'))
              ?.businessOpportunityType!;

      return [
        BusinessOpportunityTypeEnum.CHANNEL,
        BusinessOpportunityTypeEnum.FISSION_CHANNEL,
      ].includes(businessOpportunityType);
    };

    const handleSave = async (values: any) => {
      if (isEdit) {
        await update({ id: intentionId, ...values });
        message.success('修改成功');
      } else {
        const method = saveTypeRef.current === 'save' ? save : submit;

        await method(values);

        message.success('提交成功');
      }

      setOpen(false);
      onSuccess();
    };

    const handleFinish = async (values: any) => {
      const { customerId, region, storeCategory } = values;

      const [checkRes, areaQuotaRes] = await Promise.all([
        runCheckStoreCategory({
          customerId,
          contractType: ContractTypeEnum.意向合同,
          regionCode: region,
          storeCategory,
        }),
        getAreaQuota({ districtCode: region }),
      ]);

      if (checkRes.success && areaQuotaRes.remainQuotaNum > 0) {
        handleSave(values);
      } else {
        setCheckModalOpen(true);
      }
    };

    return (
      <>
        <Modal
          open={open}
          width={1000}
          title={isEdit ? '编辑意向合同' : '新建意向合同'}
          loading={loading || getStreetOpenedRegionIdsLoading || getCustomerLoading}
          destroyOnHidden
          confirmLoading={
            saveLoading || updateLoading || submitLoading || checkStoreCategoryLoading
          }
          styles={{
            body: { maxHeight: '60vh', margin: '0 -24px', padding: '0 24px' },
          }}
          modalRender={(node) => (
            <Form
              form={form}
              preserve={false}
              clearOnDestroy
              scrollToFirstError
              labelCol={{ span: 9 }}
              onFinish={handleFinish}
            >
              {node}
            </Form>
          )}
          okText="保存"
          okButtonProps={{ ghost: !isEdit }}
          footer={(_, { OkBtn, CancelBtn }) => (
            <>
              <CancelBtn />
              <OkBtn />
              {!isEdit && (
                <Button
                  loading={saveLoading || submitLoading}
                  type="primary"
                  onClick={() => {
                    saveTypeRef.current = 'submit';
                    form.submit();
                  }}
                >
                  提交审核
                </Button>
              )}
            </>
          )}
          onOk={() => {
            saveTypeRef.current = 'save';
            form.submit();
          }}
          onCancel={() => setOpen(false)}
          {...props}
        >
          <div className="flex flex-col sm:flex-row gap-5 overflow-auto h-[60vh]">
            <div className="flex flex-col gap-4 flex-1 sm:overflow-auto sm:pr-5">
              <Card
                title="加盟信息"
                classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
              >
                <Form.Item
                  label="门店性质"
                  name="storeCategory"
                  rules={[{ required: true, message: '请选择门店性质' }]}
                >
                  <Select
                    disabled={isEdit}
                    placeholder="请选择门店性质"
                    options={enum2Options(StoreCategoryEnum)}
                    onChange={async (storeCategory) => {
                      const { code } = await generateCode({ storeCategory });

                      form.setFieldValue('code', code);
                    }}
                  />
                </Form.Item>
                <Form.Item
                  label="合同编号"
                  name="code"
                  rules={[{ required: true, message: '请输入合同编号' }]}
                >
                  <Input
                    disabled={isEdit}
                    placeholder="选择门店性质后自动生成"
                    suffix={
                      generateCodeLoading ? (
                        <span className="text-primary">
                          生成中
                          <LoadingOutlined className="ml-2" />
                        </span>
                      ) : (
                        <span />
                      )
                    }
                  />
                </Form.Item>
                <Form.Item
                  label="负责人"
                  name="directUserId"
                  initialValue={shUserId}
                  rules={[{ required: true, message: '请选择负责人' }]}
                >
                  <UserSelect placeholder="请选择负责人" />
                </Form.Item>
                <Form.Item
                  label="客户名称"
                  name="customerId"
                  rules={[{ required: true, message: '请选择客户' }]}
                >
                  <RelCustomer
                    editable={!isEdit}
                    allowClear={!isEdit}
                    defaultCustomerIdToNameMap={{
                      [data?.customerId || '']: data?.customerName,
                      [createInitialValues?.customerId || '']: createInitialValues?.customerName,
                    }}
                    onChange={(id, info) => {
                      mutateBusinessOpportunityList(undefined);
                      mutateCustomer(info);
                      form.setFieldsValue({
                        bizOpportunityId: undefined,
                        signer: info?.name,
                        signerPhone: undefined,
                        signerIdentityCard: info?.identityCard,
                        ownerType: info
                          ? info.hasShop
                            ? OwnerTypeEnum.老业主
                            : OwnerTypeEnum.新业主
                          : undefined,
                      });

                      if (id) {
                        getBusinessOpportunityList(id);
                      }
                    }}
                  />
                </Form.Item>
                <Form.Item
                  label="业主类型"
                  name="ownerType"
                  rules={[{ required: true, message: '请选择业主类型' }]}
                >
                  <Select placeholder="请选择业主类型" options={enum2Options(OwnerTypeEnum)} />
                </Form.Item>
                <Form.Item
                  label="商机名称"
                  name="bizOpportunityId"
                  tooltip="只能选择当前客户处于 “意向合同” 进展的商机"
                  dependencies={['storeCategory']}
                  rules={[
                    {
                      required: true,
                      message: '请选择商机',
                    },
                  ]}
                >
                  <Select
                    allowClear
                    disabled={isEdit && !!data?.bizOpportunityId}
                    loading={getBusinessOpportunityListLoading}
                    placeholder="请选择商机"
                    options={
                      isEdit && !!data?.bizOpportunityId
                        ? [{ name: data?.bizOpportunityName, id: data?.bizOpportunityId }]
                        : (businessOpportunityList as DefaultOptionType[])
                    }
                    fieldNames={{ label: 'name', value: 'id' }}
                  />
                </Form.Item>
                <Form.Item
                  label="合作的特殊渠道"
                  name="channelType"
                  rules={[{ required: true, message: '请选择合作的特殊渠道' }]}
                >
                  <Select
                    placeholder="请选择合作的特殊渠道"
                    options={enum2Options(ContractChannelTypeEnum)}
                  />
                </Form.Item>
              </Card>
              <Card
                title="合同签约信息"
                classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
              >
                <Form.Item
                  label="所属招商顾问"
                  name="consultantUserId"
                  dependencies={['storeCategory', 'bizOpportunityId']}
                  rules={[
                    ({ getFieldValue }) => {
                      // 加盟 T 且社会商机时非必填
                      const notRequired =
                        getFieldValue('storeCategory') === StoreCategoryEnum['加盟 T'] &&
                        !getIsChannelType();

                      return {
                        required: !notRequired,
                        message: '请选择所属招商顾问',
                      };
                    },
                  ]}
                >
                  <UserSelect
                    placeholder="请选择所属招商顾问"
                    transformOptions={(options) =>
                      options.filter((i) =>
                        [IdentityTypeEnum.招商顾问, IdentityTypeEnum.大客户招商].includes(
                          i.identityType,
                        ),
                      )
                    }
                  />
                </Form.Item>
                <Form.Item
                  label="签约人"
                  name="signer"
                  rules={[{ required: true, message: '不能为空' }]}
                >
                  <Input placeholder="选择客户后自动填充" disabled />
                </Form.Item>
                <Form.Item
                  label="联系电话"
                  name="signerPhone"
                  rules={[{ required: true, message: '请选择联系电话' }]}
                >
                  <Encrypt.PhoneSelect
                    placeholder="请选择手机号"
                    options={customer?.phones?.map((phone, index) => ({
                      label: customer.phonesSensitive?.[index],
                      value: phone,
                    }))}
                    encryptSensitiveMap={{
                      [data?.signerPhone || '']: data?.signerPhoneSensitive,
                    }}
                  />
                </Form.Item>
                <Encrypt.IdCardFormItem
                  formItemProps={{
                    label: '身份证号码',
                    name: 'signerIdentityCard',
                    rules: [{ required: true, message: '请输入身份证号码' }],
                  }}
                  fieldProps={{
                    disabled: true,
                    placeholder: '选择客户后自动填充',
                    encryptSensitiveMap: {
                      [customer?.identityCard || '']: customer?.identityCardSensitive,
                      [data?.signerIdentityCard || '']: data?.signerIdentityCardSensitive,
                    },
                  }}
                />
              </Card>
              <Card
                title="场景判断"
                classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
              >
                <Form.Item
                  name="toPublic"
                  label="是否对公"
                  initialValue={false}
                  rules={[{ required: true, message: '请选择是否对公' }]}
                >
                  <Select options={yesOrNoOptions} placeholder="请选择是否对公" />
                </Form.Item>
                <Form.Item noStyle dependencies={['toPublic']}>
                  {({ getFieldValue }) =>
                    getFieldValue('toPublic') && (
                      <>
                        <Form.Item
                          label="签约主体"
                          name="signSubject"
                          rules={[{ required: true, message: '请输入签约主体' }]}
                        >
                          <Input placeholder="请输入签约主体" />
                        </Form.Item>
                        <Form.Item
                          label="统一社会信用代码"
                          name="socialCreditCode"
                          rules={[{ required: true, message: '请输入统一社会信用代码' }]}
                        >
                          <Input placeholder="请输入统一社会信用代码" />
                        </Form.Item>
                      </>
                    )
                  }
                </Form.Item>
                <EditableDate
                  editable
                  formItemProps={{
                    label: '意向对账日期',
                    name: 'intentionSignatureDate',
                    rules: [{ required: true, message: '请选择意向对账日期' }],
                  }}
                />
                <Form.Item
                  label="是否对赌"
                  name="vamFlag"
                  initialValue={false}
                  rules={[{ required: true, message: '请选择是否对赌' }]}
                >
                  <Select placeholder="请选择是否对赌" options={yesOrNoOptions} />
                </Form.Item>
                <Form.Item
                  label="有效天数"
                  name="effectiveDays"
                  rules={[{ required: true, message: '请输入有效天数' }]}
                >
                  <InputNumber
                    precision={0}
                    min={0}
                    addonAfter="天"
                    placeholder="请输入"
                    style={{ width: 140 }}
                  />
                </Form.Item>
                <EditableRegion
                  editable
                  formItemProps={{
                    label: '意向区域',
                    name: 'region',
                    rules: [{ required: true, message: '请选择意向区域' }],
                  }}
                  fieldProps={{
                    regionLevel: 4,
                    placeholder: '请选择意向区域',
                    transformDistricts,
                    onChange: (value: any) => {
                      form.setFieldsValue({
                        belongWarZone: provinceWarZoneMap[value?.[0]],
                        expansionPositionId: undefined,
                      });
                      mutateExpansionPositions(undefined);

                      if (value) {
                        getExpansionPositions(value.join('/'));
                      }
                    },
                  }}
                />
                <Form.Item
                  label="所属大区"
                  name="belongWarZone"
                  rules={[{ required: true, message: '请选择所属大区' }]}
                >
                  <Select
                    disabled
                    placeholder="根据意向区域自动匹配"
                    options={enum2Options(BelongWarZoneEnum)}
                  />
                </Form.Item>
                <Form.Item
                  label="意向点位"
                  name="intentionPositions"
                  dependencies={['bizOpportunityId']}
                  rules={[
                    // 函数形式，选择商机后才会重新生效
                    () => ({
                      required: getIsChannelType(),
                      message: '请选择意向点位',
                    }),
                  ]}
                >
                  <ChooseMapPoints multiple={false} />
                </Form.Item>
                <Form.Item noStyle dependencies={['bizOpportunityId']}>
                  {() =>
                    // 意向点位和扩容点位只能选择一个，如果意向点位是必填的，则隐藏扩容点位
                    !getIsChannelType() && (
                      <Form.Item
                        label="扩容点位"
                        name="expansionPositionId"
                        dependencies={['intentionPositions']}
                        rules={[
                          {
                            validator: (_, value) => {
                              const intentionPositions = form.getFieldValue('intentionPositions');

                              if (
                                value &&
                                Array.isArray(intentionPositions) &&
                                intentionPositions.length > 0
                              ) {
                                return Promise.reject(
                                  new Error('意向点位和扩容点位只能选择其中一个'),
                                );
                              }

                              return Promise.resolve();
                            },
                          },
                        ]}
                      >
                        <Select
                          allowClear
                          placeholder="若扩容，请选择扩容点位"
                          loading={getExpansionPositionsLoading}
                          // 编辑时如果区域一样应该要加上原来的
                          options={[
                            ...(isEdit &&
                            data?.expansionPosition &&
                            data.region === form.getFieldValue('region')
                              ? [data.expansionPosition]
                              : []),
                            ...(expansionPositions || []),
                          ].map((i) => ({
                            label: `${i.name} （经度：${i.longitude}；纬度：${i.latitude}）`,
                            value: i.id,
                          }))}
                        />
                      </Form.Item>
                    )
                  }
                </Form.Item>
              </Card>
            </div>
            <div className="flex-1 sm:overflow-auto sm:pr-5">
              <PaymentFormList />
            </div>
          </div>
        </Modal>
        <CheckCodeModal
          title={[
            { label: '加盟属性不匹配', show: !checkStoreCategoryData?.success },
            { label: '区域无剩余名额', show: areaQuota && areaQuota.remainQuotaNum <= 0 },
          ]
            .filter((i) => i.show)
            .map((i) => i.label)
            .join('、')}
          open={checkModalOpen}
          onSuccess={() => {
            setCheckModalOpen(false);
            handleSave(form.getFieldsValue());
          }}
          onCancel={() => setCheckModalOpen(false)}
          messageInfo={() => {
            const storeCategoryEnum = enum2ValueEnum(StoreCategoryEnum);

            return (
              <div className="text-[#e10600] text-sm mb-2">
                {!checkStoreCategoryData?.success && (
                  <>
                    <div>门店性质：{storeCategoryEnum[form.getFieldValue('storeCategory')]}</div>
                    <div>
                      客户性质：{storeCategoryEnum[checkStoreCategoryData?.customerNature!]}
                    </div>
                    <div>
                      区域性质：
                      {enum2ValueEnum(JoinRegionEnum)[checkStoreCategoryData?.joinRegion!]}
                    </div>
                  </>
                )}
                {areaQuota && areaQuota.remainQuotaNum <= 0 && (
                  <div>区域剩余名额：{areaQuota.remainQuotaNum}</div>
                )}
              </div>
            );
          }}
        />
      </>
    );
  },
);

export default CreateEditIntentionModal;
