import React, { useRef, useState } from 'react';
import { contractAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { ButtonGroup } from '@src/components';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { PermissionsMap, usePermission } from '@src/permissions';
import { AuditStatusEnum } from '@src/services/common/type';
import { ContractEnum } from '@src/services/contract';
import {
  batchDeleteChangeLegalPersonContract,
  getChangeLegalPersonContractDetail,
  submitChangeLegalPersonContractAudit,
} from '@src/services/contract/change-legal-person';
import {
  ChangeLegalPersonContractDTO,
  ChangeLegalPersonContractStatusEnum,
  ChangeTypeEnum,
} from '@src/services/contract/change-legal-person/type';
import { ContractNodeEnum } from '@src/services/contract/renewal/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Card, Col, Drawer, DrawerProps, Row, Tabs } from 'antd';
import CreateEditChangeLegalPersonModal from './CreateEditChangeLegalPersonModal';
import ContractAuditCard, { ContractAuditCardRef } from '../../components/ContractAuditCard';
import ContractFileList from '../../components/ContractFileList';
import ContractFileUpModal from '../../components/ContractFileUpModal';
import PaymentRecords from '../../components/PaymentRecords';

interface ChangeLegalPersonDetailDrawerProps extends DrawerProps {
  getId: () => number | null | undefined;
  onUpdate?: () => void;
  onDelete?: () => void;
  onTodoSuccess?: () => void;
}

const ChangeLegalPersonDetailDrawer = React.forwardRef<
  ModalRef,
  ChangeLegalPersonDetailDrawerProps
>(({ getId, onUpdate, onDelete, onTodoSuccess, ...props }, ref) => {
  const { modal, message } = App.useApp();
  const [open, setOpen] = useState(false);
  const [activeKey, setActiveKey] = useState('info');
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const createEditModalRef = useRef<ModalRef>(null);
  const contractAuditCardRef = useRef<ContractAuditCardRef>(null);
  const shopDetailDrawerRef = useRef<ModalRef>(null);
  const customerIdRef = useRef<number | null>(null);

  const id = getId()!;

  const checkPermission = usePermission();
  const { data: users } = useAllUsers();
  const { data, loading, refresh } = useRequest(() => getChangeLegalPersonContractDetail(id), {
    ready: open,
  });

  React.useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true);
      setActiveKey('info');
    },
    close: () => setOpen(false),
  }));

  // 是否是未提交或驳回
  const isNotSubmitOrReturn = [AuditStatusEnum.NOT_SUBMIT, AuditStatusEnum.RETURN].includes(
    data?.auditStatus!,
  );

  const handleSubmitAudit = async () => {
    modal.confirm({
      title: '提交审核',
      content:
        Array.isArray(data?.payments) && data.payments.length > 0
          ? `累计打款金额：${(
              data.payments.reduce((total, cur) => total + cur.paymentAmount, 0) || 0
            ).toFixed(2)} 元，是否提交合同审核？`
          : '确定要提交审核吗？',
      onOk: async () => {
        await submitChangeLegalPersonContractAudit(id);
        message.success('提交成功');
        refresh();
        contractAuditCardRef.current?.refresh();
        onUpdate?.();
      },
    });
  };

  const baseInfoItems: DescriptionFieldType<ChangeLegalPersonContractDTO>[] = [
    {
      label: '合同编号',
      field: 'code',
    },
    {
      label: '门店编码',
      field: 'shopId',
      children: <a onClick={() => shopDetailDrawerRef.current?.open()}>{data?.shopId}</a>,
    },
    {
      label: '门店冠名',
      field: 'shopName',
    },
    {
      label: '合同节点',
      field: 'node',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(ContractNodeEnum),
      },
    },
    {
      label: '合同状态',
      field: 'status',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(ChangeLegalPersonContractStatusEnum),
      },
    },
    {
      label: '审核状态',
      field: 'auditStatus',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: contractAuditStatusOptions,
      },
    },
    {
      label: '营业执照主体名称',
      field: 'licenseSubjectName',
    },
    {
      label: '门店实际使用面积',
      field: 'shopRealUseArea',
      children: `${data?.shopRealUseArea} m²`,
    },
    {
      label: '不动产权证地址',
      field: 'realEstateAddress',
    },
    {
      label: '原合同生效日期',
      field: 'originalEffectiveDate',
    },
    {
      label: '原合同到期日期',
      field: 'originalExpirationDate',
    },
    {
      label: '原是否对公',
      field: 'originalToPublic',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: yesOrNoOptions,
      },
    },
    {
      label: '原签约主体',
      field: 'originalSignSubject',
    },
    {
      label: '原统一社会信用代码',
      field: 'originalSocialCreditCode',
    },
    {
      label: '原业主姓名',
      field: 'originalCustomerName',
      children: (
        <a
          onClick={() => {
            customerIdRef.current = data?.originalCustomerId!;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {data?.originalCustomerName}
        </a>
      ),
    },
    {
      label: '原业主联系电话',
      field: 'originalPhone',
      valueType: ValueType.PHONE,
      fieldProps: {
        sensitiveValue: data?.originalPhoneSensitive,
      },
    },
    {
      label: '原业主身份证号',
      field: 'originalIdentityCard',
      valueType: ValueType.IDENTITY_CARD,
      fieldProps: {
        sensitiveValue: data?.originalIdentityCardSensitive,
      },
    },
    {
      label: '现是否对公',
      field: 'toPublic',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: yesOrNoOptions,
      },
    },
    {
      label: '现签约主体',
      field: 'signSubject',
    },
    {
      label: '现统一社会信用代码',
      field: 'socialCreditCode',
    },
    {
      label: '现业主姓名',
      field: 'customerName',
      children: (
        <a
          onClick={() => {
            customerIdRef.current = data?.customerId!;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {data?.customerName}
        </a>
      ),
    },
    {
      label: '现业主联系电话',
      field: 'phone',
      valueType: ValueType.PHONE,
      fieldProps: {
        sensitiveValue: data?.phoneSensitive,
      },
    },
    {
      label: '现业主身份证号',
      field: 'identityCard',
      valueType: ValueType.IDENTITY_CARD,
      fieldProps: {
        sensitiveValue: data?.identityCardSensitive,
      },
    },
    {
      label: '变更类型',
      field: 'changeType',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(ChangeTypeEnum),
      },
    },
  ];

  return (
    <Drawer
      title="法人变更合同详情"
      width={1200}
      open={open}
      push={false}
      destroyOnHidden
      onClose={() => setOpen(false)}
      {...props}
    >
      <Card loading={loading}>
        <div className="flex justify-between">
          <div className="flex flex-col gap-2">
            <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
            <p>创建时间：{data?.createTime}</p>
            <p>更新人：{users?.find((i) => i.id === data?.updateUserId)?.name}</p>
            <p>更新时间：{data?.updateTime}</p>
          </div>
          <ButtonGroup
            items={[
              {
                label: '编辑',
                show:
                  checkPermission(PermissionsMap.ContractChangeLegalPersonEdit) &&
                  isNotSubmitOrReturn,
                onClick: () => createEditModalRef.current?.open(),
              },
              {
                label: '提交审核',
                show:
                  checkPermission(PermissionsMap.ContractChangeLegalPersonSubmitAudit) &&
                  isNotSubmitOrReturn,
                onClick: handleSubmitAudit,
              },
              {
                label: (_r) => (
                  <ContractFileUpModal
                    getId={() => id}
                    onSuccess={refresh}
                    contractType={ContractEnum.变更法人合同}
                    maxCount={data?.attachments ? 10 - data?.attachments?.length : 10}
                  />
                ),
                show: !data?.attachments || data?.attachments?.length < 10,
              },
              {
                label: '删除',
                show:
                  checkPermission(PermissionsMap.ContractChangeLegalPersonDelete) &&
                  isNotSubmitOrReturn,
                danger: true,
                onClick: () =>
                  modal.confirm({
                    title: '删除',
                    content: '确定要删除该合同吗？',
                    onOk: async () => {
                      await batchDeleteChangeLegalPersonContract([id]);
                      message.success('删除成功');
                      setOpen(false);
                      onDelete?.();
                    },
                  }),
              },
            ]}
          />
        </div>
      </Card>

      <Row gutter={[20, 20]} className="mt-5">
        <Col span={24} xl={16}>
          <Card loading={loading}>
            <Tabs
              className="-mt-5"
              activeKey={activeKey}
              onTabClick={setActiveKey}
              items={[
                {
                  label: '合同详情',
                  key: 'info',
                  children: (
                    <>
                      {renderDescriptions(baseInfoItems, data)}
                      {data?.changeType === ChangeTypeEnum.门店转让 && (
                        <PaymentRecords data={data?.payments} />
                      )}
                    </>
                  ),
                },
                {
                  label: '合同文件',
                  key: 'file',
                  children: (
                    <>
                      <ContractFileList data={data?.attachments || []} onRefresh={refresh} />
                    </>
                  ),
                },
              ]}
            />
          </Card>
        </Col>
        <Col span={24} xl={8}>
          <ContractAuditCard
            ref={contractAuditCardRef}
            id={id}
            data={data}
            loading={loading}
            hidePayments={data?.changeType === ChangeTypeEnum.内部变更}
            contractType={ContractTypeEnum.变更法人合同}
            onTodoSuccess={onTodoSuccess}
            onSuccess={() => {
              refresh();
              onUpdate?.();
            }}
          />
        </Col>
      </Row>

      <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />
      <CreateEditChangeLegalPersonModal
        ref={createEditModalRef}
        getId={() => id}
        onSuccess={() => {
          refresh();
          onUpdate?.();
        }}
      />
      <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => data?.shopId} />
    </Drawer>
  );
});

export default ChangeLegalPersonDetailDrawer;
