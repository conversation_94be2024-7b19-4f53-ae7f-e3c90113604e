import React, { useRef, useState } from 'react';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { Encrypt, ShopSelect } from '@src/components';
import EditableDate from '@src/components/Editable/EditableDate';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import {
  getChangeLegalPersonContractDetail,
  saveAndSubmitChangeLegalPersonContract,
  saveChangeLegalPersonContract,
  updateChangeLegalPersonContract,
} from '@src/services/contract/change-legal-person';
import { ChangeTypeEnum } from '@src/services/contract/change-legal-person/type';
import { getCustomerDetail } from '@src/services/customers';
import { getShopDetail } from '@src/services/shop/list';
import { SignerMainTypeEnum } from '@src/services/shop/list/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Card, Form, Input, Modal, ModalProps, Select } from 'antd';
import PaymentFormList from '../../components/PaymentFormList';

interface CreateEditChangeLegalPersonModalProps extends ModalProps {
  getId: () => number | null;
  onSuccess: () => void;
}

const CreateEditChangeLegalPersonModal = React.forwardRef<
  ModalRef,
  CreateEditChangeLegalPersonModalProps
>(({ getId, onSuccess, ...props }, ref) => {
  const { message } = App.useApp();
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const saveTypeRef = useRef<'save' | 'submit'>('save');

  const id = getId();
  const isEdit = !!id;

  const changeType = Form.useWatch('changeType', form);

  const {
    data: shopDetailData,
    runAsync: getShopDetailData,
    mutate: mutateShopDetailData,
  } = useRequest(getShopDetail, {
    manual: true,
  });
  const {
    data: customer,
    loading: getCustomerLoading,
    runAsync: getCustomer,
    mutate: mutateCustomer,
  } = useRequest(getCustomerDetail, {
    manual: true,
  });
  const { data, loading, mutate } = useRequest(() => getChangeLegalPersonContractDetail(id!), {
    ready: open && isEdit,
    onSuccess: (res) => {
      form.setFieldsValue(res);
      getCustomer(res.customerId);
    },
  });
  const { runAsync: save, loading: saveLoading } = useRequest(saveChangeLegalPersonContract, {
    manual: true,
  });
  const { runAsync: submit, loading: submitLoading } = useRequest(
    saveAndSubmitChangeLegalPersonContract,
    {
      manual: true,
    },
  );
  const { runAsync: update, loading: updateLoading } = useRequest(updateChangeLegalPersonContract, {
    manual: true,
  });

  React.useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true);
      mutate(undefined);
      mutateCustomer(undefined);
      mutateShopDetailData(undefined);
    },
    close: () => setOpen(false),
  }));

  const handleSave = async (values: any) => {
    if (isEdit) {
      await update({ id, ...values });
      message.success('修改成功');
    } else {
      if (saveTypeRef.current === 'save') {
        await save(values);
        message.success('保存成功');
      } else {
        await submit(values);
        message.success('提交审核成功');
      }
    }

    setOpen(false);
    onSuccess();
  };

  const handleShopIdChange = async (shopId: string | undefined) => {
    // 先清除掉这些字段，预防后面请求出问题
    form.resetFields([
      'shopName',
      'shopRealUseArea',
      'realEstateAddress',
      'originalToPublic',
      'originalSignSubject',
      'originalSocialCreditCode',
      'originalCustomerId',
      'originalPhone',
      'originalIdentityCard',
    ]);

    if (!shopId) {
      return;
    }

    const shopDetail = await getShopDetailData(shopId);

    form.setFieldsValue({
      shopName: shopDetail.shopName,
      shopRealUseArea: shopDetail.realUseArea,
      realEstateAddress: shopDetail.propertyAddress,
      originalToPublic: shopDetail.signerMainType === SignerMainTypeEnum.公司,
      originalSignSubject: shopDetail.signerMain,
      originalSocialCreditCode: shopDetail.signerUsci,
      originalCustomerId: shopDetail.customerId,
      originalCustomerName: shopDetail.signerPhone,
      originalPhone: shopDetail.signerPhone,
      originalPhoneSensitive: shopDetail.signerPhoneSensitive,
      originalIdentityCard: shopDetail.signerIdCard,
      originalIdentityCardSensitive: shopDetail.signerIdCardSensitive,
    });
  };

  const shopInfoItems: {
    label: string;
    name: string;
    props?: Record<string, any>;
    component?: React.FunctionComponent<any>;
  }[] = [
    { label: '门店冠名', name: 'shopName' },
    {
      label: '门店实际使用面积',
      name: 'shopRealUseArea',
      component: Input,
      props: {
        addonAfter: 'm²',
      },
    },
    { label: '不动产权证地址', name: 'realEstateAddress' },
  ];

  return (
    <Modal
      title={isEdit ? '编辑法人变更合同' : '新建法人变更合同'}
      open={open}
      width={changeType === ChangeTypeEnum.门店转让 ? 1000 : undefined}
      destroyOnHidden
      loading={loading || getCustomerLoading}
      confirmLoading={saveLoading || submitLoading || updateLoading}
      styles={{
        body: { maxHeight: '60vh', margin: '0 -24px', padding: '0 24px' },
      }}
      okText="保存"
      okButtonProps={{ ghost: !isEdit }}
      modalRender={(node) => (
        <Form
          form={form}
          labelCol={{ span: 9 }}
          scrollToFirstError
          clearOnDestroy
          onFinish={handleSave}
        >
          {node}
        </Form>
      )}
      footer={(_, { OkBtn, CancelBtn }) => (
        <>
          <CancelBtn />
          <OkBtn />
          {!isEdit && (
            <Button
              loading={saveLoading || submitLoading}
              type="primary"
              onClick={() => {
                saveTypeRef.current = 'submit';
                form.submit();
              }}
            >
              提交审核
            </Button>
          )}
        </>
      )}
      onCancel={() => setOpen(false)}
      onOk={() => {
        saveTypeRef.current = 'save';
        form.submit();
      }}
      {...props}
    >
      <div className="flex flex-col sm:flex-row gap-5 overflow-auto h-[60vh]">
        <div className="flex flex-col gap-4 flex-1 sm:overflow-auto sm:pr-5">
          <Card
            title="门店信息"
            classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
          >
            <Form.Item label="合同编号" required>
              <Input disabled placeholder="根据编号规则自动生成" value={data?.code} />
            </Form.Item>
            <Form.Item
              label="门店编号"
              name="shopId"
              rules={[{ required: true, message: '请选择原门店编号' }]}
            >
              <ShopSelect disabled={isEdit} onChange={(value) => handleShopIdChange(value)} />
            </Form.Item>
            {shopInfoItems.map((item) => {
              const Component = item.component || Input.TextArea;

              return (
                <Form.Item
                  key={item.name}
                  label={item.label}
                  name={item.name}
                  rules={[{ required: true, message: '不能为空' }]}
                >
                  <Component
                    disabled
                    placeholder="选择门店后自动填充"
                    {...(item.component
                      ? {}
                      : {
                          autoSize: true,
                        })}
                    {...item.props}
                  />
                </Form.Item>
              );
            })}
            <Form.Item
              label="营业执照主体名称"
              name="licenseSubjectName"
              rules={[{ required: true, message: '请输入营业执照主体名称' }]}
            >
              <Input.TextArea autoSize placeholder="请输入营业执照主体名称" />
            </Form.Item>
          </Card>
          <Card
            title="合同信息"
            classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
          >
            <EditableDate
              editable
              formItemProps={{
                name: 'originalEffectiveDate',
                label: '原合同生效日期',
                rules: [{ required: true, message: '请选择原合同生效日期' }],
              }}
            />
            <EditableDate
              editable
              formItemProps={{
                name: 'originalExpirationDate',
                label: '原合同截止日期',
                rules: [{ required: true, message: '请选择原合同截止日期' }],
              }}
            />
          </Card>
          <Card
            title="原业主信息"
            classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
          >
            <Form.Item
              label="是否对公"
              name="originalToPublic"
              rules={[{ required: true, message: '不能为空' }]}
            >
              <Select disabled options={yesOrNoOptions} placeholder="选择门店后自动填充" />
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prev, next) => prev.originalToPublic !== next.originalToPublic}
            >
              {({ getFieldValue }) =>
                getFieldValue('originalToPublic') && (
                  <>
                    <Form.Item label="签约主体" name="originalSignSubject">
                      <Input disabled placeholder="选择门店后自动填充" />
                    </Form.Item>
                    <Form.Item label="统一社会信用代码" name="originalSocialCreditCode">
                      <Input disabled placeholder="选择门店后自动填充" />
                    </Form.Item>
                  </>
                )
              }
            </Form.Item>
            <Form.Item
              label="原业主姓名"
              name="originalCustomerId"
              rules={[{ required: true, message: '不能为空' }]}
            >
              <RelCustomer
                placeholder="选择门店后自动填充"
                defaultCustomerIdToNameMap={{
                  [data?.originalCustomerId || '']: data?.originalCustomerName,
                  [shopDetailData?.customerId || '']: shopDetailData?.signerName,
                }}
              />
            </Form.Item>
            <Encrypt.PhoneFormItem
              formItemProps={{
                label: '原业主联系电话',
                name: 'originalPhone',
                rules: [{ required: true, message: '不能为空' }],
              }}
              fieldProps={{
                disabled: true,
                placeholder: '选择门店后自动填充',
                encryptSensitiveMap: {
                  [data?.originalPhone || '']: data?.originalPhoneSensitive,
                  [shopDetailData?.signerPhone || '']: shopDetailData?.signerPhoneSensitive,
                },
              }}
            />
            <Encrypt.IdCardFormItem
              formItemProps={{
                label: '原业主身份证号',
                name: 'originalIdentityCard',
                rules: [{ required: true, message: '不能为空' }],
              }}
              fieldProps={{
                disabled: true,
                placeholder: '选择门店后自动填充',
                encryptSensitiveMap: {
                  [data?.originalIdentityCard || '']: data?.originalIdentityCardSensitive,
                  [shopDetailData?.signerIdCard || '']: shopDetailData?.signerIdCardSensitive,
                },
              }}
            />
          </Card>
          <Card
            title="现业主信息"
            classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
          >
            <Form.Item
              name="toPublic"
              label="是否对公"
              rules={[{ required: true, message: '请选择是否对公' }]}
            >
              <Select options={yesOrNoOptions} placeholder="请选择是否对公" />
            </Form.Item>
            <Form.Item noStyle shouldUpdate={(prev, next) => prev.toPublic !== next.toPublic}>
              {({ getFieldValue }) =>
                getFieldValue('toPublic') && (
                  <>
                    <Form.Item
                      label="签约主体"
                      name="signSubject"
                      rules={[{ required: true, message: '请输入统一社会信用代码' }]}
                    >
                      <Input placeholder="请输入签约主体" />
                    </Form.Item>
                    <Form.Item
                      label="统一社会信用代码"
                      name="socialCreditCode"
                      rules={[{ required: true, message: '请输入统一社会信用代码' }]}
                    >
                      <Input placeholder="请输入统一社会信用代码" />
                    </Form.Item>
                  </>
                )
              }
            </Form.Item>
            <Form.Item
              label="现业主姓名"
              name="customerId"
              rules={[{ required: true, message: '请选择现业主' }]}
            >
              <RelCustomer
                editable
                placeholder="请选择现业主"
                defaultCustomerIdToNameMap={{
                  [data?.customerId || '']: data?.customerName,
                }}
                onChange={(_, info) => {
                  mutateCustomer(info);

                  form.setFieldsValue({
                    phone: undefined,
                    identityCard: info?.identityCard,
                  });
                }}
              />
            </Form.Item>
            <Form.Item
              label="现业主联系电话"
              name="phone"
              rules={[{ required: true, message: '请选择现业主联系电话' }]}
            >
              <Encrypt.PhoneSelect
                placeholder="请选择现业主联系电话"
                options={customer?.phones?.map((phone, index) => ({
                  label: customer.phonesSensitive?.[index],
                  value: phone,
                }))}
                encryptSensitiveMap={{
                  [data?.phone || '']: data?.phoneSensitive,
                }}
              />
            </Form.Item>
            <Encrypt.IdCardFormItem
              formItemProps={{
                label: '现业主身份证号码',
                name: 'identityCard',
                rules: [{ required: true, message: '不能为空' }],
              }}
              fieldProps={{
                disabled: true,
                placeholder: '选择现业主后自动填充',
                encryptSensitiveMap: {
                  [customer?.identityCard || '']: customer?.identityCardSensitive,
                  [data?.identityCard || '']: data?.identityCardSensitive,
                },
              }}
            />
          </Card>
          <Card
            title="场景判断"
            classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
          >
            <Form.Item
              label="变更类型"
              name="changeType"
              rules={[{ required: true, message: '请选择变更类型' }]}
            >
              <Select placeholder="请选择变更类型" options={enum2Options(ChangeTypeEnum)} />
            </Form.Item>
          </Card>
        </div>
        {changeType === ChangeTypeEnum.门店转让 && (
          <div className="flex-1 sm:overflow-auto sm:pr-5">
            <PaymentFormList />
          </div>
        )}
      </div>
    </Modal>
  );
});

export default CreateEditChangeLegalPersonModal;
