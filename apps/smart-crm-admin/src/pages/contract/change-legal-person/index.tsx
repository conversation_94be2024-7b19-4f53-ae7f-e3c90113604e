import { useEffect, useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { contractAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ETable, ExportButton, TableActions } from '@src/components';
import { ETableActionType, ETableColumn, ValueType } from '@src/components/ETable';
import useTableViews from '@src/hooks/useTableViews';
import { useNoticeEventSubscription } from '@src/layout';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { AuditStatusEnum, BusinessTypeEnum } from '@src/services/common/type';
import {
  batchDeleteChangeLegalPersonContract,
  exportChangeLegalPersonContract,
  getChangeLegalPersonContractDetail,
  getChangeLegalPersonContractList,
  submitChangeLegalPersonContractAudit,
} from '@src/services/contract/change-legal-person';
import {
  ChangeLegalPersonContractDTO,
  ChangeLegalPersonContractStatusEnum,
  ChangeTypeEnum,
} from '@src/services/contract/change-legal-person/type';
import { ContractNodeEnum } from '@src/services/contract/renewal/type';
import { compatibleTableActionWidth, enum2Options, getParamsFromFilters } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Dropdown, MenuProps, Skeleton } from 'antd';
import { useSearchParams } from 'react-router-dom';
import ChangeLegalPersonDetailDrawer from './components/ChangeLegalPersonDetailDrawer';
import CreateEditChangeLegalPersonModal from './components/CreateEditChangeLegalPersonModal';

const ChangeLegalPerson = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { modal, message } = App.useApp();
  const actionRef = useRef<ETableActionType>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const currentIdRef = useRef<number | null>(null);
  const detailDrawerRef = useRef<ModalRef>(null);
  const shopIdRef = useRef<string | null>(null);
  const shopDetailDrawerRef = useRef<ModalRef>(null);
  const customerIdRef = useRef<number | null>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const createEditModalRef = useRef<ModalRef>(null);

  const checkPermission = usePermission();
  const { views, loading: getViewsLoading } = useTableViews(
    BusinessTypeEnum.CHANGE_LEGAL_PERSON_CONTRACT,
  );
  const { data, runAsync: getList } = useRequest(getChangeLegalPersonContractList, {
    manual: true,
  });

  useNoticeEventSubscription((event) => {
    if (
      event.type === 'change-legal-person' &&
      data?.result.some((i) => i.id === event.payload.id)
    ) {
      actionRef.current?.reload();
    }
  });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      currentIdRef.current = Number(id);
      detailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSubmitAudit = async (id: number) => {
    const detail = await getChangeLegalPersonContractDetail(id);

    modal.confirm({
      title: '提交审核',
      content:
        Array.isArray(detail.payments) && detail.payments.length > 0
          ? `累计打款金额：${(
              detail.payments.reduce((total, cur) => total + cur.paymentAmount, 0) || 0
            ).toFixed(2)} 元，是否提交合同审核？`
          : '确定要提交审核吗？',
      onOk: async () => {
        await submitChangeLegalPersonContractAudit(id);
        message.success('提交成功');
        actionRef.current?.reload();
      },
    });
  };

  const columns: ETableColumn<ChangeLegalPersonContractDTO>[] = [
    {
      title: '合同编号',
      dataIndex: 'code',
      fixed: 'left',
      valueType: ValueType.TEXT,
      render: (value, { id }) => (
        <a
          onClick={() => {
            currentIdRef.current = id;
            detailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '门店编号',
      dataIndex: 'shopId',
      valueType: ValueType.TEXT,
      render: (value) => (
        <a
          onClick={() => {
            shopIdRef.current = value;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '门店冠名',
      dataIndex: 'shopName',
      valueType: ValueType.TEXT,
    },
    {
      title: '合同节点',
      dataIndex: 'node',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(ContractNodeEnum),
      },
      filterProps: {
        options: enum2Options(ContractNodeEnum),
      },
    },
    {
      title: '合同状态',
      dataIndex: 'status',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(ChangeLegalPersonContractStatusEnum),
      },
      filterProps: {
        options: enum2Options(ChangeLegalPersonContractStatusEnum),
      },
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: contractAuditStatusOptions,
      },
      filterProps: {
        options: contractAuditStatusOptions,
      },
    },
    {
      title: '营业执照主体名称',
      dataIndex: 'licenseSubjectName',
      valueType: ValueType.TEXT,
    },
    {
      title: '门店实际使用面积',
      dataIndex: 'shopRealUseArea',
      valueType: ValueType.NUMBER,
      render: (value) => `${value} m²`,
    },
    {
      title: '不动产权证地址',
      dataIndex: 'realEstateAddress',
      valueType: ValueType.TEXT,
    },
    {
      title: '原合同生效日期',
      dataIndex: 'originalEffectiveDate',
      valueType: ValueType.DATE,
    },
    {
      title: '原合同到期日期',
      dataIndex: 'originalExpirationDate',
      valueType: ValueType.DATE,
    },
    {
      title: '原是否对公',
      dataIndex: 'originalToPublic',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: yesOrNoOptions,
      },
      filterProps: {
        options: [
          // 后端这里不接受 Boolean
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
      },
    },
    {
      title: '原签约主体',
      dataIndex: 'originalSignSubject',
      valueType: ValueType.TEXT,
    },
    {
      title: '原统一社会信用代码',
      dataIndex: 'originalSocialCreditCode',
      valueType: ValueType.TEXT,
    },
    {
      title: '原业主姓名',
      dataIndex: 'originalCustomerName',
      valueType: ValueType.TEXT,
      render: (value, { originalCustomerId }) => (
        <a
          onClick={() => {
            customerIdRef.current = originalCustomerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '原业主联系方式',
      dataIndex: 'originalPhone',
      valueType: ValueType.PHONE,
      fieldProps: (_, { originalPhoneSensitive }) => ({
        sensitiveValue: originalPhoneSensitive,
      }),
    },
    {
      title: '原业主身份证号',
      dataIndex: 'originalIdentityCard',
      width: 200,
      valueType: ValueType.IDENTITY_CARD,
      fieldProps: (_, { originalIdentityCardSensitive }) => ({
        sensitiveValue: originalIdentityCardSensitive,
      }),
    },
    {
      title: '现是否对公',
      dataIndex: 'toPublic',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: yesOrNoOptions,
      },
      filterProps: {
        options: [
          // 后端这里不接受 Boolean
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
      },
    },
    {
      title: '现签约主体',
      dataIndex: 'signSubject',
      valueType: ValueType.TEXT,
    },
    {
      title: '现统一社会信用代码',
      dataIndex: 'socialCreditCode',
      valueType: ValueType.TEXT,
    },
    {
      title: '现业主姓名',
      dataIndex: 'customerName',
      valueType: ValueType.TEXT,
      render: (value, { customerId }) => (
        <a
          onClick={() => {
            customerIdRef.current = customerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '现业主联系电话',
      dataIndex: 'phone',
      valueType: ValueType.PHONE,
      fieldProps: (_, { phoneSensitive }) => ({
        sensitiveValue: phoneSensitive,
      }),
    },
    {
      title: '现业主身份证号码',
      dataIndex: 'identityCard',
      width: 200,
      valueType: ValueType.IDENTITY_CARD,
      fieldProps: (_, { identityCardSensitive }) => ({
        sensitiveValue: identityCardSensitive,
      }),
    },
    {
      title: '变更类型',
      dataIndex: 'changeType',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(ChangeTypeEnum),
      },
      filterProps: {
        options: enum2Options(ChangeTypeEnum),
      },
    },
    {
      title: '更新人',
      dataIndex: 'updateUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: ValueType.DATE_TIME,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: ValueType.DATE_TIME,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      hideInFilters: true,
      hideInSettings: true,
      width: compatibleTableActionWidth(120),
      render: (_, { id, auditStatus }) => {
        // 是否是未提交或驳回
        const isNotSubmitOrReturn = [AuditStatusEnum.NOT_SUBMIT, AuditStatusEnum.RETURN].includes(
          auditStatus,
        );

        return (
          <TableActions
            shortcuts={[
              {
                label: '编辑',
                show:
                  checkPermission(PermissionsMap.ContractChangeLegalPersonEdit) &&
                  isNotSubmitOrReturn,
                onClick: () => {
                  currentIdRef.current = id;
                  createEditModalRef.current?.open();
                },
              },
            ]}
            moreItems={[
              {
                label: '提交审核',
                show:
                  checkPermission(PermissionsMap.ContractChangeLegalPersonSubmitAudit) &&
                  isNotSubmitOrReturn,
                onClick: () => handleSubmitAudit(id),
              },
              {
                label: '删除',
                danger: true,
                show:
                  checkPermission(PermissionsMap.ContractChangeLegalPersonDelete) &&
                  isNotSubmitOrReturn,
                onClick: () =>
                  modal.confirm({
                    title: '删除',
                    content: '确定要删除该合同吗？',
                    onOk: async () => {
                      await batchDeleteChangeLegalPersonContract([id]);
                      message.success('删除成功');
                      setSelectedRowKeys((prev) => prev.filter((i) => i !== id));
                      actionRef.current?.reload();
                    },
                  }),
              },
            ]}
          />
        );
      },
    },
  ];

  const batchMenuItems: MenuProps['items'] = [
    {
      label: '删除',
      key: 'delete',
      danger: true,
      auth: PermissionsMap.ContractChangeLegalPersonDelete,
      onClick: () =>
        modal.confirm({
          title: '删除',
          content: `确定要删除选中的 ${selectedRowKeys.length} 个合同吗？`,
          onOk: async () => {
            await batchDeleteChangeLegalPersonContract(selectedRowKeys);
            message.success('删除成功');
            setSelectedRowKeys([]);
            actionRef.current?.reload();
          },
        }),
    },
  ].filter((i) => checkPermission(i.auth));

  const headerNode = (
    <div className="sm:flex justify-between mb-3">
      <div className="self-end mb-2 sm:mb-0 pl-2">
        共 {data?.total || 0} 个合同
        {selectedRowKeys.length > 0 && (
          <>
            ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 个
            <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
              清空选择
            </a>
          </>
        )}
      </div>
      <div className="flex justify-end gap-2">
        {batchMenuItems.length > 0 && (
          <Dropdown
            disabled={!selectedRowKeys.length}
            menu={{
              items: batchMenuItems,
            }}
          >
            <Button type="text">
              批量操作 <CaretDownOutlined />
            </Button>
          </Dropdown>
        )}
        <Permission value={PermissionsMap.ContractChangeLegalPersonCreate}>
          <Button
            type="text"
            onClick={() => {
              currentIdRef.current = null;
              createEditModalRef.current?.open();
            }}
          >
            新建
          </Button>
        </Permission>
        <Permission value={PermissionsMap.ContractChangeLegalPersonExport}>
          <ExportButton
            total={selectedRowKeys.length || data?.total}
            request={() =>
              exportChangeLegalPersonContract({
                columns: actionRef.current!.getShowFields(),
                ...(selectedRowKeys.length
                  ? { ids: selectedRowKeys }
                  : getParamsFromFilters(actionRef.current?.getFilters())),
              })
            }
          />
        </Permission>
      </div>
    </div>
  );

  return (
    <>
      <PageContainer
        loading={getViewsLoading && <Skeleton paragraph={{ rows: 15 }} className="px-10" />}
      >
        <ETable
          rowKey="id"
          actionRef={actionRef}
          sticky
          bordered
          size="small"
          header={headerNode}
          views={views}
          pagination={{ showQuickJumper: true, showSizeChanger: true }}
          columns={columns}
          rowSelection={{
            fixed: true,
            preserveSelectedRowKeys: true,
            selectedRowKeys,
            onChange: (keys) => setSelectedRowKeys(keys as number[]),
          }}
          request={async ({ current, pageSize }, filters) => {
            const res = await getList({
              pageNum: current,
              pageSize,
              ...getParamsFromFilters(filters),
            });

            return {
              data: res.result,
              total: res.total,
            };
          }}
        />
        <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => shopIdRef.current} />
        <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />
        <CreateEditChangeLegalPersonModal
          ref={createEditModalRef}
          getId={() => currentIdRef.current}
          onSuccess={() => actionRef.current?.reload()}
        />
      </PageContainer>
      <ChangeLegalPersonDetailDrawer
        ref={detailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
        onDelete={() => actionRef.current?.reload()}
      />
    </>
  );
};

export default ChangeLegalPerson;
