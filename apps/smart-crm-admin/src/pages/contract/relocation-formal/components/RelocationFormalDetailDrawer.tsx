import React, { useRef, useState } from 'react';
import { contractAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { ButtonGroup } from '@src/components';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { PermissionsMap, usePermission } from '@src/permissions';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { AuditStatusEnum } from '@src/services/common/type';
import { ContractEnum } from '@src/services/contract';
import { DevelopTagEnum, StoreCategoryEnum } from '@src/services/contract/formal/type';
import {
  batchDeleteRelocationFormal,
  getRelocationFormalDetail,
  submitRelocationFormalAudit,
} from '@src/services/contract/relocation-formal';
import {
  RelocationFormalDTO,
  RelocationFormalStatusEnum,
} from '@src/services/contract/relocation-formal/type';
import { ContractNodeEnum } from '@src/services/contract/renewal/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Card, Col, Drawer, DrawerProps, Row, Tabs } from 'antd';
import CreateEditRelocationFormalModal from './CreateEditRelocationFormalModal';
import ContractAuditCard, { ContractAuditCardRef } from '../../components/ContractAuditCard';
import ContractFileList from '../../components/ContractFileList';
import ContractFileUpModal from '../../components/ContractFileUpModal';
import PaymentRecords from '../../components/PaymentRecords';
import RelocationIntentionDetailDrawer from '../../relocation-intention/components/RelocationIntentionDetailDrawer';

interface RelocationFormalDetailDrawerProps extends DrawerProps {
  getId: () => number | null;
  onUpdate?: () => void;
  onDelete?: () => void;
  onTodoSuccess?: () => void;
}

const RelocationFormalDetailDrawer = React.forwardRef<ModalRef, RelocationFormalDetailDrawerProps>(
  ({ getId, onUpdate, onDelete, onTodoSuccess, ...props }, ref) => {
    const { modal, message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [activeKey, setActiveKey] = useState('info');
    const customerDetailDrawerRef = useRef<ModalRef>(null);
    const createEditModalRef = useRef<ModalRef>(null);
    const shopDetailDrawerRef = useRef<ModalRef>(null);
    const relocationIntentionDetailDrawerRef = useRef<ModalRef>(null);
    const contractAuditCardRef = useRef<ContractAuditCardRef>(null);

    const id = getId()!;

    const checkPermission = usePermission();
    const { data: users } = useAllUsers();
    const { data, loading, refresh } = useRequest(() => getRelocationFormalDetail(id), {
      ready: open,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        setActiveKey('info');
      },
      close: () => setOpen(false),
    }));

    // 是否是未提交或驳回
    const isNotSubmitOrReturn = [AuditStatusEnum.NOT_SUBMIT, AuditStatusEnum.RETURN].includes(
      data?.auditStatus!,
    );

    const handleSubmitAudit = async () => {
      const amount = data?.payments?.reduce((total, cur) => total + cur.paymentAmount, 0) || 0;

      modal.confirm({
        title: '提交审核',
        content: `累计打款金额：${amount.toFixed(2)} 元，是否提交合同审核？`,
        onOk: async () => {
          await submitRelocationFormalAudit(id);
          message.success('提交成功');
          refresh();
          contractAuditCardRef.current?.refresh();
          onUpdate?.();
        },
      });
    };

    const baseInfoItems: DescriptionFieldType<RelocationFormalDTO>[] = [
      {
        label: '合同编号',
        field: 'code',
      },
      {
        label: '门店性质',
        field: 'storeCategory',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(StoreCategoryEnum),
        },
      },
      {
        label: '合同节点',
        field: 'node',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(ContractNodeEnum),
        },
      },
      {
        label: '合同状态',
        field: 'status',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(RelocationFormalStatusEnum),
        },
      },
      {
        label: '审核状态',
        field: 'auditStatus',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: contractAuditStatusOptions,
        },
      },
      {
        label: '原门店编码',
        field: 'originalShopId',
        children: <a onClick={() => shopDetailDrawerRef.current?.open()}>{data?.originalShopId}</a>,
      },
      {
        label: '原门店冠名',
        field: 'originalShopName',
      },
      {
        label: '品牌授权截止日期',
        field: 'brandAuthEndDate',
      },
      {
        label: '负责人',
        field: 'directUserId',
        valueType: ValueType.PERSON,
      },
      {
        label: '搬迁意向合同',
        field: 'intentionCode',
        valueType: ValueType.SINGLE,
        children: (
          <a onClick={() => relocationIntentionDetailDrawerRef.current?.open()}>
            {data?.intentionCode}
          </a>
        ),
      },
      {
        label: '所属招商顾问',
        field: 'consultantUserId',
        valueType: ValueType.PERSON,
      },
      {
        label: '签约人',
        field: 'signer',
      },
      {
        label: '联系电话',
        field: 'signerPhone',
        valueType: ValueType.PHONE,
        fieldProps: {
          sensitiveValue: data?.signerPhoneSensitive,
        },
      },
      {
        label: '身份证号',
        field: 'signerIdentityCard',
        valueType: ValueType.IDENTITY_CARD,
        fieldProps: {
          sensitiveValue: data?.signerIdentityCardSensitive,
        },
      },
      {
        label: '是否对公',
        field: 'toPublic',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        label: '签约主体',
        field: 'signSubject',
      },
      {
        label: '统一社会信用代码',
        field: 'socialCreditCode',
      },
      {
        label: '正式打款日期',
        field: 'paymentDate',
      },
      {
        label: '正式生效日期',
        field: 'effectiveDate',
      },
      {
        label: '正式到期日期',
        field: 'expirationDate',
      },
      {
        field: 'storeDeliveryDate',
        label: '门店交付日期',
      },
      {
        field: 'siteNo',
        label: '铺位编号',
      },
      {
        field: 'storeCode',
        label: '门店编号',
      },
      {
        field: 'storeName',
        label: '门店冠名',
      },
      {
        field: 'storeCoordinates',
        label: '门店定位',
        children:
          data?.storeCoordinates &&
          `${data.storeCoordinates.name}（经度：${data.storeCoordinates.longitude}；纬度：${data.storeCoordinates.latitude}）`,
      },
      {
        field: 'storeAddress',
        label: '门店地址',
      },
      {
        label: '所属省市区',
        field: 'region',
        valueType: ValueType.REGION,
        fieldProps: {
          regionLevel: 4,
        },
      },
      {
        label: '所属大区',
        field: 'belongWarZone',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(BelongWarZoneEnum),
        },
      },
      {
        field: 'developTag',
        label: '开发标签',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(DevelopTagEnum),
        },
      },
      {
        field: 'siteFlowId',
        label: '选址报告流程 id',
      },
    ];

    return (
      <Drawer
        title="搬迁正式合同详情"
        width={1200}
        open={open}
        push={false}
        destroyOnHidden
        onClose={() => setOpen(false)}
        {...props}
      >
        <Card loading={loading}>
          <div className="flex justify-between">
            <div className="flex flex-col gap-2">
              <p>
                客户名称：
                <a onClick={() => customerDetailDrawerRef.current?.open()}>{data?.customerName}</a>
              </p>
              <p>客户编号：{data?.customerCode}</p>
              <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
              <p>创建时间：{data?.createTime}</p>
              <p>更新人：{users?.find((i) => i.id === data?.updateUserId)?.name}</p>
              <p>更新时间：{data?.updateTime}</p>
            </div>
            <ButtonGroup
              items={[
                {
                  label: '编辑',
                  show:
                    checkPermission(PermissionsMap.ContractRelocationFormalEdit) &&
                    isNotSubmitOrReturn,
                  onClick: () => createEditModalRef.current?.open(),
                },
                {
                  label: '提交审核',
                  show:
                    checkPermission(PermissionsMap.ContractRelocationFormalSubmitAudit) &&
                    isNotSubmitOrReturn,
                  onClick: handleSubmitAudit,
                },
                {
                  label: (_r) => (
                    <ContractFileUpModal
                      getId={() => id}
                      onSuccess={refresh}
                      contractType={ContractEnum.正式搬迁合同}
                      maxCount={data?.attachments ? 10 - data?.attachments?.length : 10}
                    />
                  ),
                  show: !data?.attachments || data?.attachments?.length < 10,
                },
                {
                  label: '删除',
                  show:
                    checkPermission(PermissionsMap.ContractRelocationFormalDelete) &&
                    isNotSubmitOrReturn,
                  danger: true,
                  onClick: () =>
                    modal.confirm({
                      title: '删除',
                      content: '确定要删除该合同吗？',
                      onOk: async () => {
                        await batchDeleteRelocationFormal([id]);
                        message.success('删除成功');
                        setOpen(false);
                        onDelete?.();
                      },
                    }),
                },
              ]}
            />
          </div>
        </Card>

        <Row gutter={[20, 20]} className="mt-5">
          <Col span={24} xl={16}>
            <Card loading={loading}>
              <Tabs
                className="-mt-5"
                activeKey={activeKey}
                onTabClick={setActiveKey}
                items={[
                  {
                    label: '合同详情',
                    key: 'info',
                    children: (
                      <>
                        {renderDescriptions(baseInfoItems, data)}
                        <PaymentRecords data={data?.payments} />
                      </>
                    ),
                  },
                  {
                    label: '合同文件',
                    key: 'file',
                    children: (
                      <>
                        <ContractFileList data={data?.attachments || []} onRefresh={refresh} />
                      </>
                    ),
                  },
                ]}
              />
            </Card>
          </Col>
          <Col span={24} xl={8}>
            <ContractAuditCard
              ref={contractAuditCardRef}
              id={id}
              data={data}
              loading={loading}
              contractType={ContractTypeEnum.搬迁正式合同}
              onTodoSuccess={onTodoSuccess}
              onSuccess={() => {
                refresh();
                onUpdate?.();
              }}
            />
          </Col>
        </Row>

        <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => data?.customerId} />
        <CreateEditRelocationFormalModal
          ref={createEditModalRef}
          getId={() => id}
          onSuccess={() => {
            refresh();
            onUpdate?.();
          }}
        />
        <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => data?.originalShopId} />
        <RelocationIntentionDetailDrawer
          ref={relocationIntentionDetailDrawerRef}
          getId={() => data?.intentionId}
        />
      </Drawer>
    );
  },
);

export default RelocationFormalDetailDrawer;
