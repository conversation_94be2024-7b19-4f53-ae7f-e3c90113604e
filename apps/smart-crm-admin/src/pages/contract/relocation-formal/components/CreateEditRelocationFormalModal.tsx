import React, { useRef, useState } from 'react';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import {
  ChooseMapPoints,
  Encrypt,
  ShopSelect,
  TrainingPersonsSelect,
  UserSelect,
} from '@src/components';
import CheckCodeModal from '@src/components/CheckCodeModal';
import EditableDate from '@src/components/Editable/EditableDate';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useDistrictsStreetOpened from '@src/hooks/useDistrictsStreetOpened';
import useProvinceWarZoneMap from '@src/hooks/useProvinceWarZoneMap';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { getAreaCodeByLatLng, getSiteInfoBySiteNo } from '@src/services/common';
import { checkContractStoreCategory } from '@src/services/contract/formal';
import { DevelopTagEnum, StoreCategoryEnum } from '@src/services/contract/formal/type';
import {
  getRelocationFormalDetail,
  saveAndSubmitRelocationFormal,
  saveRelocationFormal,
  updateRelocationFormal,
} from '@src/services/contract/relocation-formal';
import { getRelocationIntentionByShopId } from '@src/services/contract/relocation-intention';
import { getCustomerDetail } from '@src/services/customers';
import { IdentityTypeEnum } from '@src/services/mini-program/identity/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import { JoinRegionEnum } from '@src/services/regional/quota/type';
import { getShopDetail } from '@src/services/shop/list';
import { ShopListItemDTO } from '@src/services/shop/list/type';
import { findSystemConfig } from '@src/services/standard-settings/business-parameter';
import { ConfigKeyEnum } from '@src/services/standard-settings/business-parameter/type';
import useUserStore from '@src/store/useUserStore';
import { enum2Options, enum2ValueEnum } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Card, Form, Input, Modal, ModalProps, Select } from 'antd';
import dayjs from 'dayjs';
import PaymentFormList from '../../components/PaymentFormList';

const { Search } = Input;

interface CreateEditRelocationFormalModalProps extends ModalProps {
  getId: () => number | null;
  onSuccess: () => void;
}

const CreateEditRelocationFormalModal = React.forwardRef<
  ModalRef,
  CreateEditRelocationFormalModalProps
>(({ getId, onSuccess, ...props }, ref) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [checkOpen, setCheckOpen] = useState(false);
  const saveTypeRef = useRef<'save' | 'submit'>('save');

  const id = getId();
  const isEdit = !!id;

  const {
    user: { shUserId },
  } = useUserStore();
  const {
    data: customer,
    loading: getCustomerLoading,
    runAsync: getCustomer,
    mutate: mutateCustomer,
  } = useRequest(getCustomerDetail, {
    manual: true,
  });
  const {
    data: relocationIntentionList,
    loading: getRelocationIntentionListLoading,
    runAsync: getRelocationIntentionList,
    mutate: mutateRelocationIntentionList,
  } = useRequest(getRelocationIntentionByShopId, {
    manual: true,
  });
  const { transformDistricts, getStreetOpenedRegionIdsLoading } = useDistrictsStreetOpened({
    ready: open,
  });
  const { data, loading, mutate } = useRequest(() => getRelocationFormalDetail(id!), {
    ready: open && isEdit,
    onSuccess: (res) => {
      form.setFieldsValue(res);

      getRelocationIntentionList(res.originalShopId);
      getCustomer(res.customerId);
    },
  });
  const { runAsync: save, loading: saveLoading } = useRequest(saveRelocationFormal, {
    manual: true,
  });
  const { runAsync: submit, loading: submitLoading } = useRequest(saveAndSubmitRelocationFormal, {
    manual: true,
  });
  const { runAsync: update, loading: updateLoading } = useRequest(updateRelocationFormal, {
    manual: true,
  });
  const provinceWarZoneMap = useProvinceWarZoneMap({ ready: open });

  const { run: searchSiteInfo } = useRequest(getSiteInfoBySiteNo, {
    manual: true,
    onSuccess: (res) => {
      if (res) {
        setAreaCode({ latitude: res.latitude, longitude: res.longitude });
        form.setFieldsValue({
          storeCoordinates: {
            latitude: res.latitude,
            longitude: res.longitude,
            name: res.address,
          },
          storeDeliveryDate: res.deliveryDate
            ? dayjs(res.deliveryDate).format('YYYY-MM-DD')
            : undefined,
          storeName: res.shopName,
          // storeAddress: res.leaseContractAddress,
        });
      }
    },
  });

  const { run: setAreaCode } = useRequest(getAreaCodeByLatLng, {
    manual: true,
    onSuccess: (res) => {
      if (res) {
        form.setFieldsValue({
          belongWarZone: provinceWarZoneMap[res.provinceCode],
          streetCode: res.streetCode,
          region: [res.provinceCode, res.cityCode, res.regionCode].join('/'),
        });
      }
    },
  });

  const { data: trainingPersonConfig, loading: trainingPersonConfigLoading } = useRequest(
    async () => {
      const res = await findSystemConfig({
        configKey: ConfigKeyEnum.TRAINING_PERSON_SWITCH,
      });

      return (res?.configValue as { switch: boolean })?.switch;
    },
  );

  const {
    data: checkStoreCategoryData,
    loading: checkStoreCategoryLoading,
    runAsync: runCheckStoreCategory,
    mutate: mutateCheckStoreCategoryData,
  } = useRequest(checkContractStoreCategory, { manual: true });

  React.useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true);
      mutate(undefined);
      mutateRelocationIntentionList(undefined);
      mutateCustomer(undefined);
      mutateCheckStoreCategoryData(undefined);
    },
    close: () => setOpen(false),
  }));

  const handleSave = async (values: any) => {
    if (isEdit) {
      await update({ id, ...values });
      message.success('修改成功');
    } else {
      if (saveTypeRef.current === 'save') {
        await save(values);
        message.success('保存成功');
      } else {
        await submit(values);
        message.success('提交审核成功');
      }
    }

    setOpen(false);
    onSuccess();
  };

  const handleFinish = async (values: any) => {
    const { customerId, originalShopId, region, storeCategory } = values;
    const res = await runCheckStoreCategory({
      customerId,
      contractType: ContractTypeEnum.搬迁正式合同,
      relevanceCode: originalShopId,
      regionCode: region,
      storeCategory,
    });

    if (res.success) {
      handleSave(values);
    } else {
      setCheckOpen(true);
    }
  };

  const relocationIntentionOptions = [
    // 编辑时把原来的加上
    ...(isEdit && data?.intentionCode && data.intentionId
      ? [{ code: data.intentionCode, id: data.intentionId }]
      : []),
    ...(relocationIntentionList || []),
  ];

  return (
    <>
      <Modal
        open={open}
        width={1000}
        title={isEdit ? '编辑搬迁正式合同' : '新建搬迁正式合同'}
        loading={
          loading ||
          getCustomerLoading ||
          getStreetOpenedRegionIdsLoading ||
          trainingPersonConfigLoading ||
          checkStoreCategoryLoading
        }
        destroyOnHidden
        confirmLoading={saveLoading || updateLoading || submitLoading}
        styles={{
          body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
        }}
        modalRender={(node) => (
          <Form
            form={form}
            clearOnDestroy
            scrollToFirstError
            labelCol={{ span: 9 }}
            onFinish={handleFinish}
          >
            {node}
          </Form>
        )}
        okText="保存"
        okButtonProps={{ ghost: !isEdit }}
        footer={(_, { OkBtn, CancelBtn }) => (
          <>
            <CancelBtn />
            <OkBtn />
            {!isEdit && (
              <Button
                loading={saveLoading || submitLoading}
                type="primary"
                onClick={() => {
                  saveTypeRef.current = 'submit';
                  form.submit();
                }}
              >
                提交审核
              </Button>
            )}
          </>
        )}
        onOk={() => {
          saveTypeRef.current = 'save';
          form.submit();
        }}
        onCancel={() => setOpen(false)}
        {...props}
      >
        <div className="flex flex-col sm:flex-row gap-5 overflow-auto h-[60vh]">
          <div className="flex flex-col gap-4 flex-1 sm:overflow-auto sm:pr-5">
            <Card
              title="原门店信息"
              classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
            >
              <Form.Item
                label="原门店编号"
                name="originalShopId"
                rules={[{ required: true, message: '请选择原门店编号' }]}
              >
                <ShopSelect
                  disabled={isEdit}
                  onChange={(shopId, option) => {
                    mutateRelocationIntentionList(undefined);
                    getRelocationIntentionList(shopId);
                    form.setFieldsValue({
                      originalShopName: (option as ShopListItemDTO | undefined)?.shopName,
                      brandAuthEndDate: undefined,
                      intentionId: undefined,
                    });
                    getShopDetail(shopId).then((res) => {
                      form.setFieldValue('brandAuthEndDate', res.brandAuthEndDate);
                    });
                  }}
                />
              </Form.Item>
              <Form.Item
                label="原门店冠名"
                name="originalShopName"
                rules={[{ required: true, message: '不能为空' }]}
              >
                <Input placeholder="选择门店后自动填充" disabled />
              </Form.Item>
              <Form.Item
                label="品牌授权截止日期"
                name="brandAuthEndDate"
                rules={[{ required: true, message: '不能为空' }]}
              >
                <Input placeholder="选择门店后自动填充" disabled />
              </Form.Item>
            </Card>
            <Card
              title="加盟信息"
              classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
            >
              <Form.Item
                label="客户名称"
                name="customerId"
                rules={[{ required: true, message: '请选择客户' }]}
              >
                <RelCustomer
                  editable
                  defaultCustomerIdToNameMap={{
                    [data?.customerId || '']: data?.customerName,
                  }}
                  onChange={(_, info) => {
                    mutateCustomer(info);
                    form.setFieldsValue({
                      signer: info?.name,
                      signerPhone: undefined,
                      signerIdentityCard: info?.identityCard,
                      storeCategory: info?.customerNature,
                    });
                  }}
                />
              </Form.Item>
              <Form.Item
                label="门店性质"
                name="storeCategory"
                rules={[{ required: true, message: '请选择门店性质' }]}
              >
                <Select
                  disabled={isEdit}
                  placeholder="请选择门店性质"
                  options={enum2Options(StoreCategoryEnum)}
                />
              </Form.Item>
              <Form.Item label="合同编号" required>
                <Input disabled placeholder="根据规则自动生成" value={data?.code} />
              </Form.Item>
              <Form.Item
                label="负责人"
                name="directUserId"
                initialValue={shUserId}
                rules={[{ required: true, message: '请选择负责人' }]}
              >
                <UserSelect placeholder="请选择负责人" />
              </Form.Item>
              <Form.Item
                name="intentionId"
                label="搬迁意向合同"
                rules={[
                  {
                    required: relocationIntentionOptions.length > 0,
                    message: '请选择搬迁意向合同',
                  },
                ]}
              >
                <Select
                  allowClear
                  placeholder="请选择搬迁意向合同"
                  loading={getRelocationIntentionListLoading}
                  options={relocationIntentionOptions}
                  fieldNames={{ label: 'code', value: 'id' }}
                />
              </Form.Item>
            </Card>
            <Card
              title="合同签约信息"
              classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
            >
              <Form.Item
                label="所属招商顾问"
                name="consultantUserId"
                rules={[{ required: true, message: '请选择所属招商顾问' }]}
              >
                <UserSelect
                  placeholder="请选择所属招商顾问"
                  transformOptions={(options) =>
                    options.filter((i) =>
                      [IdentityTypeEnum.招商顾问, IdentityTypeEnum.大客户招商].includes(
                        i.identityType,
                      ),
                    )
                  }
                />
              </Form.Item>
              <Form.Item
                label="签约人"
                name="signer"
                rules={[{ required: true, message: '不能为空' }]}
              >
                <Input placeholder="选择客户后自动填充" disabled />
              </Form.Item>
              <Form.Item
                label="联系电话"
                name="signerPhone"
                rules={[{ required: true, message: '请选择联系电话' }]}
              >
                <Encrypt.PhoneSelect
                  placeholder="请选择手机号"
                  options={customer?.phones?.map((phone, index) => ({
                    label: customer.phonesSensitive?.[index],
                    value: phone,
                  }))}
                  encryptSensitiveMap={{
                    [data?.signerPhone || '']: data?.signerPhoneSensitive,
                  }}
                />
              </Form.Item>
              <Encrypt.IdCardFormItem
                formItemProps={{
                  label: '身份证号码',
                  name: 'signerIdentityCard',
                  rules: [{ required: true, message: '请输入身份证号码' }],
                }}
                fieldProps={{
                  disabled: true,
                  placeholder: '选择客户后自动填充',
                  encryptSensitiveMap: {
                    [customer?.identityCard || '']: customer?.identityCardSensitive,
                    [data?.signerIdentityCard || '']: data?.signerIdentityCardSensitive,
                  },
                }}
              />
            </Card>
            {!!trainingPersonConfig && (
              <Card
                title="加盟信息"
                classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
              >
                <TrainingPersonsSelect editable={!isEdit} />
              </Card>
            )}
            <Card
              title="场景判断"
              classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
            >
              <Form.Item
                name="toPublic"
                label="是否对公"
                initialValue={false}
                rules={[{ required: true, message: '请选择是否对公' }]}
              >
                <Select options={yesOrNoOptions} placeholder="请选择是否对公" />
              </Form.Item>
              <Form.Item noStyle dependencies={['toPublic']}>
                {({ getFieldValue }) =>
                  getFieldValue('toPublic') && (
                    <>
                      <Form.Item
                        label="签约主体"
                        name="signSubject"
                        rules={[{ required: true, message: '请输入签约主体' }]}
                      >
                        <Input placeholder="请输入签约主体" />
                      </Form.Item>
                      <Form.Item
                        label="统一社会信用代码"
                        name="socialCreditCode"
                        rules={[{ required: true, message: '请输入统一社会信用代码' }]}
                      >
                        <Input placeholder="请输入统一社会信用代码" />
                      </Form.Item>
                    </>
                  )
                }
              </Form.Item>
            </Card>
            <Card
              title="门店信息"
              classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
            >
              <Form.Item
                label="铺位编号"
                name="siteNo"
                rules={[{ required: true, message: '请输入铺位编号' }]}
              >
                {isEdit ? (
                  <Input placeholder="请输入铺位编号" />
                ) : (
                  <Search placeholder="根据铺位编号，自动匹配门店信息" onSearch={searchSiteInfo} />
                )}
              </Form.Item>
              <EditableDate
                editable
                formItemProps={{
                  label: '门店交付日期',
                  name: 'storeDeliveryDate',
                  rules: [{ required: true, message: '请选择门店交付日期' }],
                }}
              />
              <Form.Item label="门店编号" required>
                <Input disabled placeholder="根据编号规则自动生成" value={data?.storeCode} />
              </Form.Item>
              <Form.Item
                label="门店冠名"
                name="storeName"
                rules={[{ required: true, message: '请输入门店冠名' }]}
              >
                <Input placeholder="请输入门店冠名" />
              </Form.Item>
              <Form.Item
                label="门店定位"
                name="storeCoordinates"
                getValueProps={(value) => ({
                  value: value ? [value] : undefined,
                })}
                normalize={(value) => (Array.isArray(value) ? value[0] : undefined)}
                rules={[{ required: !isEdit, message: '请选择门店定位' }]}
              >
                <ChooseMapPoints
                  multiple={false}
                  onChange={(e) => {
                    if (e?.length) {
                      setAreaCode({ latitude: e[0].latitude, longitude: e[0].longitude });
                    }
                  }}
                />
              </Form.Item>
              <Form.Item
                label="门店地址"
                name="storeAddress"
                rules={[{ required: true, message: '请输入门店地址' }]}
              >
                <Input.TextArea autoSize placeholder="请输入门店地址" />
              </Form.Item>
              <EditableRegion
                editable
                formItemProps={{
                  label: '所属省市区',
                  name: 'region',
                  rules: [{ required: true, message: '请选择所属省市区' }],
                }}
                fieldProps={{
                  regionLevel: 4,
                  transformDistricts,
                  placeholder: '请选择所属省市区',
                  onChange: (value: any) => {
                    const streetCode = value?.[3];

                    if (streetCode) {
                      form.setFieldValue('streetCode', streetCode);
                    }

                    form.setFieldValue('belongWarZone', provinceWarZoneMap[value?.[0]]);
                  },
                }}
              />
              <Form.Item hidden={true} label="所属乡镇" name="streetCode">
                <Input />
              </Form.Item>
              <Form.Item
                label="所属大区"
                name="belongWarZone"
                rules={[{ required: true, message: '请选择所属大区' }]}
              >
                <Select
                  disabled
                  placeholder="根据省份自动匹配"
                  options={enum2Options(BelongWarZoneEnum)}
                />
              </Form.Item>
              <Form.Item
                label="开发标签"
                name="developTag"
                rules={[{ required: true, message: '请选择开发标签' }]}
              >
                <Select
                  placeholder="请选择开发标签"
                  options={enum2Options(DevelopTagEnum)}
                  showSearch
                  optionFilterProp="label"
                />
              </Form.Item>
            </Card>
          </div>
          <div className="flex-1 sm:overflow-auto sm:pr-5">
            <PaymentFormList />
          </div>
        </div>
      </Modal>
      <CheckCodeModal
        title="加盟属性不匹配"
        open={checkOpen}
        onSuccess={() => {
          setCheckOpen(false);
          handleSave(form.getFieldsValue());
        }}
        onCancel={() => setCheckOpen(false)}
        messageInfo={() => {
          const storeCategoryEnum = enum2ValueEnum(StoreCategoryEnum);

          return (
            <div className="text-[#e10600] text-sm mb-2">
              <div>
                正式合同门店性质：
                {storeCategoryEnum[checkStoreCategoryData?.contractStoreCategory!]}
              </div>
              <div>
                原门店性质：
                {storeCategoryEnum[checkStoreCategoryData?.relevanceStoreCategory!]}
              </div>
              <div>客户性质：{storeCategoryEnum[checkStoreCategoryData?.customerNature!]}</div>
              <div>
                区域性质：
                {enum2ValueEnum(JoinRegionEnum)[checkStoreCategoryData?.joinRegion!]}
              </div>
            </div>
          );
        }}
      />
    </>
  );
});

export default CreateEditRelocationFormalModal;
