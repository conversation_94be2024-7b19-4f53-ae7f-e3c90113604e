import { useEffect, useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { contractAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ETable, ExportButton, TableActions } from '@src/components';
import { ETableActionType, ETableColumn, ValueType } from '@src/components/ETable';
import useTableViews from '@src/hooks/useTableViews';
import { useNoticeEventSubscription } from '@src/layout';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { AuditStatusEnum, BusinessTypeEnum } from '@src/services/common/type';
import { DevelopTagEnum, StoreCategoryEnum } from '@src/services/contract/formal/type';
import {
  batchDeleteRelocationFormal,
  exportRelocationFormal,
  getRelocationFormalDetail,
  getRelocationFormalList,
  submitRelocationFormalAudit,
} from '@src/services/contract/relocation-formal';
import {
  RelocationFormalDTO,
  RelocationFormalStatusEnum,
} from '@src/services/contract/relocation-formal/type';
import { ContractNodeEnum } from '@src/services/contract/renewal/type';
import { compatibleTableActionWidth, enum2Options, getParamsFromFilters } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Dropdown, MenuProps, Skeleton } from 'antd';
import { useSearchParams } from 'react-router-dom';
import CreateEditRelocationFormalModal from './components/CreateEditRelocationFormalModal';
import RelocationFormalDetailDrawer from './components/RelocationFormalDetailDrawer';
import RelocationIntentionDetailDrawer from '../relocation-intention/components/RelocationIntentionDetailDrawer';

const RelocationFormal = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { modal, message } = App.useApp();
  const actionRef = useRef<ETableActionType>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const currentIdRef = useRef<number | null>(null);
  const createEditModalRef = useRef<ModalRef>(null);
  const detailDrawerRef = useRef<ModalRef>(null);
  const shopIdRef = useRef<string | null>(null);
  const shopDetailDrawerRef = useRef<ModalRef>(null);
  const customerIdRef = useRef<number | null>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const relocationIntentionIdRef = useRef<number | null>(null);
  const relocationIntentionDetailDrawerRef = useRef<ModalRef>(null);

  const checkPermission = usePermission();
  const { views, loading: getViewsLoading } = useTableViews(BusinessTypeEnum.FORMAL_CONTRACT);
  const { data, runAsync: getList } = useRequest(getRelocationFormalList, { manual: true });

  useNoticeEventSubscription((event) => {
    if (event.type === 'relocation-formal' && data?.result.some((i) => i.id === event.payload.id)) {
      actionRef.current?.reload();
    }
  });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      currentIdRef.current = Number(id);
      detailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSubmitAudit = async (id: number) => {
    const detail = await getRelocationFormalDetail(id);
    const amount = detail.payments?.reduce((total, cur) => total + cur.paymentAmount, 0) || 0;

    modal.confirm({
      title: '提交审核',
      content: `累计打款金额：${amount.toFixed(2)} 元，是否提交合同审核？`,
      onOk: async () => {
        await submitRelocationFormalAudit(id);
        message.success('提交成功');
        actionRef.current?.reload();
      },
    });
  };

  const columns: ETableColumn<RelocationFormalDTO>[] = [
    {
      title: '合同编号',
      dataIndex: 'code',
      fixed: 'left',
      valueType: ValueType.TEXT,
      render: (value, { id }) => (
        <a
          onClick={() => {
            currentIdRef.current = id;
            detailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '门店性质',
      dataIndex: 'storeCategory',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(StoreCategoryEnum),
      },
      filterProps: {
        options: enum2Options(StoreCategoryEnum),
      },
    },
    {
      title: '合同节点',
      dataIndex: 'node',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(ContractNodeEnum),
      },
      filterProps: {
        options: enum2Options(ContractNodeEnum),
      },
    },
    {
      title: '合同状态',
      dataIndex: 'status',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(RelocationFormalStatusEnum),
      },
      filterProps: {
        options: enum2Options(RelocationFormalStatusEnum),
      },
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: contractAuditStatusOptions,
      },
      filterProps: {
        options: contractAuditStatusOptions,
      },
    },
    {
      title: '原门店编码',
      dataIndex: 'originalShopId',
      valueType: ValueType.TEXT,
      render: (value) => (
        <a
          onClick={() => {
            shopIdRef.current = value;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '原门店冠名',
      dataIndex: 'originalShopName',
      valueType: ValueType.TEXT,
    },
    {
      title: '品牌授权截止日期',
      dataIndex: 'brandAuthEndDate',
      valueType: ValueType.DATE,
    },
    {
      title: '负责人',
      dataIndex: 'directUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      valueType: ValueType.TEXT,
      render: (value, { customerId }) => (
        <a
          onClick={() => {
            customerIdRef.current = customerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '搬迁意向合同',
      dataIndex: 'intentionCode',
      valueType: ValueType.TEXT,
      render: (value, { intentionId }) => (
        <a
          onClick={() => {
            relocationIntentionIdRef.current = intentionId;
            relocationIntentionDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '所属招商顾问',
      dataIndex: 'consultantUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '签约人',
      dataIndex: 'signer',
      valueType: ValueType.TEXT,
    },
    {
      title: '联系电话',
      dataIndex: 'signerPhone',
      valueType: ValueType.PHONE,
      fieldProps: (_, { signerPhoneSensitive }) => ({
        sensitiveValue: signerPhoneSensitive,
      }),
    },
    {
      title: '身份证号码',
      dataIndex: 'signerIdentityCard',
      valueType: ValueType.IDENTITY_CARD,
      fieldProps: (_, { signerIdentityCardSensitive }) => ({
        sensitiveValue: signerIdentityCardSensitive,
      }),
    },
    {
      title: '是否对公',
      dataIndex: 'toPublic',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: yesOrNoOptions,
      },
      filterProps: {
        options: [
          // 后端这里不接受 Boolean
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
      },
    },
    {
      title: '签约主体',
      dataIndex: 'signSubject',
      valueType: ValueType.TEXT,
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'socialCreditCode',
      valueType: ValueType.TEXT,
    },
    {
      title: '正式打款日期',
      dataIndex: 'paymentDate',
      valueType: ValueType.DATE,
    },
    {
      title: '正式生效日期',
      dataIndex: 'effectiveDate',
      valueType: ValueType.DATE,
    },
    {
      title: '正式到期日期',
      dataIndex: 'expirationDate',
      valueType: ValueType.DATE,
    },
    {
      title: '门店交付日期',
      dataIndex: 'storeDeliveryDate',
      valueType: ValueType.DATE,
    },
    {
      title: '铺位编号',
      dataIndex: 'siteNo',
      valueType: ValueType.TEXT,
    },
    {
      title: '门店编号',
      dataIndex: 'storeCode',
      valueType: ValueType.TEXT,
    },
    {
      title: '门店冠名',
      dataIndex: 'storeName',
      valueType: ValueType.TEXT,
    },
    {
      title: '门店定位',
      dataIndex: 'storeCoordinates',
      render: (_, { storeCoordinates }) =>
        storeCoordinates &&
        `${storeCoordinates.name}（经度：${storeCoordinates.longitude}；纬度：${storeCoordinates.latitude}）`,
    },
    {
      title: '门店地址',
      dataIndex: 'storeAddress',
      valueType: ValueType.TEXT,
    },
    {
      title: '所属省市区',
      dataIndex: 'region',
      width: 220,
      valueType: ValueType.REGION,
      fieldProps: {
        regionLevel: 4,
      },
      transform: (value) => ({
        ...value,
        field: 'regionCode',
      }),
    },
    {
      title: '所属大区',
      dataIndex: 'belongWarZone',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(BelongWarZoneEnum),
      },
      filterProps: {
        options: enum2Options(BelongWarZoneEnum),
      },
    },
    {
      title: '开发标签',
      dataIndex: 'developTag',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(DevelopTagEnum),
      },
      filterProps: {
        options: enum2Options(DevelopTagEnum),
      },
    },
    {
      title: '选址报告流程 id',
      dataIndex: 'siteFlowId',
      valueType: ValueType.TEXT,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: ValueType.DATE_TIME,
    },
    {
      title: '更新人',
      dataIndex: 'updateUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: ValueType.DATE_TIME,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      hideInFilters: true,
      hideInSettings: true,
      width: compatibleTableActionWidth(120),
      render: (_, { id, auditStatus }) => {
        // 是否是未提交或驳回
        const isNotSubmitOrReturn = [AuditStatusEnum.NOT_SUBMIT, AuditStatusEnum.RETURN].includes(
          auditStatus,
        );

        return (
          <TableActions
            shortcuts={[
              {
                label: '编辑',
                show:
                  checkPermission(PermissionsMap.ContractRelocationFormalEdit) &&
                  isNotSubmitOrReturn,
                onClick: () => {
                  currentIdRef.current = id;
                  createEditModalRef.current?.open();
                },
              },
            ]}
            moreItems={[
              {
                label: '提交审核',
                show:
                  checkPermission(PermissionsMap.ContractRelocationFormalSubmitAudit) &&
                  isNotSubmitOrReturn,
                onClick: () => handleSubmitAudit(id),
              },
              {
                label: '删除',
                danger: true,
                show:
                  checkPermission(PermissionsMap.ContractRelocationFormalDelete) &&
                  isNotSubmitOrReturn,
                onClick: () =>
                  modal.confirm({
                    title: '删除',
                    content: '确定要删除该合同吗？',
                    onOk: async () => {
                      await batchDeleteRelocationFormal([id]);
                      message.success('删除成功');
                      actionRef.current?.reload();
                      setSelectedRowKeys((prev) => prev.filter((i) => i !== id));
                    },
                  }),
              },
            ]}
          />
        );
      },
    },
  ];

  const batchMenuItems: MenuProps['items'] = [
    {
      label: '删除',
      key: 'delete',
      danger: true,
      auth: PermissionsMap.ContractRelocationFormalDelete,
      onClick: () =>
        modal.confirm({
          title: '删除',
          content: `确定要删除选中的 ${selectedRowKeys.length} 个合同吗？`,
          onOk: async () => {
            await batchDeleteRelocationFormal(selectedRowKeys);
            message.success('删除成功');
            setSelectedRowKeys([]);
            actionRef.current?.reload();
          },
        }),
    },
  ].filter((i) => checkPermission(i.auth));

  const headerNode = (
    <div className="sm:flex justify-between mb-3">
      <div className="self-end mb-2 sm:mb-0 pl-2">
        共 {data?.total || 0} 个合同
        {selectedRowKeys.length > 0 && (
          <>
            ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 个
            <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
              清空选择
            </a>
          </>
        )}
      </div>
      <div className="flex justify-end gap-2">
        {batchMenuItems.length > 0 && (
          <Dropdown
            disabled={!selectedRowKeys.length}
            menu={{
              items: batchMenuItems,
            }}
          >
            <Button type="text">
              批量操作 <CaretDownOutlined />
            </Button>
          </Dropdown>
        )}
        <Permission value={PermissionsMap.ContractRelocationFormalCreate}>
          <Button
            type="text"
            onClick={() => {
              currentIdRef.current = null;
              createEditModalRef.current?.open();
            }}
          >
            新建
          </Button>
        </Permission>
        <Permission value={PermissionsMap.ContractRelocationFormalExport}>
          <ExportButton
            total={selectedRowKeys.length || data?.total}
            request={() =>
              exportRelocationFormal({
                columns: actionRef.current!.getShowFields(),
                ...(selectedRowKeys.length
                  ? { ids: selectedRowKeys }
                  : getParamsFromFilters(actionRef.current?.getFilters())),
              })
            }
          />
        </Permission>
      </div>
    </div>
  );

  return (
    <>
      <PageContainer
        loading={getViewsLoading && <Skeleton paragraph={{ rows: 15 }} className="px-10" />}
      >
        <ETable
          rowKey="id"
          actionRef={actionRef}
          sticky
          bordered
          size="small"
          header={headerNode}
          views={views}
          pagination={{ showQuickJumper: true, showSizeChanger: true }}
          columns={columns}
          rowSelection={{
            fixed: true,
            preserveSelectedRowKeys: true,
            selectedRowKeys,
            onChange: (keys) => setSelectedRowKeys(keys as number[]),
          }}
          request={async ({ current, pageSize }, filters) => {
            const res = await getList({
              pageNum: current,
              pageSize,
              ...getParamsFromFilters(filters),
            });

            return {
              data: res.result,
              total: res.total,
            };
          }}
        />
        <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => shopIdRef.current} />
        <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />
        <RelocationIntentionDetailDrawer
          ref={relocationIntentionDetailDrawerRef}
          getId={() => relocationIntentionIdRef.current}
        />
        <CreateEditRelocationFormalModal
          ref={createEditModalRef}
          getId={() => currentIdRef.current}
          onSuccess={() => actionRef.current?.reload()}
        />
      </PageContainer>
      <RelocationFormalDetailDrawer
        ref={detailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
        onDelete={() => actionRef.current?.reload()}
      />
    </>
  );
};

export default RelocationFormal;
