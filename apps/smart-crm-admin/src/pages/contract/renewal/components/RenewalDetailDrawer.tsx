import React, { useRef, useState } from 'react';
import { yesOrNoOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { ButtonGroup } from '@src/components';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { PermissionsMap, usePermission } from '@src/permissions';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { ContractEnum } from '@src/services/contract';
import { DevelopTagEnum } from '@src/services/contract/formal/type';
import {
  batchPushWeaverRenewalContract,
  deleteRenewalContract,
  getRenewalContractDetail,
} from '@src/services/contract/renewal';
import { ContractNodeEnum, RenewalContractDTO } from '@src/services/contract/renewal/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Card, Drawer, DrawerProps, Tabs } from 'antd';
import CreateEditRenewalModal from './CreateEditRenewalModal';
import ContractFileList from '../../components/ContractFileList';
import ContractFileUpModal from '../../components/ContractFileUpModal';

interface RenewalDetailDrawerProps extends DrawerProps {
  getId: () => number | null;
  onUpdate?: () => void;
  onDecrease?: () => void;
}

const RenewalDetailDrawer = React.forwardRef<ModalRef, RenewalDetailDrawerProps>(
  ({ getId, onUpdate, onDecrease, ...props }, ref) => {
    const { modal, message } = App.useApp();
    const [open, setOpen] = useState(false);
    const customerDetailDrawerRef = useRef<ModalRef>(null);
    const createEditRenewalModalRef = useRef<ModalRef>(null);
    const shopDetailDrawerRef = useRef<ModalRef>(null);
    const shopIdRef = useRef<string | null>(null);
    const [activeKey, setActiveKey] = useState('info');

    const id = getId()!;

    const checkPermission = usePermission();
    const { data: users } = useAllUsers();
    const { data, loading, refresh } = useRequest(() => getRenewalContractDetail(id), {
      ready: open,
    });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const baseInfoItems: DescriptionFieldType<RenewalContractDTO>[] = [
      {
        label: '合同编号',
        field: 'code',
      },
      {
        label: '门店编号',
        field: 'shopId',
        children: (
          <a
            onClick={() => {
              shopIdRef.current = data?.shopId!;
              shopDetailDrawerRef.current?.open();
            }}
          >
            {data?.shopId}
          </a>
        ),
      },
      {
        label: '门店冠名',
        field: 'shopName',
      },
      {
        label: '门店地址',
        field: 'shopAddress',
      },
      {
        label: '合同节点',
        field: 'contractNode',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(ContractNodeEnum),
        },
      },
      {
        label: '负责人',
        field: 'directUserId',
        valueType: ValueType.PERSON,
      },
      {
        label: '签约人',
        field: 'signer',
      },
      {
        label: '联系电话',
        field: 'signerPhone',
        valueType: ValueType.PHONE,
        fieldProps: {
          sensitiveValue: data?.signerPhoneSensitive,
        },
      },
      {
        label: '身份证号',
        field: 'signerIdentityCard',
        valueType: ValueType.IDENTITY_CARD,
        fieldProps: {
          sensitiveValue: data?.signerIdentityCardSensitive,
        },
      },
      {
        label: '省市区',
        field: 'region',
        valueType: ValueType.REGION,
      },
      {
        label: '所属大区',
        field: 'belongWarZone',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(BelongWarZoneEnum),
        },
      },
      {
        label: '开发标签',
        field: 'developTag',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(DevelopTagEnum),
        },
      },
      {
        label: '营业执照主体名称',
        field: 'licenseSubjectName',
      },
      {
        label: '原合同开始日期',
        field: 'originalContractBeginDate',
      },
      {
        label: '原合同截止日期',
        field: 'originalContractEndDate',
      },
      {
        label: '续约合同生效日期',
        field: 'contractBeginDate',
      },
      {
        label: '续约合同到期日期',
        field: 'contractEndDate',
      },
      {
        label: '门店实际使用面积',
        field: 'shopRealUseArea',
        children: `${data?.shopRealUseArea} m²`,
      },
      {
        label: '不动产权证地址',
        field: 'realEstateAddress',
      },
      {
        label: '是否对公',
        field: 'toPublic',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        label: '签约主体',
        field: 'signSubject',
        hidden: !data?.toPublic,
      },
      {
        label: '统一社会信用代码',
        field: 'socialCreditCode',
        hidden: !data?.toPublic,
      },
    ];

    return (
      <Drawer
        title="续约合同详情"
        width={800}
        push={false}
        destroyOnHidden
        open={open}
        onClose={() => {
          setOpen(false);
          setActiveKey('info');
        }}
        {...props}
      >
        <Card loading={loading}>
          <div className="flex justify-between">
            <div className="flex flex-col gap-2">
              <p>
                客户名称：
                <a onClick={() => customerDetailDrawerRef.current?.open()}>{data?.customerName}</a>
              </p>
              <p>客户编号：{data?.customerCode}</p>
              <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
              <p>创建时间：{data?.createTime}</p>
              <p>更新人：{users?.find((i) => i.id === data?.updateUserId)?.name}</p>
              <p>更新时间：{data?.updateTime}</p>
            </div>
            <ButtonGroup
              items={[
                {
                  label: '编辑',
                  show:
                    checkPermission(PermissionsMap.ContractRenewalEdit) &&
                    data?.contractNode === ContractNodeEnum.OA未发起,
                  onClick: () => {
                    createEditRenewalModalRef.current?.open();
                  },
                },
                {
                  label: '推送泛微',
                  show: data?.contractNode === ContractNodeEnum.OA未发起,
                  onClick: () =>
                    modal.confirm({
                      title: '推送泛微',
                      content: '确定要将该续约合同推送泛微吗？',
                      onOk: async () => {
                        await batchPushWeaverRenewalContract([id]);
                        message.success('推送成功');
                        refresh();
                        onUpdate?.();
                      },
                    }),
                },
                {
                  label: (_r) => (
                    <ContractFileUpModal
                      getId={() => id}
                      onSuccess={refresh}
                      contractType={ContractEnum.续约合同}
                      maxCount={data?.attachments ? 10 - data?.attachments?.length : 10}
                    />
                  ),
                  show: !data?.attachments || data?.attachments?.length < 10,
                },
                {
                  label: '删除',
                  danger: true,
                  show:
                    checkPermission(PermissionsMap.ContractRenewalDelete) &&
                    data?.contractNode === ContractNodeEnum.OA未发起,
                  onClick: () =>
                    modal.confirm({
                      title: '删除',
                      content: '确定要删除该续约合同吗？',
                      onOk: async () => {
                        await deleteRenewalContract(id);
                        message.success('删除成功');
                        setOpen(false);
                        onDecrease?.();
                      },
                    }),
                },
              ]}
            />
          </div>
        </Card>
        <Card loading={loading} className="mt-5">
          <Tabs
            className="-mt-5"
            activeKey={activeKey}
            onTabClick={setActiveKey}
            items={[
              {
                label: '合同详情',
                key: 'info',
                children: renderDescriptions(baseInfoItems, data),
              },
              {
                label: '合同文件',
                key: 'file',
                children: (
                  <>
                    <ContractFileList data={data?.attachments || []} onRefresh={refresh} />
                  </>
                ),
              },
            ]}
          />
        </Card>

        <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => data?.customerId} />
        <CreateEditRenewalModal
          ref={createEditRenewalModalRef}
          getId={() => id}
          onSuccess={() => {
            refresh();
            onUpdate?.();
          }}
        />
        <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => shopIdRef.current} />
      </Drawer>
    );
  },
);

export default RenewalDetailDrawer;
