import React, { useRef, useState } from 'react';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { Encrypt, ImportFileFormItem, ShopSelect, UserSelect } from '@src/components';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useProvinceWarZoneMap from '@src/hooks/useProvinceWarZoneMap';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { DevelopTagEnum } from '@src/services/contract/formal/type';
import {
  createRenewalContract,
  downloadRenewalContractExcelTemplate,
  getRenewalContractDetail,
  getRenewalContractOriginalBeginDate,
  importRenewalContractExcel,
  submitRenewalContract,
  updateRenewalContract,
} from '@src/services/contract/renewal';
import { getCustomerDetail } from '@src/services/customers';
import { getShopDetail } from '@src/services/shop/list';
import { SignerMainTypeEnum } from '@src/services/shop/list/type';
import useUserStore from '@src/store/useUserStore';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Form, Input, Modal, ModalProps, Select, Tabs } from 'antd';
import dayjs from 'dayjs';

interface CreateEditRenewalModalProps extends ModalProps {
  getId: () => number | null;
  onSuccess: () => void;
}

const CreateEditRenewalModal = React.forwardRef<ModalRef, CreateEditRenewalModalProps>(
  ({ getId, onSuccess, ...props }, ref) => {
    const { modal, message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    const saveType = useRef<'save' | 'submit'>('save');
    const [activeKey, setActiveKey] = useState<'manual' | 'excel'>('manual');

    const id = getId();
    const isEdit = !!id;

    const { showImportPrompt } = ImportFileFormItem.usePrompt();
    const provinceWarZoneMap = useProvinceWarZoneMap({ ready: open });
    const {
      user: { shUserId },
    } = useUserStore();
    const {
      data: customer,
      loading: getCustomerLoading,
      runAsync: getCustomer,
      mutate: mutateCustomer,
    } = useRequest(getCustomerDetail, {
      manual: true,
    });
    const { runAsync: create, loading: createLoading } = useRequest(createRenewalContract, {
      manual: true,
    });
    const { runAsync: submit, loading: submitLoading } = useRequest(submitRenewalContract, {
      manual: true,
    });
    const { runAsync: update, loading: updateLoading } = useRequest(updateRenewalContract, {
      manual: true,
    });
    const { runAsync: importExcel, loading: importExcelLoading } = useRequest(
      importRenewalContractExcel,
      {
        manual: true,
      },
    );
    const { data, loading, mutate } = useRequest(() => getRenewalContractDetail(id!), {
      ready: open && isEdit,
      onSuccess: (res) => {
        form.setFieldsValue(res);
        getCustomer(res.customerId);
      },
    });

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        setActiveKey('manual');
        mutate(undefined);
        mutateCustomer(undefined);
      },
      close: () => setOpen(false),
    }));

    const handleSave = async (values: any) => {
      if (activeKey === 'manual') {
        const params = isEdit ? { id, ...values } : values;

        if (
          params?.contractBeginDate &&
          dayjs(params.contractBeginDate).isBefore(dayjs().startOf('D'))
        ) {
          message.error('续约合同生效日期早于当前日期，不可发起');

          return;
        }

        if (saveType.current === 'save') {
          if (isEdit) {
            await update(params);
          } else {
            await create(params);
          }

          message.success('保存成功');
        } else {
          const shouldSubmit = await modal.confirm({
            title: '推送泛微',
            content: '确定要保存并推送泛微吗？',
          });

          if (!shouldSubmit) {
            return;
          }

          await submit(params);
          message.success('成功');
        }

        setOpen(false);
        onSuccess();
      } else {
        const res = await importExcel(values);

        showImportPrompt(res, onSuccess);

        setOpen(false);
      }
    };

    const shopInfoItems: {
      label: string;
      name: string;
      props?: Record<string, any>;
      component?: React.FunctionComponent<any>;
    }[] = [
      { label: '门店冠名', name: 'shopName' },
      { label: '门店地址', name: 'shopAddress' },
      { label: '原合同开始日期', name: 'originalContractBeginDate' },
      { label: '原合同截止日期', name: 'originalContractEndDate' },
      { label: '续约合同生效日期', name: 'contractBeginDate' },
      { label: '续约合同到期日期', name: 'contractEndDate' },
      {
        label: '门店实际使用面积',
        name: 'shopRealUseArea',
        component: Input,
        props: {
          addonAfter: 'm²',
        },
      },
      { label: '不动产权证地址', name: 'realEstateAddress' },
    ];

    const handleShopIdChange = async (shopId: string | undefined, shouldClear?: boolean) => {
      if (shouldClear) {
        const clearFields = [
          'shopName',
          'shopAddress',
          'originalContractBeginDate',
          'originalContractEndDate',
          'contractBeginDate',
          'contractEndDate',
          'shopRealUseArea',
          'realEstateAddress',
          'toPublic',
          'signSubject',
          'socialCreditCode',
        ];

        // 先清除掉这些字段，预防后面请求出问题
        form.resetFields(clearFields);
      }

      if (!shopId) {
        return;
      }

      const [shopDetail, originalContractBeginDate] = await Promise.all([
        getShopDetail(shopId),
        getRenewalContractOriginalBeginDate(shopId),
      ]);

      form.setFieldsValue({
        shopName: shopDetail.shopName,
        shopAddress: shopDetail.address,
        originalContractBeginDate,
        originalContractEndDate: shopDetail.brandAuthEndDate,
        contractBeginDate:
          shopDetail.brandAuthEndDate &&
          dayjs(shopDetail.brandAuthEndDate).add(1, 'day').format('YYYY-MM-DD'),
        contractEndDate:
          shopDetail.brandAuthEndDate &&
          dayjs(shopDetail.brandAuthEndDate).add(1, 'year').format('YYYY-MM-DD'),
        shopRealUseArea: shopDetail.realUseArea,
        realEstateAddress: shopDetail.propertyAddress,
        toPublic: shopDetail.signerMainType === SignerMainTypeEnum.公司,
        signSubject: shopDetail.signerMain,
        socialCreditCode: shopDetail.signerUsci,
      });
    };

    const manualFormNode = (
      <div className="flex flex-col sm:flex-row gap-5 overflow-auto h-[60vh]">
        <div className="flex-1 sm:overflow-auto sm:pr-5">
          <Form.Item
            label="门店编号"
            name="shopId"
            rules={[{ required: true, message: '请选择门店编号' }]}
          >
            <ShopSelect
              showRefresh={isEdit}
              onChange={(value) => handleShopIdChange(value, true)}
              onRefresh={() => handleShopIdChange(form.getFieldValue('shopId'))}
            />
          </Form.Item>
          <Form.Item
            label="营业执照主体名称"
            name="licenseSubjectName"
            rules={[{ required: true, message: '请输入营业执照主体名称' }]}
          >
            <Input.TextArea autoSize placeholder="请输入营业执照主体名称" />
          </Form.Item>
          {shopInfoItems.map((item) => {
            const Component = item.component || Input.TextArea;

            return (
              <Form.Item
                key={item.name}
                label={item.label}
                name={item.name}
                rules={[{ required: true, message: '不能为空' }]}
              >
                <Component
                  disabled
                  placeholder="选择门店后自动填充"
                  {...(item.component
                    ? {}
                    : {
                        autoSize: true,
                      })}
                  {...item.props}
                />
              </Form.Item>
            );
          })}
          <Form.Item
            label="是否对公"
            name="toPublic"
            rules={[{ required: true, message: '不能为空' }]}
          >
            <Select
              disabled
              placeholder="选择门店后自动填充"
              options={yesOrNoOptions}
              suffixIcon={null}
            />
          </Form.Item>
          <Form.Item noStyle shouldUpdate={(prev, next) => prev.toPublic !== next.toPublic}>
            {({ getFieldValue }) =>
              getFieldValue('toPublic') && (
                <>
                  <Form.Item
                    label="签约主体"
                    name="signSubject"
                    rules={[{ required: true, message: '请输入签约主体' }]}
                  >
                    <Input disabled placeholder="选择门店后自动填充" />
                  </Form.Item>
                  <Form.Item
                    label="统一社会信用代码"
                    name="socialCreditCode"
                    rules={[{ required: true, message: '请输入统一社会信用代码' }]}
                  >
                    <Input disabled placeholder="选择门店后自动填充" />
                  </Form.Item>
                </>
              )
            }
          </Form.Item>
        </div>
        <div className="flex-1 sm:overflow-auto sm:pr-5">
          <Form.Item
            label="负责人"
            name="directUserId"
            initialValue={shUserId}
            rules={[{ required: true, message: '请选择负责人' }]}
          >
            <UserSelect placeholder="请选择负责人" />
          </Form.Item>
          <Form.Item
            label="客户"
            name="customerId"
            rules={[{ required: true, message: '请选择客户' }]}
          >
            <RelCustomer
              editable
              defaultCustomerIdToNameMap={{
                [data?.customerId || '']: data?.customerName,
              }}
              onChange={(_, info) => {
                mutateCustomer(info);

                form.setFieldsValue({
                  signer: info?.name,
                  signerPhone: undefined,
                  signerIdentityCard: info?.identityCard,
                });
              }}
            />
          </Form.Item>
          <Form.Item
            label="签约人"
            name="signer"
            rules={[{ required: true, message: '请输入签约人' }]}
          >
            <Input placeholder="请输入签约人" />
          </Form.Item>
          <Form.Item
            label="联系电话"
            name="signerPhone"
            rules={[{ required: true, message: '请选择联系电话' }]}
          >
            <Encrypt.PhoneSelect
              placeholder="请选择手机号"
              options={customer?.phones?.map((phone, index) => ({
                label: customer.phonesSensitive?.[index],
                value: phone,
              }))}
              encryptSensitiveMap={{
                [data?.signerPhone || '']: data?.signerPhoneSensitive,
              }}
            />
          </Form.Item>
          <Encrypt.IdCardFormItem
            formItemProps={{
              label: '身份证号码',
              name: 'signerIdentityCard',
              rules: [{ required: true, message: '请输入身份证号码' }],
            }}
            fieldProps={{
              disabled: true,
              placeholder: '选择客户后自动填充',
              encryptSensitiveMap: {
                [customer?.identityCard || '']: customer?.identityCardSensitive,
                [data?.signerIdentityCard || '']: data?.signerIdentityCardSensitive,
              },
            }}
          />
          <EditableRegion
            editable
            formItemProps={{
              label: '省市区',
              name: 'region',
              rules: [{ required: true, message: '请选择省市区' }],
            }}
            fieldProps={{
              placeholder: '请选择省市区',
              onChange: (value: any) => {
                form.setFieldValue('belongWarZone', provinceWarZoneMap[value?.[0]]);
              },
            }}
          />
          <Form.Item
            label="所属大区"
            name="belongWarZone"
            rules={[{ required: true, message: '请选择所属大区' }]}
          >
            <Select
              disabled
              placeholder="选择省市区后自动匹配"
              options={enum2Options(BelongWarZoneEnum)}
            />
          </Form.Item>
          <Form.Item
            label="开发标签"
            name="developTag"
            rules={[{ required: true, message: '请选择开发标签' }]}
          >
            <Select
              placeholder="请选择开发标签"
              options={enum2Options(DevelopTagEnum)}
              showSearch
              optionFilterProp="label"
            />
          </Form.Item>
        </div>
      </div>
    );

    const confirmLoading = createLoading || submitLoading || updateLoading || importExcelLoading;

    return (
      <Modal
        title={isEdit ? '编辑续约合同' : '新建续约合同'}
        open={open}
        width={1000}
        destroyOnHidden
        styles={{
          body: { maxHeight: '60vh', margin: '0 -24px', padding: '0 24px' },
        }}
        confirmLoading={confirmLoading}
        loading={loading || getCustomerLoading}
        okText="保存"
        footer={(_, { OkBtn, CancelBtn }) => (
          <>
            <CancelBtn />
            <OkBtn />
            {activeKey === 'manual' && (
              <Button
                type="primary"
                loading={confirmLoading}
                onClick={() => {
                  saveType.current = 'submit';
                  form.submit();
                }}
              >
                推送泛微
              </Button>
            )}
          </>
        )}
        modalRender={(node) => (
          <Form
            form={form}
            labelCol={{ span: 8 }}
            clearOnDestroy
            scrollToFirstError
            onFinish={handleSave}
          >
            {node}
          </Form>
        )}
        onCancel={() => setOpen(false)}
        onOk={() => {
          saveType.current = 'save';
          form.submit();
        }}
        {...props}
      >
        {isEdit ? (
          manualFormNode
        ) : (
          <Tabs
            activeKey={activeKey}
            destroyOnHidden
            onChange={(key) => {
              setActiveKey(key as typeof activeKey);
            }}
            items={[
              {
                label: '手动新建',
                key: 'manual',
                children: manualFormNode,
              },
              {
                label: '通过 EXCEL 导入',
                key: 'excel',
                children: (
                  <ImportFileFormItem downloadTemplate={downloadRenewalContractExcelTemplate} />
                ),
              },
            ]}
          />
        )}
      </Modal>
    );
  },
);

export default CreateEditRenewalModal;
