import { useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ETable, ExportButton, TableActions } from '@src/components';
import { ETableActionType, ETableColumn, ValueType } from '@src/components/ETable';
import useTableViews from '@src/hooks/useTableViews';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { BusinessTypeEnum } from '@src/services/common/type';
import { DevelopTagEnum } from '@src/services/contract/formal/type';
import {
  batchDeleteRenewalContract,
  batchPushWeaverRenewalContract,
  deleteRenewalContract,
  exportRenewalContractList,
  getRenewalContractList,
} from '@src/services/contract/renewal';
import { ContractNodeEnum, RenewalContractDTO } from '@src/services/contract/renewal/type';
import { compatibleTableActionWidth, enum2Options, getParamsFromFilters } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Dropdown, MenuProps, Skeleton } from 'antd';
import CreateEditRenewalModal from './components/CreateEditRenewalModal';
import RenewalDetailDrawer from './components/RenewalDetailDrawer';

const Renewal = () => {
  const { modal, message } = App.useApp();
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const actionRef = useRef<ETableActionType>(null);
  const currentIdRef = useRef<number | null>(null);
  const shopIdRef = useRef<string | null>(null);
  const customerIdRef = useRef<number | null>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const shopDetailDrawerRef = useRef<ModalRef>(null);
  const createEditRenewalModalRef = useRef<ModalRef>(null);
  const renewalDetailDrawerRef = useRef<ModalRef>(null);

  const checkPermission = usePermission();
  const { views, loading: getViewsLoading } = useTableViews(BusinessTypeEnum.RENEW_CONTRACT);
  const { data, runAsync: getList } = useRequest(getRenewalContractList, { manual: true });

  const columns: ETableColumn<RenewalContractDTO>[] = [
    {
      title: '合同编号',
      dataIndex: 'code',
      fixed: 'left',
      valueType: ValueType.TEXT,
      render: (value, { id }) => (
        <a
          onClick={() => {
            currentIdRef.current = id;
            renewalDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '门店编号',
      dataIndex: 'shopId',
      valueType: ValueType.TEXT,
      render: (value) => (
        <a
          onClick={() => {
            shopIdRef.current = value;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '门店冠名',
      dataIndex: 'shopName',
      valueType: ValueType.TEXT,
    },
    {
      title: '门店地址',
      dataIndex: 'shopAddress',
      valueType: ValueType.TEXT,
    },
    {
      title: '合同节点',
      dataIndex: 'contractNode',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(ContractNodeEnum),
      },
      filterProps: {
        options: enum2Options(ContractNodeEnum),
      },
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      valueType: ValueType.TEXT,
      render: (value, { customerId }) => (
        <a
          onClick={() => {
            customerIdRef.current = customerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '客户编号',
      dataIndex: 'customerCode',
      valueType: ValueType.TEXT,
    },
    {
      title: '负责人',
      dataIndex: 'directUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '签约人',
      dataIndex: 'signer',
      valueType: ValueType.TEXT,
    },
    {
      title: '联系电话',
      dataIndex: 'signerPhone',
      valueType: ValueType.PHONE,
      fieldProps: (_, { signerPhoneSensitive }) => ({
        sensitiveValue: signerPhoneSensitive,
      }),
    },
    {
      title: '身份证号',
      dataIndex: 'signerIdentityCard',
      width: 200,
      valueType: ValueType.IDENTITY_CARD,
      fieldProps: (_, { signerIdentityCardSensitive }) => ({
        sensitiveValue: signerIdentityCardSensitive,
      }),
    },
    {
      title: '省市区',
      dataIndex: 'region',
      valueType: ValueType.REGION,
      transform: (value) => ({
        ...value,
        field: 'regionCode',
      }),
    },
    {
      title: '所属大区',
      dataIndex: 'belongWarZone',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(BelongWarZoneEnum),
      },
      filterProps: {
        options: enum2Options(BelongWarZoneEnum),
      },
    },
    {
      title: '开发标签',
      dataIndex: 'developTag',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(DevelopTagEnum),
      },
      filterProps: {
        options: enum2Options(DevelopTagEnum),
      },
    },
    {
      title: '营业执照主体名称',
      dataIndex: 'licenseSubjectName',
      valueType: ValueType.TEXT,
    },
    {
      title: '原合同开始日期',
      dataIndex: 'originalContractBeginDate',
      valueType: ValueType.DATE,
    },
    {
      title: '原合同截止日期',
      dataIndex: 'originalContractEndDate',
      valueType: ValueType.DATE,
    },
    {
      title: '续约合同生效日期',
      dataIndex: 'contractBeginDate',
      valueType: ValueType.DATE,
    },
    {
      title: '续约合同到期日期',
      dataIndex: 'contractEndDate',
      valueType: ValueType.DATE,
    },
    {
      title: '门店实际使用面积',
      dataIndex: 'shopRealUseArea',
      valueType: ValueType.NUMBER,
      render: (value) => `${value} m²`,
    },
    {
      title: '不动产权证地址',
      dataIndex: 'realEstateAddress',
      valueType: ValueType.TEXT,
    },
    {
      title: '是否对公',
      dataIndex: 'toPublic',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: yesOrNoOptions,
      },
      filterProps: {
        options: [
          // 后端这里不接受 Boolean
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
      },
    },
    {
      title: '签约主体',
      dataIndex: 'signSubject',
      valueType: ValueType.TEXT,
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'socialCreditCode',
      valueType: ValueType.TEXT,
    },
    {
      title: '更新人',
      dataIndex: 'updateUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: ValueType.DATE_TIME,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: ValueType.DATE_TIME,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      fixed: 'right',
      hideInSettings: true,
      hideInFilters: true,
      width: compatibleTableActionWidth(120),
      render: (_, { id, contractNode }) => (
        <TableActions
          shortcuts={[
            {
              label: '编辑',
              show:
                checkPermission(PermissionsMap.ContractRenewalEdit) &&
                contractNode === ContractNodeEnum.OA未发起,
              onClick: () => {
                currentIdRef.current = id;
                createEditRenewalModalRef.current?.open();
              },
            },
          ]}
          moreItems={[
            {
              label: '推送泛微',
              show: contractNode === ContractNodeEnum.OA未发起,
              onClick: () =>
                modal.confirm({
                  title: '推送泛微',
                  content: '确定要将该续约合同推送泛微吗？',
                  onOk: async () => {
                    await batchPushWeaverRenewalContract([id]);
                    message.success('推送成功');
                    actionRef.current?.reload();
                  },
                }),
            },
            {
              label: '删除',
              danger: true,
              show:
                checkPermission(PermissionsMap.ContractRenewalDelete) &&
                contractNode === ContractNodeEnum.OA未发起,
              onClick: () =>
                modal.confirm({
                  title: '删除',
                  content: '确定要删除该续约合同吗？',
                  onOk: async () => {
                    await deleteRenewalContract(id);
                    message.success('删除成功');
                    setSelectedRowKeys((prev) => prev.filter((i) => i !== id));
                    actionRef.current?.reload();
                  },
                }),
            },
          ]}
        />
      ),
    },
  ];

  const batchMenuItems: MenuProps['items'] = [
    {
      key: 'push',
      label: '推送泛微',
      onClick: () =>
        modal.confirm({
          title: '推送泛微',
          content: `确定要将选中的 ${selectedRowKeys.length} 个合同推送泛微吗？`,
          onOk: async () => {
            await batchPushWeaverRenewalContract(selectedRowKeys);
            message.success('推送成功');
            setSelectedRowKeys([]);
            actionRef.current?.reload();
          },
        }),
    },
    {
      key: 'delete',
      label: '删除',
      danger: true,
      auth: PermissionsMap.ContractRenewalDelete,
      onClick: () =>
        modal.confirm({
          title: '删除',
          content: `确定要删除选中的 ${selectedRowKeys.length} 个合同吗？`,
          onOk: async () => {
            await batchDeleteRenewalContract(selectedRowKeys);
            message.success('删除成功');
            setSelectedRowKeys([]);
            actionRef.current?.reload();
          },
        }),
    },
  ].filter((i) => checkPermission(i.auth));

  const headerNode = (
    <div className="sm:flex justify-between mb-3">
      <div className="self-end mb-2 sm:mb-0 pl-2">
        共 {data?.total || 0} 个合同
        {selectedRowKeys.length > 0 && (
          <>
            ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 个
            <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
              清空选择
            </a>
          </>
        )}
      </div>
      <div className="flex justify-end gap-2">
        {batchMenuItems.length > 0 && (
          <Dropdown
            disabled={!selectedRowKeys.length}
            menu={{
              items: batchMenuItems,
            }}
          >
            <Button type="text" className="!flex items-center">
              批量操作 <CaretDownOutlined />
            </Button>
          </Dropdown>
        )}
        <Permission value={PermissionsMap.ContractRenewalCreate}>
          <Button
            type="text"
            onClick={() => {
              currentIdRef.current = null;
              createEditRenewalModalRef.current?.open();
            }}
          >
            新建
          </Button>
        </Permission>
        <Permission value={PermissionsMap.ContractRenewalExport}>
          <ExportButton
            total={selectedRowKeys.length || data?.total}
            request={() =>
              exportRenewalContractList({
                columns: actionRef.current!.getShowFields(),
                ...(selectedRowKeys.length
                  ? { ids: selectedRowKeys }
                  : getParamsFromFilters(actionRef.current?.getFilters())),
              })
            }
          />
        </Permission>
      </div>
    </div>
  );

  return (
    <PageContainer
      loading={getViewsLoading && <Skeleton paragraph={{ rows: 15 }} className="px-10" />}
    >
      <ETable
        actionRef={actionRef}
        rowKey="id"
        sticky
        bordered
        size="small"
        views={views}
        columns={columns}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        header={headerNode}
        rowSelection={{
          fixed: true,
          selectedRowKeys,
          preserveSelectedRowKeys: true,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        request={async ({ current, pageSize }, filters) => {
          const res = await getList({
            pageNum: current,
            pageSize,
            ...getParamsFromFilters(filters),
          });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => shopIdRef.current} />
      <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />
      <CreateEditRenewalModal
        ref={createEditRenewalModalRef}
        getId={() => currentIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
      <RenewalDetailDrawer
        ref={renewalDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
        onDecrease={() => actionRef.current?.reload()}
      />
    </PageContainer>
  );
};

export default Renewal;
