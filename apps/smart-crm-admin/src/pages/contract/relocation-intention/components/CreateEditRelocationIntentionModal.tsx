import React, { useRef, useState } from 'react';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ChooseMapPoints, Encrypt, ShopSelect, UserSelect } from '@src/components';
import EditableDate from '@src/components/Editable/EditableDate';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useDistrictsStreetOpened from '@src/hooks/useDistrictsStreetOpened';
import useProvinceWarZoneMap from '@src/hooks/useProvinceWarZoneMap';
import RelCustomer from '@src/pages/clues/components/RelCustomer';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { StoreCategoryEnum } from '@src/services/contract/formal/type';
import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';
import {
  getRelocationIntentionDetail,
  saveAndSubmitRelocationIntention,
  saveRelocationIntention,
  updateRelocationIntention,
} from '@src/services/contract/relocation-intention';
import { getCustomerDetail } from '@src/services/customers';
import { IdentityTypeEnum } from '@src/services/mini-program/identity/type';
import { ShopListItemDTO } from '@src/services/shop/list/type';
import useUserStore from '@src/store/useUserStore';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Card, Form, Input, InputNumber, Modal, ModalProps, Select } from 'antd';
import PaymentFormList from '../../components/PaymentFormList';

interface CreateEditRelocationIntentionModalProps extends ModalProps {
  getId: () => number | null;
  onSuccess: () => void;
}

const CreateEditRelocationIntentionModal = React.forwardRef<
  ModalRef,
  CreateEditRelocationIntentionModalProps
>(({ getId, onSuccess, ...props }, ref) => {
  const { message } = App.useApp();
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const saveTypeRef = useRef<'save' | 'submit'>('save');

  const id = getId();
  const isEdit = !!id;

  const {
    user: { shUserId },
  } = useUserStore();
  const provinceWarZoneMap = useProvinceWarZoneMap({ ready: open });
  const { transformDistricts, getStreetOpenedRegionIdsLoading } = useDistrictsStreetOpened({
    ready: open,
  });
  const {
    data: customer,
    loading: getCustomerLoading,
    runAsync: getCustomer,
    mutate: mutateCustomer,
  } = useRequest(getCustomerDetail, {
    manual: true,
  });
  const { data, loading, mutate } = useRequest(() => getRelocationIntentionDetail(id!), {
    ready: open && isEdit,
    onSuccess: (res) => {
      form.setFieldsValue(res);
      getCustomer(res.customerId);
    },
  });
  const { runAsync: save, loading: saveLoading } = useRequest(saveRelocationIntention, {
    manual: true,
  });
  const { runAsync: submit, loading: submitLoading } = useRequest(
    saveAndSubmitRelocationIntention,
    { manual: true },
  );
  const { runAsync: update, loading: updateLoading } = useRequest(updateRelocationIntention, {
    manual: true,
  });

  React.useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true);
      mutate(undefined);
      mutateCustomer(undefined);
    },
    close: () => setOpen(false),
  }));

  const handleSave = async (values: any) => {
    if (isEdit) {
      await update({ id, ...values });
      message.success('修改成功');
    } else {
      if (saveTypeRef.current === 'save') {
        await save(values);
        message.success('保存成功');
      } else {
        await submit(values);
        message.success('提交审核成功');
      }
    }

    setOpen(false);
    onSuccess();
  };

  return (
    <Modal
      title={isEdit ? '编辑搬迁意向合同' : '新建搬迁意向合同'}
      open={open}
      width={1000}
      destroyOnHidden
      loading={loading || getStreetOpenedRegionIdsLoading || getCustomerLoading}
      confirmLoading={saveLoading || submitLoading || updateLoading}
      styles={{
        body: { maxHeight: '60vh', margin: '0 -24px', padding: '0 24px' },
      }}
      okText="保存"
      okButtonProps={{ ghost: !isEdit }}
      modalRender={(node) => (
        <Form
          form={form}
          labelCol={{ span: 9 }}
          scrollToFirstError
          clearOnDestroy
          onFinish={handleSave}
        >
          {node}
        </Form>
      )}
      footer={(_, { OkBtn, CancelBtn }) => (
        <>
          <CancelBtn />
          <OkBtn />
          {!isEdit && (
            <Button
              loading={saveLoading || submitLoading}
              type="primary"
              onClick={() => {
                saveTypeRef.current = 'submit';
                form.submit();
              }}
            >
              提交审核
            </Button>
          )}
        </>
      )}
      onCancel={() => setOpen(false)}
      onOk={() => {
        saveTypeRef.current = 'save';
        form.submit();
      }}
      {...props}
    >
      <div className="flex flex-col sm:flex-row gap-5 overflow-auto h-[60vh]">
        <div className="flex flex-col gap-4 flex-1 sm:overflow-auto sm:pr-5">
          <Card
            title="原门店信息"
            classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
          >
            <Form.Item
              label="原门店编号"
              name="originalShopId"
              rules={[{ required: true, message: '请选择原门店编号' }]}
            >
              <ShopSelect
                disabled={isEdit}
                onChange={(_, option) => {
                  form.setFieldValue(
                    'originalShopName',
                    (option as ShopListItemDTO | undefined)?.shopName,
                  );
                }}
              />
            </Form.Item>
            <Form.Item
              label="原门店冠名"
              name="originalShopName"
              rules={[{ required: true, message: '请选择门店' }]}
            >
              <Input placeholder="选择门店后自动填充" disabled />
            </Form.Item>
          </Card>
          <Card
            title="加盟信息"
            classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
          >
            <Form.Item
              label="门店性质"
              name="storeCategory"
              rules={[{ required: true, message: '请选择门店性质' }]}
            >
              <Select
                disabled={isEdit}
                placeholder="请选择门店性质"
                options={enum2Options(StoreCategoryEnum)}
              />
            </Form.Item>
            <Form.Item label="合同编号" required>
              <Input disabled placeholder="根据规则自动生成" value={data?.code} />
            </Form.Item>
            <Form.Item
              label="负责人"
              name="directUserId"
              initialValue={shUserId}
              rules={[{ required: true, message: '请选择负责人' }]}
            >
              <UserSelect placeholder="请选择负责人" />
            </Form.Item>
            <Form.Item
              label="客户名称"
              name="customerId"
              rules={[{ required: true, message: '请选择客户' }]}
            >
              <RelCustomer
                editable
                defaultCustomerIdToNameMap={{
                  [data?.customerId || '']: data?.customerName,
                }}
                onChange={(_, info) => {
                  mutateCustomer(info);

                  form.setFieldsValue({
                    signer: info?.name,
                    signerPhone: undefined,
                    signerIdentityCard: info?.identityCard,
                  });
                }}
              />
            </Form.Item>
            <Form.Item
              label="合作的特殊渠道"
              name="channelType"
              rules={[{ required: true, message: '请选择合作的特殊渠道' }]}
            >
              <Select
                placeholder="请选择合作的特殊渠道"
                options={enum2Options(ContractChannelTypeEnum)}
              />
            </Form.Item>
          </Card>
          <Card
            title="合同签约信息"
            classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
          >
            <Form.Item
              label="所属招商顾问"
              name="consultantUserId"
              rules={[
                {
                  required: true,
                  message: '请选择所属招商顾问',
                },
              ]}
            >
              <UserSelect
                placeholder="请选择所属招商顾问"
                transformOptions={(options) =>
                  options.filter((i) =>
                    [IdentityTypeEnum.招商顾问, IdentityTypeEnum.大客户招商].includes(
                      i.identityType,
                    ),
                  )
                }
              />
            </Form.Item>
            <Form.Item
              label="签约人"
              name="signer"
              rules={[{ required: true, message: '不能为空' }]}
            >
              <Input placeholder="选择客户后自动填充" disabled />
            </Form.Item>
            <Form.Item
              label="联系电话"
              name="signerPhone"
              rules={[{ required: true, message: '请选择联系电话' }]}
            >
              <Encrypt.PhoneSelect
                placeholder="请选择手机号"
                options={customer?.phones?.map((phone, index) => ({
                  label: customer.phonesSensitive?.[index],
                  value: phone,
                }))}
                encryptSensitiveMap={{
                  [data?.signerPhone || '']: data?.signerPhoneSensitive,
                }}
              />
            </Form.Item>
            <Encrypt.IdCardFormItem
              formItemProps={{
                label: '身份证号码',
                name: 'signerIdentityCard',
                rules: [{ required: true, message: '请输入身份证号码' }],
              }}
              fieldProps={{
                disabled: true,
                placeholder: '选择客户后自动填充',
                encryptSensitiveMap: {
                  [customer?.identityCard || '']: customer?.identityCardSensitive,
                  [data?.signerIdentityCard || '']: data?.signerIdentityCardSensitive,
                },
              }}
            />
          </Card>
          <Card
            title="场景判断"
            classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
          >
            <Form.Item
              name="toPublic"
              label="是否对公"
              initialValue={false}
              rules={[{ required: true, message: '请选择是否对公' }]}
            >
              <Select options={yesOrNoOptions} placeholder="请选择是否对公" />
            </Form.Item>
            <Form.Item noStyle dependencies={['toPublic']}>
              {({ getFieldValue }) =>
                getFieldValue('toPublic') && (
                  <>
                    <Form.Item
                      label="签约主体"
                      name="signSubject"
                      rules={[{ required: true, message: '请输入签约主体' }]}
                    >
                      <Input placeholder="请输入签约主体" />
                    </Form.Item>
                    <Form.Item
                      label="统一社会信用代码"
                      name="socialCreditCode"
                      rules={[{ required: true, message: '请输入统一社会信用代码' }]}
                    >
                      <Input placeholder="请输入统一社会信用代码" />
                    </Form.Item>
                  </>
                )
              }
            </Form.Item>
            <EditableDate
              editable
              formItemProps={{
                label: '意向对账日期',
                name: 'signatureDate',
                rules: [{ required: true, message: '请选择意向对账日期' }],
              }}
            />
            <Form.Item
              label="有效天数"
              name="effectiveDays"
              rules={[{ required: true, message: '请输入有效天数' }]}
            >
              <InputNumber
                precision={0}
                min={0}
                addonAfter="天"
                placeholder="请输入"
                style={{ width: 140 }}
              />
            </Form.Item>
            <EditableRegion
              editable
              formItemProps={{
                label: '意向区域',
                name: 'region',
                rules: [{ required: true, message: '请选择意向区域' }],
              }}
              fieldProps={{
                regionLevel: 4,
                placeholder: '请选择意向区域',
                transformDistricts,
                onChange: (value: any) => {
                  form.setFieldValue('belongWarZone', provinceWarZoneMap[value?.[0]]);
                },
              }}
            />
            <Form.Item
              label="所属大区"
              name="belongWarZone"
              rules={[{ required: true, message: '请选择所属大区' }]}
            >
              <Select
                disabled
                placeholder="根据意向区域自动匹配"
                options={enum2Options(BelongWarZoneEnum)}
              />
            </Form.Item>
            <Form.Item
              label="意向点位"
              name="positions"
              rules={[
                {
                  required: true,
                  message: '请选择意向点位',
                },
              ]}
            >
              <ChooseMapPoints multiple={false} />
            </Form.Item>
          </Card>
        </div>
        <div className="flex-1 sm:overflow-auto sm:pr-5">
          <PaymentFormList />
        </div>
      </div>
    </Modal>
  );
});

export default CreateEditRelocationIntentionModal;
