import { useEffect, useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { contractAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ETable, ExportButton, TableActions } from '@src/components';
import { ETableActionType, ETableColumn, ValueType } from '@src/components/ETable';
import useTableViews from '@src/hooks/useTableViews';
import { useNoticeEventSubscription } from '@src/layout';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import ShopDetailDrawer from '@src/pages/shop/list/components/ShopDetailDrawer';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { AuditStatusEnum, BusinessTypeEnum } from '@src/services/common/type';
import { StoreCategoryEnum } from '@src/services/contract/formal/type';
import { ContractChannelTypeEnum } from '@src/services/contract/intention/type';
import {
  batchDeleteRelocationIntention,
  exportRelocationIntention,
  getRelocationIntentionDetail,
  getRelocationIntentionList,
  submitRelocationIntentionAudit,
  updateRelocationIntentionStatus,
} from '@src/services/contract/relocation-intention';
import {
  RelocationIntentionDTO,
  RelocationIntentionStatusEnum,
} from '@src/services/contract/relocation-intention/type';
import { ContractNodeEnum } from '@src/services/contract/renewal/type';
import {
  compatibleTableActionWidth,
  enum2Options,
  enum2ValueEnum,
  getParamsFromFilters,
} from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Dropdown, MenuProps, Skeleton } from 'antd';
import { useSearchParams } from 'react-router-dom';
import CreateEditRelocationIntentionModal from './components/CreateEditRelocationIntentionModal';
import RelocationIntentionDetailDrawer from './components/RelocationIntentionDetailDrawer';

const RelocationIntention = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { modal, message } = App.useApp();
  const actionRef = useRef<ETableActionType>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const currentIdRef = useRef<number | null>(null);
  const detailDrawerRef = useRef<ModalRef>(null);
  const shopIdRef = useRef<string | null>(null);
  const shopDetailDrawerRef = useRef<ModalRef>(null);
  const customerIdRef = useRef<number | null>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const createEditModalRef = useRef<ModalRef>(null);

  const checkPermission = usePermission();
  const { views, loading: getViewsLoading } = useTableViews(
    BusinessTypeEnum.RELOCATION_INTENTION_CONTRACT,
  );
  const { data, runAsync: getList } = useRequest(getRelocationIntentionList, { manual: true });

  useNoticeEventSubscription((event) => {
    if (
      event.type === 'relocation-intention' &&
      data?.result.some((i) => i.id === event.payload.id)
    ) {
      actionRef.current?.reload();
    }
  });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      currentIdRef.current = Number(id);
      detailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSubmitAudit = async (id: number) => {
    const detail = await getRelocationIntentionDetail(id);
    const amount = detail.payments?.reduce((total, cur) => total + cur.paymentAmount, 0) || 0;

    modal.confirm({
      title: '提交审核',
      content: `累计打款金额：${amount.toFixed(2)} 元，是否提交合同审核？`,
      onOk: async () => {
        await submitRelocationIntentionAudit(id);
        message.success('提交成功');
        actionRef.current?.reload();
      },
    });
  };

  const handleUpdateStatus = (status: RelocationIntentionStatusEnum, id?: number) => {
    const statusLabel = enum2ValueEnum(RelocationIntentionStatusEnum)[status];

    // 是否是批量操作
    const isBatch = !id;

    modal.confirm({
      title: `标记为${statusLabel}`,
      content: isBatch
        ? `确定要将选中的 ${selectedRowKeys.length} 个合同标记为 “${statusLabel}” 吗？`
        : `确定要将该合同标记为 “${statusLabel}” 吗？`,
      onOk: async () => {
        await updateRelocationIntentionStatus({ ids: isBatch ? selectedRowKeys : [id], status });
        message.success('操作成功');
        actionRef.current?.reload();
        setSelectedRowKeys([]);
      },
    });
  };

  const columns: ETableColumn<RelocationIntentionDTO>[] = [
    {
      title: '合同编号',
      dataIndex: 'code',
      fixed: 'left',
      valueType: ValueType.TEXT,
      render: (value, { id }) => (
        <a
          onClick={() => {
            currentIdRef.current = id;
            detailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '门店性质',
      dataIndex: 'storeCategory',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(StoreCategoryEnum),
      },
      filterProps: {
        options: enum2Options(StoreCategoryEnum),
      },
    },
    {
      title: '合同节点',
      dataIndex: 'node',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(ContractNodeEnum),
      },
      filterProps: {
        options: enum2Options(ContractNodeEnum),
      },
    },
    {
      title: '合同状态',
      dataIndex: 'status',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(RelocationIntentionStatusEnum),
      },
      filterProps: {
        options: enum2Options(RelocationIntentionStatusEnum),
      },
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: contractAuditStatusOptions,
      },
      filterProps: {
        options: contractAuditStatusOptions,
      },
    },
    {
      title: '原门店编码',
      dataIndex: 'originalShopId',
      valueType: ValueType.TEXT,
      render: (value) => (
        <a
          onClick={() => {
            shopIdRef.current = value;
            shopDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '原门店冠名',
      dataIndex: 'originalShopName',
      valueType: ValueType.TEXT,
    },
    {
      title: '负责人',
      dataIndex: 'directUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      valueType: ValueType.TEXT,
      render: (value, { customerId }) => (
        <a
          onClick={() => {
            customerIdRef.current = customerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '客户编号',
      dataIndex: 'customerCode',
      valueType: ValueType.TEXT,
    },
    {
      title: '合作的特殊渠道',
      dataIndex: 'channelType',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(ContractChannelTypeEnum),
      },
      filterProps: {
        options: enum2Options(ContractChannelTypeEnum),
      },
    },
    {
      title: '所属招商顾问',
      dataIndex: 'consultantUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '签约人',
      dataIndex: 'signer',
      valueType: ValueType.TEXT,
    },
    {
      title: '联系电话',
      dataIndex: 'signerPhone',
      valueType: ValueType.PHONE,
      fieldProps: (_, { signerPhoneSensitive }) => ({
        sensitiveValue: signerPhoneSensitive,
      }),
    },
    {
      title: '身份证号',
      dataIndex: 'signerIdentityCard',
      width: 200,
      valueType: ValueType.IDENTITY_CARD,
      fieldProps: (_, { signerIdentityCardSensitive }) => ({
        sensitiveValue: signerIdentityCardSensitive,
      }),
    },
    {
      title: '是否对公',
      dataIndex: 'toPublic',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: yesOrNoOptions,
      },
      filterProps: {
        options: [
          // 后端这里不接受 Boolean
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
      },
    },
    {
      title: '签约主体',
      dataIndex: 'signSubject',
      valueType: ValueType.TEXT,
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'socialCreditCode',
      valueType: ValueType.TEXT,
    },
    {
      title: '意向对账日期',
      dataIndex: 'signatureDate',
      valueType: ValueType.DATE,
    },
    {
      title: '有效天数',
      dataIndex: 'effectiveDays',
      valueType: ValueType.NUMBER,
    },
    {
      title: '意向生效日期',
      dataIndex: 'effectiveDate',
      valueType: ValueType.DATE,
    },
    {
      title: '意向到期日期',
      dataIndex: 'expirationDate',
      valueType: ValueType.DATE,
    },
    {
      title: '意向区域',
      dataIndex: 'region',
      valueType: ValueType.REGION,
      width: 220,
      fieldProps: {
        regionLevel: 4,
      },
      transform: (value) => ({
        ...value,
        field: 'regionCode',
      }),
    },
    {
      title: '所属大区',
      dataIndex: 'belongWarZone',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(BelongWarZoneEnum),
      },
      filterProps: {
        options: enum2Options(BelongWarZoneEnum),
      },
    },
    {
      title: '意向点位',
      dataIndex: 'positions',
      valueType: ValueType.TEXT,
      valueFormatter: (_, { positions }) =>
        positions?.map((i) => `${i.name}（经度：${i.longitude}；纬度：${i.latitude}）`),
    },
    {
      title: '更新人',
      dataIndex: 'updateUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: ValueType.DATE_TIME,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: ValueType.DATE_TIME,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      hideInFilters: true,
      hideInSettings: true,
      width: compatibleTableActionWidth(120),
      render: (_, { id, auditStatus, status }) => {
        // 是否是未提交或驳回
        const isNotSubmitOrReturn = [AuditStatusEnum.NOT_SUBMIT, AuditStatusEnum.RETURN].includes(
          auditStatus,
        );

        return (
          <TableActions
            shortcuts={[
              {
                label: '编辑',
                show:
                  checkPermission(PermissionsMap.ContractRelocationIntentionEdit) &&
                  isNotSubmitOrReturn,
                onClick: () => {
                  currentIdRef.current = id;
                  createEditModalRef.current?.open();
                },
              },
            ]}
            moreItems={[
              {
                label: '提交审核',
                show:
                  checkPermission(PermissionsMap.ContractRelocationIntentionSubmitAudit) &&
                  isNotSubmitOrReturn,
                onClick: () => handleSubmitAudit(id),
              },
              {
                label: '标记为已作废需退款',
                show: [
                  RelocationIntentionStatusEnum.意向中,
                  RelocationIntentionStatusEnum.已到期,
                  RelocationIntentionStatusEnum.已作废不退款,
                ].includes(status),
                onClick: () => handleUpdateStatus(RelocationIntentionStatusEnum.已作废需退款, id),
              },
              {
                label: '标记为已作废不退款',
                show: [
                  RelocationIntentionStatusEnum.意向中,
                  RelocationIntentionStatusEnum.已到期,
                  RelocationIntentionStatusEnum.已作废需退款,
                ].includes(status),
                onClick: () => handleUpdateStatus(RelocationIntentionStatusEnum.已作废不退款, id),
              },
              {
                label: '标记为已退款',
                show: [
                  RelocationIntentionStatusEnum.意向中,
                  RelocationIntentionStatusEnum.已到期,
                  RelocationIntentionStatusEnum.已作废不退款,
                  RelocationIntentionStatusEnum.已作废需退款,
                ].includes(status),
                onClick: () => handleUpdateStatus(RelocationIntentionStatusEnum.已退款, id),
              },
              {
                label: '删除',
                show:
                  checkPermission(PermissionsMap.ContractRelocationIntentionDelete) &&
                  isNotSubmitOrReturn,
                danger: true,
                onClick: () =>
                  modal.confirm({
                    title: '删除',
                    content: '确定要删除该合同吗？',
                    onOk: async () => {
                      await batchDeleteRelocationIntention([id]);
                      message.success('删除成功');
                      setSelectedRowKeys((prev) => prev.filter((i) => i !== id));
                      actionRef.current?.reload();
                    },
                  }),
              },
            ]}
          />
        );
      },
    },
  ];

  const batchMenuItems: MenuProps['items'] = [
    {
      label: '标记为已作废需退款',
      key: 'INVALID_REFUND',
      auth: PermissionsMap.ContractRelocationIntentionInvalidRefund,
      onClick: () => handleUpdateStatus(RelocationIntentionStatusEnum.已作废需退款),
    },
    {
      label: '标记为已作废不退款',
      key: 'INVALID_NO_REFUND',
      auth: PermissionsMap.ContractRelocationIntentionInvalidNoRefund,
      onClick: () => handleUpdateStatus(RelocationIntentionStatusEnum.已作废不退款),
    },
    {
      label: '标记为已退款',
      key: 'REFUNDED',
      auth: PermissionsMap.ContractRelocationIntentionSubmitRefund,
      onClick: () => handleUpdateStatus(RelocationIntentionStatusEnum.已退款),
    },
    {
      label: '删除',
      key: 'delete',
      danger: true,
      auth: PermissionsMap.ContractRelocationIntentionDelete,
      onClick: () =>
        modal.confirm({
          title: '删除',
          content: `确定要删除选中的 ${selectedRowKeys.length} 个合同吗？`,
          onOk: async () => {
            await batchDeleteRelocationIntention(selectedRowKeys);
            message.success('删除成功');
            setSelectedRowKeys([]);
            actionRef.current?.reload();
          },
        }),
    },
  ].filter((i) => checkPermission(i.auth));

  const headerNode = (
    <div className="sm:flex justify-between mb-3">
      <div className="self-end mb-2 sm:mb-0 pl-2">
        共 {data?.total || 0} 个合同
        {selectedRowKeys.length > 0 && (
          <>
            ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 个
            <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
              清空选择
            </a>
          </>
        )}
      </div>
      <div className="flex justify-end gap-2">
        {batchMenuItems.length > 0 && (
          <Dropdown
            disabled={!selectedRowKeys.length}
            menu={{
              items: batchMenuItems,
            }}
          >
            <Button type="text">
              批量操作 <CaretDownOutlined />
            </Button>
          </Dropdown>
        )}
        <Permission value={PermissionsMap.ContractRelocationIntentionCreate}>
          <Button
            type="text"
            onClick={() => {
              currentIdRef.current = null;
              createEditModalRef.current?.open();
            }}
          >
            新建
          </Button>
        </Permission>
        <Permission value={PermissionsMap.ContractRelocationIntentionExport}>
          <ExportButton
            total={selectedRowKeys.length || data?.total}
            request={() =>
              exportRelocationIntention({
                columns: actionRef.current!.getShowFields(),
                ...(selectedRowKeys.length
                  ? { ids: selectedRowKeys }
                  : getParamsFromFilters(actionRef.current?.getFilters())),
              })
            }
          />
        </Permission>
      </div>
    </div>
  );

  return (
    <PageContainer
      loading={getViewsLoading && <Skeleton paragraph={{ rows: 15 }} className="px-10" />}
    >
      <ETable
        rowKey="id"
        actionRef={actionRef}
        sticky
        bordered
        size="small"
        header={headerNode}
        views={views}
        pagination={{ showQuickJumper: true, showSizeChanger: true }}
        columns={columns}
        rowSelection={{
          fixed: true,
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        request={async ({ current, pageSize }, filters) => {
          const res = await getList({
            pageNum: current,
            pageSize,
            ...getParamsFromFilters(filters),
          });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <ShopDetailDrawer ref={shopDetailDrawerRef} getId={() => shopIdRef.current} />
      <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />
      <CreateEditRelocationIntentionModal
        ref={createEditModalRef}
        getId={() => currentIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
      <RelocationIntentionDetailDrawer
        ref={detailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
        onDelete={() => actionRef.current?.reload()}
      />
    </PageContainer>
  );
};

export default RelocationIntention;
