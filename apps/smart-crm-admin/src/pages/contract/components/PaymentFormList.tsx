import { PlusOutlined } from '@ant-design/icons';
import { FileUpload } from '@src/components';
import { getPaymentAccountList } from '@src/services/payment';
import { PaymentAccountListItemDTO, PaymentMannerEnum } from '@src/services/payment/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { Button, Card, Form, Input, InputNumber, Select, Typography } from 'antd';

const PaymentFormList = () => {
  const form = Form.useFormInstance();

  const { data: paymentAccountList, loading: paymentAccountListLoading } = useRequest(() =>
    getPaymentAccountList(),
  );

  return (
    <Form.List
      initialValue={[undefined]}
      name="payments"
      rules={[
        {
          validator(_, value) {
            if (Array.isArray(value) && value.length > 0) {
              return Promise.resolve();
            }

            return Promise.reject(new Error('至少需要添加一条打款记录'));
          },
        },
      ]}
    >
      {(fields, { add, remove }, { errors }) => (
        <>
          <span style={{ color: 'var(--ant-color-error)' }}>
            <Form.ErrorList errors={errors} />
          </span>

          <div className="flex flex-col gap-4 payment-card-wrapper">
            {fields.map(({ name, key }, index) => (
              <Card
                key={key}
                title={`打款记录 ${index + 1}`}
                classNames={{ header: '!min-h-10 !bg-gray-50 !text-sm', body: '!pb-0' }}
                extra={
                  fields.length > 1 && (
                    <Typography.Link type="danger" onClick={() => remove(name)}>
                      移除
                    </Typography.Link>
                  )
                }
              >
                <Form.Item
                  label="打款金额"
                  name={[name, 'paymentAmount']}
                  rules={[{ required: true, message: '请输入打款金额' }]}
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    placeholder="请输入打款金额"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
                <Form.Item
                  label="打款方式"
                  name={[name, 'paymentManner']}
                  rules={[{ required: true, message: '请选择打款方式' }]}
                >
                  <Select placeholder="请选择打款方式" options={enum2Options(PaymentMannerEnum)} />
                </Form.Item>
                {/* <EditableDateTime
                  editable
                  formItemProps={{
                    label: '打款时间',
                    name: [name, 'paymentTime'],
                  }}
                  fieldProps={{
                    // 兼容移动端
                    getPopupContainer: () => document.querySelector('.payment-card-wrapper')!,
                  }}
                /> */}
                <Form.Item
                  label="收款账户"
                  name={[name, 'receivingAccount']}
                  rules={[{ required: true, message: '请选择收款账户' }]}
                >
                  <Select
                    placeholder="请选择收款账户"
                    fieldNames={{ label: 'recipientAccountName', value: 'recipientAccountName' }}
                    options={paymentAccountList}
                    loading={paymentAccountListLoading}
                    onChange={(_value, option) => {
                      const { bankBranchName, recipientAccountNo } = (option ||
                        {}) as PaymentAccountListItemDTO;

                      form.setFieldsValue({
                        payments: {
                          [name]: {
                            receivingNumber: recipientAccountNo,
                            openBank: bankBranchName,
                          },
                        },
                      });
                    }}
                  />
                </Form.Item>
                <Form.Item noStyle shouldUpdate>
                  {({ getFieldValue }) =>
                    getFieldValue(['payments', name, 'receivingAccount']) && (
                      <>
                        <Form.Item
                          label="收款账号"
                          name={[name, 'receivingNumber']}
                          rules={[{ required: true, message: '请选择收款账号' }]}
                        >
                          <Input disabled placeholder="请选择收款账号" />
                        </Form.Item>
                        <Form.Item
                          label="开户行"
                          name={[name, 'openBank']}
                          rules={[{ required: true, message: '请选择开户行' }]}
                        >
                          <Input disabled placeholder="请选择开户行" />
                        </Form.Item>
                      </>
                    )
                  }
                </Form.Item>
                <Form.Item label="备注" name={[name, 'remark']}>
                  <Input.TextArea placeholder="请输入备注" maxLength={200} autoSize />
                </Form.Item>
                <FileUpload formItemProps={{ label: '打款凭证', name: [name, 'attachments'] }} />
              </Card>
            ))}
            {fields.length < 5 && (
              <Button type="dashed" icon={<PlusOutlined />} block onClick={() => add()}>
                添加打款记录
              </Button>
            )}
          </div>
        </>
      )}
    </Form.List>
  );
};

export default PaymentFormList;
