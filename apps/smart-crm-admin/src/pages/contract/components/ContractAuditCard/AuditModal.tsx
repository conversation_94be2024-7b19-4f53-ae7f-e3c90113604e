import React from 'react';
import { EyeOutlined } from '@ant-design/icons';
import { FileUpload } from '@src/components';
import EditableDateTime from '@src/components/Editable/EditableDateTime';
import { ChangeLegalPersonContractDTO } from '@src/services/contract/change-legal-person/type';
import { FormalContractDTO } from '@src/services/contract/formal/type';
import { passContractAudit, rejectContractAudit } from '@src/services/contract/intention';
import { IntentionContractDTO } from '@src/services/contract/intention/type';
import { RelocationFormalDTO } from '@src/services/contract/relocation-formal/type';
import { RelocationIntentionDTO } from '@src/services/contract/relocation-intention/type';
import { RenovationContractDTO } from '@src/services/contract/renovation/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import { useRequest } from 'ahooks';
import { App, Card, Form, Input, Modal, ModalProps, Popover } from 'antd';
import PaymentRecords from '../PaymentRecords';

interface AuditModalProps extends ModalProps {
  id: number;
  data?:
    | IntentionContractDTO
    | FormalContractDTO
    | RelocationIntentionDTO
    | RelocationFormalDTO
    | RenovationContractDTO
    | ChangeLegalPersonContractDTO;
  contractType: ContractTypeEnum;
  hidePayments?: boolean;
  type: 'pass' | 'reject';
  onSuccess: () => void;
  onTodoSuccess?: () => void;
}

const AuditModal: React.FC<AuditModalProps> = ({
  data,
  id,
  contractType,
  type,
  hidePayments,
  onSuccess,
  onTodoSuccess,
  ...props
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();

  const { runAsync: pass, loading: passLoading } = useRequest(passContractAudit, {
    manual: true,
  });
  const { runAsync: reject, loading: rejectLoading } = useRequest(rejectContractAudit, {
    manual: true,
  });

  const handlePassOrReject = async (values: any) => {
    if (type === 'pass') {
      await pass({ id, contractType, ...values });
      message.success('审批通过');
    } else {
      await reject({ id, contractType, ...values });
      message.success('驳回成功');
    }

    onSuccess();
    onTodoSuccess?.();
  };

  return (
    <Modal
      title={type === 'pass' ? '审批通过' : '驳回'}
      className="audit-modal"
      destroyOnHidden
      confirmLoading={passLoading || rejectLoading}
      styles={{
        body: { maxHeight: '60vh', overflow: 'auto', margin: '0 -24px', padding: '0 24px' },
      }}
      onOk={form.submit}
      {...props}
    >
      <Form form={form} clearOnDestroy labelCol={{ span: 6 }} onFinish={handlePassOrReject}>
        <Form.Item
          label="说明"
          name="description"
          labelCol={{ span: 3 }}
          rules={[{ required: true, message: '请输入说明' }]}
        >
          <Input.TextArea autoSize maxLength={200} showCount placeholder="请输入说明" />
        </Form.Item>
        {type === 'pass' && !hidePayments && (
          <Form.List name="payments" initialValue={data?.payments?.map((i) => ({ id: i.id }))}>
            {(fields) =>
              fields.map(({ key, name }, index) => (
                <Card
                  key={key}
                  title={
                    <Popover
                      destroyOnHidden
                      classNames={{
                        body: 'max-w-[300px]',
                      }}
                      content={
                        <PaymentRecords
                          inPopover
                          data={data?.payments?.filter((_, idx) => idx === index)}
                        />
                      }
                    >
                      <a className="text-sm">
                        打款记录 {index + 1} <EyeOutlined className="ml-1" />
                      </a>
                    </Popover>
                  }
                  className="mt-4"
                  classNames={{ header: '!min-h-10 !bg-gray-50', body: '!pb-0' }}
                >
                  <EditableDateTime
                    editable
                    formItemProps={{
                      label: '打款时间',
                      name: [name, 'paymentTime'],
                      rules: [{ required: true, message: '请选择打款时间' }],
                    }}
                    fieldProps={{
                      // 兼容移动端
                      getPopupContainer: () => document.querySelector('.audit-modal')!,
                    }}
                  />
                  <Form.Item
                    label="记账流水号"
                    name={[name, 'accountSerialNumber']}
                    rules={[{ required: true, message: '请输入记账流水号' }]}
                  >
                    <Input placeholder="请输入记账流水号" />
                  </Form.Item>
                  <Form.Item
                    label="打款方名称"
                    name={[name, 'payerName']}
                    validateFirst
                    validateTrigger={['onChange', 'onBlur']}
                    rules={[
                      { required: true, message: '请输入打款方名称' },
                      {
                        validateTrigger: 'onBlur',
                        validator: (_, value) => {
                          if (data?.toPublic) {
                            if (value !== data?.signSubject) {
                              return Promise.reject(
                                new Error(`与签约主体 “${data.signSubject}” 不一致`),
                              );
                            }
                          } else {
                            if (data && 'signer' in data && value !== data.signer) {
                              return Promise.reject(new Error(`与签约人 “${data?.signer}” 不一致`));
                            }
                          }

                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <Input placeholder="请输入打款方名称" />
                  </Form.Item>
                  <Form.Item
                    label="打款方账号"
                    name={[name, 'payerAccount']}
                    validateFirst
                    rules={[
                      { required: true, message: '请输入打款方账号' },
                      {
                        validator: (_, value) => {
                          if (Number.isNaN(Number(value))) {
                            return Promise.reject(new Error('请输入正确的打款方账号'));
                          }

                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <Input placeholder="请输入打款方账号" />
                  </Form.Item>
                  <FileUpload
                    formItemProps={{
                      label: '回单截图',
                      name: [name, 'receiptScreenshot'],
                      rules: [{ required: true, message: '请上传回单截图' }],
                    }}
                    uploadProps={{ maxCount: 1, accept: 'image/*' }}
                  />
                </Card>
              ))
            }
          </Form.List>
        )}
      </Form>
    </Modal>
  );
};

export default AuditModal;
