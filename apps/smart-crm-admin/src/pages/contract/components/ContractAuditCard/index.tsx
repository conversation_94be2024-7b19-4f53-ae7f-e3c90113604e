import React, { useState } from 'react';
import { AuditFlow } from '@src/components';
import { AuditOperationEnum, AuditStatusEnum } from '@src/services/common/type';
import { ChangeLegalPersonContractDTO } from '@src/services/contract/change-legal-person/type';
import { FormalContractDTO } from '@src/services/contract/formal/type';
import { getContractAuditHistory, revokeContractAudit } from '@src/services/contract/intention';
import { IntentionContractDTO } from '@src/services/contract/intention/type';
import { RelocationFormalDTO } from '@src/services/contract/relocation-formal/type';
import { RelocationIntentionDTO } from '@src/services/contract/relocation-intention/type';
import { RenovationContractDTO } from '@src/services/contract/renovation/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import useUserStore from '@src/store/useUserStore';
import { useRequest } from 'ahooks';
import { App } from 'antd';
import AuditModal from './AuditModal';

interface ContractAuditCardProps {
  id: number;
  loading: boolean;
  contractType: ContractTypeEnum;
  hidePayments?: boolean;
  data?:
    | IntentionContractDTO
    | FormalContractDTO
    | RelocationIntentionDTO
    | RelocationFormalDTO
    | RenovationContractDTO
    | ChangeLegalPersonContractDTO;
  onTodoSuccess?: () => void;
  onSuccess: () => void;
}

export interface ContractAuditCardRef {
  refresh: () => void;
}

const ContractAuditCard = React.forwardRef<ContractAuditCardRef, ContractAuditCardProps>(
  ({ id, loading, contractType, hidePayments, data, onTodoSuccess, onSuccess }, ref) => {
    const { modal, message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [type, setType] = useState<'pass' | 'reject'>('pass');

    const {
      user: { shUserId },
    } = useUserStore();
    const {
      data: auditHistory = [],
      loading: getAuditHistoryLoading,
      refresh,
    } = useRequest(() => getContractAuditHistory({ id, contractType }));

    React.useImperativeHandle(ref, () => ({
      refresh,
    }));

    const handleRevoke = () => {
      modal.confirm({
        title: '撤回审核',
        content: '确定要撤回该审核吗？',
        onOk: async () => {
          await revokeContractAudit({ id, contractType });
          message.success('撤回成功');
          refresh();
          onSuccess();
        },
      });
    };

    const isAuditStatus = data?.auditStatus === AuditStatusEnum.AUDIT;
    // 是否是审批人
    const isAuditUser = !!auditHistory[auditHistory.length - 1]?.auditUserIds?.includes(shUserId);
    // 是否是提交人
    const isSubmitter = shUserId === auditHistory[0]?.auditUserId;

    return (
      <>
        <AuditFlow.OperateCard
          loading={loading || getAuditHistoryLoading}
          data={auditHistory}
          renderItem={(item, defaultRenderItem) => {
            // 合同的不通过其实是驳回
            if (item.operationStatus === AuditOperationEnum.审核不通过) {
              return (
                <>
                  <div className="flex justify-between">
                    <span>{item.auditTime}</span>
                    <span className="font-bold text-red-500">驳回</span>
                  </div>
                  <p>{item.auditUserName}驳回了审批</p>
                  {item.description && <p>说明：{item.description}</p>}
                </>
              );
            }

            return defaultRenderItem(item);
          }}
          operateButtons={[
            {
              show: isAuditStatus && isAuditUser,
              children: '通过',
              onClick: () => {
                setType('pass');
                setOpen(true);
              },
            },
            {
              show: isAuditStatus && isAuditUser,
              children: '驳回',
              danger: true,
              onClick: () => {
                setType('reject');
                setOpen(true);
              },
            },
            {
              show: isAuditStatus && isSubmitter,
              children: '撤回',
              className: 'ml-auto',
              onClick: handleRevoke,
            },
          ]}
        />

        <AuditModal
          id={id}
          open={open}
          data={data}
          contractType={contractType}
          type={type}
          hidePayments={hidePayments}
          onTodoSuccess={onTodoSuccess}
          onSuccess={() => {
            setOpen(false);
            refresh();
            onSuccess();
          }}
          onCancel={() => setOpen(false)}
        />
      </>
    );
  },
);

export default ContractAuditCard;
