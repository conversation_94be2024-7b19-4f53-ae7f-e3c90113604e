import { FileList } from '@src/components';
import { delContractFile } from '@src/services/contract';
import { Empty, message } from 'antd';

type IProps = {
  // onReload?: () => void;
  data: {
    fileUrl: string;
    fileType: string;
    fileName: string;
  }[];
  onRefresh: () => void;
};

const ContractFileList: React.FC<IProps> = ({ data, onRefresh }) => {
  if (!data || !data.length) {
    return <Empty />;
  }

  return (
    <>
      <FileList
        allowPreviewVideo={false}
        data={data || []}
        listType="text"
        removeFile={async (e) => {
          await delContractFile(e.id);
          message.success('删除成功');
          onRefresh();
        }}
      />
    </>
  );
};

export default ContractFileList;
