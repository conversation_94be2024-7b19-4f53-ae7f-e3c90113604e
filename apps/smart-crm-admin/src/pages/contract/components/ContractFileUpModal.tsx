import React, { useState } from 'react';
import { FileUpload } from '@src/components';
import { addContractFile, ContractEnum } from '@src/services/contract';
import { useRequest } from 'ahooks';
import { App, Button, Form, Modal, ModalProps } from 'antd';

interface FollowUpModalProps extends ModalProps {
  getId: () => number | null;
  contractType: ContractEnum;
  maxCount: number;
  onSuccess?: () => void;
}

const ContractFileUpModal: React.FC<FollowUpModalProps> = ({
  contractType,
  maxCount,
  getId,
  onSuccess,
  ...props
}) => {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const [open, setOpen] = useState(false);

  const contractId = getId();

  const { runAsync, loading } = useRequest(addContractFile, { manual: true });

  const handleSave = async (values: any) => {
    await runAsync({
      contractId,
      type: contractType,
      ...values,
    });
    message.success('已上传');
    setOpen(false);
    onSuccess?.();
  };

  return (
    <>
      <Button
        onClick={() => {
          setOpen(true);
        }}
      >
        上传合同文件
      </Button>
      <Modal
        title="上传合同文件"
        destroyOnHidden
        confirmLoading={loading}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
        styles={{
          body: {
            paddingTop: '30px',
          },
        }}
        {...props}
        open={open}
      >
        <Form form={form} clearOnDestroy labelCol={{ span: 4 }} onFinish={handleSave}>
          <FileUpload
            formItemProps={{
              label: '合同文件',
              name: 'attachments',
              rules: [{ required: true, message: '请上传合同文件' }],
            }}
            uploadProps={{ accept: '.pdf', maxCount }}
          />
        </Form>
      </Modal>
    </>
  );
};

export default ContractFileUpModal;
