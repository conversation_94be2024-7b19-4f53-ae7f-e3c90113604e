import React from 'react';
import { Editable } from '@src/components';
import { ValueType } from '@src/components/ETable';
import { IntentionContractDTO } from '@src/services/contract/intention/type';
import { PaymentMannerEnum } from '@src/services/payment/type';
import { enum2Options } from '@src/utils';
import { Descriptions, Empty } from 'antd';

interface PaymentRecordsProps {
  data?: IntentionContractDTO['payments'];
  inPopover?: boolean;
}

const PaymentRecords: React.FC<PaymentRecordsProps> = ({ data, inPopover }) => {
  const paymentInfoItems = [
    {
      field: 'paymentAmount',
      label: '打款金额',
    },
    {
      field: 'paymentManner',
      label: '打款方式',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(PaymentMannerEnum),
      },
    },
    {
      field: 'paymentTime',
      label: '打款时间',
    },
    {
      field: 'receivingAccount',
      label: '收款账户',
    },
    {
      field: 'receivingNumber',
      label: '收款账号',
    },
    {
      field: 'openBank',
      label: '开户行',
    },
    {
      field: 'attachments',
      label: '打款凭证',
      valueType: ValueType.ATTACHMENT,
    },
    {
      field: 'accountSerialNumber',
      label: '记账流水号',
    },
    {
      label: '打款方名称',
      field: 'payerName',
    },
    {
      label: '打款方账号',
      field: 'payerAccount',
    },
    {
      label: '回单截图',
      field: 'receiptScreenshotAttachment',
      valueType: ValueType.ATTACHMENT,
    },
    {
      field: 'remark',
      label: '备注',
    },
  ];

  return (
    <>
      {!inPopover && <p className="font-semibold text-base mt-5">打款记录</p>}
      {data && data.length > 0 ? (
        data.map((item, index) => (
          <React.Fragment key={item.id}>
            {!inPopover && (
              <div className="bg-gray-100 rounded-lg inline-block px-2 py-1 my-2">
                打款记录 {index + 1}
              </div>
            )}
            <Descriptions
              column={inPopover ? 1 : { xs: 1, sm: 1, md: 2, lg: 2, xl: 2, xxl: 2 }}
              items={paymentInfoItems.map((i) => ({
                label: i.label,
                key: i.field,
                children: (
                  <Editable
                    valueType={i.valueType}
                    initialValue={item?.[i.field as keyof typeof item]}
                    fieldProps={i.fieldProps}
                  />
                ),
              }))}
            />
          </React.Fragment>
        ))
      ) : (
        <Empty />
      )}
    </>
  );
};

export default PaymentRecords;
