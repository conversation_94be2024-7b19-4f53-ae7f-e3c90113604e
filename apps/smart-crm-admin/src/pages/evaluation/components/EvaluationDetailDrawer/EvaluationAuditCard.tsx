import React, { useState } from 'react';
import { AuditFlow } from '@src/components';
import useSimpleForm from '@src/hooks/useSimpleFormModal';
import { AuditStatusEnum } from '@src/services/common/type';
import {
  approveEvaluationTask,
  getEvaluationAuditHistory,
  rejectEvaluationTask,
} from '@src/services/evaluation';
import useUserStore from '@src/store/useUserStore';
import { useRequest } from 'ahooks';
import { App, Input } from 'antd';

interface EvaluationAuditCardProps {
  id: number;
  loading: boolean;
  auditStatus?: AuditStatusEnum;
  onSuccess: () => void;
}

export interface EvaluationAuditCardRef {
  refresh: () => void;
}

const EvaluationAuditCard = React.forwardRef<EvaluationAuditCardRef, EvaluationAuditCardProps>(
  ({ id, loading, auditStatus, onSuccess }, ref) => {
    const { message } = App.useApp();
    const [auditModalType, setAuditModalType] = useState<'approve' | 'reject'>('approve');

    const {
      user: { shUserId },
    } = useUserStore();
    const {
      data: auditHistory = [],
      loading: getAuditHistoryLoading,
      refresh,
    } = useRequest(() => getEvaluationAuditHistory(id));
    const { runAsync: approve, loading: approveLoading } = useRequest(approveEvaluationTask, {
      manual: true,
    });
    const { runAsync: reject, loading: rejectLoading } = useRequest(rejectEvaluationTask, {
      manual: true,
    });

    const { modalNode: auditModalNode, setOpen: setAuditModalOpen } = useSimpleForm({
      modalProps: {
        title: { approve: '通过', reject: '拒绝' }[auditModalType],
        confirmLoading: approveLoading || rejectLoading,
      },
      formProps: {
        onFinish: async (values) => {
          if (auditModalType === 'approve') {
            await approve({
              recordId: id,
              ...values,
            });
          } else {
            await reject({
              recordId: id,
              ...values,
            });
          }

          message.success('审批成功');
          setAuditModalOpen(false);
          refresh();
          onSuccess();
        },
      },
      formItems: [
        {
          name: 'comment',
          label: '说明',
          rules: [{ required: true, message: '请输入说明' }],
          children: <Input.TextArea placeholder="请输入说明" maxLength={500} showCount />,
        },
      ],
    });

    React.useImperativeHandle(ref, () => ({
      refresh,
    }));

    const isAuditStatus = auditStatus === AuditStatusEnum.AUDIT;
    // 是否是审批人
    const isAuditUser = !!auditHistory[auditHistory.length - 1]?.auditUserIds?.includes(shUserId);

    return (
      <>
        <AuditFlow.OperateCard
          loading={loading || getAuditHistoryLoading}
          data={auditHistory}
          operateButtons={[
            {
              show: isAuditStatus && isAuditUser,
              children: '通过',
              onClick: () => {
                setAuditModalOpen(true);
                setAuditModalType('approve');
              },
            },
            {
              show: isAuditStatus && isAuditUser,
              children: '拒绝',
              danger: true,
              onClick: () => {
                setAuditModalOpen(true);
                setAuditModalType('reject');
              },
            },
          ]}
        />

        {auditModalNode}
      </>
    );
  },
);

export default EvaluationAuditCard;
