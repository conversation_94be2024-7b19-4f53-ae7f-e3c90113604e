import React, { useRef, useState } from 'react';
import { evaluationAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { AuditFlow, ButtonGroup } from '@src/components';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import useCheckVisibleAuth from '@src/hooks/useCheckVisibleAuth';
import BusinessDetailDrawer from '@src/pages/business-opportunity/components/BusinessDetailDrawer';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import ExamRecord from '@src/pages/franchise-application/components/ExamRecord';
import CreateEditEvaluationDrawer from '@src/pages/visit/components/CreateEditEvaluationDrawer';
import VisitDetailDrawer from '@src/pages/visit/components/VisitDetailDrawer';
import { BusinessOpportunityTypeEnum } from '@src/services/business-opportunity/type';
import { AuditStatusEnum, BusinessTypeEnum } from '@src/services/common/type';
import { getEvaluationAuditAllHistory, reApproval } from '@src/services/evaluation';
import {
  getExamAnswerRecordDetail,
  getExamRecordItemQuestions,
} from '@src/services/franchise-application';
import { ExamAnswerDTO } from '@src/services/franchise-application/type';
import { EvaluationRejectReasonEnum, SuggestionEnum } from '@src/services/visit/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Card, Col, Drawer, Row, Tabs } from 'antd';
import EvaluationAuditCard, { EvaluationAuditCardRef } from './EvaluationAuditCard';

interface EvaluationDetailDrawerProps {
  getId: () => number | null | undefined;
  onUpdate?: () => void;
  onTodoSuccess?: () => void;
}

const businessType = BusinessTypeEnum.INTERVIEW_EVALUATION;

const EvaluationDetailDrawer = React.forwardRef<ModalRef, EvaluationDetailDrawerProps>(
  ({ getId, onUpdate, onTodoSuccess, ...drawerProps }, ref) => {
    const { modal, message } = App.useApp();
    const [open, setOpen] = useState(false);
    const customerDetailDrawerRef = useRef<ModalRef>(null);
    const businessOpportunityDrawerRef = useRef<ModalRef>(null);
    const createEditEvaluationDrawerRef = useRef<ModalRef>(null);
    const visitDetailDrawerRef = useRef<ModalRef>(null);
    const auditCardRef = useRef<EvaluationAuditCardRef>(null);

    const id = getId()!;

    const checkVisibleAuth = useCheckVisibleAuth();
    const { data: users } = useAllUsers({ ready: open });
    const { data: questionData, loading: getQuestionDataLoading } = useRequest(
      () => getExamRecordItemQuestions({ businessType, recordId: id }),
      {
        ready: open,
      },
    );
    const { data, loading, refresh } = useRequest(
      () =>
        getExamAnswerRecordDetail({
          businessType,
          recordId: id,
        }),
      { ready: open },
    );

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const baseInfoItems: DescriptionFieldType<ExamAnswerDTO>[] = [
      {
        label: '面审评估编号',
        field: 'code',
      },
      {
        label: '面审负责人',
        field: 'directUserId',
        valueType: ValueType.PERSON,
      },
      {
        label: '商机名称',
        field: 'businessOpportunityName',
        children: (
          <a onClick={() => businessOpportunityDrawerRef.current?.open()}>
            {data?.businessOpportunityName}
          </a>
        ),
      },
      {
        label: '到访记录编号',
        field: 'interviewVisitRecordCode',
        children: (
          <a onClick={() => visitDetailDrawerRef.current?.open()}>
            {data?.interviewVisitRecordCode}
          </a>
        ),
      },
      {
        label: '申请人是否通过',
        field: 'applyUserIsPass',
        hidden: data?.businessOpportunityType === BusinessOpportunityTypeEnum.SOCIETY,
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        label: '店长是否通过',
        field: 'storeManagerIsPass',
        hidden: data?.businessOpportunityType === BusinessOpportunityTypeEnum.SOCIETY,
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        label: '面审建议',
        field: 'suggestion',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(SuggestionEnum),
        },
      },
      {
        label: '面审评语',
        field: 'comments',
      },
      {
        label: '面审最终结果',
        field: 'auditStatus',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: evaluationAuditStatusOptions,
        },
      },
      {
        label: '未通过原因',
        field: 'rejectReasons',
        valueType: ValueType.MULTIPLE,
        fieldProps: {
          options: enum2Options(EvaluationRejectReasonEnum),
        },
      },
    ];

    return (
      <Drawer
        title="面审评估详情"
        width={1200}
        open={open}
        push={false}
        destroyOnHidden
        onClose={() => setOpen(false)}
        {...drawerProps}
      >
        <Card loading={loading}>
          <div className="flex justify-between">
            <div className="flex flex-col gap-2">
              <p>
                客户名称：
                <a onClick={() => customerDetailDrawerRef.current?.open()}>{data?.customerName}</a>
              </p>
              <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
              <p>创建时间：{data?.createTime}</p>
              <p>更新人：{users?.find((i) => i.id === data?.updateUserId)?.name}</p>
              <p>更新时间：{data?.updateTime}</p>
            </div>
            <ButtonGroup
              items={[
                {
                  label: '编辑',
                  onClick: () => createEditEvaluationDrawerRef.current?.open(),
                },
                {
                  label: '发起审批',
                  show:
                    data?.auditStatus === AuditStatusEnum.NO_PASS &&
                    checkVisibleAuth(data.directUserId),
                  onClick: () =>
                    modal.confirm({
                      title: '发起审批',
                      content: '确定要重新发起审批吗？',
                      onOk: async () => {
                        await reApproval(id);
                        message.success('操作成功');
                        refresh();
                        auditCardRef.current?.refresh();
                        onUpdate?.();
                        onTodoSuccess?.();
                      },
                    }),
                },
              ]}
            />
          </div>
        </Card>

        <Row gutter={[20, 20]} className="mt-5">
          <Col span={24} xl={16}>
            <Card loading={loading}>
              <Tabs
                className="-mt-5"
                items={[
                  {
                    label: '详细资料',
                    key: 'info',
                    children: (
                      <>
                        {renderDescriptions(baseInfoItems, data)}
                        <ExamRecord
                          data={data}
                          questionData={questionData}
                          loading={getQuestionDataLoading}
                        />
                      </>
                    ),
                  },
                  {
                    label: '审批记录',
                    key: 'audit-history',
                    children: <AuditFlow.Record request={() => getEvaluationAuditAllHistory(id)} />,
                  },
                ]}
              />
            </Card>
          </Col>
          <Col span={24} xl={8}>
            <EvaluationAuditCard
              ref={auditCardRef}
              id={id}
              loading={loading}
              auditStatus={data?.auditStatus}
              onSuccess={() => {
                refresh();
                onUpdate?.();
                onTodoSuccess?.();
              }}
            />
          </Col>
        </Row>

        <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => data?.customerId} />
        <BusinessDetailDrawer
          ref={businessOpportunityDrawerRef}
          getId={() => data?.businessOpportunityId}
        />
        <VisitDetailDrawer ref={visitDetailDrawerRef} getId={() => data?.interviewVisitRecordId} />
        <CreateEditEvaluationDrawer
          ref={createEditEvaluationDrawerRef}
          isEdit
          getId={() => id}
          onSuccess={() => {
            onUpdate?.();
            refresh();
          }}
        />
      </Drawer>
    );
  },
);

export default EvaluationDetailDrawer;
