import React, { useState } from 'react';
import { ImportFileFormItem } from '@src/components';
import { BusinessTypeEnum } from '@src/services/common/type';
import { downloadEvaluationExcelTemplate, importEvaluationExcel } from '@src/services/evaluation';
import { ExamStatusEnum } from '@src/services/standard-settings/template/type';
import { getExamAllList } from '@src/services/visit';
import { useRequest } from 'ahooks';
import { Button, Form, Modal, Select } from 'antd';

interface EvaluationImportButtonProps {
  onSuccess: () => void;
}

const EvaluationImportButton: React.FC<EvaluationImportButtonProps> = ({ onSuccess }) => {
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);

  const { showImportPrompt } = ImportFileFormItem.usePrompt();
  const { data: examList, loading: getExamListLoading } = useRequest(
    () =>
      getExamAllList({
        businessType: BusinessTypeEnum.INTERVIEW_EVALUATION,
        status: ExamStatusEnum.ENABLE,
      }),
    {
      ready: open,
    },
  );
  const { runAsync: importExcel, loading: importExcelLoading } = useRequest(importEvaluationExcel, {
    manual: true,
  });

  const handleSave = async (values: any) => {
    const res = await importExcel(values);

    showImportPrompt(res, onSuccess);

    setOpen(false);
  };

  return (
    <>
      <Button type="text" onClick={() => setOpen(true)}>
        导入
      </Button>
      <Modal
        title="导入"
        destroyOnHidden
        open={open}
        confirmLoading={importExcelLoading}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
      >
        <Form form={form} clearOnDestroy onFinish={handleSave}>
          <Form.Item
            label="模版"
            name="examPaperId"
            rules={[{ required: true, message: '请选择模版' }]}
          >
            <Select
              placeholder="请选择模版"
              loading={getExamListLoading}
              options={examList}
              fieldNames={{ label: 'name', value: 'id' }}
            />
          </Form.Item>
          <ImportFileFormItem
            downloadTemplate={async () => {
              const { examPaperId } = await form.validateFields(['examPaperId']);

              return downloadEvaluationExcelTemplate({ examPaperId });
            }}
          />
        </Form>
      </Modal>
    </>
  );
};

export default EvaluationImportButton;
