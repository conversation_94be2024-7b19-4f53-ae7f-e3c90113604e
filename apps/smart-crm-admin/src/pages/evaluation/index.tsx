import { useEffect, useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { evaluationAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ETable, ExportButton, TableActions } from '@src/components';
import { ETableActionType, ETableColumn, ValueType } from '@src/components/ETable';
import useTableViews from '@src/hooks/useTableViews';
import { useNoticeEventSubscription } from '@src/layout';
import { Permission, PermissionsMap } from '@src/permissions';
import { BusinessTypeEnum } from '@src/services/common/type';
import {
  exportExamAnswerRecord,
  getExamAnswerList,
  getExamAnswerQuestions,
} from '@src/services/franchise-application';
import { ExamAnswerDTO } from '@src/services/franchise-application/type';
import { EvaluationRejectReasonEnum, SuggestionEnum } from '@src/services/visit/type';
import { compatibleTableActionWidth, enum2Options, getParamsFromFilters } from '@src/utils';
import { useRequest } from 'ahooks';
import { Skeleton } from 'antd';
import { useSearchParams } from 'react-router-dom';
import EvaluationDetailDrawer from './components/EvaluationDetailDrawer';
import EvaluationImportButton from './components/EvaluationImportButton';
import BusinessDetailDrawer from '../business-opportunity/components/BusinessDetailDrawer';
import CustomerDetailDrawer from '../customers/components/CustomerDetailDrawer';
import { getQuestionColumns } from '../franchise-application/utils';
import CreateEditEvaluationDrawer from '../visit/components/CreateEditEvaluationDrawer';
import VisitDetailDrawer from '../visit/components/VisitDetailDrawer';

const businessType = BusinessTypeEnum.INTERVIEW_EVALUATION;

const Evaluation = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const actionRef = useRef<ETableActionType>(null);
  const currentIdRef = useRef<number | null>(null);
  const customerIdRef = useRef<number | null>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const businessDetailDrawerRef = useRef<ModalRef>(null);
  const businessIdRef = useRef<number | null>(null);
  const evaluationDetailDrawerRef = useRef<ModalRef>(null);
  const visitDetailDrawerRef = useRef<ModalRef>(null);
  const visitIdRef = useRef<number | null>(null);
  const createEditEvaluationDrawerRef = useRef<ModalRef>(null);

  const { data: questions = [], loading: getQuestionsLoading } = useRequest(() =>
    getExamAnswerQuestions(businessType),
  );
  const { data, runAsync: getList } = useRequest(getExamAnswerList, { manual: true });
  const { views, loading: getViewsLoading } = useTableViews(businessType);

  useNoticeEventSubscription((event) => {
    if (event.type === 'evaluation' && data?.result.some((i) => i.id === event.payload.id)) {
      actionRef.current?.reload();
    }
  });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      currentIdRef.current = Number(id);
      evaluationDetailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: ETableColumn<ExamAnswerDTO>[] = [
    {
      title: '面审评估表编号',
      dataIndex: 'code',
      fixed: 'left',
      valueType: ValueType.TEXT,
      render: (value, { id }) => (
        <a
          onClick={() => {
            currentIdRef.current = id;
            evaluationDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '客户姓名',
      dataIndex: 'customerName',
      valueType: ValueType.TEXT,
      render: (value, { customerId }) => (
        <a
          onClick={() => {
            customerIdRef.current = customerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '商机名称',
      dataIndex: 'businessOpportunityName',
      valueType: ValueType.TEXT,
      render: (value, { businessOpportunityId }) => (
        <a
          onClick={() => {
            businessIdRef.current = businessOpportunityId;
            businessDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '商机编号',
      dataIndex: 'businessOpportunityCode',
      valueType: ValueType.TEXT,
    },
    {
      title: '到访记录编号',
      dataIndex: 'interviewVisitRecordCode',
      valueType: ValueType.TEXT,
      render: (value, { interviewVisitRecordId }) => (
        <a
          onClick={() => {
            visitIdRef.current = interviewVisitRecordId;
            visitDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '面审负责人',
      dataIndex: 'directUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '申请人是否通过',
      dataIndex: 'applyUserIsPass',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: yesOrNoOptions,
      },
      filterProps: {
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
      },
    },
    {
      title: '店长是否通过',
      dataIndex: 'storeManagerIsPass',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: yesOrNoOptions,
      },
      filterProps: {
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
      },
    },
    {
      title: '面审建议',
      dataIndex: 'suggestion',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: enum2Options(SuggestionEnum),
      },
      filterProps: {
        options: enum2Options(SuggestionEnum),
      },
    },
    {
      title: '未通过原因',
      dataIndex: 'rejectReasons',
      valueType: ValueType.MULTIPLE,
      fieldProps: {
        options: enum2Options(EvaluationRejectReasonEnum),
      },
      filterProps: {
        options: enum2Options(EvaluationRejectReasonEnum),
      },
    },
    {
      title: '面审评语',
      dataIndex: 'comments',
      valueType: ValueType.TEXT,
    },
    {
      title: '面审最终结果',
      dataIndex: 'auditStatus',
      valueType: ValueType.SINGLE,
      fieldProps: {
        options: evaluationAuditStatusOptions,
      },
      filterProps: {
        options: evaluationAuditStatusOptions,
      },
    },
    {
      title: '更新人',
      dataIndex: 'updateUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: ValueType.DATE_TIME,
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      valueType: ValueType.PERSON,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: ValueType.DATE_TIME,
    },
    ...getQuestionColumns(questions),
    {
      title: '操作',
      key: 'action',
      align: 'center',
      fixed: 'right',
      width: compatibleTableActionWidth(100),
      hideInFilters: true,
      hideInSettings: true,
      render: (_, { id }) => (
        <TableActions
          shortcuts={[
            {
              label: '编辑',
              onClick: () => {
                currentIdRef.current = id;
                createEditEvaluationDrawerRef.current?.open();
              },
            },
          ]}
        />
      ),
    },
  ];

  const headerNode = (
    <div className="flex justify-between mb-3">
      <div className="ml-2 flex items-end">
        共 {data?.total || 0} 条记录
        {selectedRowKeys.length > 0 && (
          <>
            ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 条
            <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
              清空选择
            </a>
          </>
        )}
      </div>
      <div className="flex gap-2">
        <EvaluationImportButton onSuccess={() => actionRef.current?.reload()} />
        <Permission value={PermissionsMap.EvaluationExport}>
          <ExportButton
            total={selectedRowKeys.length || data?.total}
            request={() =>
              exportExamAnswerRecord({
                businessType,
                columns: actionRef.current!.getShowFields(),
                ...(selectedRowKeys.length
                  ? { ids: selectedRowKeys }
                  : getParamsFromFilters(actionRef.current?.getFilters())),
              })
            }
          />
        </Permission>
      </div>
    </div>
  );

  return (
    <>
      <PageContainer
        loading={
          (getViewsLoading || getQuestionsLoading) && (
            <Skeleton paragraph={{ rows: 15 }} className="px-10" />
          )
        }
      >
        <ETable
          rowKey="id"
          sticky
          actionRef={actionRef}
          header={headerNode}
          pagination={{ showSizeChanger: true, showQuickJumper: true }}
          views={views}
          columns={columns}
          rowSelection={{
            fixed: true,
            preserveSelectedRowKeys: true,
            selectedRowKeys,
            onChange: (keys) => setSelectedRowKeys(keys as number[]),
          }}
          request={async ({ current, pageSize }, filters) => {
            const res = await getList({
              businessType,
              pageNum: current,
              pageSize,
              ...getParamsFromFilters(filters),
            });

            return {
              data: res.result,
              total: res.total,
            };
          }}
        />

        <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />
        <BusinessDetailDrawer ref={businessDetailDrawerRef} getId={() => businessIdRef.current} />
        <VisitDetailDrawer ref={visitDetailDrawerRef} getId={() => visitIdRef.current} />
        <CreateEditEvaluationDrawer
          ref={createEditEvaluationDrawerRef}
          isEdit
          getId={() => currentIdRef.current}
          onSuccess={() => actionRef.current?.reload()}
        />
      </PageContainer>

      <EvaluationDetailDrawer
        ref={evaluationDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
      />
    </>
  );
};

export default Evaluation;
