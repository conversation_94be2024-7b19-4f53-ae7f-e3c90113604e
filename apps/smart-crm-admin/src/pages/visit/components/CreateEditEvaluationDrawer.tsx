import React, { useMemo, useState } from 'react';
import { yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import useAllUsers from '@src/hooks/useAllUsers';
import EditableExam from '@src/pages/franchise-application/components/EditableExam';
import { BusinessOpportunityTypeEnum } from '@src/services/business-opportunity/type';
import { BusinessTypeEnum } from '@src/services/common/type';
import { getIsStaffByCustomerId } from '@src/services/customers';
import {
  getExamAnswerRecordDetail,
  getExamRecordItemQuestions,
  updateExamAnswerRecord,
} from '@src/services/franchise-application';
import { ExamStatusEnum } from '@src/services/standard-settings/template/type';
import {
  createEvaluationAnswers,
  getEvaluationQuestions,
  getExamAllList,
  getVisitDetail,
} from '@src/services/visit';
import { EvaluationRejectReasonEnum, SuggestionEnum } from '@src/services/visit/type';
import { enum2Options } from '@src/utils';
import { useRequest } from 'ahooks';
import { App, Button, Drawer, DrawerProps, Form, FormProps, Input, Select } from 'antd';

interface CreateEditEvaluationDrawerProps extends DrawerProps {
  isEdit?: boolean;
  getId: () => number | null;
  onSuccess: () => void;
}

const businessType = BusinessTypeEnum.INTERVIEW_EVALUATION;

const CreateEditEvaluationDrawer = React.forwardRef<ModalRef, CreateEditEvaluationDrawerProps>(
  ({ isEdit, getId, onSuccess, ...props }, ref) => {
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    const [activeKey, setActiveKey] = useState<number[]>([]);

    const id = getId()!;

    const { data: users } = useAllUsers({ ready: open });
    // 评估表列表
    const { data: examList, loading: getExamListLoading } = useRequest(
      () =>
        getExamAllList({
          businessType,
          status: ExamStatusEnum.ENABLE,
        }),
      {
        ready: open && !isEdit,
      },
    );
    // 题目列表
    const {
      run: getCreateQuestionData,
      data: createQuestionData,
      loading: getCreateQuestionDataLoading,
      mutate: mutateCreateQuestionData,
    } = useRequest(getEvaluationQuestions, {
      manual: true,
      onSuccess: (res) => {
        if (!isEdit) {
          setActiveKey(res.pageList.map((i) => i.pageId));
        }
      },
    });
    const { data: visitDetail, loading: getVisitDetailLoading } = useRequest(
      () => getVisitDetail(id),
      { ready: open && !isEdit },
    );
    const { data: evaluationDetail, loading: getEvaluationDetailLoading } = useRequest(
      () =>
        getExamAnswerRecordDetail({
          businessType,
          recordId: id,
        }),
      {
        ready: open && !!isEdit,
        onSuccess: (res) => {
          form.setFieldsValue(res);
        },
      },
    );
    const { data: editQuestionData, loading: getEditQuestionDataLoading } = useRequest(
      () => getExamRecordItemQuestions({ businessType, recordId: id }),
      {
        ready: open && !!isEdit,
      },
    );
    const { runAsync: create, loading: createLoading } = useRequest(createEvaluationAnswers, {
      manual: true,
    });
    const { runAsync: update, loading: updateLoading } = useRequest(updateExamAnswerRecord, {
      manual: true,
    });
    const { runAsync: checkIsStaff, loading: checkIsStaffLoading } = useRequest(
      getIsStaffByCustomerId,
      { manual: true },
    );

    const businessOpportunityType = isEdit
      ? evaluationDetail?.businessOpportunityType
      : visitDetail?.businessOpportunityType;

    // 抹平新建和编辑试卷结构
    const questionData = useMemo(() => {
      if (isEdit) {
        return editQuestionData;
      } else {
        return createQuestionData?.pageList.map((item) => ({
          ...item,
          questions: item.questionList.map((i) => ({
            ...i,
            name: i.questionName,
            id: i.questionId,
          })),
        }));
      }
    }, [createQuestionData?.pageList, editQuestionData, isEdit]);

    React.useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true);
        setActiveKey([]);
        mutateCreateQuestionData(undefined);
      },
      close: () => setOpen(false),
    }));

    const handleSave = async ({
      suggestion,
      rejectReasons,
      comments,
      auditStatus,
      examPaperId,
      applyUserIsPass,
      storeManagerIsPass,
      ...questionValues
    }: any) => {
      const sharedParams = {
        suggestion,
        rejectReasons,
        comments,
        auditStatus,
        applyUserIsPass,
        storeManagerIsPass,
        questionAnswers: Object.keys(questionValues).map((questionKey) => ({
          questionKey,
          answer: Array.isArray(questionValues[questionKey])
            ? JSON.stringify(questionValues[questionKey])
            : questionValues[questionKey],
        })),
      };

      if (isEdit) {
        await update({
          ...sharedParams,
          businessType,
          recordId: id,
        });
      } else {
        const { staff } = await checkIsStaff(visitDetail?.customerId!);

        // 仅进行提示，允许保存
        if (staff) {
          message.warning('当前客户入职过塔斯汀不可加盟');
        }

        await create({ ...sharedParams, examPaperId, interviewVisitId: id });
      }

      message.success('保存成功');
      setOpen(false);
      onSuccess();
    };

    const onFinishFailed = (errorInfo: Parameters<Required<FormProps>['onFinishFailed']>[0]) => {
      const [{ name }] = errorInfo.errorFields;
      const pageId = questionData?.find((item) =>
        item.questions.some((question) => question.questionKey === name[0]),
      )?.pageId as number;

      // 如果校验失败的第一个问题没有展开，先展开再滚动到它的位置
      if (!activeKey.includes(pageId)) {
        setActiveKey((prev) => prev.concat(pageId));
        message.error('有内容校验未通过');
        setTimeout(() => {
          form.scrollToField(name);
        }, 500);
      } else {
        form.scrollToField(name);
      }
    };

    const drawerLoading =
      getVisitDetailLoading || getEvaluationDetailLoading || getEditQuestionDataLoading;

    const suggestionOptions = enum2Options(SuggestionEnum);

    return (
      <Drawer
        open={open}
        width={500}
        loading={drawerLoading}
        title={isEdit ? '编辑面审评估' : '新建面审评估'}
        destroyOnHidden
        drawerRender={(node) => (
          <Form
            className="h-full"
            form={form}
            clearOnDestroy
            layout="vertical"
            labelCol={{ span: 7 }}
            preserve={false}
            onFinish={handleSave}
            onFinishFailed={onFinishFailed}
          >
            {node}
          </Form>
        )}
        footer={
          !drawerLoading && (
            <div className="flex justify-end gap-2">
              <Button onClick={() => setOpen(false)}>取消</Button>
              <Button
                type="primary"
                disabled={getCreateQuestionDataLoading}
                loading={createLoading || updateLoading || checkIsStaffLoading}
                onClick={form.submit}
              >
                保存
              </Button>
            </div>
          )
        }
        onClose={() => setOpen(false)}
        {...props}
      >
        <Form.Item layout="horizontal" label="面审评估编号" required>
          <Input disabled placeholder="系统自动生成" value={isEdit ? evaluationDetail?.code : ''} />
        </Form.Item>
        <Form.Item layout="horizontal" label="客户名称" required>
          <Input
            disabled
            value={isEdit ? evaluationDetail?.customerName : visitDetail?.customerName}
          />
        </Form.Item>
        <Form.Item layout="horizontal" label="商机名称">
          <Input
            disabled
            placeholder="未选择商机"
            value={
              isEdit
                ? evaluationDetail?.businessOpportunityName
                : visitDetail?.businessOpportunityName
            }
          />
        </Form.Item>
        <Form.Item layout="horizontal" label="到访记录编号" required>
          <Input
            disabled
            value={isEdit ? evaluationDetail?.interviewVisitRecordCode : visitDetail?.code}
          />
        </Form.Item>
        <Form.Item layout="horizontal" label="面审负责人" required>
          <Input
            disabled
            value={
              users?.find(
                (i) =>
                  i.id === (isEdit ? evaluationDetail?.directUserId : visitDetail?.interviewer),
              )?.name
            }
          />
        </Form.Item>
        {businessOpportunityType === BusinessOpportunityTypeEnum.CHANNEL && (
          <>
            <Form.Item
              layout="horizontal"
              label="申请人是否通过"
              name="applyUserIsPass"
              rules={[{ required: true, message: '请选择申请人是否通过' }]}
            >
              <Select placeholder="请选择申请人是否通过" options={yesOrNoOptions} />
            </Form.Item>
            <Form.Item
              layout="horizontal"
              label="店长是否通过"
              name="storeManagerIsPass"
              rules={[{ required: true, message: '请选择店长是否通过' }]}
            >
              <Select placeholder="请选择店长是否通过" options={yesOrNoOptions} />
            </Form.Item>
          </>
        )}
        <Form.Item
          layout="horizontal"
          label="面审建议"
          name="suggestion"
          rules={[{ required: true, message: '请选择面审建议' }]}
        >
          <Select
            placeholder="面审建议"
            options={
              businessOpportunityType === BusinessOpportunityTypeEnum.SOCIETY
                ? suggestionOptions.filter((i) =>
                    [SuggestionEnum.通过, SuggestionEnum.未通过].includes(i.value),
                  )
                : suggestionOptions.filter((i) => i.value !== SuggestionEnum.通过)
            }
          />
        </Form.Item>
        <Form.Item noStyle shouldUpdate={(prev, next) => prev.suggestion !== next.suggestion}>
          {({ getFieldValue }) =>
            getFieldValue('suggestion') === SuggestionEnum.未通过 && (
              <Form.Item
                layout="horizontal"
                label="未通过原因"
                name="rejectReasons"
                rules={[{ required: true, message: '请选择未通过原因' }]}
              >
                <Select
                  placeholder="请选择未通过原因"
                  mode="multiple"
                  options={enum2Options(EvaluationRejectReasonEnum)}
                />
              </Form.Item>
            )
          }
        </Form.Item>
        <Form.Item
          layout="horizontal"
          label="面审评语"
          name="comments"
          rules={[{ required: true, message: '请输入面审评语' }]}
        >
          <Input placeholder="请输入面审评语" />
        </Form.Item>
        <Form.Item
          layout="horizontal"
          label="面审评估表"
          name="examPaperId"
          rules={[{ required: true, message: '请选择面审评估表' }]}
        >
          <Select
            disabled={isEdit}
            placeholder="请选择面审评估表"
            showSearch
            optionFilterProp="name"
            options={
              isEdit
                ? [{ name: evaluationDetail?.examPaperName, id: evaluationDetail?.examPaperId }]
                : examList
            }
            fieldNames={{ label: 'name', value: 'id' }}
            loading={getExamListLoading}
            onChange={(val) => getCreateQuestionData(val)}
          />
        </Form.Item>
        <EditableExam
          loading={getCreateQuestionDataLoading}
          questionsData={questionData}
          data={evaluationDetail}
          activeKey={activeKey}
          onActiveKeyChange={setActiveKey}
        />
      </Drawer>
    );
  },
);

export default CreateEditEvaluationDrawer;
