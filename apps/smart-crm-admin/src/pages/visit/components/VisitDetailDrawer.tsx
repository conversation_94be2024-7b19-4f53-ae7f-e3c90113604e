import React, { useRef, useState } from 'react';
import { evaluationAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { DescriptionFieldType, ModalRef } from '@src/common/interface';
import { ButtonGroup } from '@src/components';
import { ValueType } from '@src/components/ETable';
import useAllUsers from '@src/hooks/useAllUsers';
import AppointmentRecordDetailDrawer from '@src/pages/appointment-record/components/AppointmentRecordDetailDrawer';
import BusinessDetailDrawer from '@src/pages/business-opportunity/components/BusinessDetailDrawer';
import CustomerDetailDrawer from '@src/pages/customers/components/CustomerDetailDrawer';
import EvaluationDetailDrawer from '@src/pages/evaluation/components/EvaluationDetailDrawer';
import { PermissionsMap, usePermission } from '@src/permissions';
import { VisitPurposeEnum } from '@src/services/appointment-record/type';
import {
  BelongWarZoneEnum,
  BusinessOpportunityTypeEnum,
} from '@src/services/business-opportunity/type';
import { getVisitDetail } from '@src/services/visit';
import {
  EvaluationRejectReasonEnum,
  SuggestionEnum,
  VisitRecordDTO,
} from '@src/services/visit/type';
import { enum2Options, renderDescriptions } from '@src/utils';
import { useRequest } from 'ahooks';
import { Card, Drawer } from 'antd';
import CreateEditEvaluationDrawer from './CreateEditEvaluationDrawer';
import NoticeVisitModal from './NoticeVisitModal';
import TransferVisitModal from './TransferVisitModal';

interface VisitDetailDrawerProps {
  getId: () => number | null | undefined;
  onUpdate?: () => void;
}

const VisitDetailDrawer = React.forwardRef<ModalRef, VisitDetailDrawerProps>(
  ({ getId, onUpdate, ...drawerProps }, ref) => {
    const [open, setOpen] = useState(false);
    const noticeVisitModalRef = useRef<ModalRef>(null);
    const createEditEvaluationDrawerRef = useRef<ModalRef>(null);
    const customerDetailDrawerRef = useRef<ModalRef>(null);
    const businessOpportunityDrawerRef = useRef<ModalRef>(null);
    const transferVisitModalRef = useRef<ModalRef>(null);
    const appointmentRecordDetailDrawerRef = useRef<ModalRef>(null);
    const evaluationDetailDrawerRef = useRef<ModalRef>(null);

    const id = getId()!;

    const checkPermission = usePermission();
    const { data: users } = useAllUsers({ ready: open });
    const { data, loading, refresh } = useRequest(() => getVisitDetail(id), { ready: open });

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const baseInfoItems: DescriptionFieldType<VisitRecordDTO>[] = [
      {
        label: '到访记录编号',
        field: 'code',
      },
      {
        label: '面审负责人',
        field: 'interviewer',
        valueType: ValueType.PERSON,
      },
      {
        label: '商机名称',
        field: 'businessOpportunityName',
        children: (
          <a onClick={() => businessOpportunityDrawerRef.current?.open()}>
            {data?.businessOpportunityName}
          </a>
        ),
      },
      {
        label: '预约记录编号',
        field: 'appointmentCode',
        children: (
          <a onClick={() => appointmentRecordDetailDrawerRef.current?.open()}>
            {data?.appointmentCode}
          </a>
        ),
      },
      {
        label: '到访日期时间',
        field: 'visitTime',
        valueType: ValueType.DATE_TIME,
      },
      {
        label: '到访目的',
        field: 'visitPurpose',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(VisitPurposeEnum),
        },
      },
      {
        label: '到访信息是否一致',
        field: 'infoUnanimous',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        label: '意向省份',
        field: 'intentionProvinceCode',
        valueType: ValueType.REGION,
        fieldProps: {
          regionLevel: 1,
        },
      },
      {
        label: '意向区域',
        field: 'intentionRegion',
        valueType: ValueType.REGION,
        fieldProps: {
          regionLevel: 4,
        },
      },
      {
        label: '所属大区',
        field: 'belongWarZone',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(BelongWarZoneEnum),
        },
      },
      {
        label: '面审评估表编号',
        field: 'interviewEvaluationRecordCode',
        // children: (
        //   <a onClick={() => evaluationDetailDrawerRef.current?.open()}>
        //     {data?.interviewEvaluationRecordCode}
        //   </a>
        // ),
      },
      {
        label: '面审建议',
        field: 'suggestion',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: enum2Options(SuggestionEnum),
        },
      },
      {
        label: '面审最终结果',
        field: 'auditStatus',
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: evaluationAuditStatusOptions,
        },
      },
      {
        label: '未通过原因',
        field: 'rejectReasons',
        valueType: ValueType.MULTIPLE,
        fieldProps: {
          options: enum2Options(EvaluationRejectReasonEnum),
        },
      },
      {
        label: '申请人是否通过',
        field: 'applyUserIsPass',
        hidden: data?.businessOpportunityType === BusinessOpportunityTypeEnum.SOCIETY,
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        label: '店长是否通过',
        field: 'storeManagerIsPass',
        hidden: data?.businessOpportunityType === BusinessOpportunityTypeEnum.SOCIETY,
        valueType: ValueType.SINGLE,
        fieldProps: {
          options: yesOrNoOptions,
        },
      },
      {
        label: '面审评语',
        field: 'comments',
      },
    ];

    return (
      <Drawer
        title="到访记录详情"
        width={800}
        open={open}
        push={false}
        destroyOnHidden
        onClose={() => setOpen(false)}
        {...drawerProps}
      >
        <Card loading={loading}>
          <div className="flex justify-between">
            <div className="flex flex-col gap-2">
              <p>
                客户名称：
                <a onClick={() => customerDetailDrawerRef.current?.open()}>{data?.customerName}</a>
              </p>
              <p>创建人：{users?.find((i) => i.id === data?.createUserId)?.name}</p>
              <p>创建时间：{data?.createTime}</p>
            </div>
            <ButtonGroup
              items={[
                {
                  label: '新建面审评估',
                  show:
                    !data?.interviewEvaluationRecordId &&
                    checkPermission(PermissionsMap.VisitCreateEvaluation),
                  onClick: () => createEditEvaluationDrawerRef.current?.open(),
                },
                {
                  label: '转让',
                  show: checkPermission(PermissionsMap.VisitTransfer),
                  onClick: () => transferVisitModalRef.current?.open(),
                },
                {
                  label: '通知',
                  onClick: () => noticeVisitModalRef.current?.open(),
                },
              ]}
            />
          </div>
        </Card>

        <Card title="详细资料" loading={loading} className="mt-5">
          {renderDescriptions(baseInfoItems, data)}
        </Card>

        <CreateEditEvaluationDrawer
          ref={createEditEvaluationDrawerRef}
          getId={() => id}
          onSuccess={() => {
            refresh();
            onUpdate?.();
          }}
        />
        <NoticeVisitModal
          ref={noticeVisitModalRef}
          getId={() => id}
          getDefaultUserId={() => data?.businessOpportunityDirectUserId!}
        />
        <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => data?.customerId} />
        <BusinessDetailDrawer
          ref={businessOpportunityDrawerRef}
          getId={() => data?.businessOpportunityId}
        />
        <AppointmentRecordDetailDrawer
          ref={appointmentRecordDetailDrawerRef}
          getId={() => data?.appointmentId}
        />
        <TransferVisitModal
          ref={transferVisitModalRef}
          getId={() => id}
          onSuccess={() => {
            setOpen(false);
            onUpdate?.();
          }}
        />
        <EvaluationDetailDrawer
          ref={evaluationDetailDrawerRef}
          getId={() => data?.interviewEvaluationRecordId}
          onUpdate={() => {
            refresh();
            onUpdate?.();
          }}
        />
      </Drawer>
    );
  },
);

export default VisitDetailDrawer;
