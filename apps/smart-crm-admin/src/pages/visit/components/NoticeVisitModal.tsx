import React, { useEffect, useState } from 'react';
import { ModalRef } from '@src/common/interface';
import { UserSelect } from '@src/components';
import { noticeVisit } from '@src/services/visit';
import { useRequest } from 'ahooks';
import { App, Form, Modal, ModalProps } from 'antd';

interface NoticeVisitModalProps extends ModalProps {
  getDefaultUserId: () => number | undefined;
  getId: () => number | null;
}

const NoticeVisitModal = React.forwardRef<ModalRef, NoticeVisitModalProps>(
  ({ getId, getDefaultUserId, ...props }, ref) => {
    const { message } = App.useApp();
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);

    const { runAsync: notice, loading } = useRequest(noticeVisit, {
      manual: true,
    });

    useEffect(() => {
      if (open) {
        form.setFieldValue('receiverUserId', getDefaultUserId());
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [open]);

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
      close: () => setOpen(false),
    }));

    const handleSave = async (values: { receiverUserId: number }) => {
      await notice({ id: getId()!, ...values });
      message.success('通知成功');
      setOpen(false);
    };

    return (
      <Modal
        title="通知"
        destroyOnHidden
        confirmLoading={loading}
        onOk={form.submit}
        onCancel={() => setOpen(false)}
        {...props}
        open={open}
      >
        <Form form={form} onFinish={handleSave}>
          <Form.Item
            label="员工"
            name="receiverUserId"
            extra="员工会收到相应的通知"
            rules={[{ required: true, message: '请选择员工' }]}
          >
            <UserSelect />
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default NoticeVisitModal;
