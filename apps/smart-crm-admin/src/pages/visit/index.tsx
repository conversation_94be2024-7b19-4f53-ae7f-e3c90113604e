import { useEffect, useRef, useState } from 'react';
import { CaretDownOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { evaluationAuditStatusOptions, yesOrNoOptions } from '@src/common/constants';
import { ModalRef } from '@src/common/interface';
import { ExportButton, ProTable, TableActions, UserSelect } from '@src/components';
import EditableMultiple from '@src/components/Editable/EditableMultiple';
import EditableRegion from '@src/components/Editable/EditableRegion';
import useAllUsers from '@src/hooks/useAllUsers';
import useDistricts from '@src/hooks/useDistricts';
import { useNoticeEventSubscription } from '@src/layout';
import { Permission, PermissionsMap, usePermission } from '@src/permissions';
import { VisitPurposeEnum } from '@src/services/appointment-record/type';
import { BelongWarZoneEnum } from '@src/services/business-opportunity/type';
import { exportVisitRecord, getVisitRecordList } from '@src/services/visit';
import {
  EvaluationRejectReasonEnum,
  ExportVisitRecordReq,
  SuggestionEnum,
  VisitRecordDTO,
} from '@src/services/visit/type';
import {
  compatibleTableActionWidth,
  enum2Options,
  enum2ValueEnum,
  optionsToValueEnum,
} from '@src/utils';
import { useRequest } from 'ahooks';
import { Button, Dropdown, Typography } from 'antd';
import { useSearchParams } from 'react-router-dom';
import CreateEditEvaluationDrawer from './components/CreateEditEvaluationDrawer';
import NoticeVisitModal from './components/NoticeVisitModal';
import TransferVisitModal from './components/TransferVisitModal';
import VisitDetailDrawer from './components/VisitDetailDrawer';
import AppointmentRecordDetailDrawer from '../appointment-record/components/AppointmentRecordDetailDrawer';
import BusinessDetailDrawer from '../business-opportunity/components/BusinessDetailDrawer';
import CustomerDetailDrawer from '../customers/components/CustomerDetailDrawer';
import EvaluationDetailDrawer from '../evaluation/components/EvaluationDetailDrawer';

const Visit = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const actionRef = useRef<ActionType>(null);
  const currentIdRef = useRef<number | null>(null);
  const customerIdRef = useRef<number | null>(null);
  const customerDetailDrawerRef = useRef<ModalRef>(null);
  const filterParamsRef = useRef<ExportVisitRecordReq>({});
  const transferVisitModalRef = useRef<ModalRef>(null);
  const businessOpportunityIdRef = useRef<number | null>(null);
  const businessOpportunityDrawerRef = useRef<ModalRef>(null);
  const createEditEvaluationDrawerRef = useRef<ModalRef>(null);
  const noticeVisitModalRef = useRef<ModalRef>(null);
  const visitDetailDrawerRef = useRef<ModalRef>(null);
  const appointmentRecordDetailDrawerRef = useRef<ModalRef>(null);
  const appointmentIdRef = useRef<number | null>(null);
  const evaluationDetailDrawerRef = useRef<ModalRef>(null);
  const evaluationIdRef = useRef<number | null>(null);

  const checkPermission = usePermission();
  const { data: districts } = useDistricts();
  const { data: users } = useAllUsers();
  const { data, runAsync: getList } = useRequest(getVisitRecordList, { manual: true });

  // 在通知中修改了某个到访详情，列表如果有这个到访，则刷新列表
  useNoticeEventSubscription((event) => {
    if (event.type === 'visit' && data?.result.some((i) => i.id === event.payload.id)) {
      actionRef.current?.reload();
    }
  });

  useEffect(() => {
    const id = searchParams.get('id');

    if (id) {
      currentIdRef.current = Number(id);
      visitDetailDrawerRef.current?.open();

      const newSearchParams = new URLSearchParams(searchParams);

      newSearchParams.delete('id');
      setSearchParams(newSearchParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: ProColumns<VisitRecordDTO>[] = [
    {
      title: '到访记录编号',
      dataIndex: 'code',
      fixed: 'left',
      renderText: (value, { id }) => (
        <a
          onClick={() => {
            currentIdRef.current = id;
            visitDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      render: (value, { customerId }) => (
        <Typography.Text
          style={{ color: 'var(--ant-color-link)', cursor: 'pointer' }}
          ellipsis={{ tooltip: true }}
          onClick={() => {
            customerIdRef.current = customerId;
            customerDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </Typography.Text>
      ),
    },
    {
      title: '商机名称',
      dataIndex: 'businessOpportunityName',
      render: (value, { businessOpportunityId }) => (
        <Typography.Text
          ellipsis={{ tooltip: true }}
          style={{ color: 'var(--ant-color-link)', cursor: 'pointer' }}
          onClick={() => {
            businessOpportunityIdRef.current = businessOpportunityId;
            businessOpportunityDrawerRef.current?.open();
          }}
        >
          {value}
        </Typography.Text>
      ),
    },
    {
      title: '商机编号',
      dataIndex: 'businessOpportunityCode',
      search: false,
    },
    {
      title: '预约记录编号',
      dataIndex: 'appointmentCode',
      search: false,
      renderText: (value, { appointmentId }) => (
        <a
          onClick={() => {
            appointmentIdRef.current = appointmentId;
            appointmentRecordDetailDrawerRef.current?.open();
          }}
        >
          {value}
        </a>
      ),
    },
    {
      title: '到访日期时间',
      dataIndex: 'visitTime',
      valueType: (_, type) => (type === 'table' ? 'dateTime' : 'dateTimeRange'),
      search: {
        transform: ([beginVisitTime, endVisitTime]) => ({
          beginVisitTime,
          endVisitTime,
        }),
      },
    },
    {
      title: '面审负责人',
      dataIndex: 'interviewer',
      renderText: (value) => users?.find((i) => i.id === value)?.name,
      renderFormItem: () => <UserSelect />,
    },
    {
      title: '到访目的',
      dataIndex: 'visitPurpose',
      valueEnum: enum2ValueEnum(VisitPurposeEnum),
    },
    {
      title: '到访信息是否一致',
      dataIndex: 'infoUnanimous',
      search: false,
      valueEnum: optionsToValueEnum(yesOrNoOptions, true),
    },
    {
      title: '意向省份',
      dataIndex: 'intentionProvinceCode',
      search: false,
      renderText: (value) => districts?.find((i) => i.code === value)?.name,
    },
    {
      title: '意向区域',
      dataIndex: 'intentionRegion',
      search: false,
      ellipsis: true,
      renderText: (value) => <EditableRegion value={value} fieldProps={{ regionLevel: 4 }} />,
    },
    {
      title: '所属大区',
      dataIndex: 'belongWarZone',
      search: false,
      valueEnum: enum2ValueEnum(BelongWarZoneEnum),
    },
    {
      title: '面审评估表编号',
      dataIndex: 'interviewEvaluationRecordCode',
      search: false,
      // renderText: (value, { interviewEvaluationRecordId }) => (
      //   <a
      //     onClick={() => {
      //       evaluationIdRef.current = interviewEvaluationRecordId;
      //       evaluationDetailDrawerRef.current?.open();
      //     }}
      //   >
      //     {value}
      //   </a>
      // ),
    },
    {
      title: '面审建议',
      dataIndex: 'suggestion',
      search: false,
      valueEnum: enum2ValueEnum(SuggestionEnum),
    },
    {
      title: '面审最终结果',
      dataIndex: 'auditStatus',
      search: false,
      valueEnum: optionsToValueEnum(evaluationAuditStatusOptions),
    },
    {
      title: '未通过原因',
      dataIndex: 'rejectReasons',
      search: false,
      ellipsis: true,
      renderText: (value: EvaluationRejectReasonEnum[]) => (
        <EditableMultiple
          value={value}
          fieldProps={{ options: enum2Options(EvaluationRejectReasonEnum) }}
        />
      ),
    },
    {
      title: '申请人是否通过',
      dataIndex: 'applyUserIsPass',
      search: false,
      valueEnum: new Map([
        [true, '是'],
        [false, '否'],
      ]),
    },
    {
      title: '店长是否通过',
      dataIndex: 'storeManagerIsPass',
      search: false,
      valueEnum: new Map([
        [true, '是'],
        [false, '否'],
      ]),
    },
    {
      title: '面审评语',
      dataIndex: 'comments',
      search: false,
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      search: false,
      valueType: 'dateTime',
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      search: false,
      renderText: (value) => users?.find((i) => i.id === value)?.name,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      fixed: 'right',
      width: compatibleTableActionWidth(200),
      search: false,
      render: (_, record) => (
        <TableActions
          shortcuts={[
            {
              label: '新建面审评估',
              show:
                !record.interviewEvaluationRecordId &&
                checkPermission(PermissionsMap.VisitCreateEvaluation),
              onClick: () => {
                currentIdRef.current = record.id;
                createEditEvaluationDrawerRef.current?.open();
              },
            },
            {
              label: '转让',
              show: checkPermission(PermissionsMap.VisitTransfer),
              onClick: () => {
                currentIdRef.current = record.id;
                transferVisitModalRef.current?.open();
              },
            },
            {
              label: '通知',
              onClick: () => {
                currentIdRef.current = record.id;
                noticeVisitModalRef.current?.open();
              },
            },
          ]}
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        bordered
        sticky
        actionRef={actionRef}
        cardProps={false}
        columnEmptyText=""
        options={false}
        size="small"
        rowKey="id"
        search={{
          labelWidth: 'auto',
        }}
        tableAlertRender={false}
        pagination={{ showSizeChanger: true, showQuickJumper: true }}
        toolbar={{
          subTitle: (
            <span className="text-black">
              共 {data?.total || 0} 条记录
              {selectedRowKeys.length > 0 && (
                <>
                  ，已选择 <span className="text-primary mx-1">{selectedRowKeys.length}</span> 条
                  <a className="ml-4" onClick={() => setSelectedRowKeys([])}>
                    清空选择
                  </a>
                </>
              )}
            </span>
          ),
          actions: [
            <Dropdown
              disabled={!selectedRowKeys.length}
              menu={{
                items: [
                  {
                    label: '转让',
                    key: 'transfer',
                    onClick: () => {
                      currentIdRef.current = null;
                      transferVisitModalRef.current?.open();
                    },
                  },
                ],
              }}
            >
              <Button type="text" className="!flex items-center">
                批量操作 <CaretDownOutlined />
              </Button>
            </Dropdown>,
            <Permission value={PermissionsMap.VisitExport}>
              <ExportButton
                total={selectedRowKeys.length || data?.total}
                request={() =>
                  exportVisitRecord(
                    selectedRowKeys.length ? { ids: selectedRowKeys } : filterParamsRef.current,
                  )
                }
              />
            </Permission>,
          ],
        }}
        columns={columns}
        rowSelection={{
          showSort: true,
          fixed: true,
          preserveSelectedRowKeys: true,
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        scroll={{ x: columns.reduce((total, cur) => total + ((cur.width as number) || 180), 0) }}
        request={async ({ current, pageSize, ...params }) => {
          filterParamsRef.current = params as ExportVisitRecordReq;

          const res = await getList({
            pageNum: current,
            pageSize,
            ...params,
          });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <CreateEditEvaluationDrawer
        ref={createEditEvaluationDrawerRef}
        getId={() => currentIdRef.current}
        onSuccess={() => actionRef.current?.reload()}
      />
      <CustomerDetailDrawer ref={customerDetailDrawerRef} getId={() => customerIdRef.current} />
      <BusinessDetailDrawer
        ref={businessOpportunityDrawerRef}
        getId={() => businessOpportunityIdRef.current}
      />
      <TransferVisitModal
        ref={transferVisitModalRef}
        getId={() => currentIdRef.current}
        selectedRowKeys={selectedRowKeys}
        onSuccess={() => actionRef.current?.reload()}
      />
      <NoticeVisitModal
        ref={noticeVisitModalRef}
        getId={() => currentIdRef.current}
        getDefaultUserId={() =>
          data?.result.find((i) => i.id === currentIdRef.current)?.businessOpportunityDirectUserId
        }
      />
      <VisitDetailDrawer
        ref={visitDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
      />
      <AppointmentRecordDetailDrawer
        ref={appointmentRecordDetailDrawerRef}
        getId={() => appointmentIdRef.current}
      />
      <EvaluationDetailDrawer
        ref={evaluationDetailDrawerRef}
        getId={() => evaluationIdRef.current}
        onUpdate={() => actionRef.current?.reload()}
      />
    </PageContainer>
  );
};

export default Visit;
