import { UserSelect } from '@src/components';
import { editUserVisibleItem, getUserVisibleItem } from '@src/services/users/auth';
import { VisibleRangeEnum } from '@src/services/users/auth/type';
import { useRequest } from 'ahooks';
import { App, Form, Modal, ModalProps, Radio } from 'antd';

interface CreateOrEditAuthModalProps extends ModalProps {
  currentEditId: null | number;
  onSuccess: () => void;
}

const options = [
  { label: '仅本人', value: VisibleRangeEnum.ONLY_ME },
  { label: '所有人', value: VisibleRangeEnum.ALL },
  { label: '指定成员（包含本人）', value: VisibleRangeEnum.APPOINT },
];

const CreateOrEditAuthModal: React.FC<CreateOrEditAuthModalProps> = ({
  open,
  currentEditId,
  onSuccess,
  ...props
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const { loading: editLoading, runAsync: edit } = useRequest(editUserVisibleItem, {
    manual: true,
  });
  const { data, loading } = useRequest(() => getUserVisibleItem(currentEditId!), {
    ready: open && !!currentEditId,
    onSuccess: (res) => {
      form.setFieldsValue({
        visibleRange: res.visibleRange,
        users: res.visibleUsers.map((i) => i.id),
      });
    },
  });

  const handleSave = async ({
    visibleRange,
    users,
  }: {
    visibleRange: VisibleRangeEnum;
    users?: number[];
  }) => {
    if (visibleRange === VisibleRangeEnum.APPOINT) {
      const addAppointUserIds = users?.filter((id) => !data?.visibleUsers.some((i) => i.id === id));
      const removeAppointUserIds = data?.visibleUsers
        ?.filter((item) => !users?.includes(item.id))
        .map((i) => i.id);

      await edit({
        userId: currentEditId!,
        visibleRange,
        addAppointUserIds,
        removeAppointUserIds,
      });
    } else {
      await edit({ userId: currentEditId!, visibleRange });
    }

    message.success('修改成功');
    onSuccess();
  };

  return (
    <Modal
      open={open}
      title="编辑范围"
      width={600}
      loading={loading}
      destroyOnHidden
      confirmLoading={editLoading}
      modalRender={(node) => (
        <Form form={form} clearOnDestroy onFinish={handleSave}>
          {node}
        </Form>
      )}
      onOk={form.submit}
      {...props}
    >
      <Form.Item
        label="范围"
        name="visibleRange"
        rules={[{ required: true, message: '请选择范围' }]}
      >
        <Radio.Group options={options} />
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) =>
          getFieldValue('visibleRange') === VisibleRangeEnum.APPOINT && (
            <Form.Item
              label="选择成员"
              name="users"
              rules={[{ required: true, message: '请选择成员' }]}
            >
              <UserSelect
                mode="multiple"
                transformOptions={(_options) => _options.filter((i) => i.id !== currentEditId)}
              />
            </Form.Item>
          )
        }
      </Form.Item>
    </Modal>
  );
};

export default CreateOrEditAuthModal;
