import { useRef, useState } from 'react';
import { ActionType, PageContainer, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@src/components';
import useAllUsers from '@src/hooks/useAllUsers';
import { PermissionsMap, usePermission } from '@src/permissions';
import { getUserVisibleList, syncUsers } from '@src/services/users/auth';
import { UserVisibleListItem, VisibleRangeEnum } from '@src/services/users/auth/type';
import { useRequest } from 'ahooks';
import { App, Button } from 'antd';
import CreateOrEditAuthModal from './components/CreateOrEditAuthModal';

const visibleRangeLabelMap = {
  [VisibleRangeEnum.ONLY_ME]: '仅本人',
  [VisibleRangeEnum.ALL]: '所有人',
  [VisibleRangeEnum.APPOINT]: '指定成员（包含本人）',
};

const Auth = () => {
  const checkPermission = usePermission();
  const { message } = App.useApp();
  const [createOrEditAuthModal, setCreateOrEditAuthModal] = useState(false);
  const [currentEditId, setCurrentEditId] = useState<null | number>(null);
  const actionRef = useRef<ActionType>(null);

  const { runAsync: sync, loading: syncLoading } = useRequest(syncUsers, {
    manual: true,
  });
  const { refresh: refreshAllUsers } = useAllUsers({ manual: true });

  const columns: ProColumns<UserVisibleListItem>[] = [
    { title: '成员名称', dataIndex: 'userName', width: 140 },
    {
      title: '范围',
      dataIndex: 'visibleRange',
      search: false,
      render: (_, { visibleRange }) => visibleRangeLabelMap[visibleRange],
    },
    {
      title: '操作',
      width: 100,
      align: 'center',
      search: false,
      hidden: !checkPermission(PermissionsMap.UsersAuthUpdate),
      render: (_, { userId }) => (
        <Button
          type="link"
          onClick={() => {
            setCreateOrEditAuthModal(true);
            setCurrentEditId(userId);
          }}
        >
          编辑
        </Button>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        sticky
        cardProps={false}
        bordered
        actionRef={actionRef}
        options={false}
        size="small"
        toolbar={{
          title: '自定义配置每个成员可查看/管理的数据范围',
          actions: [
            <Button
              type="primary"
              loading={syncLoading}
              onClick={async () => {
                await sync();
                message.success('同步成功');
                actionRef.current?.reload();
                refreshAllUsers();
              }}
            >
              同步用户
            </Button>,
          ],
        }}
        rowKey="userId"
        columns={columns}
        request={async ({ current, pageSize, ...params }) => {
          const res = await getUserVisibleList({
            pageNum: current,
            pageSize,
            status: 'NORMAL',
            ...params,
          });

          return {
            data: res.result,
            total: res.total,
          };
        }}
      />

      <CreateOrEditAuthModal
        open={createOrEditAuthModal}
        currentEditId={currentEditId}
        onCancel={() => setCreateOrEditAuthModal(false)}
        onSuccess={() => {
          setCreateOrEditAuthModal(false);
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default Auth;
