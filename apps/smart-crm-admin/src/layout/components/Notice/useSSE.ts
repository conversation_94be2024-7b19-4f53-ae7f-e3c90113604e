import { useEffect, useRef } from 'react';
import { isInIcestark } from '@ice/stark-app';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { getToken, removeToken } from '@src/common/authorization';
import { navigateToGaiaLogin } from '@src/utils';
import { App } from 'antd';
import { useEvent } from 'rc-util';

interface UseSSEProps {
  onMessage: (e: { data: string }) => void;
}

const useSSE = ({ onMessage }: UseSSEProps) => {
  const { notification } = App.useApp();
  const retryTimer = useRef<ReturnType<typeof setTimeout> | null>(null);

  const onInternalMessage = useEvent(onMessage);

  useEffect(() => {
    let controller: AbortController | null = null;

    const connectSSE = () => {
      controller = new AbortController();

      const retry = () => {
        controller?.abort();
        retryTimer.current = setTimeout(() => {
          connectSSE();
        }, 10000);
      };

      fetchEventSource(`${isInIcestark() ? import.meta.env.VITE_API_URL : ''}/sse-event/connect`, {
        openWhenHidden: true,
        signal: controller.signal,
        headers: {
          'User-Token': getToken() as string,
        },
        onmessage: (e) => {
          try {
            onInternalMessage(e);
          } catch (error) {}
        },
        // @ts-ignore
        onopen(response) {
          if (response.status === 401) {
            notification.error({
              message: '错误提示',
              description: '未登录或登录已过期，请重新登录',
            });
            removeToken();
            navigateToGaiaLogin();
          } else if (response.status.toString().startsWith('5')) {
            retry();
          }
        },
        onclose() {
          retry();
        },
        onerror(e) {
          retry();

          // throw 阻止内部自动重试，使用自己的 retry 逻辑
          throw e;
        },
      });
    };

    connectSSE();

    return () => {
      if (retryTimer.current) {
        clearTimeout(retryTimer.current);
      }

      controller?.abort();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
};

export default useSSE;
