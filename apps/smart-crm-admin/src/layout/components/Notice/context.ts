import { createContext, useContext } from 'react';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';

export type NoticeEventEmitterType = {
  type:
    | 'franchise-application'
    | 'business-opportunity'
    | 'visit'
    | 'clue'
    | 'intention'
    | 'formal'
    | 'fission'
    | 'evaluation'
    | 'intended-area-change'
    | 'intended-contract-extension'
    | 'relocation-intention'
    | 'relocation-formal'
    | 'renovation'
    | 'change-legal-person';
  payload: { id: number };
};

interface NoticeEventEmitterContextProps {
  noticeEvent: EventEmitter<NoticeEventEmitterType>;
}

export const NoticeEventEmitterContext = createContext<NoticeEventEmitterContextProps>(
  {} as NoticeEventEmitterContextProps,
);

export const useNoticeEventEmitterContext = () => useContext(NoticeEventEmitterContext);

export const useNoticeEventSubscription = (callback: (val: NoticeEventEmitterType) => void) => {
  const context = useContext(NoticeEventEmitterContext);

  context.noticeEvent.useSubscription(callback);
};
