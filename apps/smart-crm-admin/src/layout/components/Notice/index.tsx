import { useEffect, useState } from 'react';
import { BellOutlined } from '@ant-design/icons';
import { parserJSON } from '@pkg/utils';
import { getNoticeCount, getNotices, readAllNotice, readNotice } from '@src/services/common';
import { NoticeDTO, NoticeTypeEnum } from '@src/services/common/type';
import { useRequest } from 'ahooks';
import { App, Badge, Button, Drawer, Empty, Pagination, Segmented, Skeleton, Tabs } from 'antd';
import useNoticeMap from './useNoticeMap';
import useSSE from './useSSE';

const Notice = () => {
  const { message, notification } = App.useApp();
  const [open, setOpen] = useState(false);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [type, setType] = useState(NoticeTypeEnum.CLUE);
  const [readType, setReadType] = useState<boolean | null>(null);

  const { data, mutate, loading, refresh } = useRequest(
    () =>
      getNotices({
        pageNum: current,
        pageSize,
        type,
        read: readType,
      }),
    {
      ready: open,
      refreshDeps: [current, pageSize, type, readType],
      onBefore: () => {
        mutate(undefined);
      },
    },
  );
  const { data: counts, refresh: refreshCounts } = useRequest(getNoticeCount, {
    // 可能一次性收到多条通知，获取通知数量防抖
    debounceWait: 100,
  });
  const { runAsync: runReadAllNotice, loading: readAllNoticeLoading } = useRequest(readAllNotice, {
    manual: true,
  });

  useEffect(() => {
    const onVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        refresh();
        refreshCounts();
      }
    };

    document.addEventListener('visibilitychange', onVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', onVisibilityChange);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleReadNotice = async (item: NoticeDTO, showMessage?: boolean) => {
    if (!item.read) {
      await readNotice({ notificationIds: [item.id] });

      if (showMessage) {
        message.success('标记成功');
      }

      refreshCounts();
      mutate((old) => {
        // 当前在未读面板
        if (readType === false) {
          return {
            ...old!,
            result: old!.result.filter((i) => i.id !== item.id),
          };
          // 当前在全部面板
        } else {
          const result = [...old!.result];
          const index = result.findIndex((i) => i.id === item.id);

          if (index > -1) {
            result[index].read = true;
          }

          return {
            ...old!,
            result,
          };
        }
      });
    }
  };

  const handleReadAllNotice = async () => {
    await runReadAllNotice({ type });
    message.success('标记成功');
    refreshCounts();

    // 如果在全部，更新下列表
    if (readType === null) {
      refresh();
      // 如果在未读，更新下列表
    } else if (readType === false) {
      if (current === 1) {
        refresh();
      } else {
        setCurrent(1);
      }
    }
  };

  const { noticeMap, drawersNode } = useNoticeMap({ onReadNotice: handleReadNotice });

  useSSE({
    onMessage: (e: any) => {
      const messageData = (parserJSON(e.data) || {}) as {
        content: string;
        notifyType: NoticeTypeEnum;
      };

      const content = (parserJSON(messageData.content) || {}) as NoticeDTO['content'];

      const mapItem = noticeMap[messageData.notifyType]()[content.type];
      const title = `${mapItem?.title || ''}${mapItem?.subTitle || ''}`;

      refreshCounts();

      notification.info({
        message: '消息通知',
        description: `您有一条${title}提醒，请前往通知面板查看`,
        duration: 10,
        placement: 'bottomRight',
      });

      // 打开，并且当前是全部或未读时刷新
      if (open && !readType) {
        refresh();
      }
    },
  });

  return (
    <>
      <div className="p-[6px]" onClick={() => setOpen(true)}>
        <Badge
          count={Object.values(counts || {}).reduce((total, cur) => total + cur, 0)}
          size="small"
          classNames={{ root: 'flex items-center' }}
        >
          <BellOutlined className="text-lg" />
          <span className="ml-1">通知</span>
        </Badge>
      </div>

      <Drawer
        title="通知"
        open={open}
        width={800}
        push={false}
        styles={{
          body: {
            paddingInline: 0,
          },
        }}
        destroyOnHidden
        onClose={() => setOpen(false)}
      >
        <div className="flex h-full">
          <Tabs
            activeKey={type}
            tabPosition="left"
            items={[
              { label: '线索', key: NoticeTypeEnum.CLUE },
              { label: '商机', key: NoticeTypeEnum.BO },
              { label: '面审到访', key: NoticeTypeEnum.INTERVIEW_VISIT },
              { label: '加盟申请', key: NoticeTypeEnum.ONLINE_APPLY },
              { label: '面审评估', key: NoticeTypeEnum.INTERVIEW_EVALUATION },
              { label: '合同', key: NoticeTypeEnum.CONTRACT },
              { label: '裂变申请', key: NoticeTypeEnum.FISSION_APPLY },
              { label: '意向区域变更', key: NoticeTypeEnum.CONTRACT_REGION_CHANGE },
              { label: '意向合同延期', key: NoticeTypeEnum.CONTRACT_POSTPONE },
            ].map((i) => ({
              label: (
                <Badge count={counts?.[i.key]} className="text-inherit" size="small">
                  <span className="text-base font-bold">{i.label}</span>
                </Badge>
              ),
              key: i.key,
            }))}
            onChange={(activeKey) => {
              setType(activeKey as NoticeTypeEnum);
              setReadType(null);
              setCurrent(1);
            }}
          />
          <div className="flex flex-col flex-1">
            <div className="flex-col flex gap-2 sm:flex-row justify-between items-start pr-5">
              <Segmented
                value={readType}
                options={[
                  { label: '全部', value: null },
                  { label: '已读', value: true },
                  { label: '未读', value: false },
                ]}
                onChange={(value) => {
                  setReadType(value);
                  setCurrent(1);
                }}
              />
              <Button type="link" loading={readAllNoticeLoading} onClick={handleReadAllNotice}>
                全部标记为已读
              </Button>
            </div>
            <div className="flex flex-col flex-1 overflow-auto mt-3 pl-1 pt-1 pr-5 gap-4">
              {loading ? (
                <Skeleton paragraph={{ rows: 10 }} title={false} active />
              ) : !data?.result.length ? (
                <Empty description="暂无通知" />
              ) : (
                data?.result.map((item) => {
                  const mapItem = noticeMap[item.notifyType]()[item.content.type];

                  return (
                    <div key={item.id} className="relative px-3 py-2 rounded-lg bg-gray-100/50">
                      {!item.read && (
                        <div className="absolute z-10 -top-1 -left-1 w-2 h-2 rounded-full bg-red-500" />
                      )}
                      <div className="flex justify-between">
                        <div className="font-bold">{mapItem?.subTitle}</div>
                        {!item.read && (
                          <a onClick={() => handleReadNotice(item, true)}>标记为已读</a>
                        )}
                      </div>
                      <div className="sm:flex justify-between gap-1">
                        <div className="flex-1 mt-1">{mapItem?.getContent(item)}</div>
                        <div className="mt-1 self-end">{item.createTime}</div>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
            {!!data?.result.length && (
              <div className="flex justify-end px-5 pt-5">
                <Pagination
                  current={current}
                  size="small"
                  total={data?.total}
                  showSizeChanger
                  pageSize={pageSize}
                  onChange={(page, size) => {
                    // 相等说明是改了 pageSize 触发，重置为第一页
                    if (current === page) {
                      setCurrent(1);
                    } else {
                      setCurrent(page);
                    }

                    setPageSize(size);
                  }}
                />
              </div>
            )}
          </div>
        </div>

        {drawersNode}
      </Drawer>
    </>
  );
};

export default Notice;
