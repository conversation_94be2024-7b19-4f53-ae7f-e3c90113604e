import React, { useRef } from 'react';
import { ModalRef } from '@src/common/interface';
import BusinessDetailDrawer from '@src/pages/business-opportunity/components/BusinessDetailDrawer';
import ClueDetailDrawer from '@src/pages/clues/components/ClueDetailDrawer';
import ChangeLegalPersonDetailDrawer from '@src/pages/contract/change-legal-person/components/ChangeLegalPersonDetailDrawer';
import FormalDetailDrawer from '@src/pages/contract/formal/components/FormalDetailDrawer';
import IntentionDetailDrawer from '@src/pages/contract/intention/components/IntentionDetailDrawer';
import RelocationFormalDetailDrawer from '@src/pages/contract/relocation-formal/components/RelocationFormalDetailDrawer';
import RelocationIntentionDetailDrawer from '@src/pages/contract/relocation-intention/components/RelocationIntentionDetailDrawer';
import RenovationDetailDrawer from '@src/pages/contract/renovation/components/RenovationDetailDrawer';
import EvaluationDetailDrawer from '@src/pages/evaluation/components/EvaluationDetailDrawer';
import FissionDetailDrawer from '@src/pages/fission/components/FissionDetailDrawer';
import FranchiseDetailDrawer from '@src/pages/franchise-application/components/FranchiseDetailDrawer';
import IntendedAreaChangeDetailDrawer from '@src/pages/process/intended-area-change/components/IntendedAreaChangeDetailDrawer';
import IntendedContractExtensionDetailDrawer from '@src/pages/process/intended-contract-extension/components/IntendedContractExtensionDetailDrawer';
import VisitDetailDrawer from '@src/pages/visit/components/VisitDetailDrawer';
import { NoticeContentTypeEnum, NoticeDTO, NoticeTypeEnum } from '@src/services/common/type';
import { ContractTypeEnum } from '@src/services/payment/type';
import { enum2ValueEnum } from '@src/utils';
import { NoticeEventEmitterType, useNoticeEventEmitterContext } from './context';

interface useNoticeMapProps {
  onReadNotice: (item: NoticeDTO, showMessage?: boolean) => Promise<void>;
}

const useNoticeMap = ({ onReadNotice }: useNoticeMapProps) => {
  const currentIdRef = useRef<number | null>(null);
  const businessDetailDrawerRef = useRef<ModalRef>(null);
  const franchiseDetailDrawerRef = useRef<ModalRef>(null);
  const visitDetailDrawerRef = useRef<ModalRef>(null);
  const clueDetailDrawerRef = useRef<ModalRef>(null);
  const intentionDetailDrawerRef = useRef<ModalRef>(null);
  const formalDetailDrawerRef = useRef<ModalRef>(null);
  const fissionDetailDrawerRef = useRef<ModalRef>(null);
  const evaluationDetailDrawerRef = useRef<ModalRef>(null);
  const intendedAreaChangeDetailDrawerRef = useRef<ModalRef>(null);
  const intendedContractExtensionDetailDrawerRef = useRef<ModalRef>(null);
  const relocationIntentionDetailDrawerRef = useRef<ModalRef>(null);
  const relocationFormalDetailDrawerRef = useRef<ModalRef>(null);
  const renovationDetailDrawerRef = useRef<ModalRef>(null);
  const changeLegalPersonDetailDrawerRef = useRef<ModalRef>(null);

  const { noticeEvent } = useNoticeEventEmitterContext();

  const noticeMap: Record<
    NoticeTypeEnum,
    () => Partial<
      Record<
        NoticeContentTypeEnum,
        { title?: string; subTitle: string; getContent: (item: NoticeDTO) => React.ReactNode }
      >
    >
  > = {
    [NoticeTypeEnum.BO]: () => ({
      [NoticeContentTypeEnum.BO_STAGE]: {
        subTitle: '商机阶段变更',
        getContent: (item) => {
          const { content } = item;

          return (
            <>
              <a
                onClick={() => {
                  onReadNotice(item);
                  currentIdRef.current = content.businessOpportunityId;
                  businessDetailDrawerRef.current?.open();
                }}
              >
                {content.businessOpportunityName}
              </a>{' '}
              商机阶段从 <span className="font-bold">{content.before}</span> 变更为{' '}
              <span className="font-bold">{content.after}</span>
              ，请及时查看
            </>
          );
        },
      },
      [NoticeContentTypeEnum.BO_STATUS]: {
        subTitle: '商机状态变更',
        getContent: (item) => {
          const { content } = item;

          return (
            <>
              <a
                onClick={() => {
                  onReadNotice(item);
                  currentIdRef.current = content.businessOpportunityId;
                  businessDetailDrawerRef.current?.open();
                }}
              >
                {content.businessOpportunityName}
              </a>{' '}
              商机阶段 <span className="font-bold">{content.progressName}</span> 状态从{' '}
              <span className="font-bold">{content.before}</span> 变更为{' '}
              <span className="font-bold">{content.after}</span>
              ，请及时查看
            </>
          );
        },
      },
    }),
    [NoticeTypeEnum.CLUE]: () => {
      const renderButton = (item: NoticeDTO) => (
        <a
          onClick={() => {
            onReadNotice(item);
            currentIdRef.current = item.content.clueId;
            clueDetailDrawerRef.current?.open();
          }}
        >
          {item.content.clueName}
        </a>
      );

      return {
        [NoticeContentTypeEnum.TRANSFER]: {
          subTitle: '线索转让',
          getContent: (item) => (
            <>
              {item.content.operatorName}将线索 {renderButton(item)} 转让给您，请及时处理
            </>
          ),
        },
        [NoticeContentTypeEnum.BATCH_TRANSFER]: {
          subTitle: '线索转让',
          getContent: (item) => (
            <>
              {item.content.operatorName}将 {renderButton(item)} 等 {item.content.clueCount}{' '}
              个线索转让给您，请及时处理
            </>
          ),
        },
        [NoticeContentTypeEnum.ASSIGN]: {
          subTitle: '线索分配',
          getContent: (item) => (
            <>
              {item.content.operatorName}将线索 {renderButton(item)} 分配给您，请及时处理
            </>
          ),
        },
        [NoticeContentTypeEnum.BATCH_ASSIGN]: {
          subTitle: '线索分配',
          getContent: (item) => (
            <>
              {item.content.operatorName}将 {renderButton(item)} 等 {item.content.clueCount}{' '}
              个线索分配给您，请及时处理
            </>
          ),
        },
        [NoticeContentTypeEnum.ACTIVATION]: {
          subTitle: '线索激活',
          getContent: (item) => (
            <>历史线索 {renderButton(item)}在小程序段点击立即加盟，请注意跟进</>
          ),
        },
        [NoticeContentTypeEnum.CALL_BACK_STATE]: {
          subTitle: '线索回拨',
          getContent: (item) => (
            <>
              当日线索：
              {item.content.clues?.map((clue, index, self) => (
                <React.Fragment key={clue.id}>
                  <a
                    onClick={() => {
                      currentIdRef.current = clue.id;
                      clueDetailDrawerRef.current?.open();
                    }}
                  >
                    {clue.name}
                  </a>
                  {index < self.length - 1 && '、'}
                </React.Fragment>
              ))}{' '}
              未回拨，请及时处理
            </>
          ),
        },
        [NoticeContentTypeEnum.DEPT_CALL_BACK_STATE]: {
          subTitle: '部门线索回拨',
          getContent: (item) =>
            item.content.directClues?.map((i, index) => (
              <div key={index}>
                {i.firstDirectorName}负责的 {i.clueNum} 条线索未回拨，请及时处理
              </div>
            )),
        },
      };
    },
    [NoticeTypeEnum.INTERVIEW_VISIT]: () => ({
      [NoticeContentTypeEnum.NOTICE]: {
        subTitle: '到访通知',
        getContent: (item) => {
          const { content } = item;

          return (
            <>
              {content.operatorName}在{' '}
              <a
                onClick={() => {
                  onReadNotice(item);
                  currentIdRef.current = content.interviewVisitRecordId;
                  visitDetailDrawerRef.current?.open();
                }}
              >
                {content.customerName}
              </a>{' '}
              的到访记录中@了您，请及时查看
            </>
          );
        },
      },
    }),
    [NoticeTypeEnum.ONLINE_APPLY]: () => {
      const renderButton = (item: NoticeDTO) => (
        <a
          onClick={() => {
            onReadNotice(item);
            currentIdRef.current = item.content.examOnlineApplyRecordId;
            franchiseDetailDrawerRef.current?.open();
          }}
        >
          《{item.content.examPaperName}》
        </a>
      );

      return {
        [NoticeContentTypeEnum.APPROVAL_PASS]: {
          title: '加盟申请',
          subTitle: '审批通过',
          getContent: (item) => (
            <>
              {item.content.operatorName}已经审核通过{item.content.customerName}的
              {renderButton(item)}，请及时查看
            </>
          ),
        },
        [NoticeContentTypeEnum.APPROVAL_WAITING]: {
          title: '加盟申请',
          subTitle: '待审批',
          getContent: (item) => (
            <>
              {item.content.submitUserName || item.content.customerName}提交了{renderButton(item)}
              审批，请及时处理
            </>
          ),
        },
        [NoticeContentTypeEnum.APPROVAL_REJECT]: {
          title: '加盟申请',
          subTitle: '审批拒绝',
          getContent: (item) => (
            <>
              {item.content.operatorName}拒绝了{item.content.customerName}的{renderButton(item)}
              ，请及时查看
            </>
          ),
        },
      };
    },
    [NoticeTypeEnum.INTERVIEW_EVALUATION]: () => {
      const renderButton = (item: NoticeDTO) => (
        <a
          onClick={() => {
            onReadNotice(item);
            currentIdRef.current = item.content.examInterviewEvaluationRecordId;
            evaluationDetailDrawerRef.current?.open();
          }}
        >
          《{item.content.examPaperName}》
        </a>
      );

      return {
        [NoticeContentTypeEnum.APPROVAL_PASS]: {
          title: '面审评估',
          subTitle: '审批通过',
          getContent: (item) => (
            <>
              {item.content.operatorName}已经审核通过{item.content.customerName}的
              {renderButton(item)}，请及时查看
            </>
          ),
        },
        [NoticeContentTypeEnum.APPROVAL_WAITING]: {
          title: '面审评估',
          subTitle: '待审批',
          getContent: (item) => (
            <>
              {item.content.submitUserName}提交了{renderButton(item)}
              审批，请及时处理
            </>
          ),
        },
        [NoticeContentTypeEnum.APPROVAL_REJECT]: {
          title: '面审评估',
          subTitle: '审批不通过',
          getContent: (item) => (
            <>
              {item.content.operatorName}不通过{item.content.customerName}的{renderButton(item)}
              ，请及时查看
            </>
          ),
        },
      };
    },
    [NoticeTypeEnum.CONTRACT]: () => {
      const getContractTypeText = (item: NoticeDTO) =>
        enum2ValueEnum(ContractTypeEnum)[item.content.contractType];

      const renderButton = (item: NoticeDTO) => (
        <a
          onClick={() => {
            onReadNotice(item);
            currentIdRef.current = item.content.contractId;

            const drawerRefMap = {
              [ContractTypeEnum.意向合同]: intentionDetailDrawerRef,
              [ContractTypeEnum.正式合同]: formalDetailDrawerRef,
              [ContractTypeEnum.搬迁意向合同]: relocationIntentionDetailDrawerRef,
              [ContractTypeEnum.搬迁正式合同]: relocationFormalDetailDrawerRef,
              [ContractTypeEnum.门店重装合同]: renovationDetailDrawerRef,
              [ContractTypeEnum.变更法人合同]: changeLegalPersonDetailDrawerRef,
            };

            drawerRefMap[item.content.contractType].current?.open();
          }}
        >
          {item.content.contractCode}
        </a>
      );

      return {
        [NoticeContentTypeEnum.APPROVAL_PASS]: {
          title: '合同',
          subTitle: '审批通过',
          getContent: (item) => (
            <>
              {item.content.operatorName}已经审核通过{item.content.customerName}的
              {getContractTypeText(item)} {renderButton(item)} 审批，可勾选推送至泛微 OA 中
            </>
          ),
        },
        [NoticeContentTypeEnum.APPROVAL_WAITING]: {
          title: '合同',
          subTitle: '待审批',
          getContent: (item) => (
            <>
              {item.content.submitUserName}提交了
              {getContractTypeText(item)} {renderButton(item)} 审批，请及时处理
            </>
          ),
        },
        [NoticeContentTypeEnum.APPROVAL_REJECT]: {
          title: '合同',
          subTitle: '待审批',
          getContent: (item) => (
            <>
              {item.content.operatorName}驳回了{item.content.customerName}的
              {getContractTypeText(item)} {renderButton(item)} 审批，请及时查看
            </>
          ),
        },
        [NoticeContentTypeEnum.WEAVER_OA_PUSH]: {
          title: '合同',
          subTitle: '推送泛微',
          getContent: (item) => (
            <>
              {item.content.operatorName} 将{getContractTypeText(item)} {renderButton(item)}{' '}
              推送至泛微 OA，请及时处理
            </>
          ),
        },
      };
    },
    [NoticeTypeEnum.FISSION_APPLY]: () => {
      const renderButton = (item: NoticeDTO) => (
        <a
          onClick={() => {
            onReadNotice(item);
            currentIdRef.current = item.content.fissionApplyId;
            fissionDetailDrawerRef.current?.open();
          }}
        >
          {item.content.fissionApplyCode}
        </a>
      );

      return {
        [NoticeContentTypeEnum.APPROVAL_PASS]: {
          title: '裂变申请',
          subTitle: '审批通过',
          getContent: (item) => (
            <>
              {item.content.operatorName}已经审核通过您的 {renderButton(item)}{' '}
              裂变申请审批，请及时查看
            </>
          ),
        },
        [NoticeContentTypeEnum.APPROVAL_WAITING]: {
          title: '裂变申请',
          subTitle: '待审批',
          getContent: (item) => (
            <>
              {item.content.submitUserName}提交了 {renderButton(item)} 裂变申请审批，请及时处理
            </>
          ),
        },
        [NoticeContentTypeEnum.APPROVAL_REJECT]: {
          title: '裂变申请',
          subTitle: '拒绝',
          getContent: (item) => (
            <>
              {item.content.operatorName}拒绝了您的 {renderButton(item)} 裂变申请审批，请及时查看
            </>
          ),
        },
        [NoticeContentTypeEnum.APPROVAL_RETURN]: {
          title: '裂变申请',
          subTitle: '驳回',
          getContent: (item) => (
            <>
              {item.content.operatorName}驳回了您的 {renderButton(item)} 裂变申请审批，请及时查看
            </>
          ),
        },
      };
    },
    [NoticeTypeEnum.CONTRACT_REGION_CHANGE]: () => {
      const renderButton = (item: NoticeDTO) => (
        <a
          onClick={() => {
            onReadNotice(item);
            currentIdRef.current = item.content.contractRegionChangeId;
            intendedAreaChangeDetailDrawerRef.current?.open();
          }}
        >
          {item.content.contractRegionChangeCode}
        </a>
      );

      return {
        [NoticeContentTypeEnum.APPROVAL_PASS]: {
          title: '意向区域变更',
          subTitle: '审批通过',
          getContent: (item) => (
            <>
              {item.content.operatorName}已经审核通过您的 {renderButton(item)}{' '}
              意向区域变更审批，请及时查看
            </>
          ),
        },
        [NoticeContentTypeEnum.APPROVAL_WAITING]: {
          title: '意向区域变更',
          subTitle: '待审批',
          getContent: (item) => (
            <>
              {item.content.submitUserName}提交了 {renderButton(item)} 意向区域变更审批，请及时处理
            </>
          ),
        },
        [NoticeContentTypeEnum.APPROVAL_REJECT]: {
          title: '意向区域变更',
          subTitle: '拒绝',
          getContent: (item) => (
            <>
              {item.content.operatorName}拒绝了您的 {renderButton(item)}{' '}
              意向区域变更审批，请及时查看
            </>
          ),
        },
      };
    },
    [NoticeTypeEnum.CONTRACT_POSTPONE]: () => {
      const renderButton = (item: NoticeDTO) => (
        <a
          onClick={() => {
            onReadNotice(item);
            currentIdRef.current = item.content.contractPostponeId;
            intendedContractExtensionDetailDrawerRef.current?.open();
          }}
        >
          {item.content.contractPostponeCode}
        </a>
      );

      return {
        [NoticeContentTypeEnum.APPROVAL_PASS]: {
          title: '意向合同延期',
          subTitle: '审批通过',
          getContent: (item) => (
            <>
              {item.content.operatorName}已经审核通过您的 {renderButton(item)}{' '}
              意向合同延期审批，请及时查看
            </>
          ),
        },
        [NoticeContentTypeEnum.APPROVAL_WAITING]: {
          title: '意向合同延期',
          subTitle: '待审批',
          getContent: (item) => (
            <>
              {item.content.submitUserName}提交了 {renderButton(item)} 意向合同延期审批，请及时处理
            </>
          ),
        },
        [NoticeContentTypeEnum.APPROVAL_REJECT]: {
          title: '意向合同延期',
          subTitle: '拒绝',
          getContent: (item) => (
            <>
              {item.content.operatorName}拒绝了您的 {renderButton(item)}{' '}
              意向合同延期审批，请及时查看
            </>
          ),
        },
      };
    },
  };

  const emitNoticeEvent = (noticeType: NoticeEventEmitterType['type']) => {
    noticeEvent.emit({
      type: noticeType,
      payload: { id: currentIdRef.current! },
    });
  };

  const drawersNode = (
    <>
      <BusinessDetailDrawer
        ref={businessDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => emitNoticeEvent('business-opportunity')}
      />
      <FranchiseDetailDrawer
        ref={franchiseDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => emitNoticeEvent('franchise-application')}
      />
      <VisitDetailDrawer
        ref={visitDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => emitNoticeEvent('visit')}
      />
      <ClueDetailDrawer
        ref={clueDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => emitNoticeEvent('clue')}
      />
      <IntentionDetailDrawer
        ref={intentionDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => emitNoticeEvent('intention')}
      />
      <FormalDetailDrawer
        ref={formalDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => emitNoticeEvent('formal')}
      />
      <FissionDetailDrawer
        ref={fissionDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => emitNoticeEvent('fission')}
      />
      <EvaluationDetailDrawer
        ref={evaluationDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => emitNoticeEvent('evaluation')}
      />
      <IntendedAreaChangeDetailDrawer
        ref={intendedAreaChangeDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => emitNoticeEvent('intended-area-change')}
      />
      <IntendedContractExtensionDetailDrawer
        ref={intendedContractExtensionDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => emitNoticeEvent('intended-contract-extension')}
      />
      <RelocationIntentionDetailDrawer
        ref={relocationIntentionDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => emitNoticeEvent('relocation-intention')}
      />
      <RelocationFormalDetailDrawer
        ref={relocationFormalDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => emitNoticeEvent('relocation-formal')}
      />
      <RenovationDetailDrawer
        ref={renovationDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => emitNoticeEvent('renovation')}
      />
      <ChangeLegalPersonDetailDrawer
        ref={changeLegalPersonDetailDrawerRef}
        getId={() => currentIdRef.current}
        onUpdate={() => emitNoticeEvent('change-legal-person')}
      />
    </>
  );

  return { noticeMap, drawersNode };
};

export default useNoticeMap;
