import { useRef } from 'react';
import { ModalRef } from '@src/common/interface';
import ChangeLegalPersonDetailDrawer from '@src/pages/contract/change-legal-person/components/ChangeLegalPersonDetailDrawer';
import FormalDetailDrawer from '@src/pages/contract/formal/components/FormalDetailDrawer';
import IntentionDetailDrawer from '@src/pages/contract/intention/components/IntentionDetailDrawer';
import RelocationFormalDetailDrawer from '@src/pages/contract/relocation-formal/components/RelocationFormalDetailDrawer';
import RelocationIntentionDetailDrawer from '@src/pages/contract/relocation-intention/components/RelocationIntentionDetailDrawer';
import RenovationDetailDrawer from '@src/pages/contract/renovation/components/RenovationDetailDrawer';
import EvaluationDetailDrawer from '@src/pages/evaluation/components/EvaluationDetailDrawer';
import FissionDetailDrawer from '@src/pages/fission/components/FissionDetailDrawer';
import FranchiseDetailDrawer from '@src/pages/franchise-application/components/FranchiseDetailDrawer';
import IntendedAreaChangeDetailDrawer from '@src/pages/process/intended-area-change/components/IntendedAreaChangeDetailDrawer';
import IntendedContractExtensionDetailDrawer from '@src/pages/process/intended-contract-extension/components/IntendedContractExtensionDetailDrawer';
import {
  AuditOperationEnum,
  TodoBusinessTypeEnum,
  TodoDTO,
  TodoStatusEnum,
} from '@src/services/common/type';
import { NoticeEventEmitterType, useNoticeEventEmitterContext } from '../Notice/context';

interface useTodoMapProps {
  onTodoSuccess: () => void;
}

const useTodoMap = ({ onTodoSuccess }: useTodoMapProps) => {
  const currentIdRef = useRef<number | string | null>(null);
  const fissionDetailDrawerRef = useRef<ModalRef>(null);
  const intentionDetailDrawerRef = useRef<ModalRef>(null);
  const formalDetailDrawerRef = useRef<ModalRef>(null);
  const franchiseDetailDrawerRef = useRef<ModalRef>(null);
  const evaluationDetailDrawerRef = useRef<ModalRef>(null);
  const intendedAreaChangeDetailDrawerRef = useRef<ModalRef>(null);
  const intendedContractExtensionDetailDrawerRef = useRef<ModalRef>(null);
  const relocationIntentionDetailDrawerRef = useRef<ModalRef>(null);
  const relocationFormalDetailDrawerRef = useRef<ModalRef>(null);
  const renovationDetailDrawerRef = useRef<ModalRef>(null);
  const changeLegalPersonDetailDrawerRef = useRef<ModalRef>(null);

  const { noticeEvent } = useNoticeEventEmitterContext();

  const getContentMap = (
    item: TodoDTO,
    drawerRef?: React.RefObject<ModalRef>,
    /** 不通过是否是驳回 */
    rejectIsReturn?: boolean,
  ): {
    todoContentMap: Partial<Record<TodoStatusEnum, React.ReactNode>>;
    doneContentMap: Partial<Record<AuditOperationEnum, React.ReactNode>>;
  } => {
    const renderButton = () =>
      drawerRef ? (
        <a
          onClick={() => {
            currentIdRef.current = item.businessId;
            drawerRef.current?.open();
          }}
        >
          {item.businessName}
        </a>
      ) : (
        item.businessName
      );

    const todoContentMap = {
      [TodoStatusEnum.审核中]: (
        <div>
          {item.createUserName}提交了 {renderButton()} 审批，请及时处理
        </div>
      ),
      [TodoStatusEnum.审核不通过]: (
        <div>
          {item.createUserName}
          {rejectIsReturn ? '驳回' : '拒绝'}了您的 {renderButton()} 审批，请及时查看
        </div>
      ),
      [TodoStatusEnum.驳回]: (
        <div>
          {item.createUserName}驳回了您的 {renderButton()} 审批，请及时查看
        </div>
      ),
    };

    const doneContentMap = {
      [AuditOperationEnum.审核通过]: <div>您通过了 {renderButton()} 审批</div>,
      [AuditOperationEnum.审核不通过]: (
        <div>
          您{rejectIsReturn ? '驳回' : '拒绝'}了 {renderButton()} 审批
        </div>
      ),
      [AuditOperationEnum.驳回]: <div>您驳回了 {renderButton()} 审批</div>,
    };

    return {
      todoContentMap,
      doneContentMap,
    };
  };

  const getTodoContent = (item: TodoDTO, type: 'todo' | 'done'): React.ReactNode => {
    const map: Partial<
      Record<
        TodoBusinessTypeEnum,
        {
          drawerRef: React.RefObject<ModalRef>;
          /** 不通过是否是驳回 */
          rejectIsReturn?: boolean;
        }
      >
    > = {
      [TodoBusinessTypeEnum.ONLINE_APPLY]: {
        drawerRef: franchiseDetailDrawerRef,
      },
      [TodoBusinessTypeEnum.INTERVIEW_EVALUATION]: {
        drawerRef: evaluationDetailDrawerRef,
      },
      [TodoBusinessTypeEnum.INTENTION_CONTRACT]: {
        drawerRef: intentionDetailDrawerRef,
        rejectIsReturn: true,
      },
      [TodoBusinessTypeEnum.FORMAL_CONTRACT]: {
        drawerRef: formalDetailDrawerRef,
        rejectIsReturn: true,
      },
      [TodoBusinessTypeEnum.RELOCATION_INTENTION_CONTRACT]: {
        drawerRef: relocationIntentionDetailDrawerRef,
        rejectIsReturn: true,
      },
      [TodoBusinessTypeEnum.RELOCATION_FORMAL_CONTRACT]: {
        drawerRef: relocationFormalDetailDrawerRef,
        rejectIsReturn: true,
      },
      [TodoBusinessTypeEnum.RENOVATION_CONTRACT]: {
        drawerRef: renovationDetailDrawerRef,
        rejectIsReturn: true,
      },
      [TodoBusinessTypeEnum.CHANGE_LEGAL_PERSON_CONTRACT]: {
        drawerRef: changeLegalPersonDetailDrawerRef,
        rejectIsReturn: true,
      },
      [TodoBusinessTypeEnum.FISSION_APPLY]: {
        drawerRef: fissionDetailDrawerRef,
      },
      [TodoBusinessTypeEnum.INTENTION_CONTRACT_REGION_CHANGE]: {
        drawerRef: intendedAreaChangeDetailDrawerRef,
      },
      [TodoBusinessTypeEnum.INTENTION_CONTRACT_POSTPONE]: {
        drawerRef: intendedContractExtensionDetailDrawerRef,
      },
    };

    const mapItem = map[item.businessType];

    const contentMap = getContentMap(item, mapItem?.drawerRef, mapItem?.rejectIsReturn);

    if (type === 'todo') {
      return contentMap.todoContentMap[item.status];
    } else {
      return contentMap.doneContentMap[item.operationStatus!];
    }
  };

  const emitNoticeEvent = (noticeType: NoticeEventEmitterType['type']) => {
    noticeEvent.emit({
      type: noticeType,
      payload: { id: currentIdRef.current as number },
    });
  };

  const drawersNode = (
    <>
      <FissionDetailDrawer
        ref={fissionDetailDrawerRef}
        getId={() => currentIdRef.current as number}
        onTodoSuccess={onTodoSuccess}
        onUpdate={() => emitNoticeEvent('fission')}
      />
      <IntentionDetailDrawer
        ref={intentionDetailDrawerRef}
        getId={() => currentIdRef.current as number}
        onTodoSuccess={onTodoSuccess}
        onUpdate={() => emitNoticeEvent('intention')}
      />
      <FormalDetailDrawer
        ref={formalDetailDrawerRef}
        getId={() => currentIdRef.current as number}
        onTodoSuccess={onTodoSuccess}
        onUpdate={() => emitNoticeEvent('formal')}
      />
      <FranchiseDetailDrawer
        ref={franchiseDetailDrawerRef}
        getId={() => currentIdRef.current as number}
        onTodoSuccess={onTodoSuccess}
        onUpdate={() => emitNoticeEvent('franchise-application')}
      />
      <EvaluationDetailDrawer
        ref={evaluationDetailDrawerRef}
        getId={() => currentIdRef.current as number}
        onTodoSuccess={onTodoSuccess}
        onUpdate={() => emitNoticeEvent('evaluation')}
      />
      <IntendedAreaChangeDetailDrawer
        ref={intendedAreaChangeDetailDrawerRef}
        getId={() => currentIdRef.current as number}
        onTodoSuccess={onTodoSuccess}
        onUpdate={() => emitNoticeEvent('intended-area-change')}
      />
      <IntendedContractExtensionDetailDrawer
        ref={intendedContractExtensionDetailDrawerRef}
        getId={() => currentIdRef.current as number}
        onTodoSuccess={onTodoSuccess}
        onUpdate={() => emitNoticeEvent('intended-contract-extension')}
      />
      <RelocationIntentionDetailDrawer
        ref={relocationIntentionDetailDrawerRef}
        getId={() => currentIdRef.current as number}
        onTodoSuccess={onTodoSuccess}
        onUpdate={() => emitNoticeEvent('relocation-intention')}
      />
      <RelocationFormalDetailDrawer
        ref={relocationFormalDetailDrawerRef}
        getId={() => currentIdRef.current as number}
        onTodoSuccess={onTodoSuccess}
        onUpdate={() => emitNoticeEvent('relocation-formal')}
      />
      <RenovationDetailDrawer
        ref={renovationDetailDrawerRef}
        getId={() => currentIdRef.current as number}
        onTodoSuccess={onTodoSuccess}
        onUpdate={() => emitNoticeEvent('renovation')}
      />
      <ChangeLegalPersonDetailDrawer
        ref={changeLegalPersonDetailDrawerRef}
        getId={() => currentIdRef.current as number}
        onTodoSuccess={onTodoSuccess}
        onUpdate={() => emitNoticeEvent('change-legal-person')}
      />
    </>
  );

  return { getTodoContent, drawersNode };
};

export default useTodoMap;
