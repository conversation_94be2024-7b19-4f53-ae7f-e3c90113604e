import { useState } from 'react';
import { CalendarOutlined } from '@ant-design/icons';
import { getDoneList, getTodoList } from '@src/services/common';
import { useRequest } from 'ahooks';
import { Drawer, Empty, Pagination, Segmented, Skeleton } from 'antd';
import useTodoMap from './useTodoMap';

enum SegmentedEnum {
  todo = 'todo',
  todoDone = 'todoDone',
}

const Todo = () => {
  const [open, setOpen] = useState(false);
  const [type, setType] = useState<SegmentedEnum>(SegmentedEnum.todo);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  const {
    data: todoList,
    loading: getTodoListLoading,
    refresh: refreshTodoList,
    mutate: mutateTodoList,
  } = useRequest(getTodoList, {
    ready: open && type === SegmentedEnum.todo,
  });
  const {
    data: doneData,
    loading: getDoneListLoading,
    mutate: mutateDoneData,
  } = useRequest(() => getDoneList({ pageNum: current, pageSize }), {
    ready: open && type === SegmentedEnum.todoDone,
    refreshDeps: [current, pageSize],
  });

  const { loading, total, data } = {
    [SegmentedEnum.todo]: {
      loading: getTodoListLoading,
      total: todoList?.length,
      data: todoList?.slice((current - 1) * pageSize, current * pageSize),
    },
    [SegmentedEnum.todoDone]: {
      loading: getDoneListLoading,
      total: doneData?.total,
      data: doneData?.result,
    },
  }[type];

  const { getTodoContent, drawersNode } = useTodoMap({
    onTodoSuccess: () => {
      if (type === SegmentedEnum.todo) {
        refreshTodoList();
      }
    },
  });

  return (
    <>
      <div className="flex items-center p-[6px] text-black" onClick={() => setOpen(true)}>
        <CalendarOutlined />
        <span className="ml-1 text-sm">待办</span>
      </div>

      <Drawer
        width={600}
        title="待办"
        open={open}
        destroyOnHidden
        classNames={{
          body: 'flex flex-col !px-0',
        }}
        push={false}
        onClose={() => {
          setOpen(false);
          setCurrent(1);
        }}
      >
        <div className="pb-2 px-5 overflow-auto">
          <Segmented
            value={type}
            options={[
              { label: '待办(审批)', value: SegmentedEnum.todo },
              { label: '已处理(审批)', value: SegmentedEnum.todoDone },
            ]}
            onChange={(value) => {
              setType(value as SegmentedEnum);
              setCurrent(1);
              mutateTodoList(undefined);
              mutateDoneData(undefined);
            }}
          />
        </div>
        <div className="flex-1 overflow-auto px-5 mt-4">
          {loading ? (
            <Skeleton paragraph={{ rows: 10 }} title={false} active />
          ) : !data?.length ? (
            <Empty />
          ) : (
            <div className="flex flex-col gap-4">
              {data.map((item) => {
                const { title, content } = {
                  title: item.name,
                  content: getTodoContent(item, type === SegmentedEnum.todo ? 'todo' : 'done'),
                };

                return (
                  <div key={item.id} className="px-3 py-2 rounded-lg bg-gray-100/50">
                    <div className="flex justify-between gap-2 mb-1">
                      <div className="font-bold">{title}</div>
                      <span className="flex-1 text-right">{item.createTime}</span>
                    </div>
                    {content}
                  </div>
                );
              })}
            </div>
          )}
        </div>
        {!!total && (
          <div className="flex justify-end mt-5 px-5">
            <Pagination
              current={current}
              size="small"
              total={total}
              showSizeChanger
              pageSize={pageSize}
              onChange={(page, size) => {
                // 相等说明是改了 pageSize 触发，重置为第一页
                if (current === page) {
                  setCurrent(1);
                } else {
                  setCurrent(page);
                }

                setPageSize(size);
              }}
            />
          </div>
        )}

        {drawersNode}
      </Drawer>
    </>
  );
};

export default Todo;
