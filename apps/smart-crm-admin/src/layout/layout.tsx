import { PropsWithChildren, Suspense, useMemo } from 'react';
import { ProLayout } from '@ant-design/pro-components';
import { isInIcestark } from '@ice/stark-app';
import img403 from '@src/assets/403.png';
import loadingImg from '@src/assets/loading.gif';
import logoImg from '@src/assets/logo.png';
import { removeToken } from '@src/common/authorization';
import { isMobile } from '@src/common/constants';
import { usePermission } from '@src/permissions';
import { RouteObjWithMeta } from '@src/routes/type';
import { logoutCurrentAccount } from '@src/services/common';
import useUserStore from '@src/store/useUserStore';
import { navigateToGaiaLogin } from '@src/utils';
import { useEventEmitter } from 'ahooks';
import { Dropdown, Result } from 'antd';
import { Link, Outlet, useLocation, useMatches, useNavigate } from 'react-router-dom';
import Notice from './components/Notice';
import { NoticeEventEmitterContext, NoticeEventEmitterType } from './components/Notice/context';
import Todo from './components/Todo';
import defaultLayoutSetting from './defaultLayoutSetting';

export const GlobalLayout = (props: PropsWithChildren) => {
  const location = useLocation();
  const navigate = useNavigate();

  const { user, permissions, menus } = useUserStore();
  const noticeEvent = useEventEmitter<NoticeEventEmitterType>();

  const noticeContextValue = useMemo(() => ({ noticeEvent }), [noticeEvent]);

  return (
    <NoticeEventEmitterContext.Provider value={noticeContextValue}>
      <ProLayout
        {...defaultLayoutSetting}
        {...props}
        className={isInIcestark() ? 'layout-isInIcestark' : ''}
        route={menus}
        ErrorBoundary={false}
        breadcrumbProps={{
          itemRender: (currentRoute, _, items, __) => {
            const isFirst = currentRoute?.linkPath === items[0]?.linkPath;
            const isLast = currentRoute?.linkPath === items[items.length - 1]?.linkPath;

            return isFirst || isLast ? (
              currentRoute.title
            ) : (
              <Link to={currentRoute.linkPath || ''}>{currentRoute.title}</Link>
            );
          },
        }}
        logo={logoImg}
        title="招商 CRM"
        location={location}
        menuProps={{
          onSelect: ({ key }) => navigate(key),
        }}
        menuContentRender={!permissions.length ? false : undefined}
        actionsRender={() => [<Todo />, <Notice />]}
        menuHeaderRender={isInIcestark() ? false : undefined}
        token={{
          header: {
            heightLayoutHeader: isInIcestark() ? 40 : 0,
          },
        }}
        avatarProps={
          isInIcestark() && !isMobile
            ? false
            : {
                src: logoImg,
                title: user.nickName || user.userName,
                size: 'small',
                render: (_, defaultDom) => (
                  <Dropdown
                    menu={{
                      items: [
                        {
                          label: '退出登录',
                          onClick: async () => {
                            logoutCurrentAccount().finally(() => {
                              removeToken();
                              navigateToGaiaLogin();
                            });
                          },
                          key: 'logout',
                        },
                      ],
                    }}
                    placement="topRight"
                    arrow={false}
                  >
                    {defaultDom}
                  </Dropdown>
                ),
              }
        }
      >
        <Suspense
          fallback={
            <div className="w-full h-screen flex flex-col justify-center items-center">
              <img className="size-20" src={loadingImg} alt="" />
              <span className="mt-3 text-[#86909C]">加载中...</span>
            </div>
          }
        >
          <PermissionLayout />
        </Suspense>
      </ProLayout>
    </NoticeEventEmitterContext.Provider>
  );
};

/**
 * 根据权限判断是否渲染组件
 * @param props
 * @returns
 */
const PermissionLayout = () => {
  const checkPermission = usePermission();
  const matches = useMatches();

  const lastRoute = matches[matches.length - 1];

  const permission = (lastRoute?.handle as RouteObjWithMeta['handle'])?.permission;
  const { permissions } = useUserStore();

  if (!permissions.length || !checkPermission(permission)) {
    return (
      <Result
        title="访问受限"
        subTitle="您没有该页面权限，请联系管理员开通后重试。"
        className="mt-[200px]"
        icon={<img src={img403} alt="" className="m-auto w-[160px]" />}
      />
    );
  }

  return <Outlet />;
};
