import { ProLayoutProps } from '@ant-design/pro-components';

/**
 * @name
 */
const defaultLayoutSetting: ProLayoutProps & {
  pwa?: boolean;
  logo?: string;
} = {
  navTheme: 'light',
  // colorPrimary: '#E10600',
  layout: 'side',
  // contentWidth: 'Fluid',
  // fixedHeader: true,
  // fixSiderbar: true,
  // colorWeak: false,
  // title: 'Ant Design Pro',
  // pwa: true,
  // iconfontUrl: '',
  token: {
    // 参见ts声明，demo 见文档，通过token 修改样式
    // https://procomponents.ant.design/components/layout#%E9%80%9A%E8%BF%87-token-%E4%BF%AE%E6%94%B9%E6%A0%B7%E5%BC%8F
    // header: {
    //   colorBgHeader: '#E10600',
    //   colorHeaderTitle: '#fff',
    //   colorTextRightActionsItem: '#fff',
    // },
    sider: {
      // colorTextMenuTitle: '#000', //sider 的标题字体颜色
      colorMenuItemDivider: 'transparent', // menuItem 分割线的颜色
      // colorTextMenu: '#000', // menuItem 的字体颜色
      // colorTextMenuSecondary: '#000', //menu 的二级字体颜色，比如 footer 和 action 的 icon
      // colorTextMenuActive: '#000', //menuItem hover 的选中字体颜色
      // colorTextMenuSelected: 'var(--ant-color-primary)', // menuItem 的选中字体颜色
      // colorTextMenuItemHover: 'var(--ant-color-primary)', // menuItem 的 hover 字体颜色
      // colorBgMenuItemHover: 'transparent', //menuItem 的 hover 背景颜色
      // colorBgMenuItemSelected: '#ffdcd4', // menuItem 的选中背景颜色
      // colorBgMenuItemCollapsedElevated: 'transparent', //收起 menuItem 的弹出菜单背景颜色
    },
  },
};

export default defaultLayoutSetting;
