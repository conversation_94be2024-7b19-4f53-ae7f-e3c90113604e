import React from 'react';
import { MenuDataItem, ProColumns, ProLayoutProps } from '@ant-design/pro-components';
import { isMobile } from '@src/common/constants';
import { DescriptionFieldGroupType, DescriptionFieldType } from '@src/common/interface';
import { Editable } from '@src/components';
import { FiltersType, ValueType } from '@src/components/ETable';
import { TemplateQuestionTypeEnum } from '@src/pages/standard-settings/template/components/QuestionConfig/type';
import { commonCheckPermission } from '@src/permissions';
import { RouteObjWithMeta } from '@src/routes/type';
import { FieldItemType } from '@src/services/clues/my/type';
import { RegionModeEnum } from '@src/services/common/type';
import { Descriptions, DescriptionsProps, message, UploadFile } from 'antd';
import { get } from 'lodash-es';

/**
 * 通过递归将AppRoute转换为ProLayout的menu数据
 * @param routes
 * @returns
 */
export const convertAppRouteToProLayoutMenuData = (
  route: RouteObjWithMeta,
  permissions: string[],
): ProLayoutProps['route'] => {
  const hiddenInMenu = route.index ? true : route.handle?.hiddenInMenu;

  const hasPermission = commonCheckPermission(permissions, route.handle?.permission);

  if (hiddenInMenu || !hasPermission) return undefined; // 在菜单中隐藏自己和子节点

  const newRoute: MenuDataItem = {
    ...route.handle,
    path: route.path,
  };

  if (!route.children) {
    return newRoute;
  }

  return {
    ...newRoute,
    children: route.children.reduce<Required<Required<ProLayoutProps>['route']>['children']>(
      (total, child) => {
        const menu = convertAppRouteToProLayoutMenuData(child, permissions);

        if (menu) {
          total.push(menu);
        }

        return total;
      },
      [],
    ),
  };
};

/**
 * 表格筛选转换成请求需要的格式
 * @param filters
 * @returns
 */
export const getParamsFromFilters = (filters?: FiltersType) => ({
  searchRelation: filters?.searchRelation,
  // 数组转成字符串，逗号隔开
  fields: filters?.value?.map((i) => ({
    ...i,
    value: ['number', 'boolean'].includes(typeof i.value) ? i.value : String(i.value),
  })),
});

export const downloadUrl = (url?: string, name?: string) => {
  if (!url) {
    return;
  }

  const link = document.createElement('a');

  link.href = url;

  if (name) {
    link.download = name;
  }

  link.click();
  link.remove();
};

export const getFilterProps = (
  item: Pick<FieldItemType, 'options' | 'precisions'> & {
    valueType: ValueType | TemplateQuestionTypeEnum;
  },
) => {
  if ([ValueType.SINGLE, ValueType.MULTIPLE].includes(item.valueType as any)) {
    return {
      options: item.options,
    };
  }

  if (item.valueType === ValueType.NUMBER) {
    return {
      precision: item.precisions,
    };
  }
};

export const getFieldProps = (
  item: Pick<FieldItemType, 'options' | 'precisions' | 'regionLevel' | 'regionMode'> & {
    valueType: ValueType | TemplateQuestionTypeEnum;
    sensitiveValue?: string;
    encryptSensitiveMap?: Record<string, any>;
  },
) => {
  if ([ValueType.SINGLE, ValueType.MULTIPLE].includes(item.valueType as any)) {
    return {
      options: item.options,
    };
  }

  if (item.valueType === ValueType.NUMBER) {
    return {
      precision: item.precisions,
      max: 999999999,
    };
  }

  if (item.valueType === ValueType.REGION) {
    return {
      regionLevel: item.regionLevel,
      multiple: item.regionMode === RegionModeEnum.MULTIPLE,
    };
  }

  if ([ValueType.PHONE, ValueType.IDENTITY_CARD].includes(item.valueType as any)) {
    return {
      sensitiveValue: item.sensitiveValue,
      encryptSensitiveMap: item.encryptSensitiveMap,
    };
  }
};

export const getDefaultActiveKeyBySearchParam = (tabs: string[], searchParam: string | null) => {
  if (searchParam && tabs.includes(searchParam)) {
    return searchParam;
  }

  return tabs[0];
};

export const optionsToValueEnum = <
  T extends string | number | boolean | symbol,
  U extends boolean = false,
>(
  options: { label: string; value: T }[],
  useMap?: U,
): U extends true ? Map<T, string> : Record<string, string> => {
  if (useMap) {
    return options.reduce((prev, cur) => {
      prev.set(cur.value, cur.label);

      return prev;
    }, new Map<T, string>()) as any;
  }

  return options.reduce((prev, cur) => {
    prev[String(cur.value)] = cur.label;

    return prev;
  }, {} as Record<string, string>) as any;
};

/* ts 的 enum 转 options */
export const enum2Options = <T extends Record<string, string | number>>(
  values: T,
  type: 'string' | 'number' = 'string',
) =>
  Object.keys(values).reduce<{ label: string; value: T[keyof T] }[]>((total, label) => {
    const value = values[label as keyof T];

    if (type === 'string') {
      total.push({ label, value });
    } else {
      if (typeof value === 'number') {
        total.push({ label, value });
      }
    }

    return total;
  }, []);

/* ts 的 enum 转 valueEnum */
export function enum2ValueEnum(obj: Record<any, any>, type: 'string' | 'number' = 'string') {
  return Object.keys(obj).reduce<Record<string, string>>((total, label) => {
    const value = obj[label];

    if (type === 'number') {
      if (typeof value === 'number') {
        total[value] = label;
      }
    } else {
      total[value] = label;
    }

    total[value] = label;

    return total;
  }, {});
}

export const calcTableWidth = (columns: ProColumns[], defaultWidth = 180) =>
  columns.reduce(
    (total, cur) =>
      total + (cur.hidden || cur.hideInTable ? 0 : (cur.width as number) ?? defaultWidth),
    0,
  );

/**
 * before: type.indexOf('image/') === 0
 * after: type.indexOf('image') === 0
 */
const isImageFileType = (type: string): boolean => type.indexOf('image') === 0;
const extname = (url: string = '') => {
  const temp = url.split('/');
  const filename = temp[temp.length - 1];
  const filenameWithoutSuffix = filename.split(/#|\?/)[0];

  return (/\.[^./\\]*$/.exec(filenameWithoutSuffix) || [''])[0];
};

/**
 *  copy from https://github.com/ant-design/ant-design/blob/4ad5830eecfb87471cd8ac588c5d992862b70770/components/upload/utils.tsx#L47-L68
 *  因为小程序提交过来的图片的 fileType 为 "image"，在 antd 内部 isImageUrl 判断不为图片，导致不能在 Upload 中展示缩略图。修改上面的 isImageFileType 判断
 */
export const isImageUrl = (file: UploadFile): boolean => {
  if (file.type) {
    return isImageFileType(file.type);
  }

  const url: string = (file.thumbUrl || file.url) as string;
  const extension = extname(url);

  if (
    /^data:image\//.test(url) ||
    /(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico)$/i.test(extension)
  ) {
    return true;
  }

  if (/^data:/.test(url)) {
    // other file types of base64
    return false;
  }

  if (extension) {
    // other file types which have extension
    return false;
  }

  return true;
};

export const compatibleTableActionWidth = (width: number) => {
  return isMobile ? 60 : width;
};

export async function copyClipboard(text: string) {
  if (navigator.clipboard && navigator.clipboard.writeText) {
    return await navigator.clipboard.writeText(text);
  }

  const el = document.createElement('textarea');

  el.value = text;
  el.style.position = 'absolute';
  el.style.left = '-9999px';
  document.body.appendChild(el);
  el.focus();
  el.select();
  el.setSelectionRange(0, text.length);
  document.execCommand('copy');
  document.body.removeChild(el);
}

function renderCommonDescriptions<T>(
  items: DescriptionFieldType[],
  data: T,
  props?: DescriptionsProps,
) {
  return (
    <Descriptions
      column={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2, xxl: 2 }}
      items={items
        .filter((i) => !i.hidden)
        .map((i, index) => ({
          label: i.label,
          key: index,
          children:
            'children' in i ? (
              i.children
            ) : (
              <Editable
                valueType={i.valueType}
                fieldProps={i.fieldProps}
                formItemProps={i.formItemProps}
                initialValue={get(data, i.field)}
              />
            ),
        }))}
      {...props}
    />
  );
}

export function renderDescriptions<T>(
  items: DescriptionFieldType[] | DescriptionFieldGroupType[],
  data: T,
) {
  if (!items.length) {
    return null;
  }

  const isCommon = 'field' in items[0];

  if (isCommon) {
    return renderCommonDescriptions(items as DescriptionFieldType[], data);
  } else {
    return (
      <div className="flex flex-col gap-5">
        {(items as DescriptionFieldGroupType[]).map((item, index) => (
          <React.Fragment key={index}>
            {renderCommonDescriptions(item.children, data, { title: item.label })}
          </React.Fragment>
        ))}
      </div>
    );
  }
}

export const previewPdf = (url: string) => {
  message.info('正在打开 PDF 文件，请稍等...');
  fetch(url)
    .then((response) => response.blob())
    .then((blob) => {
      const pdfUrl = URL.createObjectURL(blob);

      window.open(pdfUrl);
    });
};

export const navigateToGaiaLogin = () => {
  window.location.href = `${import.meta.env.VITE_GAIA_API_URL}/#/user/login?redirectUrl=${
    window.location.href
  }`;
};
